#!/usr/bin/env python
"""
Comprehensive test runner for Vierla Backend
Provides easy test execution with coverage reporting
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.testing')

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}")
    print(f"Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def main():
    parser = argparse.ArgumentParser(description='Run Vierla Backend Tests')
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--app', help='Test specific app (e.g., authentication)')
    parser.add_argument('--test', help='Run specific test')
    
    args = parser.parse_args()
    
    # Change to backend directory
    os.chdir(backend_dir)
    
    # Base pytest command
    base_cmd = ['python', '-m', 'pytest']
    
    # Add verbosity
    if args.verbose:
        base_cmd.extend(['-v', '--tb=short'])
    
    # Add coverage if requested
    if args.coverage:
        base_cmd.extend(['--cov=apps', '--cov-report=term-missing', '--cov-report=html'])
    
    # Determine test path
    test_path = 'tests/'
    if args.unit:
        test_path = 'tests/unit/'
    elif args.integration:
        test_path = 'tests/integration/'
    elif args.app:
        test_path = f'tests/unit/test_{args.app}.py'
    elif args.test:
        test_path = args.test
    
    # Build final command
    cmd = base_cmd + [test_path]
    
    print("🧪 Vierla Backend Test Runner")
    print("=" * 50)
    
    # Set environment variable
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = 'config.settings.testing'
    
    # Run tests
    success = run_command(cmd, f"Running tests: {test_path}")
    
    if success:
        print("\n🎉 All tests completed successfully!")
        if args.coverage:
            print("\n📊 Coverage report generated in htmlcov/index.html")
    else:
        print("\n💥 Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == '__main__':
    main()
