import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CategoryCard, { ServiceCategory, CategoryCardProps } from '../CategoryCard';

// Mock service category data
const mockCategory: ServiceCategory = {
  id: 'category-1',
  name: 'Hair Services',
  slug: 'hair-services',
  icon: '💇‍♀️',
  color: '#FF5722',
  mobile_icon: '✂️',
  is_popular: true,
  service_count: 15,
  image: undefined,
};

const mockCategoryWithImage: ServiceCategory = {
  ...mockCategory,
  id: 'category-2',
  name: 'Spa Services',
  image: 'https://example.com/spa-image.jpg',
};

const mockCategoryNotPopular: ServiceCategory = {
  ...mockCategory,
  id: 'category-3',
  name: 'Nail Services',
  is_popular: false,
  service_count: 8,
};

const defaultProps: CategoryCardProps = {
  category: mockCategory,
  onPress: jest.fn(),
  testID: 'category-card',
};

describe('CategoryCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders correctly with all category information', () => {
      const { getByTestId, getByText } = render(<CategoryCard {...defaultProps} />);

      expect(getByTestId('category-card')).toBeTruthy();
      expect(getByTestId('category-card-name')).toBeTruthy();
      expect(getByTestId('category-card-service-count')).toBeTruthy();
      expect(getByTestId('category-card-icon-container')).toBeTruthy();
      expect(getByTestId('category-card-icon')).toBeTruthy();

      expect(getByText('Hair Services')).toBeTruthy();
      expect(getByText('15 services')).toBeTruthy();
      expect(getByText('✂️')).toBeTruthy(); // mobile_icon takes precedence
    });

    it('renders category image when provided', () => {
      const props = {
        ...defaultProps,
        category: mockCategoryWithImage,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-image')).toBeTruthy();
    });

    it('renders icon container when no image provided', () => {
      const { getByTestId } = render(<CategoryCard {...defaultProps} />);
      expect(getByTestId('category-card-icon-container')).toBeTruthy();
    });

    it('uses mobile_icon when available', () => {
      const { getByText } = render(<CategoryCard {...defaultProps} />);
      expect(getByText('✂️')).toBeTruthy();
    });

    it('falls back to regular icon when mobile_icon not available', () => {
      const categoryWithoutMobileIcon = {
        ...mockCategory,
        mobile_icon: undefined,
      };
      const props = {
        ...defaultProps,
        category: categoryWithoutMobileIcon,
      };
      const { getByText } = render(<CategoryCard {...props} />);
      expect(getByText('💇‍♀️')).toBeTruthy();
    });

    it('renders popular badge for popular categories', () => {
      const { getByTestId, getByText } = render(<CategoryCard {...defaultProps} />);
      expect(getByTestId('category-card-popular-badge')).toBeTruthy();
      expect(getByText('Popular')).toBeTruthy();
    });

    it('does not render popular badge for non-popular categories', () => {
      const props = {
        ...defaultProps,
        category: mockCategoryNotPopular,
      };
      const { queryByTestId } = render(<CategoryCard {...props} />);
      expect(queryByTestId('category-card-popular-badge')).toBeNull();
    });

    it('displays correct service count with singular form', () => {
      const singleServiceCategory = {
        ...mockCategory,
        service_count: 1,
      };
      const props = {
        ...defaultProps,
        category: singleServiceCategory,
      };
      const { getByText } = render(<CategoryCard {...props} />);
      expect(getByText('1 service')).toBeTruthy();
    });

    it('displays correct service count with plural form', () => {
      const { getByText } = render(<CategoryCard {...defaultProps} />);
      expect(getByText('15 services')).toBeTruthy();
    });
  });

  describe('Variants', () => {
    it('applies default styling for default variant', () => {
      const { getByTestId } = render(<CategoryCard {...defaultProps} />);
      const card = getByTestId('category-card');
      expect(card).toBeTruthy();
    });

    it('applies compact styling for compact variant', () => {
      const props = {
        ...defaultProps,
        variant: 'compact' as const,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      const card = getByTestId('category-card');
      expect(card).toBeTruthy();
    });

    it('applies icon-only styling for icon-only variant', () => {
      const props = {
        ...defaultProps,
        variant: 'icon-only' as const,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      const card = getByTestId('category-card');
      expect(card).toBeTruthy();
    });

    it('hides service count in icon-only variant', () => {
      const props = {
        ...defaultProps,
        variant: 'icon-only' as const,
      };
      const { queryByTestId } = render(<CategoryCard {...props} />);
      expect(queryByTestId('category-card-service-count')).toBeNull();
    });

    it('hides popular badge in icon-only variant', () => {
      const props = {
        ...defaultProps,
        variant: 'icon-only' as const,
      };
      const { queryByTestId } = render(<CategoryCard {...props} />);
      expect(queryByTestId('category-card-popular-badge')).toBeNull();
    });
  });

  describe('Interaction', () => {
    it('calls onPress when card is pressed', () => {
      const onPress = jest.fn();
      const props = {
        ...defaultProps,
        onPress,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      
      fireEvent.press(getByTestId('category-card'));
      expect(onPress).toHaveBeenCalledTimes(1);
    });

    it('handles text truncation for long category names', () => {
      const longNameCategory = {
        ...mockCategory,
        name: 'This is a very long category name that should be truncated when displayed',
      };
      const props = {
        ...defaultProps,
        category: longNameCategory,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-name')).toBeTruthy();
    });

    it('allows multiple lines for category name in icon-only variant', () => {
      const longNameCategory = {
        ...mockCategory,
        name: 'Long Category Name',
      };
      const props = {
        ...defaultProps,
        category: longNameCategory,
        variant: 'icon-only' as const,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-name')).toBeTruthy();
    });
  });

  describe('Color Customization', () => {
    it('applies custom color to icon container', () => {
      const customColorCategory = {
        ...mockCategory,
        color: '#9C27B0',
      };
      const props = {
        ...defaultProps,
        category: customColorCategory,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-icon-container')).toBeTruthy();
    });

    it('falls back to default color when no color provided', () => {
      const noColorCategory = {
        ...mockCategory,
        color: '',
      };
      const props = {
        ...defaultProps,
        category: noColorCategory,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-icon-container')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('has proper testID for accessibility testing', () => {
      const { getByTestId } = render(<CategoryCard {...defaultProps} />);
      expect(getByTestId('category-card')).toBeTruthy();
    });

    it('handles custom testID prop', () => {
      const props = {
        ...defaultProps,
        testID: 'custom-category-card',
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('custom-category-card')).toBeTruthy();
    });

    it('provides accessible text for service count', () => {
      const { getByTestId } = render(<CategoryCard {...defaultProps} />);
      expect(getByTestId('category-card-service-count')).toBeTruthy();
    });
  });

  describe('Edge Cases', () => {
    it('handles zero service count', () => {
      const zeroServiceCategory = {
        ...mockCategory,
        service_count: 0,
      };
      const props = {
        ...defaultProps,
        category: zeroServiceCategory,
      };
      const { getByText } = render(<CategoryCard {...props} />);
      expect(getByText('0 services')).toBeTruthy();
    });

    it('handles missing icon gracefully', () => {
      const noIconCategory = {
        ...mockCategory,
        icon: '',
        mobile_icon: '',
      };
      const props = {
        ...defaultProps,
        category: noIconCategory,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-icon-container')).toBeTruthy();
    });

    it('handles very long category names', () => {
      const veryLongNameCategory = {
        ...mockCategory,
        name: 'This is an extremely long category name that should be handled gracefully by the component without breaking the layout or causing any rendering issues',
      };
      const props = {
        ...defaultProps,
        category: veryLongNameCategory,
      };
      const { getByTestId } = render(<CategoryCard {...props} />);
      expect(getByTestId('category-card-name')).toBeTruthy();
    });
  });
});
