# Service Catalog Deployment Guide

## Overview

This guide covers the deployment of the Vierla Service Catalog system in production environments. The system is designed for high availability, scalability, and mobile-first performance.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   <PERSON><PERSON><PERSON> App    │    │   PostgreSQL    │
│    (Nginx)      │────│   (Gun<PERSON>)    │────│   (PostGIS)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │     Redis       │    │     Ce<PERSON>y      │
                       │   (Cache/Queue) │────│   (Workers)     │
                       └─────────────────┘    └─────────────────┘
```

## Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04 LTS or CentOS 8+
- **CPU**: 4+ cores (8+ recommended for production)
- **RAM**: 8GB minimum (16GB+ recommended)
- **Storage**: 100GB SSD minimum
- **Network**: 1Gbps connection

### Software Dependencies
- Python 3.11+
- PostgreSQL 14+ with PostGIS extension
- Redis 6.0+
- Nginx 1.18+
- Node.js 18+ (for frontend assets)
- Docker & Docker Compose (optional)

## Environment Setup

### 1. System Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install system dependencies
sudo apt install -y \
    python3.11 \
    python3.11-venv \
    python3.11-dev \
    postgresql-14 \
    postgresql-14-postgis-3 \
    redis-server \
    nginx \
    git \
    curl \
    build-essential \
    libpq-dev \
    gdal-bin \
    libgdal-dev

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

### 2. Database Setup

```bash
# Switch to postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE vierla_production;
CREATE USER vierla_user WITH PASSWORD 'secure_password_here';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE vierla_production TO vierla_user;
ALTER USER vierla_user CREATEDB;

-- Enable PostGIS extension
\c vierla_production
CREATE EXTENSION postgis;
CREATE EXTENSION postgis_topology;

-- Exit psql
\q
```

### 3. Redis Configuration

```bash
# Edit Redis configuration
sudo nano /etc/redis/redis.conf

# Key settings:
# maxmemory 2gb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000

# Restart Redis
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

## Application Deployment

### 1. Code Deployment

```bash
# Create application directory
sudo mkdir -p /opt/vierla
sudo chown $USER:$USER /opt/vierla
cd /opt/vierla

# Clone repository
git clone https://github.com/vierla/backend.git
cd backend

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements/production.txt
```

### 2. Environment Configuration

```bash
# Create environment file
cp .env.example .env.production

# Edit environment variables
nano .env.production
```

**Production Environment Variables:**
```bash
# Django Settings
DJANGO_SETTINGS_MODULE=config.settings.production
SECRET_KEY=your_super_secret_key_here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DATABASE_URL=postgres://vierla_user:secure_password@localhost:5432/vierla_production

# Redis
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True

# Media and Static Files
MEDIA_ROOT=/opt/vierla/media
STATIC_ROOT=/opt/vierla/static
MEDIA_URL=/media/
STATIC_URL=/static/

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password

# Logging
LOG_LEVEL=INFO
LOG_FILE=/var/log/vierla/django.log

# Performance
CONN_MAX_AGE=60
DATABASE_POOL_SIZE=20

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
```

### 3. Database Migration

```bash
# Activate virtual environment
source venv/bin/activate

# Run migrations
python manage.py migrate --settings=config.settings.production

# Create superuser
python manage.py createsuperuser --settings=config.settings.production

# Collect static files
python manage.py collectstatic --noinput --settings=config.settings.production

# Load initial data (optional)
python manage.py loaddata fixtures/categories.json --settings=config.settings.production
```

## Web Server Configuration

### 1. Gunicorn Setup

```bash
# Create Gunicorn configuration
sudo nano /opt/vierla/backend/gunicorn.conf.py
```

**Gunicorn Configuration:**
```python
# gunicorn.conf.py
import multiprocessing

bind = "127.0.0.1:8000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "gevent"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 5
preload_app = True
reload = False

# Logging
accesslog = "/var/log/vierla/gunicorn_access.log"
errorlog = "/var/log/vierla/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "vierla_backend"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190
```

### 2. Systemd Service

```bash
# Create systemd service file
sudo nano /etc/systemd/system/vierla-backend.service
```

**Service Configuration:**
```ini
[Unit]
Description=Vierla Backend Django Application
After=network.target postgresql.service redis.service

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/vierla/backend
Environment=DJANGO_SETTINGS_MODULE=config.settings.production
ExecStart=/opt/vierla/backend/venv/bin/gunicorn config.wsgi:application -c gunicorn.conf.py
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/vierla/media /var/log/vierla

[Install]
WantedBy=multi-user.target
```

### 3. Nginx Configuration

```bash
# Create Nginx site configuration
sudo nano /etc/nginx/sites-available/vierla-backend
```

**Nginx Configuration:**
```nginx
upstream vierla_backend {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Basic Settings
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Static Files
    location /static/ {
        alias /opt/vierla/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Media Files
    location /media/ {
        alias /opt/vierla/media/;
        expires 1M;
        add_header Cache-Control "public";
        access_log off;
    }

    # API Endpoints
    location /api/ {
        proxy_pass http://vierla_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # Admin Interface
    location /admin/ {
        proxy_pass http://vierla_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health Check
    location /health/ {
        proxy_pass http://vierla_backend;
        access_log off;
    }

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    location /api/ {
        limit_req zone=api burst=20 nodelay;
    }
}
```

## Celery Configuration

### 1. Celery Worker Service

```bash
# Create Celery worker service
sudo nano /etc/systemd/system/vierla-celery.service
```

**Worker Service:**
```ini
[Unit]
Description=Vierla Celery Worker
After=network.target redis.service

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/opt/vierla/backend
Environment=DJANGO_SETTINGS_MODULE=config.settings.production
ExecStart=/opt/vierla/backend/venv/bin/celery -A config worker -l info --detach
ExecStop=/opt/vierla/backend/venv/bin/celery -A config control shutdown
ExecReload=/opt/vierla/backend/venv/bin/celery -A config control reload
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. Celery Beat Service

```bash
# Create Celery beat service
sudo nano /etc/systemd/system/vierla-celery-beat.service
```

**Beat Service:**
```ini
[Unit]
Description=Vierla Celery Beat
After=network.target redis.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/vierla/backend
Environment=DJANGO_SETTINGS_MODULE=config.settings.production
ExecStart=/opt/vierla/backend/venv/bin/celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## SSL Certificate Setup

### 1. Let's Encrypt Installation

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

## Service Management

### 1. Start Services

```bash
# Create log directory
sudo mkdir -p /var/log/vierla
sudo chown www-data:www-data /var/log/vierla

# Change ownership
sudo chown -R www-data:www-data /opt/vierla

# Enable and start services
sudo systemctl daemon-reload
sudo systemctl enable vierla-backend
sudo systemctl enable vierla-celery
sudo systemctl enable vierla-celery-beat
sudo systemctl enable nginx

sudo systemctl start vierla-backend
sudo systemctl start vierla-celery
sudo systemctl start vierla-celery-beat
sudo systemctl restart nginx
```

### 2. Service Status Check

```bash
# Check service status
sudo systemctl status vierla-backend
sudo systemctl status vierla-celery
sudo systemctl status vierla-celery-beat
sudo systemctl status nginx

# Check logs
sudo journalctl -u vierla-backend -f
sudo tail -f /var/log/vierla/django.log
sudo tail -f /var/log/nginx/access.log
```

## Monitoring and Logging

### 1. Log Rotation

```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/vierla
```

**Logrotate Configuration:**
```
/var/log/vierla/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload vierla-backend
    endscript
}
```

### 2. Health Monitoring

```bash
# Create health check script
sudo nano /opt/vierla/scripts/health_check.sh
```

**Health Check Script:**
```bash
#!/bin/bash

# Check Django application
curl -f http://localhost:8000/health/ || exit 1

# Check database connection
sudo -u www-data /opt/vierla/backend/venv/bin/python /opt/vierla/backend/manage.py check --database default --settings=config.settings.production || exit 1

# Check Redis connection
redis-cli ping || exit 1

echo "All services healthy"
```

## Backup Strategy

### 1. Database Backup

```bash
# Create backup script
sudo nano /opt/vierla/scripts/backup_db.sh
```

**Backup Script:**
```bash
#!/bin/bash

BACKUP_DIR="/opt/vierla/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="vierla_production"

mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -h localhost -U vierla_user -d $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

### 2. Media Files Backup

```bash
# Create media backup script
sudo nano /opt/vierla/scripts/backup_media.sh
```

**Media Backup Script:**
```bash
#!/bin/bash

BACKUP_DIR="/opt/vierla/backups"
DATE=$(date +%Y%m%d_%H%M%S)
MEDIA_DIR="/opt/vierla/media"

mkdir -p $BACKUP_DIR

# Create media backup
tar -czf $BACKUP_DIR/media_backup_$DATE.tar.gz -C $MEDIA_DIR .

# Keep only last 7 days of media backups
find $BACKUP_DIR -name "media_backup_*.tar.gz" -mtime +7 -delete

echo "Media backup completed: media_backup_$DATE.tar.gz"
```

### 3. Automated Backups

```bash
# Add to crontab
sudo crontab -e

# Add these lines:
# Database backup every 6 hours
0 */6 * * * /opt/vierla/scripts/backup_db.sh

# Media backup daily at 2 AM
0 2 * * * /opt/vierla/scripts/backup_media.sh
```

## Performance Optimization

### 1. Database Optimization

```sql
-- Connect to database
sudo -u postgres psql vierla_production

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_catalog_service_provider_id ON catalog_service(provider_id);
CREATE INDEX CONCURRENTLY idx_catalog_service_category_id ON catalog_service(category_id);
CREATE INDEX CONCURRENTLY idx_catalog_service_is_active ON catalog_service(is_active);
CREATE INDEX CONCURRENTLY idx_catalog_serviceprovider_location ON catalog_serviceprovider USING GIST(ST_Point(longitude, latitude));

-- Analyze tables
ANALYZE;
```

### 2. Redis Optimization

```bash
# Edit Redis configuration for production
sudo nano /etc/redis/redis.conf

# Add these optimizations:
# maxmemory 4gb
# maxmemory-policy allkeys-lru
# tcp-keepalive 60
# timeout 300
```

## Security Hardening

### 1. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Fail2Ban Setup

```bash
# Install Fail2Ban
sudo apt install fail2ban

# Create Nginx jail
sudo nano /etc/fail2ban/jail.local
```

**Fail2Ban Configuration:**
```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
```

## Troubleshooting

### Common Issues

1. **Service won't start**: Check logs with `journalctl -u service-name`
2. **Database connection errors**: Verify PostgreSQL is running and credentials are correct
3. **Static files not loading**: Run `collectstatic` and check Nginx configuration
4. **High memory usage**: Monitor with `htop` and adjust worker counts
5. **Slow queries**: Check database indexes and query optimization

### Performance Monitoring

```bash
# Monitor system resources
htop
iotop
nethogs

# Monitor Django performance
sudo tail -f /var/log/vierla/django.log | grep "slow"

# Monitor database performance
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE state = 'active';"
```

## Scaling Considerations

### Horizontal Scaling
- Use load balancer (HAProxy/AWS ALB)
- Deploy multiple application servers
- Implement database read replicas
- Use CDN for static/media files

### Vertical Scaling
- Increase server resources
- Optimize database configuration
- Tune Gunicorn worker count
- Implement caching strategies

## Support

For deployment support:
- Documentation: https://docs.vierla.com/deployment
- Support Email: <EMAIL>
- Emergency Contact: +1-555-VIERLA-1
