#!/usr/bin/env python
"""
Performance Monitoring Script for Vierla Backend
Monitors CPU, memory, and system performance metrics
"""
import time
import psutil
from datetime import datetime


def get_performance_stats():
    """Get current system performance statistics"""
    try:
        stats = {}
        
        # CPU usage
        stats['cpu_percent'] = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        stats['memory_percent'] = memory.percent
        stats['memory_available'] = round(memory.available / (1024**3), 2)  # GB
        stats['memory_total'] = round(memory.total / (1024**3), 2)  # GB
        
        # Disk usage
        disk = psutil.disk_usage('/')
        stats['disk_percent'] = round((disk.used / disk.total) * 100, 1)
        stats['disk_free'] = round(disk.free / (1024**3), 2)  # GB
        
        # Network I/O
        net_io = psutil.net_io_counters()
        stats['bytes_sent'] = round(net_io.bytes_sent / (1024**2), 2)  # MB
        stats['bytes_recv'] = round(net_io.bytes_recv / (1024**2), 2)  # MB
        
        # Process count
        stats['process_count'] = len(psutil.pids())
        
        return stats
    except Exception as e:
        return {'error': str(e)}


def monitor_performance():
    """Main monitoring loop"""
    print("=== VIERLA PERFORMANCE METRICS MONITORING ===")
    print("Monitoring system performance every 15 seconds...")
    print("Press Ctrl+C to stop\n")
    
    while True:
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            stats = get_performance_stats()
            
            if 'error' in stats:
                print(f"[{timestamp}] Performance error: {stats['error']}")
            else:
                print(f"[{timestamp}] CPU: {stats['cpu_percent']}% | "
                      f"RAM: {stats['memory_percent']}% ({stats['memory_available']}GB free) | "
                      f"Disk: {stats['disk_percent']}% ({stats['disk_free']}GB free)")
                print(f"           Network: ↑{stats['bytes_sent']}MB ↓{stats['bytes_recv']}MB | "
                      f"Processes: {stats['process_count']}")
                print("---")
            
            time.sleep(15)
            
        except KeyboardInterrupt:
            print("\nPerformance monitoring stopped.")
            break
        except Exception as e:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] Monitoring error: {e}")
            time.sleep(15)


if __name__ == "__main__":
    monitor_performance()
