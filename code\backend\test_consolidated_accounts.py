"""
Consolidated Test Accounts Tests
Tests to verify all test accounts work correctly with PostgreSQL database
"""

import pytest
import requests
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import authenticate
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from authentication.models import User
from catalog.models import ServiceProvider, ServiceCategory


class ConsolidatedTestAccountsTest(APITestCase):
    """Test all consolidated test accounts for authentication and functionality"""

    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.login_url = reverse('authentication:login')

        # Test accounts from consolidated documentation
        self.test_accounts = [
            {
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'role': 'customer',
                'expected_status': 'working',
                'user_id': 27,
                'full_name': 'Test Customer'
            },
            {
                'email': '<EMAIL>', 
                'password': 'TestPass123!',
                'role': 'service_provider',
                'expected_status': 'working',
                'user_id': 30,
                'full_name': 'Test Provider'
            },
            {
                'email': '<EMAIL>',
                'password': 'password123',
                'role': 'customer',
                'expected_status': 'email_not_verified',
                'user_id': 1
            },
            {
                'email': '<EMAIL>',
                'password': 'demo123',
                'role': 'customer',
                'expected_status': 'email_not_verified',
                'user_id': 33
            },
            {
                'email': '<EMAIL>',
                'password': 'testpass123',
                'role': 'customer',
                'expected_status': 'email_not_verified',
                'user_id': 32
            },
            {
                'email': '<EMAIL>',
                'password': 'AdminPass123!',
                'role': 'admin',
                'expected_status': 'invalid_credentials'
            }
        ]

        # Create test accounts in test database
        self.create_test_accounts()

    def create_test_accounts(self):
        """Create test accounts in test database"""
        from django.db import transaction

        for account in self.test_accounts:
            if account['expected_status'] in ['working', 'email_not_verified']:
                try:
                    with transaction.atomic():
                        user = User.objects.create_user(
                            email=account['email'],
                            username=account['email'],  # Use email as username
                            password=account['password'],
                            first_name=account.get('full_name', '').split()[0] if account.get('full_name') else 'Test',
                            last_name=account.get('full_name', '').split()[-1] if account.get('full_name') else 'User',
                            role=account['role'],
                            is_verified=(account['expected_status'] == 'working'),
                            account_status='active' if account['expected_status'] == 'working' else 'pending_verification',
                            is_test_account=True
                        )

                        # Create service provider profile if needed
                        if account['role'] == 'service_provider':
                            category, _ = ServiceCategory.objects.get_or_create(
                                slug='test-category',
                                defaults={'name': 'Test Category', 'description': 'Test category'}
                            )
                            ServiceProvider.objects.get_or_create(
                                user=user,
                                defaults={
                                    'business_name': 'Test Business',
                                    'business_description': 'Test service provider business',
                                    'business_phone': '+**********',
                                    'business_email': user.email,
                                    'address': '123 Test Street',
                                    'city': 'Test City',
                                    'state': 'Test State',
                                    'zip_code': '12345'
                                }
                            )

                except Exception as e:
                    print(f"Error creating user {account['email']}: {e}")
                    # Continue with other accounts

    def test_working_customer_account(self):
        """Test primary customer account authentication"""
        account = next(acc for acc in self.test_accounts if acc['email'] == '<EMAIL>')
        
        response = self.client.post(self.login_url, {
            'email': account['email'],
            'password': account['password']
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        
        # Verify user data
        user_data = response.data['user']
        self.assertEqual(user_data['email'], account['email'])
        self.assertEqual(user_data['role'], account['role'])
    
    def test_working_provider_account(self):
        """Test primary provider account authentication"""
        account = next(acc for acc in self.test_accounts if acc['email'] == '<EMAIL>')
        
        response = self.client.post(self.login_url, {
            'email': account['email'],
            'password': account['password']
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        
        # Verify user data
        user_data = response.data['user']
        self.assertEqual(user_data['email'], account['email'])
        self.assertEqual(user_data['role'], account['role'])
    
    def test_email_not_verified_accounts(self):
        """Test accounts with unverified emails"""
        unverified_accounts = [
            acc for acc in self.test_accounts 
            if acc['expected_status'] == 'email_not_verified'
        ]
        
        for account in unverified_accounts:
            with self.subTest(email=account['email']):
                response = self.client.post(self.login_url, {
                    'email': account['email'],
                    'password': account['password']
                })
                
                # Should return 400 with email verification error
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('email', response.data.get('non_field_errors', [''])[0].lower())
    
    def test_invalid_credentials_accounts(self):
        """Test accounts with invalid credentials"""
        invalid_accounts = [
            acc for acc in self.test_accounts 
            if acc['expected_status'] == 'invalid_credentials'
        ]
        
        for account in invalid_accounts:
            with self.subTest(email=account['email']):
                response = self.client.post(self.login_url, {
                    'email': account['email'],
                    'password': account['password']
                })
                
                # Should return 400 with invalid credentials error
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('credentials', response.data.get('non_field_errors', [''])[0].lower())
    
    def test_all_accounts_database_consistency(self):
        """Test that all documented accounts exist in database with correct data"""
        working_accounts = [
            acc for acc in self.test_accounts 
            if acc['expected_status'] in ['working', 'email_not_verified']
        ]
        
        for account in working_accounts:
            with self.subTest(email=account['email']):
                try:
                    user = User.objects.get(email=account['email'])
                    self.assertEqual(user.role, account['role'])
                    
                    # Check user ID if specified
                    if 'user_id' in account:
                        self.assertEqual(user.id, account['user_id'])
                    
                    # Check full name if specified
                    if 'full_name' in account:
                        expected_name = account['full_name']
                        actual_name = f"{user.first_name} {user.last_name}".strip()
                        self.assertEqual(actual_name, expected_name)
                        
                except User.DoesNotExist:
                    self.fail(f"User {account['email']} should exist in database")


class TestAccountPasswordValidationTest(TestCase):
    """Test password validation for all test accounts"""
    
    def test_password_strength_requirements(self):
        """Test that all test account passwords meet strength requirements"""
        test_passwords = [
            'TestPass123!',  # Strong password
            'password123',   # Weak password (no uppercase, no special chars)
            'demo123',       # Weak password
            'testpass123',   # Weak password
            'AdminPass123!'  # Strong password
        ]
        
        strong_passwords = ['TestPass123!', 'AdminPass123!']
        weak_passwords = ['password123', 'demo123', 'testpass123']
        
        # Test strong passwords
        for password in strong_passwords:
            with self.subTest(password=password):
                # Should meet Django's password validation
                from django.contrib.auth.password_validation import validate_password
                try:
                    validate_password(password)
                except Exception as e:
                    self.fail(f"Strong password {password} should pass validation: {e}")
        
        # Test weak passwords (should fail validation in production)
        for password in weak_passwords:
            with self.subTest(password=password):
                from django.contrib.auth.password_validation import validate_password
                with self.assertRaises(Exception):
                    validate_password(password)


class TestAccountRolePermissionsTest(APITestCase):
    """Test role-based permissions for test accounts"""
    
    def setUp(self):
        """Set up test environment"""
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        
        # Create test category for provider tests
        self.test_category = ServiceCategory.objects.create(
            name='Test Category',
            slug='test-category'
        )
    
    def test_customer_account_permissions(self):
        """Test customer account has correct permissions"""
        # Login as customer
        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        })
        
        if response.status_code == status.HTTP_200_OK:
            token = response.data['access']
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            # Customer should be able to access services
            services_url = reverse('catalog:service-list')
            response = self.client.get(services_url)
            self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND])
            
            # Customer should NOT be able to create services
            service_data = {
                'name': 'Test Service',
                'description': 'Test description',
                'price': 50.00,
                'duration': 60
            }
            response = self.client.post(services_url, service_data)
            self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_400_BAD_REQUEST])
    
    def test_provider_account_permissions(self):
        """Test provider account has correct permissions"""
        # Login as provider
        response = self.client.post(self.login_url, {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        })
        
        if response.status_code == status.HTTP_200_OK:
            token = response.data['access']
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            # Provider should be able to access services
            services_url = reverse('catalog:service-list')
            response = self.client.get(services_url)
            self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND])
            
            # Provider should be able to create services (if they have a ServiceProvider profile)
            try:
                user = User.objects.get(email='<EMAIL>')
                if hasattr(user, 'serviceprovider'):
                    service_data = {
                        'name': 'Test Service',
                        'description': 'Test description',
                        'price': 50.00,
                        'duration': 60
                    }
                    response = self.client.post(services_url, service_data)
                    # Should either succeed or fail with validation error, not permission error
                    self.assertNotEqual(response.status_code, status.HTTP_403_FORBIDDEN)
            except User.DoesNotExist:
                self.skipTest("Provider account not found in database")


class TestAccountDataMigrationTest(TransactionTestCase):
    """Test data migration from SQLite to PostgreSQL"""
    
    def test_account_data_integrity_after_migration(self):
        """Test that account data maintains integrity after migration"""
        # This test verifies that migrated data is consistent
        expected_accounts = [
            {'email': '<EMAIL>', 'role': 'customer'},
            {'email': '<EMAIL>', 'role': 'service_provider'},
            {'email': '<EMAIL>', 'role': 'customer'},
            {'email': '<EMAIL>', 'role': 'customer'},
            {'email': '<EMAIL>', 'role': 'customer'}
        ]
        
        for account_data in expected_accounts:
            with self.subTest(email=account_data['email']):
                try:
                    user = User.objects.get(email=account_data['email'])
                    self.assertEqual(user.role, account_data['role'])
                    self.assertIsNotNone(user.date_joined)
                    self.assertIsNotNone(user.email)
                    
                    # Verify password is properly hashed
                    self.assertTrue(user.password.startswith('pbkdf2_sha256$'))
                    
                except User.DoesNotExist:
                    # Account might not exist yet - this is acceptable during migration
                    pass
    
    def test_provider_profiles_migrated_correctly(self):
        """Test that service provider profiles are migrated correctly"""
        try:
            provider_user = User.objects.get(email='<EMAIL>')
            
            # Check if ServiceProvider profile exists
            if hasattr(provider_user, 'serviceprovider'):
                provider_profile = provider_user.serviceprovider
                self.assertIsNotNone(provider_profile.business_name)
                self.assertIsNotNone(provider_profile.category)
                
        except User.DoesNotExist:
            self.skipTest("Provider account not found - may not be migrated yet")


class TestAccountAPIEndpointsTest(APITestCase):
    """Test API endpoints work correctly with test accounts"""
    
    def test_login_endpoint_with_all_accounts(self):
        """Test login endpoint with all test account types"""
        test_cases = [
            {'email': '<EMAIL>', 'password': 'TestPass123!', 'should_succeed': True},
            {'email': '<EMAIL>', 'password': 'TestPass123!', 'should_succeed': True},
            {'email': '<EMAIL>', 'password': 'password123', 'should_succeed': False},
            {'email': '<EMAIL>', 'password': 'password', 'should_succeed': False}
        ]
        
        for test_case in test_cases:
            with self.subTest(email=test_case['email']):
                response = self.client.post(reverse('authentication:login'), {
                    'email': test_case['email'],
                    'password': test_case['password']
                })
                
                if test_case['should_succeed']:
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertIn('access', response.data)
                else:
                    self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_user_profile_endpoints(self):
        """Test user profile endpoints with authenticated test accounts"""
        # Login as customer
        login_response = self.client.post(reverse('authentication:login'), {
            'email': '<EMAIL>',
            'password': 'TestPass123!'
        })
        
        if login_response.status_code == status.HTTP_200_OK:
            token = login_response.data['access']
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            # Test profile endpoint
            try:
                profile_url = reverse('authentication:profile')
                response = self.client.get(profile_url)
                self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_404_NOT_FOUND])
            except:
                # Profile endpoint might not exist yet
                pass


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
