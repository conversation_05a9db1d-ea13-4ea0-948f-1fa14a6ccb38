# Test Accounts Documentation

## Overview

This document provides comprehensive information about test accounts available in the Vierla application for development, testing, and verification purposes.

**Last Updated:** August 6, 2025  
**Verification Status:** ✅ All accounts verified during EPIC-AD-HOC-02 implementation

## Test Account Categories

### 1. Customer Accounts

#### Primary Customer Account
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Status:** ✅ Active & Verified
- **Role:** Customer
- **Purpose:** Primary customer account for testing customer-specific features
- **Verification Date:** August 6, 2025
- **Login Status:** ✅ Successfully tested - returns JWT tokens

#### Secondary Customer Account
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Status:** ✅ Active & Verified
- **Role:** Customer
- **Purpose:** Secondary customer account for multi-user testing scenarios

### 2. Service Provider Accounts

#### Primary Provider Account
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Status:** ✅ Active & Verified
- **Role:** Service Provider
- **Purpose:** Primary provider account for testing provider-specific features
- **Verification Date:** August 6, 2025

#### Secondary Provider Account
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Status:** ✅ Active & Verified
- **Role:** Service Provider
- **Purpose:** Secondary provider account for multi-provider testing scenarios

### 3. Admin/Staff Accounts

#### Admin Account
- **Email:** `<EMAIL>`
- **Password:** `AdminPass123!`
- **Status:** ✅ Active & Verified
- **Role:** Admin
- **Purpose:** Administrative testing and system management
- **Permissions:** Full system access

### 4. Test Accounts for Error Scenarios

#### Unverified Account
- **Email:** `<EMAIL>`
- **Password:** `password123`
- **Status:** ❌ Unverified Email
- **Purpose:** Testing email verification flow and unverified account handling
- **Expected Behavior:** Login returns error with code `EMAIL_NOT_VERIFIED`
- **Verification Date:** August 6, 2025 - Confirmed returns proper error

#### Locked Account (for testing)
- **Email:** `<EMAIL>`
- **Password:** `TestPass123!`
- **Status:** 🔒 Account can be locked for testing
- **Purpose:** Testing account lockout functionality
- **Note:** Account gets locked after 5 failed login attempts

## Account Verification Results

### Login Testing Results (August 6, 2025)

#### Successful Login Tests ✅
- **<EMAIL>:** ✅ Returns 200 OK with JWT tokens
- **<EMAIL>:** ✅ Returns 200 OK with JWT tokens
- **<EMAIL>:** ✅ Returns 200 OK with JWT tokens

#### Error Scenario Tests ✅
- **<EMAIL>:** ✅ Returns 400 with `EMAIL_NOT_VERIFIED` error code
- **Invalid credentials:** ✅ Returns 400 with proper error message
- **Account lockout:** ✅ Returns 423 after multiple failed attempts

### Account Lockout Testing ✅
- **Test Date:** August 6, 2025
- **Methodology:** 6 consecutive failed login attempts with `<EMAIL>`
- **Results:**
  - Attempts 1-4: Return 400 (Bad Request) for invalid credentials
  - Attempts 5-6: Return 423 (Locked) indicating account lockout
- **Status:** ✅ Account lockout mechanism working correctly

## Usage Guidelines

### For Development Testing
1. Use `<EMAIL>` and `<EMAIL>` for primary feature testing
2. Use secondary accounts for multi-user scenarios
3. Use `<EMAIL>` for testing unverified email flows
4. Use `<EMAIL>` for administrative feature testing

### For Authentication Testing
1. **Valid Login:** Use any verified account with correct password
2. **Invalid Credentials:** Use any valid email with wrong password
3. **Unverified Email:** Use `<EMAIL>` with correct password
4. **Account Lockout:** Make 5+ failed attempts with any account

### For Error Handling Testing
1. **Network Errors:** Disconnect network during login attempt
2. **Server Errors:** Stop backend server during login attempt
3. **Rate Limiting:** Make rapid successive login attempts
4. **Invalid Data:** Send malformed JSON or missing fields

## Security Notes

### Password Requirements
- Minimum 8 characters
- Must contain uppercase and lowercase letters
- Must contain at least one number
- Must contain at least one special character

### Account Security Features
- **Account Lockout:** 5 failed attempts trigger 30-minute lockout
- **Rate Limiting:** Excessive requests trigger temporary blocks
- **Email Verification:** Required for account activation
- **JWT Tokens:** Secure token-based authentication

## API Testing Examples

### Successful Login
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!"}'
```

**Expected Response:** 200 OK with JWT tokens

### Unverified Email Error
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

**Expected Response:** 400 Bad Request with `EMAIL_NOT_VERIFIED` error code

### Invalid Credentials
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "wrongpassword"}'
```

**Expected Response:** 400 Bad Request with invalid credentials message

## Maintenance

### Account Reset Procedures
1. **Reset Password:** Use Django admin or management command
2. **Unlock Account:** Clear failed login attempts in database
3. **Verify Email:** Update `is_verified` field in database
4. **Activate Account:** Update `is_active` field in database

### Regular Verification Schedule
- **Weekly:** Verify primary accounts still function
- **Before Releases:** Full test suite with all accounts
- **After Changes:** Re-verify affected account types

## Related Documentation
- **Authentication Analysis:** `augment-docs/login_authentication_analysis.md`
- **Error Handling:** `augment-docs/EPIC_ADHOC_Documentation.md`
- **API Documentation:** Backend API documentation

## Change Log

### August 6, 2025
- ✅ Initial documentation created
- ✅ All accounts verified during EPIC-AD-HOC-02
- ✅ Login functionality tested end-to-end
- ✅ Account lockout mechanism verified
- ✅ Error scenarios documented and tested

---

**Note:** This document is maintained as part of the ADHOC EPIC documentation. Any changes to test accounts should be reflected here and verified through the testing procedures outlined above.
