/**
 * Authentication API Service Test Suite
 * Tests for all authentication-related API calls
 */

import axios from 'axios';
import { authAPI, LoginRequest, RegisterRequest, SocialAuthRequest } from '../auth';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the API client
jest.mock('../client', () => ({
  apiClient: {
    post: jest.fn(),
    get: jest.fn(),
    patch: jest.fn(),
  },
}));

import { apiClient } from '../client';
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('Authentication API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should make POST request to login endpoint with credentials', async () => {
      const loginData: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '1',
            email: '<EMAIL>',
            username: 'testuser',
            first_name: 'Test',
            last_name: 'User',
            full_name: 'Test User',
            role: 'customer',
            is_verified: true,
            account_status: 'active',
            created_at: '2023-01-01T00:00:00Z',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.login(loginData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/login/', loginData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle login error', async () => {
      const loginData: LoginRequest = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const mockError = new Error('Invalid credentials');
      mockedApiClient.post.mockRejectedValue(mockError);

      await expect(authAPI.login(loginData)).rejects.toThrow('Invalid credentials');
      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/login/', loginData);
    });
  });

  describe('register', () => {
    it('should make POST request to register endpoint with user data', async () => {
      const registerData: RegisterRequest = {
        email: '<EMAIL>',
        username: 'newuser',
        password: 'password123',
        password_confirm: 'password123',
        first_name: 'New',
        last_name: 'User',
        phone: '+**********',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '2',
            email: '<EMAIL>',
            username: 'newuser',
            first_name: 'New',
            last_name: 'User',
            full_name: 'New User',
            role: 'customer',
            is_verified: false,
            account_status: 'pending_verification',
            phone: '+**********',
            created_at: '2023-01-01T00:00:00Z',
          },
          message: 'Registration successful. Please check your email to verify your account.',
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.register(registerData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/register/', registerData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('socialAuth', () => {
    it('should make POST request to social auth endpoint with Google data', async () => {
      const socialAuthData: SocialAuthRequest = {
        provider: 'google',
        identity_token: 'mock-google-token',
        email: '<EMAIL>',
        first_name: 'Google',
        last_name: 'User',
        user_id: 'google-user-id',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '3',
            email: '<EMAIL>',
            username: '<EMAIL>',
            first_name: 'Google',
            last_name: 'User',
            full_name: 'Google User',
            role: 'customer',
            is_verified: true,
            account_status: 'active',
            created_at: '2023-01-01T00:00:00Z',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.socialAuth(socialAuthData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/social/', socialAuthData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should make POST request to social auth endpoint with Apple data', async () => {
      const socialAuthData: SocialAuthRequest = {
        provider: 'apple',
        identity_token: 'mock-apple-token',
        email: '<EMAIL>',
        first_name: 'Apple',
        last_name: 'User',
        user_id: 'apple-user-id',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '4',
            email: '<EMAIL>',
            username: '<EMAIL>',
            first_name: 'Apple',
            last_name: 'User',
            full_name: 'Apple User',
            role: 'customer',
            is_verified: true,
            account_status: 'active',
            created_at: '2023-01-01T00:00:00Z',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.socialAuth(socialAuthData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/social/', socialAuthData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('logout', () => {
    it('should make POST request to logout endpoint with refresh token', async () => {
      const refreshToken = 'mock-refresh-token';

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.logout(refreshToken);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/logout/', {
        refresh: refreshToken,
      });
    });
  });

  describe('getProfile', () => {
    it('should make GET request to profile endpoint', async () => {
      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await authAPI.getProfile();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/auth/profile/');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('updateProfile', () => {
    it('should make PATCH request to profile update endpoint', async () => {
      const updateData = {
        first_name: 'Updated',
        last_name: 'Name',
        phone: '+**********',
      };

      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Updated',
          last_name: 'Name',
          full_name: 'Updated Name',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          phone: '+**********',
          created_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await authAPI.updateProfile(updateData);

      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', updateData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('changePassword', () => {
    it('should make POST request to change password endpoint', async () => {
      const passwordData = {
        current_password: 'oldpassword',
        new_password: 'newpassword123',
      };

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.changePassword(passwordData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/change-password/', passwordData);
    });
  });

  describe('requestPasswordReset', () => {
    it('should make POST request to password reset endpoint', async () => {
      const resetData = { email: '<EMAIL>' };

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.requestPasswordReset(resetData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/password-reset/', resetData);
    });
  });

  describe('confirmPasswordReset', () => {
    it('should make POST request to password reset confirm endpoint', async () => {
      const confirmData = {
        token: 'reset-token',
        password: 'newpassword123',
      };

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.confirmPasswordReset(confirmData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/password-reset/confirm/', confirmData);
    });
  });

  describe('verifyEmail', () => {
    it('should make POST request to verify email endpoint', async () => {
      const token = 'verification-token';

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.verifyEmail(token);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/verify-email/', { token });
    });
  });

  describe('resendVerification', () => {
    it('should make POST request to resend verification endpoint', async () => {
      const email = '<EMAIL>';

      mockedApiClient.post.mockResolvedValue({ data: {} });

      await authAPI.resendVerification(email);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/resend-verification/', { email });
    });
  });

  describe('checkAuthStatus', () => {
    it('should make GET request to auth status endpoint', async () => {
      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await authAPI.checkAuthStatus();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/auth/status/');
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('Consolidated Test Accounts Integration', () => {
    const testAccounts = [
      {
        email: '<EMAIL>',
        password: 'TestPass123!',
        expectedRole: 'customer',
        expectedId: 27,
        description: 'Primary customer test account'
      },
      {
        email: '<EMAIL>',
        password: 'TestPass123!',
        expectedRole: 'service_provider',
        expectedId: 30,
        description: 'Primary service provider test account'
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        expectedRole: 'customer',
        expectedId: 1,
        description: 'Legacy test account'
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        expectedRole: 'customer',
        expectedId: 33,
        description: 'Demo account for presentations'
      },
      {
        email: '<EMAIL>',
        password: 'testpass123',
        expectedRole: 'customer',
        expectedId: 32,
        description: 'Debug account for development'
      }
    ];

    testAccounts.forEach((account) => {
      it(`should authenticate ${account.description} (${account.email})`, async () => {
        const mockResponse = {
          data: {
            access: 'mock-access-token',
            refresh: 'mock-refresh-token',
            user: {
              id: account.expectedId,
              email: account.email,
              role: account.expectedRole,
              is_verified: true,
              account_status: 'active',
              first_name: 'Test',
              last_name: 'User',
            },
          },
        };

        mockedApiClient.post.mockResolvedValueOnce(mockResponse);

        const result = await authAPI.login({
          email: account.email,
          password: account.password,
        });

        expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/login/', {
          email: account.email,
          password: account.password,
        });

        expect(result.user.email).toBe(account.email);
        expect(result.user.role).toBe(account.expectedRole);
        expect(result.user.id).toBe(account.expectedId);
        expect(result.access).toBe('mock-access-token');
        expect(result.refresh).toBe('mock-refresh-token');
      });
    });

    it('should handle authentication flow for service provider with profile', async () => {
      const mockLoginResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: 30,
            email: '<EMAIL>',
            role: 'service_provider',
            is_verified: true,
            account_status: 'active',
            first_name: 'Test',
            last_name: 'Provider',
          },
        },
      };

      const mockProfileResponse = {
        data: {
          id: 30,
          email: '<EMAIL>',
          role: 'service_provider',
          service_provider: {
            id: 'provider-uuid',
            business_name: 'Test Business',
            business_description: 'Test service provider business',
            is_verified: false,
          },
        },
      };

      mockedApiClient.post.mockResolvedValueOnce(mockLoginResponse);
      mockedApiClient.get.mockResolvedValueOnce(mockProfileResponse);

      const loginResult = await authAPI.login({
        email: '<EMAIL>',
        password: 'TestPass123!',
      });

      const profileResult = await authAPI.getProfile();

      expect(loginResult.user.role).toBe('service_provider');
      expect(profileResult.service_provider).toBeDefined();
      expect(profileResult.service_provider.business_name).toBe('Test Business');
    });
  });

  describe('Authentication Error Handling', () => {
    it('should handle API connection errors', async () => {
      const connectionError = new Error('Network Error');
      connectionError.name = 'NetworkError';

      mockedApiClient.post.mockRejectedValueOnce(connectionError);

      await expect(authAPI.login({
        email: '<EMAIL>',
        password: 'TestPass123!',
      })).rejects.toThrow('Network Error');
    });

    it('should handle invalid API base URL', async () => {
      const mockError = {
        code: 'ECONNREFUSED',
        message: 'connect ECONNREFUSED 192.168.2.65:8000',
        response: undefined,
      };

      mockedApiClient.post.mockRejectedValueOnce(mockError);

      await expect(authAPI.login({
        email: '<EMAIL>',
        password: 'TestPass123!',
      })).rejects.toMatchObject({
        code: 'ECONNREFUSED',
        message: expect.stringContaining('ECONNREFUSED'),
      });
    });

    it('should handle account locked scenarios', async () => {
      const lockError = {
        response: {
          status: 423,
          data: {
            detail: 'Account is temporarily locked due to multiple failed login attempts.',
            error_code: 'ACCOUNT_LOCKED',
            retry_after: 1800,
          },
        },
      };

      mockedApiClient.post.mockRejectedValueOnce(lockError);

      await expect(authAPI.login({
        email: '<EMAIL>',
        password: 'wrong-password',
      })).rejects.toMatchObject({
        response: {
          status: 423,
          data: {
            error_code: 'ACCOUNT_LOCKED',
          },
        },
      });
    });

    it('should handle rate limiting', async () => {
      const rateLimitError = {
        response: {
          status: 429,
          data: {
            detail: 'Too many requests. Please try again later.',
            retry_after: 60,
          },
        },
      };

      mockedApiClient.post.mockRejectedValueOnce(rateLimitError);

      await expect(authAPI.login({
        email: '<EMAIL>',
        password: 'TestPass123!',
      })).rejects.toMatchObject({
        response: {
          status: 429,
        },
      });
    });
  });
});
