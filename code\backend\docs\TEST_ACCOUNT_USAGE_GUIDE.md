# Test Account System Usage Guide

## Overview

The Vierla test account system provides a comprehensive solution for creating, managing, and maintaining test data for development, testing, and demonstration purposes. This guide covers all aspects of using the test account system effectively.

## Quick Start

### 1. Basic Test Account Creation

Create basic test accounts for immediate development use:

```bash
# Create basic test accounts (customers and providers)
python manage.py create_test_accounts --quick

# Create only customer accounts
python manage.py create_test_accounts --customers-only

# Create only provider accounts
python manage.py create_test_accounts --providers-only

# Force recreation of existing accounts
python manage.py create_test_accounts --force
```

### 2. Comprehensive Sample Data Generation

Generate realistic sample data for comprehensive testing:

```bash
# Generate default sample data (20 customers, 15 providers)
python manage.py generate_sample_data

# Generate custom amounts of data
python manage.py generate_sample_data --customers 50 --providers 30

# Generate with specific services per provider
python manage.py generate_sample_data --services-per-provider 6
```

### 3. Complete Environment Setup

Set up a complete test environment with all data:

```bash
# Quick setup for development
python manage.py seed_startup_data --quick

# Full setup with comprehensive data
python manage.py seed_startup_data

# Force recreation of all data
python manage.py seed_startup_data --force
```

## Test Account Credentials

### Default Test Accounts

All test accounts use the same password for convenience:

**Password:** `TestPass123!`

### Customer Accounts

| Email | Name | Role |
|-------|------|------|
| <EMAIL> | Test Customer | Customer |
| <EMAIL> | Emma Thompson | Customer |
| <EMAIL> | John Smith | Customer |
| <EMAIL> | Sarah Wilson | Customer |
| <EMAIL> | Michael Brown | Customer |

### Provider Accounts

| Email | Business Name | Category | Role |
|-------|---------------|----------|------|
| <EMAIL> | Test Beauty Salon | Hair & Beauty | Provider |
| <EMAIL> | Trendy Cuts & Color Studio | Hair & Beauty | Provider |
| <EMAIL> | Serenity Wellness Spa | Wellness & Spa | Provider |
| <EMAIL> | Elite Personal Training | Fitness & Training | Provider |

## API Access

### Authentication

Use any test account credentials to authenticate with the API:

```bash
# Login endpoint
POST /api/auth/login/
{
    "email": "<EMAIL>",
    "password": "TestPass123!"
}
```

### API Endpoints

#### Customer Endpoints
- `GET /api/services/` - Browse available services
- `GET /api/providers/` - Browse service providers
- `POST /api/bookings/` - Create bookings (when implemented)

#### Provider Endpoints
- `GET /api/provider/services/` - List provider's services
- `POST /api/provider/services/` - Create new service
- `PUT /api/provider/services/{id}/` - Update service
- `DELETE /api/provider/services/{id}/` - Delete service
- `GET /api/provider/services/dashboard_summary/` - Dashboard data

### API Documentation

Access interactive API documentation:
- **Swagger UI:** http://localhost:8000/api/docs/
- **ReDoc:** http://localhost:8000/api/redoc/

## Management Commands Reference

### Account Creation Commands

#### `create_test_accounts`

Creates basic test accounts for development.

```bash
python manage.py create_test_accounts [options]

Options:
  --force                 Force recreation of existing accounts
  --quick                 Create minimal accounts for faster setup
  --customers-only        Create only customer accounts
  --providers-only        Create only provider accounts
```

**Examples:**
```bash
# Basic usage
python manage.py create_test_accounts

# Quick setup for development
python manage.py create_test_accounts --quick

# Recreate existing accounts
python manage.py create_test_accounts --force

# Create only customers
python manage.py create_test_accounts --customers-only
```

#### `generate_sample_data`

Generates comprehensive sample data using factories.

```bash
python manage.py generate_sample_data [options]

Options:
  --customers N           Number of customers to create (default: 20)
  --providers N           Number of providers to create (default: 15)
  --services-per-provider N  Average services per provider (default: 4)
  --force                 Force recreation of existing data
```

**Examples:**
```bash
# Default generation
python manage.py generate_sample_data

# Custom amounts
python manage.py generate_sample_data --customers 100 --providers 50

# More services per provider
python manage.py generate_sample_data --services-per-provider 8
```

#### `seed_startup_data`

Comprehensive data seeding for complete environment setup.

```bash
python manage.py seed_startup_data [options]

Options:
  --force                 Force recreation of existing data
  --quick                 Create minimal data for quick setup
  --skip-verification     Skip data verification step
```

**Examples:**
```bash
# Full environment setup
python manage.py seed_startup_data

# Quick development setup
python manage.py seed_startup_data --quick

# Force complete recreation
python manage.py seed_startup_data --force
```

### Cleanup Commands

#### `cleanup_test_accounts`

Safely removes test accounts and related data.

```bash
python manage.py cleanup_test_accounts [options]

Options:
  --dry-run              Show what would be deleted without deleting
  --force                Force deletion without confirmation
  --customers-only       Delete only customer accounts
  --providers-only       Delete only provider accounts
```

**Examples:**
```bash
# Preview what would be deleted
python manage.py cleanup_test_accounts --dry-run

# Clean up all test accounts
python manage.py cleanup_test_accounts --force

# Clean up only customers
python manage.py cleanup_test_accounts --customers-only --force
```

### Verification Commands

#### `verify_sample_data`

Verifies data integrity and consistency.

```bash
python manage.py verify_sample_data [options]

Options:
  --detailed             Show detailed verification results
  --fix-issues           Attempt to fix found issues automatically
```

**Examples:**
```bash
# Basic verification
python manage.py verify_sample_data

# Detailed verification with fixes
python manage.py verify_sample_data --detailed --fix-issues
```

#### `audit_test_accounts`

Performs security audit of test accounts.

```bash
python manage.py audit_test_accounts [options]

Options:
  --fix-issues           Attempt to fix security issues
  --production-check     Perform production-specific checks
  --detailed             Show detailed audit information
```

**Examples:**
```bash
# Basic security audit
python manage.py audit_test_accounts

# Detailed audit with fixes
python manage.py audit_test_accounts --detailed --fix-issues

# Production safety check
python manage.py audit_test_accounts --production-check
```

## Development Workflows

### Daily Development Setup

```bash
# 1. Clean slate
python manage.py cleanup_test_accounts --force

# 2. Create fresh test accounts
python manage.py create_test_accounts --quick

# 3. Verify everything is working
python manage.py verify_sample_data
```

### Comprehensive Testing Setup

```bash
# 1. Full environment setup
python manage.py seed_startup_data --force

# 2. Verify data integrity
python manage.py verify_sample_data --detailed

# 3. Security audit
python manage.py audit_test_accounts --detailed
```

### Demo Environment Preparation

```bash
# 1. Generate realistic sample data
python manage.py generate_sample_data --customers 50 --providers 25

# 2. Verify everything looks good
python manage.py verify_sample_data --detailed

# 3. Test API access
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!"}'
```

## Security Considerations

### Environment Isolation

The test account system includes built-in security measures:

- **Development Only:** Test accounts can only be created in development/testing environments
- **Production Protection:** Automatic cleanup of test accounts if found in production
- **Email Domain Validation:** Test accounts must use `@test.com` domain
- **Environment Detection:** Automatic environment detection and appropriate restrictions

### Security Audit

Regular security audits are recommended:

```bash
# Run security audit
python manage.py audit_test_accounts --detailed

# Check for production issues
python manage.py audit_test_accounts --production-check

# Fix any found issues
python manage.py audit_test_accounts --fix-issues
```

### Production Safety

In production environments:
- Test account creation is automatically blocked
- Existing test accounts are flagged for removal
- Security middleware monitors for test accounts
- Automatic cleanup can be enabled (use with caution)

## Troubleshooting

### Common Issues

#### "Test account creation is only allowed in development/testing environments"

**Solution:** Ensure your environment is properly configured:
```python
# In settings.py
DEBUG = True
ENVIRONMENT = 'development'
```

#### "Service limit reached"

**Solution:** Unverified providers are limited to 3 services. Either:
1. Verify the provider: `provider.is_verified = True; provider.save()`
2. Use verified test accounts from the sample data

#### "Duplicate email addresses found"

**Solution:** Clean up existing accounts first:
```bash
python manage.py cleanup_test_accounts --force
```

#### Database integrity errors

**Solution:** Run data verification and fixes:
```bash
python manage.py verify_sample_data --fix-issues
```

### Getting Help

1. **Check logs:** Look for detailed error messages in the command output
2. **Run verification:** Use `verify_sample_data` to check data integrity
3. **Security audit:** Use `audit_test_accounts` to check for security issues
4. **Clean slate:** Use `cleanup_test_accounts --force` to start fresh

## Best Practices

### Development

1. **Regular Cleanup:** Clean up test accounts regularly to avoid clutter
2. **Environment Separation:** Keep test accounts isolated to development
3. **Data Verification:** Run verification after major changes
4. **Security Audits:** Regular security audits to ensure proper isolation

### Testing

1. **Fresh Data:** Use fresh test data for each test suite
2. **Realistic Data:** Use comprehensive sample data for integration tests
3. **Edge Cases:** Test with various account types and configurations
4. **Cleanup:** Always clean up after tests

### Demonstration

1. **Realistic Data:** Use comprehensive sample data for demos
2. **Diverse Accounts:** Show various provider types and services
3. **Data Verification:** Verify data integrity before demos
4. **Backup Plan:** Have cleanup commands ready if needed

## Advanced Usage

### Custom Data Generation

Create custom test data using the factory classes:

```python
from catalog.factories import CustomerFactory, ProviderFactory, ServiceFactory

# Create custom customer
customer = CustomerFactory.create_customer(
    first_name='Custom',
    last_name='Customer',
    email='<EMAIL>'
)

# Create custom provider
provider = ProviderFactory.create_provider(
    category_slug='hair-beauty',
    business_name='Custom Salon'
)

# Create custom service
service = ServiceFactory.create_service(
    provider=provider,
    name='Custom Service',
    base_price=75.00
)
```

### Programmatic Access

Use the TestAccountManager for programmatic access:

```python
from catalog.security import TestAccountManager

# Check if test accounts are allowed
if TestAccountManager.validate_environment():
    # Create test user
    user = TestAccountManager.create_test_user(
        email='<EMAIL>',
        username='<EMAIL>',
        password='TestPass123!',
        first_name='Programmatic',
        last_name='User'
    )
    
    # Get all test accounts
    test_accounts = TestAccountManager.get_test_accounts()
    
    # Cleanup when done
    TestAccountManager.cleanup_test_accounts()
```

## Maintenance

### Regular Maintenance Tasks

1. **Weekly:** Run security audit
2. **Monthly:** Clean up unused test accounts
3. **Before releases:** Verify no test accounts in production
4. **After major changes:** Run comprehensive data verification

### Monitoring

Monitor test account usage:
- Check for test accounts in production
- Monitor test account creation patterns
- Audit test account security regularly
- Track test data generation performance

### Updates

When updating the test account system:
1. Run existing tests to ensure compatibility
2. Update documentation for any new features
3. Test in development environment first
4. Run security audit after changes
