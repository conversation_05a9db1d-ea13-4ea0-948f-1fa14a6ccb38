# Frontend Architecture: A Resilient Foundation with Expo and React Native

This document outlines a modern, high-performance, and scalable frontend architecture for the rebuilt mobile application. It leverages the latest advancements in the React Native and Expo ecosystem to establish a foundation that is secure, maintainable, and explicitly designed to overcome the technical debt incurred by the previous implementation. The architecture prioritizes developer experience, long-term scalability, and a superior end-user experience through a performance-first approach.

## Section 1: Foundational Strategy & Project Structure

This section establishes the core technical strategy, project organization, and development environment. The goal is to create a clean, maintainable, and highly efficient foundation that directly addresses the technical debt of the previous application by making deliberate, forward-looking technology choices.

### 1.1. Technology Stack & Core Frameworks

The selection of the core framework and its version is the single most important decision in this rebuild. It dictates the available tools, performance characteristics, and future upgrade paths for the application.

**Recommendation:** The application rebuild will standardize on **Expo SDK 53+** and the corresponding **React Native 0.79+**.

**Analysis & Justification:** This recommendation is a strategic alignment with the future trajectory of the React Native ecosystem. Expo SDK 53 is a pivotal release that provides first-class support for React Native's New Architecture, which is a cornerstone of our performance strategy.[1, 2] Adopting this version grants immediate access to a suite of critical improvements:

*   **New Architecture Support:** Enables the use of the Fabric rendering engine and TurboModules by default, which offers more direct and synchronous communication between JavaScript and native threads, significantly boosting performance and responsiveness.[1]
*   **Modern APIs:** Provides stable and improved APIs for essential functionalities, such as `expo-audio` and the more robust `expo-background-task`, which are critical for the application's feature set.[1, 3]
*   **Enhanced Tooling:** Integrates powerful development tools like Expo Atlas for bundle analysis and offers streamlined EAS Build and Submit processes for TestFlight and the Google Play Store.[1, 3]
*   **Future-Proofing:** Aligns the project with the latest versions of underlying dependencies like React 19 (experimental) and TypeScript 5.3, ensuring access to the latest language features and performance optimizations.[3, 4]

By starting the rebuild on this foundation, the project avoids the significant technical debt associated with maintaining older, less efficient architectures and ensures a smoother path for future upgrades. A clean slate approach allows for the adoption of best practices from day one.[2]

### 1.2. The Monorepo Imperative: Fostering Scalability and Code Reuse

To address the long-term strategic needs of the business beyond a single mobile application, a monolithic repository (monorepo) structure is mandated. This architectural pattern is fundamental to preventing future technical debt and fostering a culture of code reuse.

**Recommendation:** The project will adopt a **PNPM-based monorepo structure**. While tools like Lerna, Nx, or Turborepo can be used as task runners, PNPM will serve as the core package manager due to its superior handling of dependencies in a React Native context.[5, 6]

**Analysis & Justification:** A monorepo centralizes the code for multiple projects into a single repository.[7] While this introduces some initial complexity, the long-term benefits are immense and directly address the pitfalls of isolated development.[7]

The monorepo transforms this dynamic. By establishing shared, internal packages, it creates a single source of truth that serves all applications within the repository.[8] This is not merely an organizational choice; it is a strategic decision that turns the codebase into a reusable platform, dramatically lowering the cost and effort required for future development.

**Proposed Structure:**
The monorepo will be organized with distinct `apps` and `packages` directories, a structure endorsed by Expo's official documentation and various successful boilerplate projects.[5, 9, 10]

```
/
├── apps/
│   └── mobile/              # The main Expo application, created via `pnpm create expo-app apps/mobile`
├── packages/
│   ├── ui/                  # Shared, unstyled UI primitive components (e.g., Button, Card, Input)
│   ├── utils/               # Shared helper functions (e.g., date formatters, validators, currency formatters)
│   └── types/               # Shared TypeScript types for API contracts, ensuring backend and frontend are in sync
├── package.json             # Root package.json with workspace definitions for PNPM
├── pnpm-workspace.yaml      # Defines the location of workspaces (e.g., 'apps/*', 'packages/*')
└── tsconfig.base.json       # A base TypeScript configuration extended by all packages and apps
```

**Tooling Rationale:**
*   **PNPM:** Chosen over Yarn or NPM for its efficient, symlink-based approach to `node_modules`. This significantly reduces disk space usage and, more importantly, mitigates many of the complex dependency hoisting and phantom dependency issues that can plague React Native monorepos, especially when native modules are involved.[5, 6]
*   **Lerna (Optional):** While PNPM workspaces handle dependency management, Lerna can be used as a lightweight and battle-tested task runner to execute commands across multiple packages simultaneously.[5]

This structure ensures that when a UI component in `packages/ui` is updated, the change is instantly reflected in the mobile app.

### 1.3. Performance-First Architecture: Embracing the Future of React Native

The previous application's technical debt was likely rooted in poor performance. This rebuild will address this head-on by adopting a performance-first architecture from the outset.

**Recommendation:** The project will enable **React Native's New Architecture (Fabric and TurboModules)** by default and will enable the **React Compiler** as soon as it is stable within the Expo ecosystem.

**Analysis & Justification:**
*   **The New Architecture:** The legacy React Native architecture relied on an asynchronous "bridge" which was a known performance bottleneck.[1] The New Architecture replaces this with a more direct, synchronous communication layer called the JavaScript Interface (JSI), resulting in a noticeably faster and smoother user experience.[1] While some third-party libraries may still have compatibility issues, the ecosystem is rapidly adapting, and the performance benefits are too significant to ignore for a new build.[3, 2]
*   **The React Compiler:** A significant source of performance issues is the manual implementation of memoization hooks (`useMemo`, `useCallback`). The React Compiler is a revolutionary tool that automates this process, drastically reducing unnecessary re-renders without cluttering the code with manual optimizations.[4]

**Implementation Strategy:** The project will begin with the New Architecture enabled. If a critical, incompatible third-party dependency is identified, the team will temporarily disable the New Architecture for that specific feature or module and create a ticket to track the library's update or find a replacement.

### 1.4. Development Environment and Tooling

A disciplined and well-tooled development environment is essential for maintaining code quality and preventing the accumulation of new technical debt.

**Recommendation:** A strict, high-quality development environment will be enforced through configuration and automation.

*   **TypeScript:** The project will use TypeScript with `"strict": true` enabled. The use of the `any` type will be strictly forbidden.[11]
*   **Linting and Formatting:** ESLint and Prettier will be configured to enforce a consistent code style, integrated into the development workflow using Husky and `lint-staged` to automatically format and lint all staged files before they can be committed.[10]
*   **Builds and Testing:** Development will prioritize the use of **Development Builds** created with EAS (Expo Application Services) over Expo Go. Development Builds provide a production-like environment that includes any custom native modules.[1]
*   **Bundle Analysis:** The team will be required to regularly analyze the application bundle using **Expo Atlas**. This tool is activated by running the development server with the `EXPO_ATLAS=1` environment variable and provides a visual map of the JavaScript bundle.[1, 3]
*   **Static JavaScript Features:** Developers will be trained to use modern ESM features (`import`/`export`) over CommonJS (`require`/`module.exports`) wherever possible to enable static analysis and tree shaking, which can significantly reduce the final bundle size.[11]

## Section 2: Core Application Architecture

This section details the internal architecture of the mobile app itself, focusing on state management, navigation, and the construction of the user interface.

### 2.1. A Hybrid State Management Strategy

This architecture adopts a pragmatic, hybrid strategy for state management to optimize for both developer velocity and application stability.

**Recommendation:** The application will utilize a combination of **Zustand** for local and simple global state, and **Redux Toolkit (RTK)** for complex, critical global state.

**Analysis & Justification:** By defining clear boundaries for when to use each tool, we can leverage the strengths of both.[12, 13]

*   **Zustand for UI and Ephemeral State:**
    *   **Role:** Zustand will be the default choice for state that is local to a feature or screen, or for simple global state that doesn't have complex update logic.
    *   **Examples:** Managing the visibility of a modal, handling the state of a multi-step form, tracking the loading status of a single component.
    *   **Rationale:** Zustand is incredibly lightweight, requires minimal boilerplate, and uses a simple, hook-based API that feels natural to React developers.[14, 15, 13] It does not require wrapping the application in a `<Provider>`, making it easy to adopt incrementally.[15, 13]

*   **Redux Toolkit for Core Application State:**
    *   **Role:** RTK will be reserved for managing the application's most critical, complex, and persistent global state.
    *   **Examples:** The user's authentication session (tokens, user profile), application-wide settings, and cached data fetched from the API.
    *   **Rationale:** For complex state interactions, the structured and opinionated nature of RTK is a significant advantage.[16, 13] Its "slice" pattern enforces a predictable, unidirectional data flow, making state changes easier to trace and debug.[13] Furthermore, RTK's powerful ecosystem, including the Redux DevTools and RTK Query, provides capabilities that Zustand does not offer out of the box.[17, 18]

This hybrid approach establishes a clear mental model: Zustand is for the "view layer" state, while RTK is for the "data and session layer" state.[19]

### 2.2. Navigation with Expo Router

A clean, scalable navigation structure is essential for a good user experience and a maintainable codebase.

**Recommendation:** All application navigation will be implemented using **Expo Router v5+**.

**Analysis & Justification:** Expo Router has matured into a powerful, full-featured routing solution for React Native. Its file-based routing paradigm is intuitive and scales well.[3] Version 5 introduces several critical features:

*   **Guarded Routes:** Allows for the protection of certain routes based on authentication status.[3]
*   **Route Prefetching:** The ability to prefetch routes using `<Link prefetch />` can significantly improve perceived performance.[3]
*   **Simplified Auth Flows:** The introduction of a virtual root navigator makes handling redirects during authentication more robust.[3]

**Authentication Flow Design:**
The authentication flow will be implemented using a modern, context-based approach that leverages Expo Router's conditional rendering capabilities, a significant improvement over older methods.[20, 21]

1.  **Auth Context and State:** An `AuthContext` will provide authentication status and methods (`signIn`, `signOut`). The underlying state will be managed by the Redux Toolkit store.[22, 23]
2.  **Secure Token Restoration:** On application launch, the `AuthContext` will attempt to restore the user's token from `expo-secure-store`. During this process, a splash or loading screen will be displayed.[22, 23]
3.  **Conditional Navigation:** The root layout file, `(app)/_layout.tsx`, will conditionally render screen groups based on the authentication state from the `AuthContext`.

    ```typescript
    // Example: (app)/_layout.tsx
    import { useAuth } from '../context/AuthContext';
    import { SplashScreen, Stack } from 'expo-router';

    export default function RootLayout() {
      const { userToken, isLoading } = useAuth();

      if (isLoading) {
        return <SplashScreen />;
      }

      return (
        <Stack>
          {userToken? (
            // Screens for authenticated users
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          ) : (
            // Screens for unauthenticated users
            <Stack.Screen name="(auth)" options={{ headerShown: false }} />
          )}
        </Stack>
      );
    }
    ```
4.  **Clean Stack Management:** This conditional approach ensures that when a user logs in, the auth screens are completely unmounted from the navigation stack, preventing users from navigating back to them using the hardware back button.[22, 23]

### 2.3. UI Component Library

To ensure visual consistency and development efficiency, a centralized UI component library is essential.

**Recommendation:** A custom, branded component library will be built within the `packages/ui` monorepo package, leveraging the new `expo-ui` components as a foundation.

**Analysis & Justification:** While third-party UI kits can accelerate initial development, they often come with significant bloat and opinionated styling.[24] The new `expo-ui` library offers a superior alternative: a set of lightweight, theme-aware, and accessible primitive components maintained by the Expo team.[24] These primitives will serve as the unstyled building blocks for our custom component library, providing the accessibility and consistency of official components while maintaining full control over our application's design language.

## Section 3: Security and Advanced Features

This section addresses critical aspects of the application's architecture related to security and the implementation of advanced, non-UI features.

### 3.1. Secure Token Storage

The improper storage of authentication tokens is one of the most severe security vulnerabilities a mobile application can have.

**Recommendation:** All sensitive data, particularly JWT access and refresh tokens, will be stored using **`expo-secure-store`**.

**Analysis & Justification:** Standard `AsyncStorage` is an unencrypted, key-value store and must **never** be used for storing sensitive information.[25, 26] `expo-secure-store` provides a simple, cross-platform API that abstracts the underlying native secure storage mechanisms of each platform.[27] On iOS, it uses the **Keychain Services**, and on Android, it uses the **Encrypted Shared Preferences** class, ensuring that authentication tokens are protected by the strongest security measures available on the native platforms.[28, 25, 26]

### 3.2. Robust Background Operations

The on-demand nature of the application requires certain tasks to run reliably even when the app is not in the foreground.

**Recommendation:** The application will use **`expo-background-task`** for all complex or long-running operations that need to execute in the background.

**Analysis & Justification:** Simpler APIs like `expo-background-fetch` are not suitable for critical operations. `expo-background-task` provides a more robust and flexible API for managing demanding background work.[1] This is essential for features such as periodic data syncing, location updates, or offline content downloads, ensuring these critical operations complete successfully.[1, 3]