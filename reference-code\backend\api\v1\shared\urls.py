"""
Shared API URL Configuration
Shared endpoints that can be used by both customers and providers
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create a router for ViewSets
router = DefaultRouter()

# Register ViewSets with the router
# router.register(r'example', views.ExampleViewSet, basename='example')

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Add any additional URL patterns here
    # path('example/', views.ExampleView.as_view(), name='example'),
]
