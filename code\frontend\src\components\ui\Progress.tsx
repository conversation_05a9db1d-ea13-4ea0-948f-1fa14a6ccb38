/**
 * Enhanced Progress Component
 * Based on shadcn/ui design patterns for React Native
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
  ViewProps,
} from 'react-native';

import { colors, spacing } from '../../theme';
import { createVariants, mergeStyles } from '../../lib/utils';

export interface ProgressProps extends ViewProps {
  value?: number; // 0-100
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'destructive';
  size?: 'sm' | 'default' | 'lg';
  animated?: boolean;
  showValue?: boolean;
  style?: ViewStyle;
}

const progressVariants = createVariants(
  {
    // Base styles
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 4,
    overflow: 'hidden' as const,
  },
  {
    variant: {
      default: {
        backgroundColor: colors.backgroundSecondary,
      },
      secondary: {
        backgroundColor: colors.border,
      },
      success: {
        backgroundColor: `${colors.success}20`,
      },
      warning: {
        backgroundColor: `${colors.warning}20`,
      },
      destructive: {
        backgroundColor: `${colors.error}20`,
      },
    },
    size: {
      sm: {
        height: 4,
        borderRadius: 2,
      },
      default: {
        height: 8,
        borderRadius: 4,
      },
      lg: {
        height: 12,
        borderRadius: 6,
      },
    },
  },
  {
    variant: 'default',
    size: 'default',
  }
);

const fillVariants = createVariants(
  {
    // Base fill styles
    height: '100%',
    borderRadius: 4,
  },
  {
    variant: {
      default: {
        backgroundColor: colors.primary,
      },
      secondary: {
        backgroundColor: colors.textSecondary,
      },
      success: {
        backgroundColor: colors.success,
      },
      warning: {
        backgroundColor: colors.warning,
      },
      destructive: {
        backgroundColor: colors.error,
      },
    },
    size: {
      sm: {
        borderRadius: 2,
      },
      default: {
        borderRadius: 4,
      },
      lg: {
        borderRadius: 6,
      },
    },
  },
  {
    variant: 'default',
    size: 'default',
  }
);

export const Progress: React.FC<ProgressProps> = ({
  value = 0,
  variant = 'default',
  size = 'default',
  animated = true,
  style,
  ...props
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const clampedValue = Math.max(0, Math.min(100, value));

  useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: clampedValue,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(clampedValue);
    }
  }, [clampedValue, animated, animatedValue]);

  const progressStyle = progressVariants({ variant, size });
  const fillStyle = fillVariants({ variant, size });
  
  const finalProgressStyle = mergeStyles(progressStyle, style);

  const animatedWidth = animatedValue.interpolate({
    inputRange: [0, 100],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <View style={finalProgressStyle} {...props}>
      <Animated.View
        style={[
          fillStyle,
          {
            width: animatedWidth,
          },
        ]}
      />
    </View>
  );
};

export default Progress;
