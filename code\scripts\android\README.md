# Android Emulator Screenshot Automation

This directory contains scripts and tools for automating screenshot capture from Android emulators during development and testing.

## Overview

The screenshot automation system provides:
- Automated screenshot capture from Android emulators
- Timestamp-based file naming
- Configurable output directories
- Error handling and logging
- Cross-platform support (PowerShell + Batch)

## Files

- `take-screenshot.ps1` - Main PowerShell script for screenshot automation
- `take-screenshot.bat` - Batch wrapper for easier execution
- `README.md` - This documentation file

## Prerequisites

1. **Android SDK Platform Tools** - ADB must be installed and accessible
   - Default path: `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools`
   - Ensure ADB is in your system PATH or the script will add it temporarily

2. **Android Emulator** - Must be running and connected
   - Verify with: `adb devices`

3. **PowerShell** - Version 5.0 or higher (Windows PowerShell or PowerShell Core)

## Usage

### Quick Start

```bash
# Take a screenshot with auto-generated filename
.\code\scripts\android\take-screenshot.bat

# Take a screenshot with custom filename
.\code\scripts\android\take-screenshot.bat "login-screen"
```

### PowerShell Direct Usage

```powershell
# Basic usage
.\code\scripts\android\take-screenshot.ps1

# Custom filename
.\code\scripts\android\take-screenshot.ps1 -Filename "home-screen"

# Custom output directory
.\code\scripts\android\take-screenshot.ps1 -OutputPath "debug\screenshots"

# Specific device (if multiple connected)
.\code\scripts\android\take-screenshot.ps1 -DeviceId "emulator-5554"

# Show help
.\code\scripts\android\take-screenshot.ps1 -Help
```

## Configuration

### Default Settings

- **Output Directory**: `augment-docs\screenshots`
- **Filename Format**: `screenshot_YYYY-MM-DD_HH-mm-ss.png`
- **Device Selection**: Auto-detect first available device
- **ADB Path**: `C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools`

### Customization

You can modify the script variables at the top of `take-screenshot.ps1`:

```powershell
$ADB_PATH = "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools"
```

## Output

Screenshots are saved as PNG files in the specified output directory. The script provides:

- Success/failure status
- File location and size information
- Timestamp logging
- Error messages with troubleshooting hints

### Example Output

```
============================================================================
Android Screenshot Automation v1.0.0
============================================================================

[2025-08-06 12:59:45] [INFO] Auto-detected device: emulator-5554
Configuration:
  Device: emulator-5554
  Output: augment-docs\screenshots\screenshot_2025-08-06_12-59-45.png

[2025-08-06 12:59:46] [INFO] Taking screenshot from device: emulator-5554
[2025-08-06 12:59:46] [INFO] Created output directory: augment-docs\screenshots
Screenshot saved successfully!
Location: augment-docs\screenshots\screenshot_2025-08-06_12-59-45.png
Size: 245.67 KB

Screenshot operation completed successfully!
```

## Troubleshooting

### Common Issues

1. **"ADB command failed"**
   - Ensure Android SDK Platform Tools are installed
   - Check that ADB path is correct
   - Verify emulator is running: `adb devices`

2. **"No devices connected"**
   - Start the Android emulator
   - Enable USB debugging if using physical device
   - Check device connection: `adb devices`

3. **"Screenshot command failed"**
   - Device may be locked or in sleep mode
   - Try unlocking the device screen
   - Restart ADB server: `adb kill-server && adb start-server`

4. **Permission errors**
   - Run PowerShell as Administrator
   - Check write permissions to output directory
   - Ensure output directory exists or can be created

### Debug Mode

For additional debugging information, you can modify the script to include verbose logging or run ADB commands manually:

```bash
# Check device status
adb devices -l

# Test screenshot manually
adb exec-out screencap -p > test-screenshot.png

# Check ADB version
adb version
```

## Integration

This screenshot automation can be integrated into:

- **Development workflows** - Capture UI states during development
- **Testing pipelines** - Document test results and failures
- **Bug reporting** - Automatically capture error states
- **Documentation** - Generate UI screenshots for documentation

### Example Integration

```powershell
# In a test script
.\code\scripts\android\take-screenshot.ps1 -Filename "test-login-success"
# ... run tests ...
.\code\scripts\android\take-screenshot.ps1 -Filename "test-login-failure"
```

## Security Considerations

- Screenshots may contain sensitive information
- Ensure proper access controls on screenshot directories
- Consider automatic cleanup of old screenshots
- Be mindful of personal data in captured screens

## Future Enhancements

Potential improvements for the screenshot automation system:

- Video recording capabilities
- Automatic screenshot comparison
- Integration with test frameworks
- Cloud storage upload
- Batch screenshot capture
- OCR text extraction from screenshots
- Automated UI element detection
