"""
Customer API Serializers - Enhanced based on Backend Agent feedback
Customer-specific serializers with optimized data structures
"""

from rest_framework import serializers
from django.utils import timezone
from apps.catalog.models import Service, ServiceProvider, ServiceCategory
from apps.bookings.models import Booking
from apps.catalog.serializers import ServiceSerializer, ServiceProviderSerializer


class CustomerServiceSerializer(ServiceSerializer):
    """
    Customer-optimized service serializer
    Includes customer-specific fields and optimizations
    """
    is_favorited = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()
    next_available_slot = serializers.SerializerMethodField()
    provider_rating = serializers.ReadOnlyField(source='provider.average_rating')
    provider_verified = serializers.ReadOnlyField(source='provider.is_verified')
    
    class Meta(ServiceSerializer.Meta):
        fields = ServiceSerializer.Meta.fields + [
            'is_favorited',
            'distance',
            'next_available_slot',
            'provider_rating',
            'provider_verified',
        ]
    
    def get_is_favorited(self, obj):
        """Check if service is in customer's favorites"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # This would check against a Favorite model
            # For now, return False
            return False
        return False
    
    def get_distance(self, obj):
        """Calculate distance from customer location"""
        request = self.context.get('request')
        if request and hasattr(request, 'customer_location'):
            # This would calculate actual distance using PostGIS
            # For now, return None
            return None
        return None
    
    def get_next_available_slot(self, obj):
        """Get next available booking slot"""
        # This would query availability slots
        # For now, return a placeholder
        return timezone.now() + timezone.timedelta(days=1)


class CustomerBookingSerializer(serializers.ModelSerializer):
    """
    Customer-optimized booking serializer
    Includes customer-specific fields and validation
    """
    service_name = serializers.ReadOnlyField(source='service.name')
    service_image = serializers.ReadOnlyField(source='service.image.url')
    provider_name = serializers.ReadOnlyField(source='service.provider.business_name')
    provider_phone = serializers.ReadOnlyField(source='service.provider.phone')
    provider_address = serializers.ReadOnlyField(source='service.provider.address')
    can_cancel = serializers.SerializerMethodField()
    can_reschedule = serializers.SerializerMethodField()
    time_until_appointment = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = [
            'id',
            'service',
            'service_name',
            'service_image',
            'provider_name',
            'provider_phone',
            'provider_address',
            'scheduled_datetime',
            'status',
            'total_price',
            'notes',
            'can_cancel',
            'can_reschedule',
            'time_until_appointment',
            'created_at',
            'updated_at',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'total_price']
    
    def get_can_cancel(self, obj):
        """Check if booking can be cancelled"""
        if obj.status in ['completed', 'cancelled']:
            return False
        
        # Allow cancellation up to 24 hours before appointment
        time_until = obj.scheduled_datetime - timezone.now()
        return time_until.total_seconds() > 24 * 3600
    
    def get_can_reschedule(self, obj):
        """Check if booking can be rescheduled"""
        if obj.status in ['completed', 'cancelled']:
            return False
        
        # Allow rescheduling up to 12 hours before appointment
        time_until = obj.scheduled_datetime - timezone.now()
        return time_until.total_seconds() > 12 * 3600
    
    def get_time_until_appointment(self, obj):
        """Get time until appointment in seconds"""
        if obj.scheduled_datetime > timezone.now():
            return (obj.scheduled_datetime - timezone.now()).total_seconds()
        return 0
    
    def validate_scheduled_datetime(self, value):
        """Validate booking datetime"""
        if value <= timezone.now():
            raise serializers.ValidationError("Booking must be scheduled for a future time")
        
        # Check if it's too far in the future (e.g., 6 months)
        max_future = timezone.now() + timezone.timedelta(days=180)
        if value > max_future:
            raise serializers.ValidationError("Booking cannot be scheduled more than 6 months in advance")
        
        return value


class CustomerSearchSerializer(serializers.Serializer):
    """
    Customer search request serializer
    """
    query = serializers.CharField(max_length=255, required=True)
    type = serializers.ChoiceField(
        choices=['services', 'providers', 'all'],
        default='all'
    )
    location = serializers.CharField(max_length=100, required=False)
    category = serializers.IntegerField(required=False)
    price_min = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    price_max = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    rating_min = serializers.DecimalField(max_digits=3, decimal_places=2, required=False)
    
    def validate_location(self, value):
        """Validate location format (lat,lng,radius)"""
        if value:
            try:
                parts = value.split(',')
                if len(parts) != 3:
                    raise ValueError()
                lat, lng, radius = map(float, parts)
                
                if not (-90 <= lat <= 90):
                    raise ValueError("Invalid latitude")
                if not (-180 <= lng <= 180):
                    raise ValueError("Invalid longitude")
                if radius <= 0:
                    raise ValueError("Invalid radius")
                    
            except ValueError:
                raise serializers.ValidationError(
                    "Location must be in format 'latitude,longitude,radius'"
                )
        return value


class CustomerFavoriteSerializer(serializers.Serializer):
    """
    Customer favorites serializer
    """
    service_id = serializers.IntegerField()
    service = CustomerServiceSerializer(read_only=True)
    added_at = serializers.DateTimeField(read_only=True)
    
    def validate_service_id(self, value):
        """Validate service exists and is active"""
        try:
            service = Service.objects.get(id=value, is_active=True)
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found or inactive")
        return value


class CustomerProfileSerializer(serializers.Serializer):
    """
    Customer profile serializer with customer-specific fields
    """
    id = serializers.IntegerField(read_only=True)
    email = serializers.EmailField(read_only=True)
    first_name = serializers.CharField(max_length=30)
    last_name = serializers.CharField(max_length=30)
    phone = serializers.CharField(max_length=20, required=False)
    date_of_birth = serializers.DateField(required=False)
    preferred_location = serializers.CharField(max_length=255, required=False)
    notification_preferences = serializers.JSONField(required=False)
    total_bookings = serializers.IntegerField(read_only=True)
    favorite_categories = serializers.ListField(
        child=serializers.CharField(),
        read_only=True
    )
    
    def validate_phone(self, value):
        """Validate phone number format"""
        if value and not value.replace('+', '').replace('-', '').replace(' ', '').isdigit():
            raise serializers.ValidationError("Invalid phone number format")
        return value


class CustomerDashboardSerializer(serializers.Serializer):
    """
    Customer dashboard data serializer
    """
    upcoming_bookings_count = serializers.IntegerField()
    total_bookings = serializers.IntegerField()
    favorite_services_count = serializers.IntegerField()
    recent_bookings = CustomerBookingSerializer(many=True)
    recommended_services = CustomerServiceSerializer(many=True)
    nearby_providers = ServiceProviderSerializer(many=True)
    
    class Meta:
        fields = [
            'upcoming_bookings_count',
            'total_bookings',
            'favorite_services_count',
            'recent_bookings',
            'recommended_services',
            'nearby_providers',
        ]
