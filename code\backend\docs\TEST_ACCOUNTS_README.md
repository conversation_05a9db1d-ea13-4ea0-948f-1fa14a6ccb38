# Vierla Test Account System

A comprehensive test account and sample data management system for the Vierla beauty services platform.

## 🚀 Quick Start

### 1. Create Test Accounts

```bash
# Create basic test accounts for immediate use
python manage.py create_test_accounts --quick

# Create comprehensive sample data
python manage.py generate_sample_data
```

### 2. Login with Test Credentials

**For current working test accounts, see:** `/code/docs/CONSOLIDATED_TEST_ACCOUNTS.md`

**Primary Working Accounts:**
- **Customer:** `<EMAIL>` / `TestPass123!`
- **Provider:** `<EMAIL>` / `TestPass123!`

**Note:** This file contains system documentation. For actual test credentials, always refer to the consolidated documentation.

### 3. Access the API

```bash
# Login to get access token
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!"}'

# Use the token for API requests
curl -X GET http://localhost:8000/api/services/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📋 Features

### ✅ Test Account Management
- **Automated Creation:** Generate realistic test accounts with a single command
- **Role-Based Accounts:** Customer and service provider accounts with proper permissions
- **Bulk Operations:** Create, manage, and cleanup multiple accounts efficiently
- **Security Isolation:** Test accounts are isolated and disabled in production

### ✅ Sample Data Generation
- **Realistic Data:** Generate realistic business names, addresses, and service descriptions
- **Comprehensive Coverage:** Services, providers, categories, and customer accounts
- **Configurable Scale:** Generate from minimal to large datasets
- **Data Relationships:** Proper relationships between providers, services, and categories

### ✅ Security & Safety
- **Environment Detection:** Automatic detection of development vs production environments
- **Production Protection:** Test accounts automatically blocked in production
- **Security Auditing:** Built-in security audit tools and monitoring
- **Safe Cleanup:** Comprehensive cleanup tools with dry-run capabilities

### ✅ Developer Experience
- **Management Commands:** Easy-to-use Django management commands
- **Comprehensive Documentation:** Detailed usage guides and API references
- **Testing Integration:** Built-in test factories for unit and integration tests
- **Verification Tools:** Data integrity verification and issue detection

## 🛠️ Management Commands

### Account Creation

| Command | Purpose | Example |
|---------|---------|---------|
| `create_test_accounts` | Create basic test accounts | `python manage.py create_test_accounts --quick` |
| `generate_sample_data` | Generate comprehensive sample data | `python manage.py generate_sample_data --customers 50` |
| `seed_startup_data` | Complete environment setup | `python manage.py seed_startup_data` |

### Maintenance

| Command | Purpose | Example |
|---------|---------|---------|
| `cleanup_test_accounts` | Remove test accounts | `python manage.py cleanup_test_accounts --force` |
| `verify_sample_data` | Verify data integrity | `python manage.py verify_sample_data --detailed` |
| `audit_test_accounts` | Security audit | `python manage.py audit_test_accounts --fix-issues` |

## 📊 Sample Data Overview

### Generated Accounts

- **20+ Customer Accounts:** Diverse demographics and realistic names
- **15+ Provider Accounts:** Various business types and specializations
- **6+ Service Categories:** Hair & Beauty, Wellness & Spa, Fitness, etc.
- **60+ Services:** Realistic pricing, descriptions, and durations

### Data Quality

- **Realistic Names:** Culturally diverse and appropriate names
- **Valid Addresses:** Real Toronto/Ottawa addresses with proper postal codes
- **Market Pricing:** Service prices aligned with market standards
- **Business Logic:** All data follows proper business rules and relationships

## 🔒 Security Features

### Environment Protection

```python
# Automatic environment detection
if TestAccountSecurity.is_production_environment():
    raise PermissionDenied("Test accounts not allowed in production")
```

### Test Account Isolation

- **Email Domain Validation:** Test accounts must use `@test.com` domain
- **Flagged Accounts:** All test accounts marked with `is_test_account=True`
- **Automatic Cleanup:** Production environments automatically clean test accounts
- **Security Middleware:** Continuous monitoring for test accounts in production

### Audit Tools

```bash
# Run security audit
python manage.py audit_test_accounts --detailed

# Check production safety
python manage.py audit_test_accounts --production-check
```

## 🧪 Testing Integration

### Test Factories

```python
from catalog.factories import CustomerFactory, ProviderFactory, ServiceFactory

# Create test data in your tests
customer = CustomerFactory.create_customer()
provider = ProviderFactory.create_provider(category_slug='hair-beauty')
service = ServiceFactory.create_service(provider)
```

### Test Account Manager

```python
from catalog.security import TestAccountManager

# Programmatic test account management
if TestAccountManager.validate_environment():
    user = TestAccountManager.create_test_user(
        email='<EMAIL>',
        password='TestPass123!'
    )
```

## 📚 Documentation

### Complete Guides

- **[Usage Guide](TEST_ACCOUNT_USAGE_GUIDE.md):** Comprehensive usage instructions
- **[API Reference](TEST_ACCOUNT_API_REFERENCE.md):** Complete API documentation
- **[System Design](TEST_ACCOUNT_SYSTEM_DESIGN.md):** Architecture and design decisions

### Quick References

- **Default Password:** `TestPass123!` (all accounts)
- **Test Domain:** `@test.com` (all test emails)
- **API Base URL:** `http://localhost:8000/api/`
- **Admin Panel:** `http://localhost:8000/admin/`

## 🔧 Development Workflows

### Daily Development

```bash
# Start fresh each day
python manage.py cleanup_test_accounts --force
python manage.py create_test_accounts --quick
```

### Integration Testing

```bash
# Set up comprehensive test environment
python manage.py seed_startup_data
python manage.py verify_sample_data --detailed
```

### Demo Preparation

```bash
# Generate realistic demo data
python manage.py generate_sample_data --customers 50 --providers 25
python manage.py audit_test_accounts --detailed
```

## 🚨 Troubleshooting

### Common Issues

**"Test account creation is only allowed in development"**
```bash
# Check your environment settings
echo $ENVIRONMENT
# Should be 'development' or 'testing'
```

**"Service limit reached"**
```bash
# Unverified providers are limited to 3 services
# Use verified test accounts or verify manually
```

**"Duplicate email addresses found"**
```bash
# Clean up existing accounts first
python manage.py cleanup_test_accounts --force
```

### Getting Help

1. **Run verification:** `python manage.py verify_sample_data --detailed`
2. **Check security:** `python manage.py audit_test_accounts --detailed`
3. **Clean slate:** `python manage.py cleanup_test_accounts --force`
4. **Check logs:** Look for detailed error messages in command output

## 🎯 Best Practices

### Development

- ✅ Use `--quick` flag for faster daily setup
- ✅ Run verification after major changes
- ✅ Clean up test accounts regularly
- ✅ Use realistic data for integration testing

### Testing

- ✅ Use fresh test data for each test suite
- ✅ Leverage test factories for unit tests
- ✅ Clean up after tests complete
- ✅ Test with various account types

### Security

- ✅ Never use test accounts in production
- ✅ Run regular security audits
- ✅ Monitor for test accounts in production
- ✅ Use proper environment configuration

## 📈 Performance

### Generation Speed

- **Quick Mode:** ~5 seconds for basic accounts
- **Standard Mode:** ~30 seconds for comprehensive data
- **Large Scale:** ~2 minutes for 100+ accounts

### Database Impact

- **Minimal Footprint:** Efficient data generation
- **Clean Relationships:** Proper foreign key relationships
- **Optimized Queries:** Bulk operations where possible

## 🔄 Maintenance

### Regular Tasks

- **Weekly:** Security audit and cleanup
- **Monthly:** Comprehensive data verification
- **Before Releases:** Production safety check
- **After Updates:** Test system functionality

### Monitoring

```bash
# Check test account status
python manage.py audit_test_accounts

# Verify data integrity
python manage.py verify_sample_data

# Monitor generation performance
time python manage.py generate_sample_data
```

## 🤝 Contributing

### Adding New Features

1. **Test Factories:** Add new factories in `catalog/factories.py`
2. **Management Commands:** Add commands in `catalog/management/commands/`
3. **Security Features:** Update `catalog/security.py`
4. **Documentation:** Update relevant documentation files

### Testing Changes

```bash
# Run test account system tests
python manage.py test catalog.tests.test_account_management

# Test management commands
python manage.py create_test_accounts --quick
python manage.py verify_sample_data --detailed
```

## 📄 License

This test account system is part of the Vierla project and follows the same licensing terms.

## 🆘 Support

For issues, questions, or contributions:

1. **Check Documentation:** Review the comprehensive guides
2. **Run Diagnostics:** Use built-in verification and audit tools
3. **Create Issue:** Report bugs or request features
4. **Security Issues:** Report security concerns immediately

---

**Ready to start developing with realistic test data!** 🚀

Use `python manage.py seed_startup_data --quick` to get started immediately.
