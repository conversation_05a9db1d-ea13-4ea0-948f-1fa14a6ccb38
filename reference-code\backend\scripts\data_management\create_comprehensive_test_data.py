#!/usr/bin/env python
"""
Create comprehensive test data for Vierla booking system
This script creates service providers across all major beauty service categories
Following rules.md - using Toronto and Ottawa addresses for mock data
"""
import os
import django
from decimal import Decimal
import random

# Set up Django environment BEFORE importing models
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models AFTER Django setup
from apps.authentication.models import User, UserProfile
from apps.catalog.models import ServiceCategory, ServiceProvider, Service

def create_service_categories():
    """Create comprehensive service categories following rules.md"""
    categories_data = [
        {
            'name': 'Hair Services',
            'slug': 'hair-services',
            'description': 'Professional hair cutting, styling, coloring, and treatments',
            'icon': '💇‍♀️',
            'color': '#667eea',
            'is_popular': True,
            'sort_order': 1
        },
        {
            'name': 'Nail Services',
            'slug': 'nail-services',
            'description': 'Manicures, pedicures, nail art, and nail care',
            'icon': '💅',
            'color': '#764ba2',
            'is_popular': True,
            'sort_order': 2
        },
        {
            'name': 'Lash Services',
            'slug': 'lash-services',
            'description': 'Eyelash extensions, lifts, tinting, and brow services',
            'icon': '👁️',
            'color': '#f093fb',
            'is_popular': True,
            'sort_order': 3
        },
        {
            'name': 'Braiding',
            'slug': 'braiding',
            'description': 'Professional braiding and protective styles',
            'icon': '🤎',
            'color': '#4facfe',
            'is_popular': True,
            'sort_order': 4
        },
        {
            'name': 'Locs & Twists',
            'slug': 'locs-twists',
            'description': 'Loc maintenance, installation, and twist styles',
            'icon': '🌀',
            'color': '#43e97b',
            'is_popular': True,
            'sort_order': 5
        },
        {
            'name': 'Makeup Services',
            'slug': 'makeup-services',
            'description': 'Professional makeup application and lessons',
            'icon': '💄',
            'color': '#fa709a',
            'is_popular': True,
            'sort_order': 6
        }
    ]
    
    created_categories = []
    for cat_data in categories_data:
        category, created = ServiceCategory.objects.get_or_create(
            slug=cat_data['slug'],
            defaults=cat_data
        )
        if created:
            print(f"✅ Created category: {category.name}")
        else:
            print(f"📋 Category exists: {category.name}")
        created_categories.append(category)
    
    return created_categories

def create_test_providers(categories):
    """Create test service providers across all categories with Toronto/Ottawa addresses"""
    # Toronto and Ottawa addresses following rules.md
    toronto_addresses = [
        {'address': '123 Queen Street West', 'city': 'Toronto', 'state': 'Ontario', 'zip': 'M5H 2M9'},
        {'address': '456 King Street East', 'city': 'Toronto', 'state': 'Ontario', 'zip': 'M5A 1L1'},
        {'address': '789 Yonge Street', 'city': 'Toronto', 'state': 'Ontario', 'zip': 'M4W 2G8'},
        {'address': '321 Bloor Street West', 'city': 'Toronto', 'state': 'Ontario', 'zip': 'M5S 1W7'},
    ]
    
    ottawa_addresses = [
        {'address': '100 Rideau Street', 'city': 'Ottawa', 'state': 'Ontario', 'zip': 'K1N 9J7'},
        {'address': '250 Bank Street', 'city': 'Ottawa', 'state': 'Ontario', 'zip': 'K2P 1X4'},
        {'address': '75 Sparks Street', 'city': 'Ottawa', 'state': 'Ontario', 'zip': 'K1P 5A5'},
    ]
    
    providers_data = [
        # Hair Services - Toronto
        {
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Johnson',
            'business_name': 'Elite Hair Studio',
            'business_description': 'Premium hair styling and color services in downtown Toronto',
            'category_name': 'Hair Services',
            'location': toronto_addresses[0],
            'services': [
                {'name': 'Haircut & Style', 'price': 85, 'duration': 90},
                {'name': 'Hair Color', 'price': 150, 'duration': 180},
                {'name': 'Highlights', 'price': 200, 'duration': 240}
            ]
        },
        # Hair Services - Ottawa
        {
            'email': '<EMAIL>',
            'first_name': 'Marcus',
            'last_name': 'Williams',
            'business_name': 'Urban Cuts Barbershop',
            'business_description': 'Modern barbering and men\'s grooming in Ottawa',
            'category_name': 'Hair Services',
            'location': ottawa_addresses[0],
            'services': [
                {'name': 'Classic Cut', 'price': 45, 'duration': 45},
                {'name': 'Beard Trim', 'price': 25, 'duration': 30},
                {'name': 'Hot Towel Shave', 'price': 55, 'duration': 60}
            ]
        },
        # Nail Services - Toronto
        {
            'email': '<EMAIL>',
            'first_name': 'Jessica',
            'last_name': 'Chen',
            'business_name': 'Luxe Nail Lounge',
            'business_description': 'High-end nail art and spa services',
            'category_name': 'Nail Services',
            'location': toronto_addresses[1],
            'services': [
                {'name': 'Gel Manicure', 'price': 65, 'duration': 75},
                {'name': 'Pedicure', 'price': 55, 'duration': 60},
                {'name': 'Nail Art Design', 'price': 85, 'duration': 90}
            ]
        },
        # Lash Services - Ottawa
        {
            'email': '<EMAIL>',
            'first_name': 'Amanda',
            'last_name': 'Rodriguez',
            'business_name': 'Lash Perfection Studio',
            'business_description': 'Expert lash extensions and brow services',
            'category_name': 'Lash Services',
            'location': ottawa_addresses[1],
            'services': [
                {'name': 'Classic Lash Extensions', 'price': 120, 'duration': 120},
                {'name': 'Volume Lashes', 'price': 180, 'duration': 150},
                {'name': 'Lash Lift & Tint', 'price': 85, 'duration': 75}
            ]
        },
        # Braiding - Toronto
        {
            'email': '<EMAIL>',
            'first_name': 'Keisha',
            'last_name': 'Thompson',
            'business_name': 'Crown Braiding Studio',
            'business_description': 'Authentic African braiding and protective styles',
            'category_name': 'Braiding',
            'location': toronto_addresses[2],
            'services': [
                {'name': 'Box Braids', 'price': 200, 'duration': 300},
                {'name': 'Cornrows', 'price': 120, 'duration': 180},
                {'name': 'Senegalese Twists', 'price': 250, 'duration': 360}
            ]
        },
        # Locs & Twists - Ottawa
        {
            'email': '<EMAIL>',
            'first_name': 'Jamal',
            'last_name': 'Davis',
            'business_name': 'Natural Roots Loc Studio',
            'business_description': 'Specialized loc maintenance and natural hair care',
            'category_name': 'Locs & Twists',
            'location': ottawa_addresses[2],
            'services': [
                {'name': 'Loc Maintenance', 'price': 80, 'duration': 120},
                {'name': 'Loc Installation', 'price': 300, 'duration': 480},
                {'name': 'Two-Strand Twists', 'price': 100, 'duration': 150}
            ]
        },
        # Makeup Services - Toronto
        {
            'email': '<EMAIL>',
            'first_name': 'Isabella',
            'last_name': 'Martinez',
            'business_name': 'Glamour Makeup Artistry',
            'business_description': 'Professional makeup for all occasions',
            'category_name': 'Makeup Services',
            'location': toronto_addresses[3],
            'services': [
                {'name': 'Bridal Makeup', 'price': 200, 'duration': 120},
                {'name': 'Special Event Makeup', 'price': 120, 'duration': 90},
                {'name': 'Makeup Lesson', 'price': 150, 'duration': 120}
            ]
        }
    ]
    
    created_providers = []
    for provider_data in providers_data:
        # Create user with unique username
        user, created = User.objects.get_or_create(
            email=provider_data['email'],
            defaults={
                'username': provider_data['email'],  # Use email as username to avoid conflicts
                'first_name': provider_data['first_name'],
                'last_name': provider_data['last_name'],
                'role': 'service_provider',
                'is_verified': True,
                'account_status': 'active'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            print(f"✅ Created user: {user.email}")
        
        # Create user profile
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'city': provider_data['location']['city'],
                'state': provider_data['location']['state'],
                'country': 'Canada',
                'auto_accept_bookings': True
            }
        )
        
        # Find category
        category = next((cat for cat in categories if cat.name == provider_data['category_name']), None)
        if not category:
            print(f"❌ Category not found: {provider_data['category_name']}")
            continue
        
        # Create service provider with Toronto/Ottawa addresses
        provider, created = ServiceProvider.objects.get_or_create(
            user=user,
            defaults={
                'business_name': provider_data['business_name'],
                'business_description': provider_data['business_description'],
                'business_phone': f'+1{random.randint(**********, **********)}',  # Toronto/Ottawa area codes
                'business_email': provider_data['email'],
                'address': provider_data['location']['address'],
                'city': provider_data['location']['city'],
                'state': provider_data['location']['state'],
                'zip_code': provider_data['location']['zip'],
                'country': 'Canada',
                'latitude': 43.6532 + random.uniform(-0.1, 0.1) if provider_data['location']['city'] == 'Toronto' else 45.4215 + random.uniform(-0.1, 0.1),
                'longitude': -79.3832 + random.uniform(-0.1, 0.1) if provider_data['location']['city'] == 'Toronto' else -75.6972 + random.uniform(-0.1, 0.1),
                'is_verified': True,
                'is_featured': random.choice([True, False]),
                'rating': round(random.uniform(4.0, 5.0), 1),
                'review_count': random.randint(10, 100),
                'years_of_experience': random.randint(2, 15)
            }
        )
        
        if created:
            provider.categories.add(category)
            print(f"✅ Created provider: {provider.business_name} in {provider.city}")
            
            # Create services for this provider
            for service_data in provider_data['services']:
                service, created = Service.objects.get_or_create(
                    provider=provider,
                    name=service_data['name'],
                    defaults={
                        'category': category,
                        'description': f"Professional {service_data['name'].lower()} service",
                        'short_description': f"High-quality {service_data['name'].lower()}",
                        'base_price': Decimal(str(service_data['price'])),
                        'duration': service_data['duration'],
                        'is_active': True,
                        'is_available': True
                    }
                )
                if created:
                    print(f"  ✅ Created service: {service.name} - ${service.base_price}")
        
        created_providers.append(provider)
    
    return created_providers

def main():
    print("🚀 Creating Comprehensive Test Data for Vierla")
    print("=" * 60)
    print("📍 Following rules.md - Using Toronto and Ottawa addresses")
    
    # Create categories
    print("\n📂 Creating Service Categories...")
    categories = create_service_categories()
    
    # Create providers
    print("\n👥 Creating Service Providers...")
    providers = create_test_providers(categories)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST DATA SUMMARY:")
    print(f"✅ Categories: {len(categories)}")
    print(f"✅ Providers: {len(providers)}")
    print(f"✅ Total Services: {Service.objects.count()}")
    print(f"✅ Total Users: {User.objects.count()}")
    
    print("\n🔑 TEST ACCOUNT CREDENTIALS:")
    print("Provider Accounts (password: testpass123):")
    for provider in providers:
        print(f"  📧 {provider.user.email} - {provider.business_name} ({provider.city})")
    
    print("\n🎉 Comprehensive test data creation completed!")
    print("📍 All addresses are in Toronto and Ottawa as per rules.md")

if __name__ == '__main__':
    main()
