# Frontend Authentication Guide

## Overview

This guide covers the frontend authentication implementation for the Vierla mobile application built with React Native and Expo.

## Architecture

### Navigation Structure

```
AppNavigator (Root)
├── AuthNavigator (Unauthenticated)
│   ├── LoginScreen
│   ├── RegisterScreen
│   └── ForgotPasswordScreen
└── MainNavigator (Authenticated)
    ├── HomeScreen
    ├── ServicesScreen
    ├── BookingsScreen
    └── ProfileScreen
```

### State Management

The app uses a combination of:
- **React Query**: API state management and caching
- **AsyncStorage**: Persistent authentication state
- **React Navigation**: Navigation state and authentication flow

## Components

### Authentication Screens

#### LoginScreen
**Location**: `src/screens/auth/LoginScreen.tsx`

Features:
- Email/password form with validation
- Social authentication buttons (Google/Apple)
- "Remember me" functionality
- Error handling and loading states
- Navigation to registration and password reset

**Usage:**
```typescript
import { LoginScreen } from '../screens/auth/LoginScreen';

// Navigation prop is automatically provided by React Navigation
<LoginScreen navigation={navigation} />
```

#### RegisterScreen
**Location**: `src/screens/auth/RegisterScreen.tsx`

Features:
- Multi-field registration form
- Real-time validation
- Password confirmation
- Terms and conditions acceptance
- Automatic login after successful registration

#### ForgotPasswordScreen
**Location**: `src/screens/auth/ForgotPasswordScreen.tsx`

Features:
- Email input for password reset
- Email validation
- Success/error messaging
- Navigation back to login

### Reusable Components

#### Button Component
**Location**: `src/components/Button.tsx`

```typescript
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  leftIcon?: string;
  rightIcon?: string;
}
```

**Example:**
```typescript
<Button
  title="Sign In"
  onPress={handleLogin}
  loading={isLoading}
  variant="primary"
  size="large"
/>
```

#### Input Component
**Location**: `src/components/Input.tsx`

```typescript
interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  secureTextEntry?: boolean;
  keyboardType?: KeyboardTypeOptions;
  leftIcon?: string;
  rightIcon?: string;
}
```

**Example:**
```typescript
<Input
  label="Email"
  placeholder="Enter your email"
  value={email}
  onChangeText={setEmail}
  error={errors.email}
  keyboardType="email-address"
  leftIcon="mail"
/>
```

#### SocialButton Component
**Location**: `src/components/SocialButton.tsx`

```typescript
interface SocialButtonProps {
  provider: 'google' | 'apple';
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
}
```

## API Integration

### Authentication Service
**Location**: `src/services/api/auth.ts`

The authentication service provides methods for all auth-related API calls:

```typescript
export const authAPI = {
  login: (data: LoginRequest) => Promise<AuthResponse>,
  register: (data: RegisterRequest) => Promise<AuthResponse>,
  socialAuth: (data: SocialAuthRequest) => Promise<AuthResponse>,
  logout: (refreshToken: string) => Promise<void>,
  getProfile: () => Promise<User>,
  updateProfile: (data: ProfileUpdateRequest) => Promise<User>,
  changePassword: (data: ChangePasswordRequest) => Promise<void>,
  requestPasswordReset: (data: { email: string }) => Promise<void>,
  confirmPasswordReset: (data: PasswordResetConfirmRequest) => Promise<void>,
  verifyEmail: (token: string) => Promise<void>,
  resendVerification: (email: string) => Promise<void>,
  checkAuthStatus: () => Promise<User>
};
```

### API Client Configuration
**Location**: `src/services/api/client.ts`

```typescript
const apiClient = axios.create({
  baseURL: 'http://127.0.0.1:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use((config) => {
  const token = AsyncStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## Authentication Flow

### Login Process

1. **User Input**: User enters email and password
2. **Validation**: Client-side validation checks
3. **API Call**: POST request to `/auth/login/`
4. **Token Storage**: Store access/refresh tokens in AsyncStorage
5. **User Data**: Store user profile data
6. **Navigation**: Navigate to main app screens

```typescript
const handleLogin = async () => {
  try {
    setLoading(true);
    const response = await authAPI.login({ email, password });
    
    await AsyncStorage.multiSet([
      ['access_token', response.access],
      ['refresh_token', response.refresh],
      ['user', JSON.stringify(response.user)],
    ]);
    
    navigation.replace('Main');
  } catch (error) {
    handleAuthError(error);
  } finally {
    setLoading(false);
  }
};
```

### Registration Process

1. **Form Validation**: Comprehensive client-side validation
2. **API Call**: POST request to `/auth/register/`
3. **Auto-Login**: Automatic login after successful registration
4. **Email Verification**: Prompt for email verification if required

### Social Authentication

1. **Provider Selection**: User selects Google or Apple
2. **Token Retrieval**: Get identity token from provider
3. **API Call**: POST request to `/auth/social/`
4. **Account Creation**: Create or link existing account
5. **Login**: Complete authentication flow

```typescript
const handleGoogleSignIn = async () => {
  try {
    // TODO: Implement actual Google Sign-In
    const googleToken = await GoogleSignIn.signIn();
    
    const response = await authAPI.socialAuth({
      provider: 'google',
      identity_token: googleToken.idToken,
      email: googleToken.user.email,
      first_name: googleToken.user.givenName,
      last_name: googleToken.user.familyName,
    });
    
    await storeAuthData(response);
    navigation.replace('Main');
  } catch (error) {
    handleAuthError(error);
  }
};
```

## Error Handling

### Error Types

```typescript
interface AuthError {
  response?: {
    status: number;
    data: {
      detail?: string;
      [field: string]: string[];
    };
  };
  message: string;
}
```

### Error Handling Strategy

```typescript
const handleAuthError = (error: AuthError) => {
  if (error.response?.status === 400) {
    // Validation errors
    const errorData = error.response.data;
    if (errorData.detail) {
      Alert.alert('Error', errorData.detail);
    } else {
      setFieldErrors(errorData);
    }
  } else if (error.response?.status === 401) {
    Alert.alert('Authentication Failed', 'Invalid credentials');
  } else if (error.response?.status === 423) {
    Alert.alert('Account Locked', 'Too many failed attempts');
  } else {
    Alert.alert('Error', 'An unexpected error occurred');
  }
};
```

## Form Validation

### Validation Rules

```typescript
const validateEmail = (email: string): string | undefined => {
  if (!email.trim()) return 'Email is required';
  if (!/\S+@\S+\.\S+/.test(email)) return 'Invalid email format';
  return undefined;
};

const validatePassword = (password: string): string | undefined => {
  if (!password.trim()) return 'Password is required';
  if (password.length < 8) return 'Password must be at least 8 characters';
  return undefined;
};

const validatePasswordConfirm = (password: string, confirm: string): string | undefined => {
  if (password !== confirm) return 'Passwords do not match';
  return undefined;
};
```

## Testing

### Test Structure

```
src/
├── __tests__/
│   └── simple.test.ts
├── components/
│   └── __tests__/
│       ├── Button.test.tsx
│       └── Input.test.tsx
├── screens/
│   └── auth/
│       └── __tests__/
│           ├── LoginScreen.test.tsx
│           └── RegisterScreen.test.tsx
└── services/
    └── api/
        └── __tests__/
            └── auth-simple.test.ts
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npx jest src/__tests__/simple.test.ts

# Run tests with coverage
npm run test:coverage
```

## Styling and Theming

### Design System

The app uses a consistent design system with:
- **Colors**: Primary, secondary, accent, error, success, warning
- **Typography**: Heading1, heading2, heading3, body, caption, button
- **Spacing**: Consistent padding and margins
- **Components**: Reusable styled components

### Theme Configuration

```typescript
export const theme = {
  colors: {
    primary: '#1C1C1E',
    secondary: '#8E8E93',
    accent: '#007AFF',
    error: '#FF3B30',
    success: '#34C759',
    warning: '#FF9500',
    background: '#FFFFFF',
    surface: '#F2F2F7',
  },
  typography: {
    heading1: { fontSize: 32, fontWeight: '700' },
    heading2: { fontSize: 24, fontWeight: '600' },
    body: { fontSize: 16, fontWeight: '400' },
    caption: { fontSize: 14, fontWeight: '400' },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
};
```

## Performance Optimization

### Best Practices

1. **Lazy Loading**: Screens loaded on demand
2. **Image Optimization**: Proper image sizing and caching
3. **Bundle Splitting**: Code splitting for better load times
4. **Memory Management**: Proper cleanup of listeners and timers
5. **Network Optimization**: Request caching and retry logic

### React Query Configuration

```typescript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});
```

## Deployment

### Build Configuration

```bash
# Development build
expo start

# Production build
expo build:android
expo build:ios

# Web build
expo build:web
```

### Environment Configuration

```typescript
// app.config.js
export default {
  expo: {
    name: 'Vierla',
    slug: 'vierla',
    version: '1.0.0',
    platforms: ['ios', 'android', 'web'],
    extra: {
      apiUrl: process.env.API_URL || 'http://127.0.0.1:8000/api',
    },
  },
};
```
