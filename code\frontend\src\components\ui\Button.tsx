/**
 * Enhanced Button Component
 * Based on shadcn/ui design patterns for React Native
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { createVariants, mergeStyles } from '../../lib/utils';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

// Button variants will be created inside the component using theme hook

export const Button: React.FC<ButtonProps> = ({
  variant = 'default',
  size = 'default',
  loading = false,
  disabled = false,
  children,
  style,
  textStyle,
  onPress,
  testID = 'button',
  ...props
}) => {
  const { colors, spacing, typography } = useTheme();
  const isDisabled = disabled || loading;

  // Create button variants using theme
  const buttonVariants = createVariants({
    base: {
      borderRadius: 8,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      flexDirection: 'row' as const,
      minHeight: 44,
    },
    variants: {
      variant: {
        default: {
          backgroundColor: colors.primary,
          shadowColor: colors.black,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        },
        destructive: {
          backgroundColor: colors.error,
          shadowColor: colors.black,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        },
        outline: {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.primary,
        },
        secondary: {
          backgroundColor: colors.background.secondary,
        },
        ghost: {
          backgroundColor: 'transparent',
        },
        link: {
          backgroundColor: 'transparent',
          paddingHorizontal: 0,
          paddingVertical: 0,
          minHeight: 'auto' as any,
        },
      },
      size: {
        default: {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm,
          minHeight: 44,
        },
        sm: {
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs,
          minHeight: 36,
          borderRadius: 6,
        },
        lg: {
          paddingHorizontal: spacing.lg,
          paddingVertical: spacing.md,
          minHeight: 52,
          borderRadius: 10,
        },
        icon: {
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.sm,
          minHeight: 44,
          width: 44,
        },
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  });

  // Create text variants using theme
  const textVariants = createVariants({
    base: {
      fontSize: typography.fontSize.base,
      textAlign: 'center' as const,
      fontWeight: '600' as const,
    },
    variants: {
      variant: {
        default: {
          color: colors.white,
        },
        destructive: {
          color: colors.white,
        },
        outline: {
          color: colors.text.primary,
        },
        secondary: {
          color: colors.text.primary,
        },
        ghost: {
          color: colors.text.primary,
        },
        link: {
          color: colors.primary,
          textDecorationLine: 'underline' as const,
        },
      },
      size: {
        default: {
          fontSize: 16,
        },
        sm: {
          fontSize: 14,
        },
        lg: {
          fontSize: 18,
        },
        icon: {
          fontSize: 16,
        },
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  });

  // Create styles using theme
  const styles = StyleSheet.create({
    disabled: {
      opacity: 0.5,
    },
    disabledText: {
      opacity: 0.7,
    },
    loadingIndicator: {
      marginRight: spacing.xs,
    },
  });

  const buttonStyle = buttonVariants({ variant, size });
  const buttonTextStyle = textVariants({ variant, size });

  const finalButtonStyle = mergeStyles(
    buttonStyle,
    isDisabled && styles.disabled,
    style
  );

  const finalTextStyle = mergeStyles(
    buttonTextStyle,
    isDisabled && styles.disabledText,
    textStyle
  );

  const handlePress = (event: any) => {
    if (!isDisabled && onPress) {
      onPress(event);
    }
  };

  return (
    <TouchableOpacity
      style={finalButtonStyle}
      onPress={handlePress}
      disabled={isDisabled}
      activeOpacity={isDisabled ? 1 : 0.7}
      testID={testID}
      {...props}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'default' || variant === 'destructive' ? colors.white : colors.primary}
          style={styles.loadingIndicator}
        />
      )}

      {typeof children === 'string' ? (
        <Text style={finalTextStyle}>{children}</Text>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

export default Button;
