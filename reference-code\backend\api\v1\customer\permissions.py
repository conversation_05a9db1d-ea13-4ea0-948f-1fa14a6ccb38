"""
Customer API Permissions - Enhanced based on Backend Agent feedback
Role-based permissions for customer-specific endpoints
"""

from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsCustomerUser(permissions.BasePermission):
    """
    Permission class to ensure user has customer role
    """
    message = "You must be a customer to access this endpoint."
    
    def has_permission(self, request, view):
        """Check if user is authenticated and has customer role"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user has customer role
        return hasattr(request.user, 'customer_profile') or request.user.role == 'customer'
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permissions for customer"""
        # Customer can only access their own objects
        if hasattr(obj, 'customer'):
            return obj.customer == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        
        return True


class IsCustomerOwner(permissions.BasePermission):
    """
    Permission class to ensure customer can only access their own data
    """
    message = "You can only access your own data."
    
    def has_object_permission(self, request, view, obj):
        """Check if customer owns the object"""
        if hasattr(obj, 'customer'):
            return obj.customer == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanCreateBooking(permissions.BasePermission):
    """
    Permission class for booking creation with additional validation
    """
    message = "You cannot create bookings at this time."
    
    def has_permission(self, request, view):
        """Check if customer can create bookings"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Check if user is a customer
        if not (hasattr(request.user, 'customer_profile') or request.user.role == 'customer'):
            return False
        
        # Additional checks for booking creation
        if request.method == 'POST':
            # Check if customer has verified email
            if not request.user.email_verified:
                self.message = "Please verify your email before creating bookings."
                return False
            
            # Check if customer is not suspended
            if hasattr(request.user, 'is_suspended') and request.user.is_suspended:
                self.message = "Your account is suspended. Contact support."
                return False
        
        return True


class CanCancelBooking(permissions.BasePermission):
    """
    Permission class for booking cancellation
    """
    message = "You cannot cancel this booking."
    
    def has_object_permission(self, request, view, obj):
        """Check if customer can cancel the booking"""
        # Must be the booking owner
        if obj.customer != request.user:
            return False
        
        # Check if booking can be cancelled based on status
        if obj.status in ['completed', 'cancelled']:
            self.message = "This booking cannot be cancelled."
            return False
        
        # Check cancellation time window (24 hours before)
        from django.utils import timezone
        time_until = obj.scheduled_datetime - timezone.now()
        if time_until.total_seconds() <= 24 * 3600:  # 24 hours
            self.message = "Bookings cannot be cancelled within 24 hours of the appointment."
            return False
        
        return True


class CanRescheduleBooking(permissions.BasePermission):
    """
    Permission class for booking rescheduling
    """
    message = "You cannot reschedule this booking."
    
    def has_object_permission(self, request, view, obj):
        """Check if customer can reschedule the booking"""
        # Must be the booking owner
        if obj.customer != request.user:
            return False
        
        # Check if booking can be rescheduled based on status
        if obj.status in ['completed', 'cancelled']:
            self.message = "This booking cannot be rescheduled."
            return False
        
        # Check rescheduling time window (12 hours before)
        from django.utils import timezone
        time_until = obj.scheduled_datetime - timezone.now()
        if time_until.total_seconds() <= 12 * 3600:  # 12 hours
            self.message = "Bookings cannot be rescheduled within 12 hours of the appointment."
            return False
        
        return True


class CustomerAPIPermission(permissions.BasePermission):
    """
    Comprehensive permission class for customer API endpoints
    Combines multiple permission checks
    """
    
    def has_permission(self, request, view):
        """Check general API access permissions"""
        # Must be authenticated
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Must be a customer
        if not (hasattr(request.user, 'customer_profile') or request.user.role == 'customer'):
            return False
        
        # Check API access permissions based on view action
        action = getattr(view, 'action', None)
        
        if action in ['create', 'update', 'partial_update', 'destroy']:
            # Check if customer can perform write operations
            if hasattr(request.user, 'is_read_only') and request.user.is_read_only:
                return False
        
        return True
    
    def has_object_permission(self, request, view, obj):
        """Check object-level permissions"""
        # Customer can only access their own data
        if hasattr(obj, 'customer'):
            return obj.customer == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        
        # For public objects (like services), allow read access
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return False
