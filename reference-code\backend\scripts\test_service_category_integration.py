#!/usr/bin/env python
"""
Test Service Category Integration End-to-End
Verify functionality from backend database to frontend CustomerHomeScreen rendering
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceCategory

# API Base URL
API_BASE = "http://************:8000/api"

def print_header(title):
    """Print formatted section header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def test_database_categories():
    """Test service categories in database"""
    print_header("DATABASE CATEGORIES TEST")
    
    categories = ServiceCategory.objects.filter(is_active=True).order_by('sort_order')
    
    print(f"📊 Found {categories.count()} active categories in database:")
    
    expected_categories = [
        'Hair Services', 'Nail Services', 'Massage', 'Skincare', 
        'Makeup', 'Barbering', 'Lash Services', 'Braiding'
    ]
    
    db_categories = []
    for category in categories[:8]:  # First 8 for home screen
        print(f"   ✅ {category.name} (Sort: {category.sort_order})")
        print(f"      Icon: {category.icon} | Color: {category.color}")
        print(f"      Slug: {category.slug} | Popular: {category.is_popular}")
        db_categories.append(category.name)
    
    # Check if we have the expected categories
    missing_categories = []
    for expected in expected_categories:
        found = any(expected.lower() in cat.lower() for cat in db_categories)
        if not found:
            missing_categories.append(expected)
    
    if missing_categories:
        print(f"\n⚠️  Missing expected categories: {missing_categories}")
    else:
        print(f"\n✅ All expected categories found in database")
    
    return categories

def test_api_categories():
    """Test service categories API endpoint"""
    print_header("API CATEGORIES TEST")
    
    try:
        response = requests.get(f"{API_BASE}/catalog/categories/", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            categories = data.get('results', data) if isinstance(data, dict) else data
            
            print(f"✅ API Response: {response.status_code}")
            print(f"📊 API returned {len(categories)} categories")
            
            print(f"\n📋 API Categories (first 8):")
            for i, category in enumerate(categories[:8]):
                print(f"   {i+1}. {category.get('name', 'N/A')}")
                print(f"      ID: {category.get('id', 'N/A')}")
                print(f"      Slug: {category.get('slug', 'N/A')}")
                print(f"      Icon: {category.get('icon', 'N/A')}")
                print(f"      Color: {category.get('color', 'N/A')}")
                print(f"      Popular: {category.get('is_popular', False)}")
                print(f"      Service Count: {category.get('service_count', 0)}")
            
            return categories
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API Request failed: {e}")
        return None

def test_frontend_service_integration():
    """Test frontend service integration"""
    print_header("FRONTEND SERVICE INTEGRATION TEST")
    
    # Check if customerService.ts exists and has proper structure
    frontend_service_path = Path("../frontend_v1/src/services/customerService.ts")
    
    if frontend_service_path.exists():
        print(f"✅ customerService.ts found")
        
        # Read the service file to check for category-related functions
        try:
            with open(frontend_service_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Check for category-related functions
            category_functions = [
                'getServiceCategories',
                'ServiceCategory',
                'categories',
                'fetchCategories'
            ]
            
            found_functions = []
            for func in category_functions:
                if func in content:
                    found_functions.append(func)
            
            print(f"📋 Found category-related code:")
            for func in found_functions:
                print(f"   ✅ {func}")
            
            if not found_functions:
                print(f"⚠️  No category-related functions found in customerService.ts")
            
            # Check API base URL
            if "************:8000" in content or "localhost:8000" in content:
                print(f"✅ Backend API URL configured")
            else:
                print(f"⚠️  Backend API URL may not be configured")
                
        except Exception as e:
            print(f"❌ Error reading customerService.ts: {e}")
    else:
        print(f"❌ customerService.ts not found at {frontend_service_path}")

def test_customer_home_screen():
    """Test CustomerHomeScreen component"""
    print_header("CUSTOMER HOME SCREEN TEST")
    
    # Check CustomerHomeScreen component
    home_screen_path = Path("../frontend_v1/src/screens/customer/CustomerHomeScreen.tsx")
    
    if home_screen_path.exists():
        print(f"✅ CustomerHomeScreen.tsx found")
        
        try:
            with open(home_screen_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for category-related code
            category_elements = [
                'categories',
                'ServiceCategory',
                'Browse Services',
                'Browse Categories',
                'getServiceCategories',
                'useServiceCategories'
            ]
            
            found_elements = []
            for element in category_elements:
                if element in content:
                    found_elements.append(element)
            
            print(f"📋 Found category-related elements in CustomerHomeScreen:")
            for element in found_elements:
                print(f"   ✅ {element}")
            
            if len(found_elements) >= 3:
                print(f"✅ CustomerHomeScreen appears to have category integration")
            else:
                print(f"⚠️  CustomerHomeScreen may need category integration")
                
        except Exception as e:
            print(f"❌ Error reading CustomerHomeScreen.tsx: {e}")
    else:
        print(f"❌ CustomerHomeScreen.tsx not found at {home_screen_path}")

def test_category_data_flow():
    """Test complete data flow from database to frontend"""
    print_header("CATEGORY DATA FLOW TEST")
    
    print("🔄 Testing complete data flow:")
    print("   Database → API → Frontend Service → Home Screen")
    
    # Step 1: Database test
    db_categories = test_database_categories()
    db_success = db_categories.count() >= 8
    print(f"\n1. Database: {'✅ PASS' if db_success else '❌ FAIL'}")
    
    # Step 2: API test
    api_categories = test_api_categories()
    api_success = api_categories is not None and len(api_categories) >= 8
    print(f"2. API: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    # Step 3: Frontend service test
    test_frontend_service_integration()
    print(f"3. Frontend Service: ✅ CHECKED")
    
    # Step 4: Home screen test
    test_customer_home_screen()
    print(f"4. Home Screen: ✅ CHECKED")
    
    # Overall assessment
    overall_success = db_success and api_success
    
    print(f"\n{'='*60}")
    print(f"🎯 INTEGRATION TEST SUMMARY")
    print(f"{'='*60}")
    
    if overall_success:
        print(f"🎉 SUCCESS: Service category integration is working!")
        print(f"✅ Database has {db_categories.count()} active categories")
        print(f"✅ API returns {len(api_categories) if api_categories else 0} categories")
        print(f"✅ Frontend components are in place")
        print(f"✅ Ready for end-to-end testing")
    else:
        print(f"⚠️  PARTIAL: Some integration issues detected")
        if not db_success:
            print(f"❌ Database: Insufficient categories")
        if not api_success:
            print(f"❌ API: Endpoint issues")
    
    return overall_success

def main():
    """Main integration test function"""
    print_header("SERVICE CATEGORY INTEGRATION TEST")
    print("🎯 End-to-end verification from backend to frontend")
    
    try:
        success = test_category_data_flow()
        
        if success:
            print(f"\n🚀 NEXT STEPS:")
            print(f"   1. ✅ Start frontend development server")
            print(f"   2. ✅ Navigate to CustomerHomeScreen")
            print(f"   3. ✅ Verify categories display correctly")
            print(f"   4. ✅ Test category navigation and filtering")
            
            print(f"\n🔧 DEVELOPMENT COMMANDS:")
            print(f"   Backend: python manage.py runserver ************:8000")
            print(f"   Frontend: npm start (in frontend_v1 directory)")
            print(f"   API Test: curl http://************:8000/api/catalog/categories/")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
