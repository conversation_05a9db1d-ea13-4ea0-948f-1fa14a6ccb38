# ============================================================================
# Android Emulator Screenshot Automation Script
# ============================================================================
# This script automates taking screenshots from the Android emulator
# and saves them to the appropriate directory with timestamps.
#
# Usage: .\code\scripts\android\take-screenshot.ps1 [options]
# Options:
#   -OutputPath <path>    Custom output directory (default: augment-docs\screenshots)
#   -Filename <name>      Custom filename (default: timestamp-based)
#   -DeviceId <id>        Specific device ID (default: auto-detect)
#   -Help                 Show this help message
# ============================================================================

param(
    [string]$OutputPath = "augment-docs\screenshots",
    [string]$Filename = "",
    [string]$DeviceId = "",
    [switch]$Help
)

# Script configuration
$SCRIPT_NAME = "Android Screenshot Automation"
$SCRIPT_VERSION = "1.0.0"
$ADB_PATH = "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools"

# Colors for output
$COLOR_GREEN = "`e[92m"
$COLOR_YELLOW = "`e[93m"
$COLOR_RED = "`e[91m"
$COLOR_BLUE = "`e[94m"
$COLOR_CYAN = "`e[96m"
$COLOR_RESET = "`e[0m"

function Show-Help {
    Write-Host "${COLOR_BLUE}============================================================================${COLOR_RESET}"
    Write-Host "${COLOR_BLUE}$SCRIPT_NAME v$SCRIPT_VERSION${COLOR_RESET}"
    Write-Host "${COLOR_BLUE}============================================================================${COLOR_RESET}"
    Write-Host ""
    Write-Host "Usage: ${COLOR_CYAN}.\code\scripts\android\take-screenshot.ps1 [options]${COLOR_RESET}"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  ${COLOR_YELLOW}-OutputPath <path>${COLOR_RESET}    Custom output directory (default: augment-docs\screenshots)"
    Write-Host "  ${COLOR_YELLOW}-Filename <name>${COLOR_RESET}      Custom filename (default: timestamp-based)"
    Write-Host "  ${COLOR_YELLOW}-DeviceId <id>${COLOR_RESET}        Specific device ID (default: auto-detect)"
    Write-Host "  ${COLOR_YELLOW}-Help${COLOR_RESET}                 Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  ${COLOR_CYAN}.\code\scripts\android\take-screenshot.ps1${COLOR_RESET}"
    Write-Host "  ${COLOR_CYAN}.\code\scripts\android\take-screenshot.ps1 -Filename 'login-screen'${COLOR_RESET}"
    Write-Host "  ${COLOR_CYAN}.\code\scripts\android\take-screenshot.ps1 -OutputPath 'debug\screenshots'${COLOR_RESET}"
    Write-Host ""
    exit 0
}

function Write-Log {
    param([string]$Level, [string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] [$Level] $Message"
}

function Test-AdbConnection {
    try {
        # Add ADB to PATH for this session
        $env:Path += ";$ADB_PATH"

        # Test ADB connection using full path
        $adbExe = Join-Path $ADB_PATH "adb.exe"
        if (!(Test-Path $adbExe)) {
            throw "ADB executable not found at $adbExe"
        }

        $devices = & $adbExe devices 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "ADB command failed"
        }
        
        # Parse device list
        $deviceLines = $devices -split "`n" | Where-Object { $_ -match "device$" }
        if ($deviceLines.Count -eq 0) {
            throw "No devices connected"
        }
        
        return $deviceLines[0].Split("`t")[0]
    }
    catch {
        Write-Host "${COLOR_RED}Error: Failed to connect to ADB or no devices found${COLOR_RESET}"
        Write-Host "${COLOR_YELLOW}Please ensure:${COLOR_RESET}"
        Write-Host "1. Android emulator is running"
        Write-Host "2. ADB is installed at: $ADB_PATH"
        Write-Host "3. USB debugging is enabled"
        exit 1
    }
}

function Take-Screenshot {
    param([string]$Device, [string]$OutputFile)
    
    try {
        Write-Log "INFO" "Taking screenshot from device: $Device"
        
        # Create output directory if it doesn't exist
        $outputDir = Split-Path $OutputFile -Parent
        if (!(Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
            Write-Log "INFO" "Created output directory: $outputDir"
        }
        
        # Take screenshot using ADB
        $adbExe = Join-Path $ADB_PATH "adb.exe"
        $tempFile = [System.IO.Path]::GetTempFileName()
        & $adbExe -s $Device exec-out screencap -p > $tempFile
        
        if ($LASTEXITCODE -ne 0) {
            throw "Screenshot command failed"
        }
        
        # Move temp file to final location
        Move-Item $tempFile $OutputFile -Force
        
        Write-Host "${COLOR_GREEN}Screenshot saved successfully!${COLOR_RESET}"
        Write-Host "Location: ${COLOR_BLUE}$OutputFile${COLOR_RESET}"
        
        # Get file size for confirmation
        $fileInfo = Get-Item $OutputFile
        Write-Host "Size: ${COLOR_CYAN}$([math]::Round($fileInfo.Length / 1KB, 2)) KB${COLOR_RESET}"
        
        return $true
    }
    catch {
        Write-Host "${COLOR_RED}Error taking screenshot: $($_.Exception.Message)${COLOR_RESET}"
        return $false
    }
}

# Main execution
if ($Help) {
    Show-Help
}

Write-Host "${COLOR_BLUE}============================================================================${COLOR_RESET}"
Write-Host "${COLOR_BLUE}$SCRIPT_NAME v$SCRIPT_VERSION${COLOR_RESET}"
Write-Host "${COLOR_BLUE}============================================================================${COLOR_RESET}"
Write-Host ""

# Test ADB connection and get device
if ($DeviceId -eq "") {
    $DeviceId = Test-AdbConnection
    Write-Log "INFO" "Auto-detected device: $DeviceId"
} else {
    Write-Log "INFO" "Using specified device: $DeviceId"
}

# Generate filename if not provided
if ($Filename -eq "") {
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $Filename = "screenshot_$timestamp.png"
} elseif (-not $Filename.EndsWith(".png")) {
    $Filename += ".png"
}

# Construct full output path
$fullOutputPath = Join-Path $OutputPath $Filename

Write-Host "${COLOR_CYAN}Configuration:${COLOR_RESET}"
Write-Host "  Device: ${COLOR_YELLOW}$DeviceId${COLOR_RESET}"
Write-Host "  Output: ${COLOR_YELLOW}$fullOutputPath${COLOR_RESET}"
Write-Host ""

# Take the screenshot
$success = Take-Screenshot -Device $DeviceId -OutputFile $fullOutputPath

if ($success) {
    Write-Host ""
    Write-Host "${COLOR_GREEN}Screenshot operation completed successfully!${COLOR_RESET}"
    exit 0
} else {
    Write-Host ""
    Write-Host "${COLOR_RED}Screenshot operation failed!${COLOR_RESET}"
    exit 1
}
