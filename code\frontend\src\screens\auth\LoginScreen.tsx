/**
 * Login Screen
 * User authentication screen with email/password login
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TextInput,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useMutation } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';

// import { SocialButton } from '../../components';
import { Card, CardContent, Text, Button } from '../../components/ui';
// Temporarily comment out Input to isolate the issue
// import { Input } from '../../components/ui';
import { authAPI, LoginRequest, SocialAuthRequest } from '../../services/api/auth';
import {
  EnhancedError,
  parseApiError,
  getLoginErrorMessage,
  isAccountLockedError,
  isRateLimitedError,
  formatErrorForLogging
} from '../../utils/errorHandler';
import { theme } from '../../theme';
import { TestAccountsPanel } from '../../components/dev/TestAccountsPanel';
import { DEFAULT_TEST_ACCOUNTS } from '../../config/testAccounts';

interface LoginScreenProps {
  navigation: any; // TODO: Type this properly with navigation types
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  const [socialLoading, setSocialLoading] = useState<{ google: boolean; apple: boolean }>({
    google: false,
    apple: false,
  });
  const [showTestAccounts, setShowTestAccounts] = useState(false);

  // Test account helper for development
  const useTestAccount = (accountType: 'customer' | 'service_provider' | 'admin' = 'customer') => {
    if (__DEV__) {
      const testAccount = DEFAULT_TEST_ACCOUNTS[accountType];
      setEmail(testAccount.email);
      setPassword(testAccount.password);
      setErrors({});
    }
  };

  // Success handler for both login types
  const handleAuthSuccess = async (response: any) => {
    try {
      // Store tokens and user data
      await AsyncStorage.multiSet([
        ['access_token', response.access],
        ['refresh_token', response.refresh],
        ['user', JSON.stringify(response.user)],
      ]);

      // Navigate to main app
      navigation.replace('Main');
    } catch (error) {
      console.error('Error storing auth data:', error);
      Alert.alert('Error', 'Failed to save login information');
    }
  };

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: (data: LoginRequest) => authAPI.login(data),
    onSuccess: handleAuthSuccess,
    onError: (error: EnhancedError) => {
      console.error(formatErrorForLogging(error, 'LOGIN_SCREEN'));

      const errorInfo = parseApiError(error);
      const userMessage = getLoginErrorMessage(error);

      // Handle specific error types
      if (isAccountLockedError(error)) {
        const retryMinutes = Math.ceil((error.retryAfter || 1800) / 60);
        Alert.alert(
          'Account Temporarily Locked',
          `Your account has been temporarily locked due to multiple failed login attempts. Please try again in ${retryMinutes} minutes.`,
          [{ text: 'OK' }]
        );
      } else if (isRateLimitedError(error)) {
        Alert.alert(
          'Too Many Attempts',
          'You have made too many requests. Please wait a moment before trying again.',
          [{ text: 'OK' }]
        );
      } else if (error.response?.status === 400) {
        const errorData = error.response.data;
        if (errorData.detail) {
          Alert.alert('Login Failed', userMessage);
        } else {
          // Handle field-specific validation errors
          setErrors(errorData);
        }
      } else {
        Alert.alert(errorInfo.title, errorInfo.message);
      }
    },
  });

  // Social authentication mutation
  const socialAuthMutation = useMutation({
    mutationFn: (data: SocialAuthRequest) => authAPI.socialAuth(data),
    onSuccess: handleAuthSuccess,
    onError: (error: any) => {
      console.error('Social auth error:', error);
      setSocialLoading({ google: false, apple: false });

      if (error.response?.status === 400) {
        const errorData = error.response.data;
        Alert.alert('Authentication Failed', errorData.detail || 'Invalid social authentication token');
      } else {
        Alert.alert('Error', 'Social authentication failed. Please try again.');
      }
    },
  });

  const validateForm = (): boolean => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!password.trim()) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = () => {
    if (validateForm()) {
      loginMutation.mutate({ email: email.trim(), password });
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleSignUp = () => {
    navigation.navigate('Register');
  };

  const handleGoogleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, google: true }));

    try {
      // TODO: Implement actual Google Sign-In
      // For now, show a placeholder message
      Alert.alert(
        'Google Sign-In',
        'Google Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, google: false })) }]
      );
    } catch (error) {
      console.error('Google sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, google: false }));
      Alert.alert('Error', 'Google sign-in failed. Please try again.');
    }
  };

  const handleAppleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, apple: true }));

    try {
      // TODO: Implement actual Apple Sign-In
      // For now, show a placeholder message
      Alert.alert(
        'Apple Sign-In',
        'Apple Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, apple: false })) }]
      );
    } catch (error) {
      console.error('Apple sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, apple: false }));
      Alert.alert('Error', 'Apple sign-in failed. Please try again.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="heading1" align="center" style={styles.title}>
              Welcome Back
            </Text>
            <Text variant="body" color="secondary" align="center" style={styles.subtitle}>
              Sign in to your Vierla account
            </Text>
          </View>

          <View style={styles.form}>
            <Card style={styles.formCard}>
              <CardContent>
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  style={styles.input}
                />

                <TextInput
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Enter your password"
                  secureTextEntry
                  style={styles.input}
                />

                <Button
                  title="Sign In"
                  onPress={handleLogin}
                  loading={loginMutation.isPending}
                  style={styles.loginButton}
                />

                <Button
                  title="Forgot Password?"
                  onPress={handleForgotPassword}
                  variant="outline"
                  style={styles.forgotButton}
                />

                {__DEV__ && (
                  <View style={styles.devButtons}>
                    <Button
                      title="Quick Customer Login"
                      onPress={() => useTestAccount('customer')}
                      variant="ghost"
                      style={styles.testAccountButton}
                    />
                    <Button
                      title="All Test Accounts"
                      onPress={() => setShowTestAccounts(true)}
                      variant="outline"
                      style={styles.testAccountButton}
                    />
                  </View>
                )}
              </CardContent>
            </Card>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text variant="caption" color="secondary" style={styles.dividerText}>
                or continue with
              </Text>
              <View style={styles.dividerLine} />
            </View>

            <Card style={styles.socialCard}>
              <CardContent>
                <Text variant="caption" color="secondary" align="center" style={styles.socialTitle}>
                  Social Login (Coming Soon)
                </Text>
                <View style={styles.socialButtons} testID="social-buttons-container">
                  {/* Temporarily comment out SocialButton for testing */}
                  {/* <SocialButton
                    provider="google"
                    onPress={handleGoogleSignIn}
                    loading={socialLoading.google}
                    disabled={true} // Disabled until implemented
                  />
                  <SocialButton
                    provider="apple"
                    onPress={handleAppleSignIn}
                    loading={socialLoading.apple}
                    disabled={true} // Disabled until implemented
                  /> */}
                </View>
              </CardContent>
            </Card>
          </View>

          <View style={styles.footer}>
            <Text variant="body" color="secondary" align="center">
              Don't have an account?{' '}
              <Text variant="body" color="accent" onPress={handleSignUp}>
                Sign Up
              </Text>
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Test Accounts Panel for Development */}
      {__DEV__ && (
        <TestAccountsPanel
          visible={showTestAccounts}
          onClose={() => setShowTestAccounts(false)}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  header: {
    marginTop: theme.spacing.xl * 2,
    marginBottom: theme.spacing.xl,
  },
  title: {
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    marginBottom: 0,
  },
  form: {
    flex: 1,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
  },
  formCard: {
    marginBottom: theme.spacing.lg,
  },
  loginButton: {
    marginTop: theme.spacing.xs,
    marginBottom: theme.spacing.md,
  },
  forgotButton: {
    marginBottom: theme.spacing.lg,
  },
  devButtons: {
    gap: theme.spacing.xs,
  },
  testAccountButton: {
    marginTop: theme.spacing.sm,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.text.tertiary,
  },
  dividerText: {
    marginHorizontal: theme.spacing.md,
  },
  socialCard: {
    marginBottom: theme.spacing.xl,
  },
  socialTitle: {
    marginBottom: theme.spacing.sm,
  },
  socialButtons: {
    marginBottom: 0,
  },
  footer: {
    paddingBottom: theme.spacing.xl,
  },
});
