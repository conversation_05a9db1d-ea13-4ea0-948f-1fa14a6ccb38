import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { providerServiceAPI } from '../../services/api';

interface Service {
  id: string;
  name: string;
  description: string;
  category: {
    id: string;
    name: string;
  };
  base_price: number;
  duration: number;
  is_available: boolean;
  is_active: boolean;
  is_popular: boolean;
  booking_count: number;
  created_at: string;
}

interface ProviderSummary {
  total_services: number;
  active_services: number;
  inactive_services: number;
  is_verified: boolean;
  service_limit: number | null;
}

export const ProviderServicesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [services, setServices] = useState<Service[]>([]);
  const [providerSummary, setProviderSummary] = useState<ProviderSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);

  const fetchServices = async () => {
    try {
      const response = await providerServiceAPI.getServices();
      setServices(response.data.results || []);
      setProviderSummary(response.data.provider_summary);
    } catch (error) {
      console.error('Failed to fetch services:', error);
      Alert.alert('Error', 'Failed to load services');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchServices();
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchServices();
    }, [])
  );

  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    service.category.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const navigateToAddService = () => {
    navigation.navigate('AddService' as never);
  };

  const navigateToEditService = (service: Service) => {
    navigation.navigate('EditService' as never, { serviceId: service.id } as never);
  };

  const toggleServiceStatus = async (service: Service) => {
    try {
      await providerServiceAPI.toggleServiceStatus(service.id);
      fetchServices(); // Refresh the list
    } catch (error) {
      Alert.alert('Error', 'Failed to update service status');
    }
  };

  const deleteService = async (service: Service) => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await providerServiceAPI.deleteService(service.id);
              fetchServices(); // Refresh the list
            } catch (error) {
              Alert.alert('Error', 'Failed to delete service');
            }
          },
        },
      ]
    );
  };

  const toggleSelection = (serviceId: string) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const selectAll = () => {
    if (selectedServices.length === filteredServices.length) {
      setSelectedServices([]);
    } else {
      setSelectedServices(filteredServices.map(s => s.id));
    }
  };

  const bulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedServices.length === 0) {
      Alert.alert('Error', 'Please select services first');
      return;
    }

    const actionText = action === 'activate' ? 'activate' : action === 'deactivate' ? 'deactivate' : 'delete';
    
    Alert.alert(
      `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Services`,
      `Are you sure you want to ${actionText} ${selectedServices.length} service(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: actionText.charAt(0).toUpperCase() + actionText.slice(1),
          style: action === 'delete' ? 'destructive' : 'default',
          onPress: async () => {
            try {
              await providerServiceAPI.bulkUpdateServices(selectedServices, action);
              setSelectedServices([]);
              setSelectionMode(false);
              fetchServices();
            } catch (error) {
              Alert.alert('Error', `Failed to ${actionText} services`);
            }
          },
        },
      ]
    );
  };

  const renderServiceItem = ({ item }: { item: Service }) => (
    <TouchableOpacity
      style={[
        styles.serviceItem,
        selectedServices.includes(item.id) && styles.selectedServiceItem
      ]}
      onPress={() => {
        if (selectionMode) {
          toggleSelection(item.id);
        } else {
          navigateToEditService(item);
        }
      }}
      onLongPress={() => {
        if (!selectionMode) {
          setSelectionMode(true);
          toggleSelection(item.id);
        }
      }}
    >
      {selectionMode && (
        <TouchableOpacity
          style={styles.checkbox}
          onPress={() => toggleSelection(item.id)}
        >
          <Icon
            name={selectedServices.includes(item.id) ? 'check-box' : 'check-box-outline-blank'}
            size={24}
            color={colors.primary}
          />
        </TouchableOpacity>
      )}

      <View style={styles.serviceContent}>
        <View style={styles.serviceHeader}>
          <Text style={styles.serviceName}>{item.name}</Text>
          <View style={styles.serviceActions}>
            {item.is_popular && (
              <Icon name="star" size={16} color={colors.warning} style={styles.popularIcon} />
            )}
            <View style={[
              styles.statusBadge,
              { backgroundColor: item.is_available ? colors.success : colors.error }
            ]}>
              <Text style={styles.statusText}>
                {item.is_available ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        </View>

        <Text style={styles.serviceCategory}>{item.category.name}</Text>
        <Text style={styles.serviceDescription} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.serviceDetails}>
          <Text style={styles.servicePrice}>${item.base_price.toFixed(2)}</Text>
          <Text style={styles.serviceDuration}>{item.duration}min</Text>
          <Text style={styles.serviceBookings}>{item.booking_count} bookings</Text>
        </View>

        {!selectionMode && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => toggleServiceStatus(item)}
              testID="toggle-status-button"
            >
              <Icon
                name={item.is_available ? 'visibility-off' : 'visibility'}
                size={20}
                color={colors.primary}
              />
              <Text style={styles.actionButtonText}>
                {item.is_available ? 'Disable' : 'Enable'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => navigateToEditService(item)}
            >
              <Icon name="edit" size={20} color={colors.primary} />
              <Text style={styles.actionButtonText}>Edit</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => deleteService(item)}
              testID="delete-service-button"
            >
              <Icon name="delete" size={20} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>Delete</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Provider Summary */}
      {providerSummary && (
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.total_services}</Text>
              <Text style={styles.summaryLabel}>Total</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.active_services}</Text>
              <Text style={styles.summaryLabel}>Active</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.inactive_services}</Text>
              <Text style={styles.summaryLabel}>Inactive</Text>
            </View>
          </View>
          
          {providerSummary.service_limit && (
            <View style={styles.limitInfo}>
              <Icon name="info" size={16} color={colors.warning} />
              <Text style={styles.limitText}>
                {providerSummary.service_limit - providerSummary.total_services} services remaining
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color={colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search services..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={colors.textSecondary}
        />
      </View>

      {/* Action Bar */}
      <View style={styles.actionBar}>
        {selectionMode ? (
          <>
            <TouchableOpacity style={styles.selectAllButton} onPress={selectAll}>
              <Text style={styles.selectAllText}>
                {selectedServices.length === filteredServices.length ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setSelectionMode(false);
                setSelectedServices([]);
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity style={styles.addButton} onPress={navigateToAddService}>
            <Icon name="add" size={20} color={colors.white} />
            <Text style={styles.addButtonText}>Add Service</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Bulk Actions */}
      {selectionMode && selectedServices.length > 0 && (
        <View style={styles.bulkActions}>
          <Text style={styles.selectedCount}>{selectedServices.length} selected</Text>
          <View style={styles.bulkActionButtons}>
            <TouchableOpacity
              style={styles.bulkActionButton}
              onPress={() => bulkAction('activate')}
            >
              <Text style={styles.bulkActionText}>Activate</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.bulkActionButton}
              onPress={() => bulkAction('deactivate')}
            >
              <Text style={styles.bulkActionText}>Deactivate</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.bulkActionButton, styles.deleteButton]}
              onPress={() => bulkAction('delete')}
            >
              <Text style={[styles.bulkActionText, { color: colors.error }]}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={filteredServices}
        renderItem={renderServiceItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        testID="services-list"
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon name="room-service" size={64} color={colors.textSecondary} />
            <Text style={styles.emptyTitle}>No services found</Text>
            <Text style={styles.emptyDescription}>
              {searchQuery ? 'Try adjusting your search' : 'Create your first service to get started'}
            </Text>
            {!searchQuery && (
              <TouchableOpacity style={styles.emptyButton} onPress={navigateToAddService}>
                <Text style={styles.emptyButtonText}>Add Service</Text>
              </TouchableOpacity>
            )}
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  listContainer: {
    flexGrow: 1,
  },
  header: {
    backgroundColor: colors.white,
    paddingBottom: spacing.md,
  },
  summaryCard: {
    margin: spacing.lg,
    padding: spacing.lg,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    ...typography.h2,
    color: colors.primary,
  },
  summaryLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  limitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  limitText: {
    ...typography.caption,
    color: colors.warning,
    marginLeft: spacing.xs,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  addButtonText: {
    ...typography.body,
    color: colors.white,
    marginLeft: spacing.sm,
    fontWeight: '600',
  },
  selectAllButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  selectAllText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  cancelButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  cancelButtonText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  bulkActions: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  selectedCount: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  bulkActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  bulkActionButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 6,
    backgroundColor: colors.white,
  },
  deleteButton: {
    backgroundColor: colors.errorLight,
  },
  bulkActionText: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
  },
  serviceItem: {
    backgroundColor: colors.white,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedServiceItem: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  checkbox: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    zIndex: 1,
  },
  serviceContent: {
    flex: 1,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  serviceName: {
    ...typography.h3,
    color: colors.textPrimary,
    flex: 1,
    marginRight: spacing.md,
  },
  serviceActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  popularIcon: {
    marginRight: spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  serviceCategory: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  serviceDescription: {
    ...typography.body,
    color: colors.textSecondary,
    marginBottom: spacing.md,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  servicePrice: {
    ...typography.body,
    color: colors.success,
    fontWeight: '700',
  },
  serviceDuration: {
    ...typography.body,
    color: colors.textSecondary,
  },
  serviceBookings: {
    ...typography.body,
    color: colors.textSecondary,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  actionButtonText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
  },
  emptyDescription: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  emptyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  emptyButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
