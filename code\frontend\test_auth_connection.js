/**
 * Quick Authentication Connection Test
 * Tests if the API connection fix works with real backend
 */

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function testAuthConnection() {
  console.log('🔍 Testing Authentication Connection...');
  console.log(`📡 API Base URL: ${API_BASE_URL}`);
  
  try {
    // Test 1: Try login with consolidated test account (this also tests connectivity)
    console.log('\n1️⃣ Testing login with consolidated test account...');
    const loginData = {
      email: '<EMAIL>',
      password: 'TestPass123!'
    };
    
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login/`, loginData, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Login successful!');
    console.log('📋 Response data:');
    console.log(`   User ID: ${loginResponse.data.user.id}`);
    console.log(`   Email: ${loginResponse.data.user.email}`);
    console.log(`   Role: ${loginResponse.data.user.role}`);
    console.log(`   Verified: ${loginResponse.data.user.is_verified}`);
    console.log(`   Access Token: ${loginResponse.data.access ? 'Present' : 'Missing'}`);
    console.log(`   Refresh Token: ${loginResponse.data.refresh ? 'Present' : 'Missing'}`);
    
    // Test 2: Try authenticated request
    console.log('\n2️⃣ Testing authenticated request...');
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile/`, {
      headers: {
        'Authorization': `Bearer ${loginResponse.data.access}`,
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    console.log('✅ Authenticated request successful!');
    console.log('📋 Profile data:');
    console.log(`   Full Name: ${profileResponse.data.first_name} ${profileResponse.data.last_name}`);
    console.log(`   Account Status: ${profileResponse.data.account_status || 'N/A'}`);
    
    console.log('\n🎉 All authentication tests passed!');
    console.log('✅ API connection fix is working correctly');
    
  } catch (error) {
    console.log('\n❌ Authentication test failed:');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔌 Connection refused - Backend may not be running');
      console.log(`   Tried to connect to: ${error.config?.baseURL || API_BASE_URL}`);
    } else if (error.response) {
      console.log(`📡 HTTP Error: ${error.response.status}`);
      console.log(`📋 Error data:`, error.response.data);
    } else if (error.request) {
      console.log('📡 Network error - No response received');
      console.log('🔍 Request details:', error.request);
    } else {
      console.log('❓ Unexpected error:', error.message);
    }
    
    process.exit(1);
  }
}

// Run the test
testAuthConnection();
