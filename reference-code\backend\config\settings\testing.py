"""
<PERSON><PERSON><PERSON> Backend - Testing Settings
Optimized for fast test execution with mobile testing considerations
"""
from .base import *
import tempfile

# Override for testing
DEBUG = False
SECRET_KEY = 'test-secret-key-for-testing-only'

# Test database - use in-memory SQLite for speed
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        },
    }
}

# Enable migrations for tests (needed for proper database setup)
# MIGRATION_MODULES can be disabled later for faster tests if needed

# Use dummy cache for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    },
    'sessions': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Disable Celery for testing
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Use console email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Simplified password validation for testing
AUTH_PASSWORD_VALIDATORS = []

# Faster password hashing for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
    'loggers': {
        'django': {
            'handlers': ['null'],
            'propagate': False,
        },
        'apps': {
            'handlers': ['null'],
            'propagate': False,
        },
    },
}

# Test media files
MEDIA_ROOT = tempfile.mkdtemp()

# Mobile testing configurations
MOBILE_OPTIMIZATION = {
    'CACHE_TIMEOUT': 1,  # Very short for testing
    'MAX_PAYLOAD_SIZE': 1024,  # Small for testing
    'BATTERY_AWARE_PROCESSING': False,  # Disable for consistent testing
    'NETWORK_ADAPTATION': False,  # Disable for consistent testing
    'COMPRESSION_ENABLED': False,  # Disable for faster testing
}

# Test-specific DRF settings
REST_FRAMEWORK.update({
    'DEFAULT_THROTTLE_RATES': {
        'anon': '1000/hour',  # Higher limits for testing
        'user': '10000/hour',
        'mobile': '2000/hour',
        'login': '50/minute',
        'register': '30/minute',
        'password_reset': '30/hour',
    },
})

# JWT settings for testing
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),  # Short for testing
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=10),
    'ROTATE_REFRESH_TOKENS': False,  # Disable for simpler testing
    'BLACKLIST_AFTER_ROTATION': False,
})

# Test file storage
DEFAULT_FILE_STORAGE = 'django.core.files.storage.InMemoryStorage'
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Disable CORS checks in testing
CORS_ALLOW_ALL_ORIGINS = True
