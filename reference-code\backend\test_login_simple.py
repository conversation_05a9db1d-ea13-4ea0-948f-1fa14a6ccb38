#!/usr/bin/env python3
"""
Simple login test <NAME_EMAIL> authentication
"""
import requests
import json

API_BASE_URL = 'http://192.168.2.65:8000/api'

def test_login(email, password):
    """Test login with specific credentials"""
    print(f"Testing login: {email} with password: {password}")
    
    login_data = {
        'email': email,
        'password': password
    }
    
    try:
        response = requests.post(
            f'{API_BASE_URL}/auth/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ LOGIN SUCCESSFUL!")
            print(f"User: {data.get('user', {}).get('email', 'N/A')}")
            print(f"Role: {data.get('user', {}).get('role', 'N/A')}")
            print(f"Token: {data.get('access', 'N/A')[:50]}...")
            return True
        else:
            print("❌ LOGIN FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    print("=" * 60)
    print("VIERLA AUTHENTICATION TEST")
    print("=" * 60)
    
    # Test both password variations
    print("\n1. Testing with 'testpass123' (backend default):")
    success1 = test_login('<EMAIL>', 'testpass123')
    
    print("\n2. Testing with 'Testpass123' (user mentioned):")
    success2 = test_login('<EMAIL>', 'Testpass123')
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"testpass123: {'✅ SUCCESS' if success1 else '❌ FAILED'}")
    print(f"Testpass123: {'✅ SUCCESS' if success2 else '❌ FAILED'}")
    print("=" * 60)

if __name__ == '__main__':
    main()
