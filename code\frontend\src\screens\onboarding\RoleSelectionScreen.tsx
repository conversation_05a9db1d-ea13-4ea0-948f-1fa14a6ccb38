/**
 * RoleSelectionScreen Component
 * 
 * Allows users to choose between Customer and Service Provider roles.
 * This determines their onboarding flow and app experience.
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text } from '../../components/Text';
import { Button } from '../../components/Button';
import { colors, spacing } from '../../theme';

type RoleSelectionNavigationProp = StackNavigationProp<any, 'RoleSelection'>;

type UserRole = 'customer' | 'service_provider';

interface RoleOption {
  id: UserRole;
  title: string;
  description: string;
  icon: string;
  benefits: string[];
}

const roleOptions: RoleOption[] = [
  {
    id: 'customer',
    title: 'I need services',
    description: 'Find and book trusted service providers in your area',
    icon: '🔍',
    benefits: [
      'Browse local services',
      'Read verified reviews',
      'Secure booking & payment',
      'Direct communication',
    ],
  },
  {
    id: 'service_provider',
    title: 'I provide services',
    description: 'Grow your business by connecting with customers',
    icon: '🛠️',
    benefits: [
      'Reach more customers',
      'Manage your bookings',
      'Secure payments',
      'Build your reputation',
    ],
  },
];

export const RoleSelectionScreen: React.FC = () => {
  const navigation = useNavigation<RoleSelectionNavigationProp>();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
  };

  const handleContinue = async () => {
    if (!selectedRole) {
      Alert.alert('Please select a role', 'Choose whether you need services or provide services to continue.');
      return;
    }

    setIsLoading(true);

    try {
      // Save selected role to AsyncStorage
      await AsyncStorage.setItem('selected_role', selectedRole);
      await AsyncStorage.setItem('onboarding_step', 'role_selected');

      // Navigate to appropriate onboarding flow
      if (selectedRole === 'customer') {
        navigation.navigate('CustomerOnboarding');
      } else {
        navigation.navigate('ProviderOnboarding');
      }
    } catch (error) {
      console.error('Error saving role selection:', error);
      Alert.alert('Error', 'Failed to save your selection. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderRoleOption = (option: RoleOption) => {
    const isSelected = selectedRole === option.id;

    return (
      <TouchableOpacity
        key={option.id}
        style={[
          styles.roleOption,
          isSelected && styles.roleOptionSelected,
        ]}
        onPress={() => handleRoleSelect(option.id)}
        testID={`role-option-${option.id}`}
      >
        <View style={styles.roleHeader}>
          <Text variant="h1" style={styles.roleIcon}>
            {option.icon}
          </Text>
          
          <View style={styles.roleInfo}>
            <Text variant="h3" style={styles.roleTitle}>
              {option.title}
            </Text>
            
            <Text variant="body" color="secondary" style={styles.roleDescription}>
              {option.description}
            </Text>
          </View>

          {/* Selection Indicator */}
          <View style={[
            styles.selectionIndicator,
            isSelected && styles.selectionIndicatorSelected,
          ]}>
            {isSelected && (
              <Text variant="caption" style={styles.checkmark}>
                ✓
              </Text>
            )}
          </View>
        </View>

        {/* Benefits List */}
        <View style={styles.benefitsList}>
          {option.benefits.map((benefit, index) => (
            <View key={index} style={styles.benefitItem}>
              <Text variant="caption" style={styles.benefitBullet}>
                •
              </Text>
              <Text variant="caption" color="secondary" style={styles.benefitText}>
                {benefit}
              </Text>
            </View>
          ))}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={handleBack} 
          style={styles.backButton}
          testID="back-button"
        >
          <Text variant="button" color="primary">
            ← Back
          </Text>
        </TouchableOpacity>
        
        <Text variant="h2" style={styles.headerTitle}>
          How will you use Vierla?
        </Text>
        
        <Text variant="body" color="secondary" style={styles.headerSubtitle}>
          Choose your role to get a personalized experience
        </Text>
      </View>

      {/* Role Options */}
      <View style={styles.content}>
        <View style={styles.roleOptions}>
          {roleOptions.map(renderRoleOption)}
        </View>
      </View>

      {/* Action Section */}
      <View style={styles.actionSection}>
        <Button
          title="Continue"
          onPress={handleContinue}
          loading={isLoading}
          disabled={!selectedRole}
          style={styles.continueButton}
          testID="continue-button"
        />
        
        <Text variant="caption" color="secondary" style={styles.disclaimer}>
          You can change this later in your profile settings
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.lg,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: spacing.md,
  },
  headerTitle: {
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  headerSubtitle: {
    lineHeight: 22,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  roleOptions: {
    gap: spacing.md,
  },
  roleOption: {
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 12,
    padding: spacing.lg,
    backgroundColor: colors.white,
  },
  roleOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '08', // 8% opacity
  },
  roleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  roleIcon: {
    fontSize: 32,
    marginRight: spacing.md,
  },
  roleInfo: {
    flex: 1,
  },
  roleTitle: {
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  roleDescription: {
    lineHeight: 20,
  },
  selectionIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  selectionIndicatorSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
  },
  checkmark: {
    color: colors.white,
    fontWeight: 'bold',
  },
  benefitsList: {
    gap: spacing.xs,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  benefitBullet: {
    color: colors.primary,
    marginRight: spacing.sm,
    lineHeight: 18,
  },
  benefitText: {
    flex: 1,
    lineHeight: 18,
  },
  actionSection: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    paddingTop: spacing.md,
  },
  continueButton: {
    marginBottom: spacing.sm,
  },
  disclaimer: {
    textAlign: 'center',
    lineHeight: 16,
  },
});

export default RoleSelectionScreen;
