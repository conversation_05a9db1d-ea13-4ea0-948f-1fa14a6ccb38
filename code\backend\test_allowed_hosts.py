"""
Test for Django ALLOWED_HOSTS configuration
Tests that the required IP addresses are properly configured in ALLOWED_HOSTS
"""
import pytest
import os
import django
from django.conf import settings
from django.test import TestCase, override_settings
from django.core.exceptions import DisallowedHost
from django.http import HttpRequest
from django.test.client import RequestFactory

# Configure Django settings for testing without database
if not settings.configured:
    settings.configure(
        DEBUG=True,
        SECRET_KEY='test-secret-key',
        ALLOWED_HOSTS=['localhost', '127.0.0.1', '0.0.0.0', 'testserver'],
        USE_TZ=True,
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': ':memory:',
            }
        },
        INSTALLED_APPS=[
            'django.contrib.contenttypes',
            'django.contrib.auth',
        ],
    )
    django.setup()


class AllowedHostsConfigurationTest(TestCase):
    """Test ALLOWED_HOSTS configuration for mobile development"""
    
    def test_localhost_allowed(self):
        """Test that localhost is in ALLOWED_HOSTS"""
        self.assertIn('localhost', settings.ALLOWED_HOSTS)
    
    def test_127_0_0_1_allowed(self):
        """Test that 127.0.0.1 is in ALLOWED_HOSTS"""
        self.assertIn('127.0.0.1', settings.ALLOWED_HOSTS)
    
    def test_network_ip_192_168_2_65_allowed(self):
        """Test that ************ is in ALLOWED_HOSTS for mobile development"""
        self.assertIn('************', settings.ALLOWED_HOSTS)
    
    def test_android_emulator_ip_allowed(self):
        """Test that ******** is in ALLOWED_HOSTS for Android emulator"""
        self.assertIn('********', settings.ALLOWED_HOSTS)
    
    def test_request_from_network_ip_accepted(self):
        """Test that HTTP requests from ************ are accepted"""
        factory = RequestFactory()
        request = factory.get('/api/', HTTP_HOST='************:8000')
        
        # This should not raise DisallowedHost exception
        try:
            host = request.get_host()
            # The host should be accepted without raising an exception
            self.assertEqual(host, '************:8000')
        except DisallowedHost:
            self.fail("Request from ************:8000 should be allowed but was rejected")
    
    def test_request_from_localhost_accepted(self):
        """Test that HTTP requests from localhost are accepted"""
        factory = RequestFactory()
        request = factory.get('/api/', HTTP_HOST='localhost:8000')
        
        try:
            host = request.get_host()
            self.assertEqual(host, 'localhost:8000')
        except DisallowedHost:
            self.fail("Request from localhost:8000 should be allowed but was rejected")
    
    def test_disallowed_host_rejected(self):
        """Test that requests from non-allowed hosts are properly rejected"""
        factory = RequestFactory()
        request = factory.get('/api/', HTTP_HOST='malicious-host.com')
        
        # This should raise DisallowedHost exception
        with self.assertRaises(DisallowedHost):
            request.get_host()


if __name__ == '__main__':
    pytest.main([__file__])
