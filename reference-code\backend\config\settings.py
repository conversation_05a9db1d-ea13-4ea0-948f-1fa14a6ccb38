"""
<PERSON><PERSON><PERSON>end - Settings Module
Automatically loads the appropriate settings based on environment
"""
import os

# Determine which settings to use based on environment
ENVIRONMENT = os.environ.get('DJANGO_ENVIRONMENT', 'development')

if ENVIRONMENT == 'production':
    from .settings.production import *
elif ENVIRONMENT == 'testing':
    from .settings.testing import *
else:
    from .settings.development import *
