/**
 * WelcomeScreen Component
 * 
 * The first screen users see when opening the app for the first time.
 * Provides app introduction and entry point to the onboarding flow.
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import { Text } from '../../components/Text';
import { Button } from '../../components/Button';
import { useTheme } from '../../contexts/ThemeContext';

const { width, height } = Dimensions.get('window');

type WelcomeScreenNavigationProp = StackNavigationProp<any, 'Welcome'>;

export const WelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();
  const { colors, spacing, typography } = useTheme();

  const handleGetStarted = () => {
    navigation.navigate('Initialization');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.primary,
    },
    header: {
      flex: 0.6,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: spacing.xl,
    },
    logoContainer: {
      alignItems: 'center',
    },
    logoPlaceholder: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: spacing.lg,
    },
    logoText: {
      fontSize: 48,
      fontWeight: 'bold',
    },
    appName: {
      marginBottom: spacing.sm,
    },
    tagline: {
      textAlign: 'center',
      opacity: 0.9,
    },
    content: {
      flex: 0.4,
      paddingHorizontal: spacing.xl,
      paddingBottom: spacing.xl,
      justifyContent: 'space-between',
    },
    features: {
      marginBottom: spacing.xl,
    },
    feature: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    featureIcon: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      marginRight: spacing.md,
    },
    actions: {
      gap: spacing.md,
    },
    primaryButton: {
      backgroundColor: colors.white,
      paddingVertical: spacing.md,
      borderRadius: 12,
    },
    primaryButtonText: {
      color: colors.primary,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      textAlign: 'center',
    },
    secondaryButton: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: colors.white,
      paddingVertical: spacing.md,
      borderRadius: 12,
    },
    secondaryButtonText: {
      color: colors.white,
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.medium,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      {/* Header Section */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          {/* App Logo Placeholder */}
          <View style={styles.logoPlaceholder}>
            <Text variant="h1" color="white" style={styles.logoText}>
              V
            </Text>
          </View>
          
          <Text variant="h1" color="white" style={styles.appName}>
            Vierla
          </Text>
          
          <Text variant="body" color="white" style={styles.tagline}>
            Your trusted service marketplace
          </Text>
        </View>
      </View>

      {/* Content Section */}
      <View style={styles.content}>
        <View style={styles.welcomeContent}>
          <Text variant="h2" style={styles.welcomeTitle}>
            Welcome to Vierla
          </Text>
          
          <Text variant="body" color="secondary" style={styles.welcomeDescription}>
            Connect with trusted service providers in your area. From home repairs to personal services, find exactly what you need.
          </Text>

          {/* Feature Highlights */}
          <View style={styles.features}>
            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">🔍</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Discover Services
              </Text>
            </View>

            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">⭐</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Trusted Reviews
              </Text>
            </View>

            <View style={styles.feature}>
              <View style={styles.featureIcon}>
                <Text variant="h3">💬</Text>
              </View>
              <Text variant="caption" style={styles.featureText}>
                Easy Communication
              </Text>
            </View>
          </View>
        </View>

        {/* Action Section */}
        <View style={styles.actionSection}>
          <Button
            title="Get Started"
            onPress={handleGetStarted}
            style={styles.getStartedButton}
            testID="get-started-button"
          />
          
          {/* App Version */}
          <Text variant="caption" color="secondary" style={styles.version}>
            Version 1.0.0
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default WelcomeScreen;
