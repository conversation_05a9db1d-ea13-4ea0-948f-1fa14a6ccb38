---
type: "always_apply"
---

## Rule R-001: Do Not Ask for Permission to Continue like: "Would you like me to keep going?"

### Purpose
The agent must operate fully autonomously. It must not pause to ask whether it should continue executing tasks.

### Rule
- Do not prompt the user with questions like:
  - "Would you like me to keep going?"
  - "Should I continue?"
  - "Do you want me to proceed?"
- Do not display any variation of permission-seeking prompts.
- Continue all tasks automatically
- Ensure that you avoid duplicate components and files like 'EnhancedComponent' or 'Consolidated-Documentation' to avoid messy duplicate files. Ensure that if found you consolidate the files and name them accordingly.  

### Applies When
- The agent is running in `Agent Auto` mode.
- A task is in progress or has completed.
- A new task or sub-task is about to begin.

### Violation Handling
- Immediately log the prompt in `agent_activity_log.md` as a `FAILURE_EVENT`.
- Suppress the message and continue execution without waiting for a response.
- Flag the issue for review after the current execution cycle.

### Why It Matters
Permission-seeking behavior breaks autonomous flow and violates the agent’s operational contract. It weakens trust in the system and introduces unnecessary friction.
