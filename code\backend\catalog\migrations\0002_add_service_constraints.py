# Generated by Django 5.2.4 on 2025-08-05 07:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("catalog", "0001_initial"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="service",
            constraint=models.UniqueConstraint(
                condition=models.Q(("is_active", True)),
                fields=("provider", "name"),
                name="unique_active_service_name_per_provider",
            ),
        ),
        migrations.AddConstraint(
            model_name="service",
            constraint=models.CheckConstraint(
                condition=models.Q(("base_price__gte", 0)), name="positive_base_price"
            ),
        ),
        migrations.AddConstraint(
            model_name="service",
            constraint=models.CheckConstraint(
                condition=models.Q(("duration__gt", 0)), name="positive_duration"
            ),
        ),
    ]
