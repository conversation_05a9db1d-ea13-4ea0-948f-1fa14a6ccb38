#!/usr/bin/env python
"""
Comprehensive Startup Data Seeder for Vierla Backend
Automatically seeds all mock data during development startup
"""

import os
import sys
import django
import subprocess
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.core.management import call_command
from django.db import transaction
from apps.authentication.models import User
from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.bookings.models import Booking


def print_header(title):
    """Print formatted section header"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")


def print_step(step, description):
    """Print formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 50)


def check_existing_data():
    """Check if data already exists"""
    print_header("CHECKING EXISTING DATA")
    
    categories_count = ServiceCategory.objects.count()
    providers_count = ServiceProvider.objects.count()
    services_count = Service.objects.count()
    users_count = User.objects.count()
    bookings_count = Booking.objects.count()
    
    print(f"📊 Current Database State:")
    print(f"   - Service Categories: {categories_count}")
    print(f"   - Service Providers: {providers_count}")
    print(f"   - Services: {services_count}")
    print(f"   - Users: {users_count}")
    print(f"   - Bookings: {bookings_count}")
    
    has_data = any([categories_count > 0, providers_count > 0, services_count > 0, users_count > 0])
    
    if has_data:
        print(f"\n⚠️  Existing data detected!")
        response = input("Do you want to continue and add more data? (y/n): ").lower().strip()
        if response != 'y':
            print("❌ Startup seeding cancelled by user.")
            return False
    else:
        print(f"\n✅ Database is empty - ready for seeding!")
    
    return True


def run_script(script_path, description):
    """Run a Python script and handle errors"""
    try:
        print(f"🔄 Running: {description}")
        result = subprocess.run([
            sys.executable, script_path
        ], cwd=backend_dir, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT (5 minutes)")
        return False
    except Exception as e:
        print(f"💥 {description} - ERROR: {e}")
        return False


def seed_service_categories():
    """Seed service categories"""
    print_step(1, "Service Categories Creation")
    
    script_path = backend_dir / "scripts" / "verify_service_categories.py"
    return run_script(script_path, "Service Categories Seeding")


def seed_service_providers():
    """Seed service providers"""
    print_step(2, "Service Providers Creation")
    
    script_path = backend_dir / "scripts" / "create_mock_service_providers.py"
    return run_script(script_path, "Service Providers Seeding")


def seed_services():
    """Seed services and pricing"""
    print_step(3, "Services and Pricing Creation")
    
    script_path = backend_dir / "scripts" / "create_service_listings.py"
    return run_script(script_path, "Services and Pricing Seeding")


def seed_customers():
    """Seed customer accounts"""
    print_step(4, "Customer Accounts Creation")
    
    script_path = backend_dir / "scripts" / "create_mock_customers.py"
    return run_script(script_path, "Customer Accounts Seeding")


def seed_booking_history():
    """Seed booking history and transactions"""
    print_step(5, "Booking History and Transactions Creation")
    
    script_path = backend_dir / "scripts" / "create_booking_history.py"
    return run_script(script_path, "Booking History Seeding")


def verify_seeding():
    """Verify all data was seeded correctly"""
    print_step(6, "Data Verification")
    
    script_path = backend_dir / "scripts" / "verify_database_seeding.py"
    return run_script(script_path, "Database Verification")


def print_summary():
    """Print final summary"""
    print_header("STARTUP SEEDING COMPLETE")
    
    # Get final counts
    categories_count = ServiceCategory.objects.count()
    providers_count = ServiceProvider.objects.count()
    services_count = Service.objects.count()
    users_count = User.objects.count()
    bookings_count = Booking.objects.count()
    
    print(f"🎉 Final Database State:")
    print(f"   ✅ Service Categories: {categories_count}")
    print(f"   ✅ Service Providers: {providers_count}")
    print(f"   ✅ Services: {services_count}")
    print(f"   ✅ Users: {users_count}")
    print(f"   ✅ Bookings: {bookings_count}")
    
    print(f"\n🚀 Vierla Backend Ready for Development!")
    print(f"📱 Frontend can now connect to fully populated backend")
    print(f"🧪 All test data available for comprehensive testing")
    
    print(f"\n📋 Quick Access:")
    print(f"   🌐 API Base: http://************:8000/api/")
    print(f"   📚 API Docs: http://************:8000/api/docs/")
    print(f"   🔧 Admin Panel: http://************:8000/admin/")
    
    print(f"\n🔑 Test Credentials:")
    print(f"   👤 Customer: <EMAIL> / VierlaTest123!")
    print(f"   🏢 Provider: <EMAIL> / VierlaTest123!")
    print(f"   👑 Admin: <EMAIL> / VierlaAdmin123!")


def main():
    """Main startup seeding function"""
    print_header("VIERLA BACKEND STARTUP DATA SEEDER")
    print("🎯 Comprehensive mock data population for development environment")
    
    try:
        # Check existing data
        if not check_existing_data():
            return False
        
        # Run seeding steps
        steps = [
            seed_service_categories,
            seed_service_providers,
            seed_services,
            seed_customers,
            seed_booking_history,
            verify_seeding
        ]
        
        success_count = 0
        for step_func in steps:
            if step_func():
                success_count += 1
            else:
                print(f"\n⚠️  Step failed but continuing with remaining steps...")
        
        # Print summary
        print_summary()
        
        if success_count == len(steps):
            print(f"\n🎉 ALL STEPS COMPLETED SUCCESSFULLY!")
            return True
        else:
            print(f"\n⚠️  {success_count}/{len(steps)} steps completed successfully")
            print(f"Some steps may have failed - check output above")
            return False
            
    except KeyboardInterrupt:
        print(f"\n❌ Startup seeding interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Startup seeding failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
