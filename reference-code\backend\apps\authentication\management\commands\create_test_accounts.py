"""
Django management command to create test accounts for Vierla backend development
Creates service provider and customer test accounts with proper roles and verification
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.authentication.models import UserProfile
from apps.catalog.models import ServiceProvider, ServiceCategory

User = get_user_model()


class Command(BaseCommand):
    help = 'Create test accounts for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing test accounts',
        )

    def handle(self, *args, **options):
        force = options['force']

        self.stdout.write(
            self.style.SUCCESS('Creating test accounts for Vierla backend...')
        )

        # Create customer test account
        customer_created = self.create_customer_account(force)

        # Create service provider test account
        provider_created = self.create_provider_account(force)

        if customer_created or provider_created:
            self.stdout.write(
                self.style.SUCCESS('Test accounts created successfully!')
            )
            self.stdout.write('\nTest Account Details:')
            self.stdout.write('=' * 50)

            if customer_created:
                self.stdout.write('\nCustomer Account:')
                self.stdout.write('Email: <EMAIL>')
                self.stdout.write('Password: testpass123')
                self.stdout.write('Role: Customer')

            if provider_created:
                self.stdout.write('\nService Provider Account:')
                self.stdout.write('Email: <EMAIL>')
                self.stdout.write('Password: testpass123')
                self.stdout.write('Role: Service Provider')

            self.stdout.write('\nAdmin Account (already exists):')
            self.stdout.write('Email: <EMAIL>')
            self.stdout.write('Password: admin123')
            self.stdout.write('Role: Admin/Superuser')
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Test accounts already exist. Use --force to recreate.')
            )

    def create_customer_account(self, force=False):
        """Create customer test account"""
        email = '<EMAIL>'

        if User.objects.filter(email=email).exists():
            if force:
                User.objects.filter(email=email).delete()
                self.stdout.write(
                    f'Deleted existing customer account: {email}')
            else:
                self.stdout.write(f'Customer account already exists: {email}')
                return False

        # Create customer user
        customer = User.objects.create_user(
            email=email,
            password='testpass123',
            first_name='Test',
            last_name='Customer',
            role=User.UserRole.CUSTOMER,
            account_status=User.AccountStatus.ACTIVE,
            is_verified=True,
            email_verified_at=timezone.now(),
            is_test_account=True
        )

        # Update user profile (created automatically by signal)
        profile = customer.profile
        profile.city = 'Toronto'
        profile.state = 'Ontario'
        profile.country = 'Canada'
        profile.search_radius = 25
        profile.save()

        self.stdout.write(f'Created customer account: {email}')
        return True

    def create_provider_account(self, force=False):
        """Create service provider test account"""
        email = '<EMAIL>'

        if User.objects.filter(email=email).exists():
            if force:
                User.objects.filter(email=email).delete()
                self.stdout.write(
                    f'Deleted existing provider account: {email}')
            else:
                self.stdout.write(f'Provider account already exists: {email}')
                return False

        # Create provider user
        provider_user = User.objects.create_user(
            email=email,
            password='testpass123',
            first_name='Test',
            last_name='Provider',
            role=User.UserRole.SERVICE_PROVIDER,
            account_status=User.AccountStatus.ACTIVE,
            is_verified=True,
            email_verified_at=timezone.now(),
            phone='+**********',
            is_test_account=True
        )

        # Update user profile (created automatically by signal)
        profile = provider_user.profile
        profile.city = 'Toronto'
        profile.state = 'Ontario'
        profile.country = 'Canada'
        profile.search_radius = 50
        profile.auto_accept_bookings = True
        profile.save()

        # Create service provider profile
        # First, ensure we have a category
        category, created = ServiceCategory.objects.get_or_create(
            name='Hair & Beauty',
            defaults={
                'description': 'Hair styling and beauty services',
                'slug': 'hair-beauty',
                'is_active': True,
                'is_popular': True,
                'sort_order': 1
            }
        )

        provider_profile = ServiceProvider.objects.create(
            user=provider_user,
            business_name='Test Beauty Salon',
            business_description='A test beauty salon for development and testing',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Test Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            latitude=43.6532,
            longitude=-79.3832,
            website='https://testbeautysalon.com',
            is_verified=True,
            is_featured=True,
            rating=4.8,
            review_count=25,
            years_of_experience=5
        )

        # Add category to provider
        provider_profile.categories.add(category)

        self.stdout.write(f'Created provider account: {email}')
        self.stdout.write(
            f'Created provider profile: {provider_profile.business_name}')
        return True
