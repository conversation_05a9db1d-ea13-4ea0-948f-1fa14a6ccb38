#!/usr/bin/env python3
"""
Check database tables script
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection

def check_tables():
    """Check what tables exist in the database."""
    cursor = connection.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print("Available tables:")
    for table in tables:
        print(f"  - {table[0]}")
    
    # Check specific tables we need
    important_tables = [
        'authentication_user',
        'auth_user', 
        'catalog_serviceprovider',
        'bookings_booking',
        'django_session'
    ]
    
    print("\nChecking important tables:")
    for table_name in important_tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"  ✅ {table_name}: {count} records")
        except Exception as e:
            print(f"  ❌ {table_name}: {e}")

if __name__ == "__main__":
    check_tables()
