/**
 * Pricing Step Component
 * Second step of multi-step service creation form
 */

import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../../theme';
import { FormData, FormErrors } from '../MultiStepServiceForm';

interface PricingStepProps {
  formData: FormData;
  errors: FormErrors;
  onUpdateFormData: (field: keyof FormData, value: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export const PricingStep: React.FC<PricingStepProps> = ({
  formData,
  errors,
  onUpdateFormData,
  onNext,
  onPrevious,
  isFirstStep,
}) => {
  const renderPriceInput = (
    label: string,
    field: keyof FormData,
    placeholder: string,
    prefix: string = '$'
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <View style={[styles.priceInputContainer, errors[field] && styles.inputError]}>
        <Text style={styles.pricePrefix}>{prefix}</Text>
        <TextInput
          style={styles.priceInput}
          value={formData[field] as string}
          onChangeText={(value) => {
            // Only allow numbers and decimal point
            const numericValue = value.replace(/[^0-9.]/g, '');
            // Prevent multiple decimal points
            const parts = numericValue.split('.');
            if (parts.length > 2) {
              return;
            }
            onUpdateFormData(field, numericValue);
          }}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          keyboardType="decimal-pad"
        />
      </View>
      {errors[field] && (
        <Text style={styles.errorText}>{errors[field]}</Text>
      )}
    </View>
  );

  const renderPriceTypeSelector = () => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>Pricing Type *</Text>
      <View style={styles.priceTypeContainer}>
        <TouchableOpacity
          style={[
            styles.priceTypeOption,
            formData.price_type === 'fixed' && styles.priceTypeOptionSelected,
          ]}
          onPress={() => onUpdateFormData('price_type', 'fixed')}
        >
          <Icon 
            name="attach-money" 
            size={24} 
            color={formData.price_type === 'fixed' ? colors.white : colors.primary} 
          />
          <Text style={[
            styles.priceTypeText,
            formData.price_type === 'fixed' && styles.priceTypeTextSelected,
          ]}>
            Fixed Price
          </Text>
          <Text style={[
            styles.priceTypeDescription,
            formData.price_type === 'fixed' && styles.priceTypeDescriptionSelected,
          ]}>
            One set price for the service
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.priceTypeOption,
            formData.price_type === 'range' && styles.priceTypeOptionSelected,
          ]}
          onPress={() => onUpdateFormData('price_type', 'range')}
        >
          <Icon 
            name="trending-up" 
            size={24} 
            color={formData.price_type === 'range' ? colors.white : colors.primary} 
          />
          <Text style={[
            styles.priceTypeText,
            formData.price_type === 'range' && styles.priceTypeTextSelected,
          ]}>
            Price Range
          </Text>
          <Text style={[
            styles.priceTypeDescription,
            formData.price_type === 'range' && styles.priceTypeDescriptionSelected,
          ]}>
            Minimum to maximum price
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Pricing</Text>
          <Text style={styles.subtitle}>
            Set your service pricing to attract customers
          </Text>
        </View>

        <View style={styles.form}>
          {renderPriceTypeSelector()}

          {renderPriceInput(
            formData.price_type === 'range' ? 'Minimum Price *' : 'Service Price *',
            'base_price',
            '0.00'
          )}

          {formData.price_type === 'range' && renderPriceInput(
            'Maximum Price *',
            'max_price',
            '0.00'
          )}
        </View>

        <View style={styles.helpSection}>
          <View style={styles.helpItem}>
            <Icon name="info-outline" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              <Text style={styles.helpTextBold}>Fixed Price:</Text> Best for standardized services with consistent scope
            </Text>
          </View>
          
          <View style={styles.helpItem}>
            <Icon name="info-outline" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              <Text style={styles.helpTextBold}>Price Range:</Text> Ideal when service complexity can vary
            </Text>
          </View>

          <View style={styles.helpItem}>
            <Icon name="lightbulb-outline" size={20} color={colors.warning} />
            <Text style={styles.helpText}>
              Research competitor pricing to stay competitive while ensuring profitability
            </Text>
          </View>
        </View>

        {formData.base_price && (
          <View style={styles.previewSection}>
            <Text style={styles.previewTitle}>Price Preview</Text>
            <View style={styles.pricePreview}>
              <Text style={styles.pricePreviewText}>
                {formData.price_type === 'fixed' 
                  ? `$${formData.base_price}`
                  : `$${formData.base_price}${formData.max_price ? ` - $${formData.max_price}` : '+'}`
                }
              </Text>
              <Text style={styles.pricePreviewLabel}>
                {formData.price_type === 'fixed' ? 'Fixed Price' : 'Price Range'}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.buttonSecondary]}
          onPress={onPrevious}
          disabled={isFirstStep}
        >
          <Icon name="arrow-back" size={20} color={colors.primary} />
          <Text style={[styles.buttonText, styles.buttonTextSecondary]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.buttonPrimary]}
          onPress={onNext}
        >
          <Text style={[styles.buttonText, styles.buttonTextPrimary]}>
            Next
          </Text>
          <Icon name="arrow-forward" size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: spacing.lg,
  },
  title: {
    ...typography.h2,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  form: {
    paddingBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  pricePrefix: {
    ...typography.body,
    color: colors.textSecondary,
    paddingLeft: spacing.md,
    fontWeight: '600',
  },
  priceInput: {
    flex: 1,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...typography.caption,
    color: colors.error,
    marginTop: spacing.xs,
  },
  priceTypeContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  priceTypeOption: {
    flex: 1,
    borderWidth: 2,
    borderColor: colors.border,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  priceTypeOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
  },
  priceTypeText: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  priceTypeTextSelected: {
    color: colors.white,
  },
  priceTypeDescription: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  priceTypeDescriptionSelected: {
    color: colors.white,
    opacity: 0.9,
  },
  helpSection: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  helpText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 18,
  },
  helpTextBold: {
    fontWeight: '600',
    color: colors.textPrimary,
  },
  previewSection: {
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  previewTitle: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  pricePreview: {
    alignItems: 'center',
  },
  pricePreviewText: {
    ...typography.h2,
    color: colors.primary,
    fontWeight: '700',
  },
  pricePreviewLabel: {
    ...typography.caption,
    color: colors.primary,
    marginTop: spacing.xs,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    minWidth: 120,
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
  },
  buttonText: {
    ...typography.body,
    fontWeight: '600',
    marginHorizontal: spacing.xs,
  },
  buttonTextPrimary: {
    color: colors.white,
  },
  buttonTextSecondary: {
    color: colors.primary,
  },
});
