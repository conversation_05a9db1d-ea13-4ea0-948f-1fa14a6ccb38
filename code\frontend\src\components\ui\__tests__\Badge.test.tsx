/**
 * Badge Component Tests
 * Tests for the enhanced shadcn/ui Badge component
 */

import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { Badge, StatusBadge, PriorityBadge, CountBadge } from '../Badge';

describe('Badge Component', () => {
  it('renders correctly with default props', () => {
    render(<Badge>Test Badge</Badge>);
    
    const badge = screen.getByText('Test Badge');
    expect(badge).toBeTruthy();
  });

  it('renders different variants correctly', () => {
    const { rerender } = render(<Badge variant="default">Default</Badge>);
    expect(screen.getByText('Default')).toBeTruthy();

    rerender(<Badge variant="secondary">Secondary</Badge>);
    expect(screen.getByText('Secondary')).toBeTruthy();

    rerender(<Badge variant="destructive">Destructive</Badge>);
    expect(screen.getByText('Destructive')).toBeTruthy();

    rerender(<Badge variant="success">Success</Badge>);
    expect(screen.getByText('Success')).toBeTruthy();

    rerender(<Badge variant="warning">Warning</Badge>);
    expect(screen.getByText('Warning')).toBeTruthy();

    rerender(<Badge variant="info">Info</Badge>);
    expect(screen.getByText('Info')).toBeTruthy();

    rerender(<Badge variant="outline">Outline</Badge>);
    expect(screen.getByText('Outline')).toBeTruthy();
  });

  it('renders different sizes correctly', () => {
    const { rerender } = render(<Badge size="sm">Small</Badge>);
    expect(screen.getByText('Small')).toBeTruthy();

    rerender(<Badge size="md">Medium</Badge>);
    expect(screen.getByText('Medium')).toBeTruthy();

    rerender(<Badge size="lg">Large</Badge>);
    expect(screen.getByText('Large')).toBeTruthy();
  });

  it('applies custom styles correctly', () => {
    const customStyle = { backgroundColor: 'red' };
    const customTextStyle = { color: 'blue' };
    
    render(
      <Badge style={customStyle} textStyle={customTextStyle}>
        Custom Styled
      </Badge>
    );
    
    expect(screen.getByText('Custom Styled')).toBeTruthy();
  });

  it('has correct testID', () => {
    render(<Badge testID="custom-badge">Test</Badge>);
    
    const badge = screen.getByTestId('custom-badge');
    expect(badge).toBeTruthy();
  });

  it('renders with default testID when not provided', () => {
    render(<Badge>Default TestID</Badge>);
    
    const badge = screen.getByTestId('badge');
    expect(badge).toBeTruthy();
  });

  it('handles non-string children correctly', () => {
    render(
      <Badge>
        <React.Fragment>
          Complex Children
        </React.Fragment>
      </Badge>
    );
    
    expect(screen.getByText('Complex Children')).toBeTruthy();
  });
});

describe('StatusBadge Component', () => {
  it('renders active status correctly', () => {
    render(<StatusBadge status="active">Active</StatusBadge>);
    expect(screen.getByText('Active')).toBeTruthy();
  });

  it('renders inactive status correctly', () => {
    render(<StatusBadge status="inactive">Inactive</StatusBadge>);
    expect(screen.getByText('Inactive')).toBeTruthy();
  });

  it('renders pending status correctly', () => {
    render(<StatusBadge status="pending">Pending</StatusBadge>);
    expect(screen.getByText('Pending')).toBeTruthy();
  });

  it('renders error status correctly', () => {
    render(<StatusBadge status="error">Error</StatusBadge>);
    expect(screen.getByText('Error')).toBeTruthy();
  });
});

describe('PriorityBadge Component', () => {
  it('renders low priority correctly', () => {
    render(<PriorityBadge priority="low">Low</PriorityBadge>);
    expect(screen.getByText('Low')).toBeTruthy();
  });

  it('renders medium priority correctly', () => {
    render(<PriorityBadge priority="medium">Medium</PriorityBadge>);
    expect(screen.getByText('Medium')).toBeTruthy();
  });

  it('renders high priority correctly', () => {
    render(<PriorityBadge priority="high">High</PriorityBadge>);
    expect(screen.getByText('High')).toBeTruthy();
  });

  it('renders urgent priority correctly', () => {
    render(<PriorityBadge priority="urgent">Urgent</PriorityBadge>);
    expect(screen.getByText('Urgent')).toBeTruthy();
  });
});

describe('CountBadge Component', () => {
  it('renders count correctly', () => {
    render(<CountBadge count={5} />);
    expect(screen.getByText('5')).toBeTruthy();
  });

  it('renders count with max limit', () => {
    render(<CountBadge count={150} maxCount={99} />);
    expect(screen.getByText('99+')).toBeTruthy();
  });

  it('renders count within max limit', () => {
    render(<CountBadge count={50} maxCount={99} />);
    expect(screen.getByText('50')).toBeTruthy();
  });

  it('uses default max count of 99', () => {
    render(<CountBadge count={150} />);
    expect(screen.getByText('99+')).toBeTruthy();
  });
});
