"""
Authentication serializers for Vierla Beauty Services Marketplace
Enhanced Django REST Framework serializers with mobile-first design
"""
from rest_framework import serializers
from rest_framework.validators import UniqueValidator
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone as django_timezone
from django.conf import settings
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample, extend_schema_field
import uuid
from datetime import timedelta

from .models import User, UserProfile, EmailVerificationToken, PasswordResetToken


class UserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for UserProfile model
    Handles extended user information and preferences
    """

    full_address = serializers.ReadOnlyField()
    has_location = serializers.ReadOnlyField()

    class Meta:
        model = UserProfile
        fields = [
            'address', 'city', 'state', 'zip_code', 'country',
            'latitude', 'longitude', 'full_address', 'has_location',
            'business_name', 'business_description', 'years_of_experience',
            'website', 'instagram', 'facebook',
            'search_radius', 'auto_accept_bookings',
            'show_phone_publicly', 'show_email_publicly', 'allow_reviews',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at',
                            'full_address', 'has_location']

    def validate_latitude(self, value):
        """Validate latitude is within valid range"""
        if value is not None and not (-90 <= value <= 90):
            raise serializers.ValidationError(
                _("Latitude must be between -90 and 90 degrees."))
        return value

    def validate_longitude(self, value):
        """Validate longitude is within valid range"""
        if value is not None and not (-180 <= value <= 180):
            raise serializers.ValidationError(
                _("Longitude must be between -180 and 180 degrees."))
        return value

    def validate_years_of_experience(self, value):
        """Validate years of experience is reasonable"""
        if value is not None and (value < 0 or value > 50):
            raise serializers.ValidationError(
                _("Years of experience must be between 0 and 50."))
        return value


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model - Simplified for schema generation
    """

    full_name = serializers.CharField(read_only=True)
    is_customer = serializers.BooleanField(read_only=True)
    is_service_provider = serializers.BooleanField(read_only=True)
    is_account_locked = serializers.BooleanField(read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'phone', 'role', 'is_customer', 'is_service_provider',
            'is_account_locked', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'username', 'account_status', 'is_verified',
            'email_verified_at', 'phone_verified_at', 'full_name',
            'is_customer', 'is_service_provider', 'is_account_locked',
            'created_at', 'updated_at', 'last_activity'
        ]
        extra_kwargs = {
            'email': {'validators': [UniqueValidator(queryset=User.objects.all())]},
            'phone': {'required': False},
            'avatar': {'required': False},
            'date_of_birth': {'required': False},
            'bio': {'required': False},
        }

    def validate_email(self, value):
        """Validate email format and uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                _("A user with this email already exists."))
        return value.lower()

    def validate_phone(self, value):
        """Validate phone number format"""
        if value and User.objects.filter(phone=value).exclude(pk=self.instance.pk if self.instance else None).exists():
            raise serializers.ValidationError(
                _("A user with this phone number already exists."))
        return value

    def validate_role(self, value):
        """Validate user role"""
        if self.instance and self.instance.role != value:
            # Only allow role changes in specific circumstances
            if not self.context.get('allow_role_change', False):
                raise serializers.ValidationError(
                    _("Role cannot be changed after account creation."))
        return value


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration
    Handles new user creation with validation
    """

    password = serializers.CharField(
        write_only=True,
        min_length=8,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        style={'input_type': 'password'}
    )
    terms_accepted = serializers.BooleanField(write_only=True)

    class Meta:
        model = User
        fields = [
            'email', 'password', 'password_confirm', 'first_name', 'last_name',
            'phone', 'role', 'preferred_language', 'timezone',
            'email_notifications', 'sms_notifications', 'push_notifications',
            'terms_accepted'
        ]
        extra_kwargs = {
            'email': {'validators': [UniqueValidator(queryset=User.objects.all())]},
            'first_name': {'required': True},
            'last_name': {'required': True},
            'phone': {'required': False},
        }

    def validate_email(self, value):
        """Validate email format and uniqueness"""
        return value.lower()

    def validate_terms_accepted(self, value):
        """Validate that terms and conditions are accepted"""
        if not value:
            raise serializers.ValidationError(
                _("You must accept the terms and conditions to register."))
        return value

    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({
                'password_confirm': _("Password confirmation does not match.")
            })

        # Remove password_confirm and terms_accepted from validated data
        attrs.pop('password_confirm')
        attrs.pop('terms_accepted')

        return attrs

    def create(self, validated_data):
        """Create new user with email verification"""
        password = validated_data.pop('password')

        # Create user (UserProfile will be created automatically by signal)
        user = User.objects.create_user(
            password=password,
            **validated_data
        )

        # Create email verification token
        self._create_email_verification_token(user)

        return user

    def _create_email_verification_token(self, user):
        """Create email verification token for new user"""
        token = str(uuid.uuid4())
        expires_at = django_timezone.now() + timedelta(hours=24)

        EmailVerificationToken.objects.create(
            user=user,
            token=token,
            expires_at=expires_at
        )

        # TODO: Send verification email
        # This would be implemented with Celery task
        return token


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login
    Handles authentication with email and password
    """

    email = serializers.EmailField()
    password = serializers.CharField(
        style={'input_type': 'password'},
        trim_whitespace=False
    )
    device_token = serializers.CharField(required=False, allow_blank=True)
    remember_me = serializers.BooleanField(default=False)

    def validate_email(self, value):
        """Normalize email to lowercase"""
        return value.lower()

    def validate(self, attrs):
        """Authenticate user and validate account status"""
        email = attrs.get('email')
        password = attrs.get('password')
        device_token = attrs.get('device_token')

        if email and password:
            # Check if user exists
            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                raise serializers.ValidationError({
                    'non_field_errors': [_('Invalid email or password.')]
                })

            # Check if account is locked
            if user.is_account_locked:
                raise serializers.ValidationError({
                    'non_field_errors': [_('Account is temporarily locked. Please try again later.')]
                })

            # Check if account is active
            if not user.is_active:
                raise serializers.ValidationError({
                    'non_field_errors': [_('Account is deactivated. Please contact support.')]
                })

            # Authenticate user
            user = authenticate(
                request=self.context.get('request'),
                username=email,
                password=password
            )

            if not user:
                # Increment failed login attempts
                try:
                    failed_user = User.objects.get(email=email)
                    failed_user.increment_failed_login()
                except User.DoesNotExist:
                    pass

                raise serializers.ValidationError({
                    'non_field_errors': [_('Invalid email or password.')]
                })

            # Reset failed login attempts on successful authentication
            user.reset_failed_login()

            # Update device token if provided
            if device_token:
                user.device_token = device_token
                user.save(update_fields=['device_token'])

            # Update last activity
            user.update_last_activity()

            attrs['user'] = user
            return attrs
        else:
            raise serializers.ValidationError({
                'non_field_errors': [_('Must include email and password.')]
            })


class PasswordChangeSerializer(serializers.Serializer):
    """
    Serializer for password change
    Requires current password for security
    """

    old_password = serializers.CharField(
        style={'input_type': 'password'},
        trim_whitespace=False
    )
    new_password = serializers.CharField(
        min_length=8,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(
        style={'input_type': 'password'}
    )

    def validate_old_password(self, value):
        """Validate current password"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError(
                _('Current password is incorrect.'))
        return value

    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({
                'new_password_confirm': _("Password confirmation does not match.")
            })
        return attrs

    def save(self):
        """Update user password"""
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.last_password_change = django_timezone.now()
        user.save(update_fields=['password', 'last_password_change'])
        return user


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    Handles forgot password functionality
    """

    email = serializers.EmailField()

    def validate_email(self, value):
        """Validate email exists"""
        email = value.lower()
        try:
            user = User.objects.get(email=email, is_active=True)
        except User.DoesNotExist:
            # Don't reveal if email exists for security
            pass
        return email

    def save(self):
        """Create password reset token and send email"""
        email = self.validated_data['email']

        try:
            user = User.objects.get(email=email, is_active=True)

            # Invalidate existing tokens
            PasswordResetToken.objects.filter(
                user=user, is_used=False).update(is_used=True)

            # Create new token
            token = str(uuid.uuid4())
            expires_at = django_timezone.now() + timedelta(hours=1)  # 1 hour expiry

            PasswordResetToken.objects.create(
                user=user,
                token=token,
                expires_at=expires_at
            )

            # TODO: Send password reset email
            # This would be implemented with Celery task

        except User.DoesNotExist:
            # Don't reveal if email exists for security
            pass


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for password reset confirmation
    Handles password reset with token
    """

    token = serializers.CharField()
    new_password = serializers.CharField(
        min_length=8,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(
        style={'input_type': 'password'}
    )

    def validate_token(self, value):
        """Validate reset token"""
        try:
            reset_token = PasswordResetToken.objects.get(
                token=value,
                is_used=False
            )
            if reset_token.is_expired:
                raise serializers.ValidationError(
                    _('Password reset token has expired.'))

            self.reset_token = reset_token
            return value
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError(
                _('Invalid password reset token.'))

    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({
                'new_password_confirm': _("Password confirmation does not match.")
            })
        return attrs

    def save(self):
        """Reset user password"""
        user = self.reset_token.user
        user.set_password(self.validated_data['new_password'])
        user.last_password_change = django_timezone.now()
        user.save(update_fields=['password', 'last_password_change'])

        # Mark token as used
        self.reset_token.is_used = True
        self.reset_token.save(update_fields=['is_used'])

        return user


class EmailVerificationSerializer(serializers.Serializer):
    """
    Serializer for email verification
    Handles email verification with token
    """

    token = serializers.CharField()

    def validate_token(self, value):
        """Validate verification token"""
        try:
            verification_token = EmailVerificationToken.objects.get(
                token=value,
                is_used=False
            )
            if verification_token.is_expired:
                raise serializers.ValidationError(
                    _('Email verification token has expired.'))

            self.verification_token = verification_token
            return value
        except EmailVerificationToken.DoesNotExist:
            raise serializers.ValidationError(
                _('Invalid email verification token.'))

    def save(self):
        """Verify user email"""
        user = self.verification_token.user
        user.verify_email()

        # Mark token as used
        self.verification_token.is_used = True
        self.verification_token.save(update_fields=['is_used'])

        return user


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user profile
    Handles profile information updates
    """

    class Meta:
        model = UserProfile
        fields = [
            'address', 'city', 'state', 'zip_code', 'country',
            'latitude', 'longitude',
            'business_name', 'business_description', 'years_of_experience',
            'website', 'instagram', 'facebook',
            'search_radius', 'auto_accept_bookings',
            'show_phone_publicly', 'show_email_publicly', 'allow_reviews'
        ]

    def validate_latitude(self, value):
        """Validate latitude is within valid range"""
        if value is not None and not (-90 <= value <= 90):
            raise serializers.ValidationError(
                _("Latitude must be between -90 and 90 degrees."))
        return value

    def validate_longitude(self, value):
        """Validate longitude is within valid range"""
        if value is not None and not (-180 <= value <= 180):
            raise serializers.ValidationError(
                _("Longitude must be between -180 and 180 degrees."))
        return value
