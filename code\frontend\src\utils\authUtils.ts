/**
 * Authentication Utilities
 * Helper functions for authentication-related operations
 * Part of EPIC-05-CRITICAL: Authentication & UI Enhancement
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../services/api/auth';
import { TEST_ACCOUNTS, isTestAccount } from '../config/testAccounts';

/**
 * Check if user is authenticated
 */
export const checkAuthStatus = async (): Promise<{
  isAuthenticated: boolean;
  user: any | null;
  needsRefresh: boolean;
}> => {
  try {
    const [accessToken, userData] = await Promise.all([
      AsyncStorage.getItem('access_token'),
      AsyncStorage.getItem('user'),
    ]);

    if (!accessToken || !userData) {
      return { isAuthenticated: false, user: null, needsRefresh: false };
    }

    const user = JSON.parse(userData);

    // Check if token is expired
    if (isTokenExpired(accessToken)) {
      return { isAuthenticated: false, user, needsRefresh: true };
    }

    return { isAuthenticated: true, user, needsRefresh: false };
  } catch (error) {
    console.error('Auth status check error:', error);
    return { isAuthenticated: false, user: null, needsRefresh: false };
  }
};

/**
 * Check if token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return true;

    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    // Add 5 minute buffer to prevent edge cases
    return payload.exp < (currentTime + 300);
  } catch {
    return true;
  }
};

/**
 * Clear all authentication data
 */
export const clearAuthData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

/**
 * Store authentication data
 */
export const storeAuthData = async (authResponse: {
  access: string;
  refresh: string;
  user: any;
}): Promise<void> => {
  try {
    await AsyncStorage.multiSet([
      ['access_token', authResponse.access],
      ['refresh_token', authResponse.refresh],
      ['user', JSON.stringify(authResponse.user)],
    ]);
  } catch (error) {
    console.error('Error storing auth data:', error);
    throw error;
  }
};

/**
 * Get stored user data
 */
export const getStoredUser = async (): Promise<any | null> => {
  try {
    const userData = await AsyncStorage.getItem('user');
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting stored user:', error);
    return null;
  }
};

/**
 * Get stored access token
 */
export const getStoredToken = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem('access_token');
  } catch (error) {
    console.error('Error getting stored token:', error);
    return null;
  }
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }

  // For test accounts, allow simple passwords
  if (isTestAccount(password)) {
    return { isValid: true, errors: [] };
  }

  return { isValid: errors.length === 0, errors };
};

/**
 * Get user role display name
 */
export const getUserRoleDisplayName = (role: string): string => {
  switch (role) {
    case 'customer':
      return 'Customer';
    case 'service_provider':
      return 'Service Provider';
    case 'admin':
      return 'Administrator';
    default:
      return 'User';
  }
};

/**
 * Check if user has permission for action
 */
export const hasPermission = (userRole: string, requiredRole: string): boolean => {
  const roleHierarchy = {
    customer: 1,
    service_provider: 2,
    admin: 3,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
};

/**
 * Format authentication error message
 */
export const formatAuthError = (error: any): string => {
  if (error?.response?.status === 400) {
    return 'Invalid email or password. Please check your credentials.';
  }

  if (error?.response?.status === 401) {
    return 'Authentication failed. Please try again.';
  }

  if (error?.response?.status === 423) {
    return 'Your account has been temporarily locked. Please try again later.';
  }

  if (error?.response?.status === 429) {
    return 'Too many login attempts. Please wait before trying again.';
  }

  if (error?.response?.status >= 500) {
    return 'Server error. Please try again in a few minutes.';
  }

  if (error?.code === 'NETWORK_ERROR' || !error?.response) {
    return 'Network error. Please check your internet connection.';
  }

  return 'Login failed. Please try again.';
};

/**
 * Get test account by email (for development)
 */
export const getTestAccountByEmail = (email: string) => {
  if (!__DEV__) return null;
  
  return Object.values(TEST_ACCOUNTS).find(account => account.email === email);
};

/**
 * Log authentication event (for debugging)
 */
export const logAuthEvent = (event: string, data?: any): void => {
  if (__DEV__) {
    console.log(`[AUTH] ${event}`, data || '');
  }
};

/**
 * Create authentication headers
 */
export const createAuthHeaders = async (): Promise<Record<string, string>> => {
  const token = await getStoredToken();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return headers;
};

/**
 * Refresh authentication token
 */
export const refreshAuthToken = async (): Promise<{
  success: boolean;
  error?: string;
}> => {
  try {
    const refreshToken = await AsyncStorage.getItem('refresh_token');
    
    if (!refreshToken) {
      return { success: false, error: 'No refresh token available' };
    }

    // This would typically call the refresh endpoint
    // For now, we'll simulate the refresh logic
    const response = await fetch(`${process.env.API_BASE_URL || 'http://127.0.0.1:8000/api'}/auth/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshToken }),
    });

    if (response.ok) {
      const data = await response.json();
      
      await AsyncStorage.multiSet([
        ['access_token', data.access],
        ['refresh_token', data.refresh || refreshToken],
      ]);

      return { success: true };
    } else {
      return { success: false, error: 'Token refresh failed' };
    }
  } catch (error) {
    return { success: false, error: 'Network error during token refresh' };
  }
};

/**
 * Logout user and clear all data
 */
export const logoutUser = async (): Promise<void> => {
  try {
    const refreshToken = await AsyncStorage.getItem('refresh_token');
    
    if (refreshToken) {
      try {
        await authAPI.logout(refreshToken);
      } catch (error) {
        // Ignore logout API errors, still clear local data
        console.warn('Logout API error:', error);
      }
    }

    await clearAuthData();
    logAuthEvent('User logged out');
  } catch (error) {
    console.error('Logout error:', error);
    // Still clear local data even if there's an error
    await clearAuthData();
  }
};

export default {
  checkAuthStatus,
  isTokenExpired,
  clearAuthData,
  storeAuthData,
  getStoredUser,
  getStoredToken,
  isValidEmail,
  validatePassword,
  getUserRoleDisplayName,
  hasPermission,
  formatAuthError,
  getTestAccountByEmail,
  logAuthEvent,
  createAuthHeaders,
  refreshAuthToken,
  logoutUser,
};
