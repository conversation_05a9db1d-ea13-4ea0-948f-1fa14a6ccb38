"""
Django admin configuration for authentication models
"""
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User, UserProfile, EmailVerificationToken, PasswordResetToken


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Enhanced admin interface for User model"""

    list_display = (
        'email', 'first_name', 'last_name', 'role', 'account_status',
        'is_verified', 'is_active', 'created_at'
    )
    list_filter = (
        'role', 'account_status', 'is_verified', 'is_active', 'is_staff',
        'two_factor_enabled', 'created_at'
    )
    search_fields = ('email', 'first_name', 'last_name', 'phone')
    ordering = ('-created_at',)
    readonly_fields = (
        'created_at', 'updated_at', 'last_activity', 'email_verified_at',
        'phone_verified_at', 'last_password_change', 'failed_login_attempts'
    )

    fieldsets = (
        (None, {
            'fields': ('email', 'password')
        }),
        (_('Personal info'), {
            'fields': (
                'first_name', 'last_name', 'phone', 'date_of_birth',
                'avatar', 'bio'
            )
        }),
        (_('Account'), {
            'fields': (
                'role', 'account_status', 'is_verified', 'email_verified_at',
                'phone_verified_at'
            )
        }),
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser', 'groups',
                'user_permissions'
            ),
            'classes': ('collapse',)
        }),
        (_('Mobile & Notifications'), {
            'fields': (
                'device_token', 'preferred_language', 'timezone',
                'email_notifications', 'sms_notifications', 'push_notifications'
            ),
            'classes': ('collapse',)
        }),
        (_('Security'), {
            'fields': (
                'two_factor_enabled', 'last_password_change',
                'failed_login_attempts', 'account_locked_until'
            ),
            'classes': ('collapse',)
        }),
        (_('Development'), {
            'fields': ('is_test_account',),
            'classes': ('collapse',)
        }),
        (_('Important dates'), {
            'fields': (
                'last_login', 'created_at', 'updated_at', 'last_activity'
            ),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'email', 'first_name', 'last_name', 'role',
                'password1', 'password2'
            ),
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset for admin list view"""
        return super().get_queryset(request).select_related('profile')


class UserProfileInline(admin.StackedInline):
    """Inline admin for UserProfile"""
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile Information'
    fields = (
        ('address', 'city'),
        ('state', 'zip_code', 'country'),
        ('latitude', 'longitude'),
        ('business_name', 'years_of_experience'),
        'business_description',
        ('website', 'instagram', 'facebook'),
        ('search_radius', 'auto_accept_bookings'),
        ('show_phone_publicly', 'show_email_publicly', 'allow_reviews'),
    )


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin interface for UserProfile model"""

    list_display = (
        'user', 'city', 'state', 'country', 'business_name',
        'has_location', 'created_at'
    )
    list_filter = ('country', 'state', 'auto_accept_bookings', 'allow_reviews')
    search_fields = (
        'user__email', 'user__first_name', 'user__last_name',
        'business_name', 'city', 'state'
    )
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('User'), {
            'fields': ('user',)
        }),
        (_('Location'), {
            'fields': (
                ('address', 'city'),
                ('state', 'zip_code', 'country'),
                ('latitude', 'longitude')
            )
        }),
        (_('Business Information'), {
            'fields': (
                'business_name', 'business_description',
                'years_of_experience'
            )
        }),
        (_('Social Media'), {
            'fields': ('website', 'instagram', 'facebook'),
            'classes': ('collapse',)
        }),
        (_('Preferences'), {
            'fields': (
                'search_radius', 'auto_accept_bookings'
            )
        }),
        (_('Privacy'), {
            'fields': (
                'show_phone_publicly', 'show_email_publicly',
                'allow_reviews'
            )
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def has_location(self, obj):
        """Display if user has set location"""
        return obj.has_location
    has_location.boolean = True
    has_location.short_description = 'Has Location'


@admin.register(EmailVerificationToken)
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    """Admin interface for EmailVerificationToken model"""

    list_display = (
        'user', 'token', 'created_at', 'expires_at',
        'is_used', 'is_expired'
    )
    list_filter = ('is_used', 'created_at', 'expires_at')
    search_fields = ('user__email', 'token')
    readonly_fields = ('created_at', 'is_expired', 'is_valid')

    def is_expired(self, obj):
        """Display if token is expired"""
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = 'Expired'


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """Admin interface for PasswordResetToken model"""

    list_display = (
        'user', 'token', 'created_at', 'expires_at',
        'is_used', 'is_expired'
    )
    list_filter = ('is_used', 'created_at', 'expires_at')
    search_fields = ('user__email', 'token')
    readonly_fields = ('created_at', 'is_expired', 'is_valid')

    def is_expired(self, obj):
        """Display if token is expired"""
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = 'Expired'


# Add UserProfile inline to UserAdmin
UserAdmin.inlines = [UserProfileInline]
