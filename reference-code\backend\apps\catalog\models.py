"""
Service Catalog models for Vierla Beauty Services Marketplace
Enhanced Django 4.2.16 implementation with mobile-first design
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator, RegexValidator
from django.utils import timezone as django_timezone
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
import uuid

User = get_user_model()


class ServiceCategory(models.Model):
    """
    Service categories for organizing beauty services
    Enhanced with mobile-first design and hierarchical support
    """

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Basic Information
    name = models.CharField(
        _('category name'),
        max_length=100,
        unique=True,
        help_text=_('Name of the service category')
    )
    slug = models.SlugField(
        _('slug'),
        max_length=120,
        unique=True,
        help_text=_('URL-friendly version of the name')
    )
    description = models.TextField(
        _('description'),
        help_text=_('Detailed description of the category')
    )

    # Visual Elements
    icon = models.CharField(
        _('icon'),
        max_length=50,
        help_text=_('Icon identifier (emoji or icon name)')
    )
    color = models.CharField(
        _('color'),
        max_length=7,
        default='#8FBC8F',  # Sage green default
        validators=[RegexValidator(
            regex=r'^#[0-9A-Fa-f]{6}$',
            message=_('Color must be a valid hex color code')
        )],
        help_text=_('Hex color code for the category')
    )
    image = models.ImageField(
        _('category image'),
        upload_to='categories/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Category banner image')
    )

    # Hierarchical Support
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='subcategories',
        help_text=_('Parent category for hierarchical organization')
    )

    # Status and Ordering
    is_popular = models.BooleanField(
        _('is popular'),
        default=False,
        help_text=_('Mark as popular category for featured display')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether this category is active and visible')
    )
    sort_order = models.PositiveIntegerField(
        _('sort order'),
        default=0,
        help_text=_('Order for displaying categories')
    )

    # Mobile Optimization
    mobile_icon = models.CharField(
        _('mobile icon'),
        max_length=50,
        blank=True,
        help_text=_('Optimized icon for mobile display')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_service_categories'
        verbose_name = _('Service Category')
        verbose_name_plural = _('Service Categories')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['slug']),
            models.Index(fields=['is_active', 'is_popular']),
            models.Index(fields=['sort_order']),
            models.Index(fields=['parent']),
        ]

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} > {self.name}"
        return self.name

    @property
    def full_name(self):
        """Return full hierarchical name"""
        if self.parent:
            return f"{self.parent.name} > {self.name}"
        return self.name

    @property
    def level(self):
        """Return the hierarchical level (0 for root categories)"""
        level = 0
        parent = self.parent
        while parent:
            level += 1
            parent = parent.parent
        return level

    def get_descendants(self):
        """Get all descendant categories"""
        descendants = []
        for child in self.subcategories.all():
            descendants.append(child)
            descendants.extend(child.get_descendants())
        return descendants

    def get_ancestors(self):
        """Get all ancestor categories"""
        ancestors = []
        parent = self.parent
        while parent:
            ancestors.append(parent)
            parent = parent.parent
        return ancestors


class ServiceProvider(models.Model):
    """
    Service provider business profiles
    Enhanced with mobile-first design and comprehensive business information
    """

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # User Relationship
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='provider_profile',
        help_text=_('Associated user account')
    )

    # Business Information
    business_name = models.CharField(
        _('business name'),
        max_length=255,
        help_text=_('Official business name')
    )
    business_description = models.TextField(
        _('business description'),
        help_text=_('Detailed description of services and business')
    )
    business_phone = models.CharField(
        _('business phone'),
        max_length=20,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Phone number must be in valid format')
        )],
        help_text=_('Business contact phone number')
    )
    business_email = models.EmailField(
        _('business email'),
        help_text=_('Business contact email address')
    )

    # Location Information
    address = models.TextField(
        _('business address'),
        help_text=_('Full business address')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        help_text=_('Business city')
    )
    state = models.CharField(
        _('state/province'),
        max_length=100,
        help_text=_('State or province')
    )
    zip_code = models.CharField(
        _('zip/postal code'),
        max_length=20,
        help_text=_('Zip or postal code')
    )
    country = models.CharField(
        _('country'),
        max_length=100,
        default='Canada',
        help_text=_('Country')
    )

    # Geolocation for mobile app
    latitude = models.DecimalField(
        _('latitude'),
        max_digits=10,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Latitude coordinate for location-based services')
    )
    longitude = models.DecimalField(
        _('longitude'),
        max_digits=11,
        decimal_places=8,
        blank=True,
        null=True,
        help_text=_('Longitude coordinate for location-based services')
    )

    # Online Presence
    website = models.URLField(
        _('website'),
        blank=True,
        help_text=_('Business website URL')
    )
    instagram_handle = models.CharField(
        _('Instagram handle'),
        max_length=100,
        blank=True,
        help_text=_('Instagram username without @')
    )
    facebook_url = models.URLField(
        _('Facebook page'),
        blank=True,
        help_text=_('Facebook business page URL')
    )

    # Media
    profile_image = models.ImageField(
        _('profile image'),
        upload_to='providers/profiles/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Business profile picture')
    )
    cover_image = models.ImageField(
        _('cover image'),
        upload_to='providers/covers/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Business cover/banner image')
    )

    # Business Status and Verification
    is_verified = models.BooleanField(
        _('is verified'),
        default=False,
        help_text=_('Whether the business is verified by admin')
    )
    is_featured = models.BooleanField(
        _('is featured'),
        default=False,
        help_text=_('Whether to feature this provider prominently')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether the provider is active and accepting bookings')
    )

    # Categories
    categories = models.ManyToManyField(
        ServiceCategory,
        related_name='providers',
        help_text=_('Service categories offered by this provider')
    )

    # Ratings and Reviews
    rating = models.DecimalField(
        _('average rating'),
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[
            MinValueValidator(Decimal('0.00')),
            MaxValueValidator(Decimal('5.00'))
        ],
        help_text=_('Average rating from customer reviews')
    )
    review_count = models.PositiveIntegerField(
        _('review count'),
        default=0,
        help_text=_('Total number of reviews received')
    )

    # Business Metrics
    total_bookings = models.PositiveIntegerField(
        _('total bookings'),
        default=0,
        help_text=_('Total number of completed bookings')
    )
    years_of_experience = models.PositiveIntegerField(
        _('years of experience'),
        blank=True,
        null=True,
        help_text=_('Years of professional experience')
    )

    # Mobile Optimization
    mobile_optimized = models.BooleanField(
        _('mobile optimized'),
        default=True,
        help_text=_('Whether provider profile is optimized for mobile')
    )

    # Store Customization Settings
    store_theme_primary_color = models.CharField(
        _('store theme primary color'),
        max_length=7,
        default='#7C9A85',
        help_text=_('Primary color for store theme (hex format)')
    )
    store_theme_accent_color = models.CharField(
        _('store theme accent color'),
        max_length=7,
        default='#E8F5E8',
        help_text=_('Accent color for store theme (hex format)')
    )
    store_theme_layout = models.CharField(
        _('store theme layout'),
        max_length=10,
        choices=[
            ('grid', _('Grid Layout')),
            ('list', _('List Layout')),
            ('card', _('Card Layout')),
        ],
        default='card',
        help_text=_('Layout style for store display')
    )

    # Display Options
    display_show_prices = models.BooleanField(
        _('display show prices'),
        default=True,
        help_text=_('Whether to show service prices')
    )
    display_show_duration = models.BooleanField(
        _('display show duration'),
        default=True,
        help_text=_('Whether to show service duration')
    )
    display_show_ratings = models.BooleanField(
        _('display show ratings'),
        default=True,
        help_text=_('Whether to show ratings and reviews')
    )
    display_show_availability = models.BooleanField(
        _('display show availability'),
        default=True,
        help_text=_('Whether to show real-time availability')
    )

    # Portfolio Settings
    portfolio_type = models.CharField(
        _('portfolio type'),
        max_length=10,
        choices=[
            ('upload', _('Upload Images')),
            ('instagram', _('Instagram Feed')),
        ],
        default='upload',
        help_text=_('Type of portfolio display')
    )
    portfolio_instagram_max_posts = models.PositiveIntegerField(
        _('instagram max posts'),
        default=9,
        validators=[
            MinValueValidator(3),
            MaxValueValidator(20)
        ],
        help_text=_('Maximum number of Instagram posts to display')
    )
    portfolio_show_instagram_captions = models.BooleanField(
        _('show instagram captions'),
        default=True,
        help_text=_('Whether to show Instagram post captions')
    )
    portfolio_layout = models.CharField(
        _('portfolio layout'),
        max_length=10,
        choices=[
            ('grid', _('Grid Layout')),
            ('masonry', _('Masonry Layout')),
            ('carousel', _('Carousel Layout')),
        ],
        default='grid',
        help_text=_('Layout style for portfolio display')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_service_providers'
        verbose_name = _('Service Provider')
        verbose_name_plural = _('Service Providers')
        ordering = ['-rating', '-review_count', 'business_name']
        indexes = [
            models.Index(fields=['business_name']),
            models.Index(fields=['city', 'state']),
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['rating']),
            models.Index(fields=['is_active', 'is_verified']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.business_name

    @property
    def full_address(self):
        """Return formatted full address"""
        address_parts = [
            self.address,
            self.city,
            self.state,
            self.zip_code,
            self.country
        ]
        return ', '.join([part for part in address_parts if part])

    @property
    def has_location(self):
        """Check if provider has geolocation coordinates"""
        return self.latitude is not None and self.longitude is not None

    @property
    def display_rating(self):
        """Return formatted rating for display"""
        if self.review_count == 0:
            return "No reviews yet"
        return f"{self.rating:.1f} ({self.review_count} reviews)"

    def update_rating(self, new_rating, review_count_change=1):
        """Update average rating with new review"""
        if self.review_count == 0:
            self.rating = new_rating
            self.review_count += review_count_change
        else:
            total_rating = self.rating * self.review_count + new_rating
            self.review_count += review_count_change
            self.rating = total_rating / self.review_count
        self.save(update_fields=['rating', 'review_count'])


class Service(models.Model):
    """
    Individual services offered by providers
    Enhanced with mobile-first design and comprehensive service information
    """

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Provider Relationship
    provider = models.ForeignKey(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='services',
        help_text=_('Service provider offering this service')
    )

    # Category Relationship
    category = models.ForeignKey(
        ServiceCategory,
        on_delete=models.CASCADE,
        related_name='services',
        help_text=_('Category this service belongs to')
    )

    # Service Information
    name = models.CharField(
        _('service name'),
        max_length=200,
        help_text=_('Name of the service')
    )
    description = models.TextField(
        _('service description'),
        help_text=_('Detailed description of the service')
    )
    short_description = models.CharField(
        _('short description'),
        max_length=255,
        blank=True,
        help_text=_('Brief description for mobile display')
    )

    # Pricing Information
    base_price = models.DecimalField(
        _('base price'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Base price for the service')
    )
    price_type = models.CharField(
        _('price type'),
        max_length=20,
        choices=[
            ('fixed', _('Fixed Price')),
            ('hourly', _('Hourly Rate')),
            ('range', _('Price Range')),
            ('consultation', _('Consultation Required')),
        ],
        default='fixed',
        help_text=_('Type of pricing structure')
    )
    max_price = models.DecimalField(
        _('maximum price'),
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Maximum price for range pricing')
    )

    # Duration Information
    duration = models.PositiveIntegerField(
        _('duration (minutes)'),
        help_text=_('Service duration in minutes')
    )
    buffer_time = models.PositiveIntegerField(
        _('buffer time (minutes)'),
        default=15,
        help_text=_('Buffer time between appointments')
    )

    # Service Media
    image = models.ImageField(
        _('service image'),
        upload_to='services/%Y/%m/',
        blank=True,
        null=True,
        help_text=_('Main service image')
    )

    # Service Status
    is_popular = models.BooleanField(
        _('is popular'),
        default=False,
        help_text=_('Mark as popular service for featured display')
    )
    is_available = models.BooleanField(
        _('is available'),
        default=True,
        help_text=_('Whether service is currently available for booking')
    )
    is_active = models.BooleanField(
        _('is active'),
        default=True,
        help_text=_('Whether service is active in the system')
    )

    # Service Requirements and Preparation
    requirements = models.JSONField(
        _('service requirements'),
        default=list,
        blank=True,
        help_text=_('List of requirements or preparations needed')
    )
    preparation_instructions = models.TextField(
        _('preparation instructions'),
        blank=True,
        help_text=_('Instructions for client preparation')
    )

    # Mobile Optimization
    mobile_description = models.CharField(
        _('mobile description'),
        max_length=100,
        blank=True,
        help_text=_('Optimized description for mobile display')
    )

    # Service Metrics
    booking_count = models.PositiveIntegerField(
        _('booking count'),
        default=0,
        help_text=_('Total number of bookings for this service')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_services'
        verbose_name = _('Service')
        verbose_name_plural = _('Services')
        ordering = ['provider', 'category', 'name']
        indexes = [
            models.Index(fields=['provider']),
            models.Index(fields=['category']),
            models.Index(fields=['name']),
            models.Index(fields=['base_price']),
            models.Index(fields=['duration']),
            models.Index(fields=['is_active', 'is_available']),
            models.Index(fields=['is_popular']),
            models.Index(fields=['created_at']),
        ]
        unique_together = [['provider', 'name']]

    def __str__(self):
        return f"{self.provider.business_name} - {self.name}"

    @property
    def display_price(self):
        """Return formatted price for display"""
        if self.price_type == 'fixed':
            return f"${self.base_price:.2f}"
        elif self.price_type == 'hourly':
            return f"${self.base_price:.2f}/hour"
        elif self.price_type == 'range' and self.max_price:
            return f"${self.base_price:.2f} - ${self.max_price:.2f}"
        elif self.price_type == 'consultation':
            return "Consultation Required"
        return f"${self.base_price:.2f}"

    @property
    def display_duration(self):
        """Return formatted duration for display"""
        hours = self.duration // 60
        minutes = self.duration % 60

        if hours > 0 and minutes > 0:
            return f"{hours}h {minutes}m"
        elif hours > 0:
            return f"{hours}h"
        else:
            return f"{minutes}m"

    @property
    def total_duration_with_buffer(self):
        """Return total duration including buffer time"""
        return self.duration + self.buffer_time

    def increment_booking_count(self):
        """Increment booking count when service is booked"""
        self.booking_count += 1
        self.save(update_fields=['booking_count'])


class OperatingHours(models.Model):
    """
    Operating hours for service providers
    Enhanced with mobile-first design and flexible scheduling
    """

    DAYS_OF_WEEK = [
        ('monday', _('Monday')),
        ('tuesday', _('Tuesday')),
        ('wednesday', _('Wednesday')),
        ('thursday', _('Thursday')),
        ('friday', _('Friday')),
        ('saturday', _('Saturday')),
        ('sunday', _('Sunday')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Provider Relationship
    provider = models.ForeignKey(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='operating_hours',
        help_text=_('Service provider these hours belong to')
    )

    # Day and Time Information
    day = models.CharField(
        _('day of week'),
        max_length=10,
        choices=DAYS_OF_WEEK,
        help_text=_('Day of the week')
    )
    is_open = models.BooleanField(
        _('is open'),
        default=True,
        help_text=_('Whether the provider is open on this day')
    )
    open_time = models.TimeField(
        _('opening time'),
        help_text=_('Time when provider opens')
    )
    close_time = models.TimeField(
        _('closing time'),
        help_text=_('Time when provider closes')
    )

    # Break Times
    break_start = models.TimeField(
        _('break start time'),
        blank=True,
        null=True,
        help_text=_('Start time of lunch/break period')
    )
    break_end = models.TimeField(
        _('break end time'),
        blank=True,
        null=True,
        help_text=_('End time of lunch/break period')
    )

    # Special Notes
    notes = models.CharField(
        _('notes'),
        max_length=255,
        blank=True,
        help_text=_('Special notes for this day')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_operating_hours'
        verbose_name = _('Operating Hours')
        verbose_name_plural = _('Operating Hours')
        unique_together = [['provider', 'day']]
        ordering = ['provider', 'day']
        indexes = [
            models.Index(fields=['provider', 'day']),
            models.Index(fields=['is_open']),
        ]

    def __str__(self):
        if self.is_open:
            return f"{self.provider.business_name} - {self.get_day_display()}: {self.open_time} - {self.close_time}"
        return f"{self.provider.business_name} - {self.get_day_display()}: Closed"

    @property
    def is_24_hours(self):
        """Check if open 24 hours"""
        return self.open_time == self.close_time

    @property
    def has_break(self):
        """Check if there's a break period"""
        return self.break_start and self.break_end

    def get_available_slots(self, slot_duration=30):
        """Get available time slots for the day"""
        if not self.is_open:
            return []

        slots = []
        current_time = self.open_time

        while current_time < self.close_time:
            # Check if current time conflicts with break
            if self.has_break:
                if self.break_start <= current_time < self.break_end:
                    current_time = self.break_end
                    continue

            # Add slot if there's enough time before closing
            slot_end = (django_timezone.datetime.combine(django_timezone.date.today(), current_time) +
                        django_timezone.timedelta(minutes=slot_duration)).time()

            if slot_end <= self.close_time:
                slots.append(current_time)

            # Move to next slot
            current_time = (django_timezone.datetime.combine(django_timezone.date.today(), current_time) +
                            django_timezone.timedelta(minutes=slot_duration)).time()

        return slots


class ServiceAvailability(models.Model):
    """
    Service-specific availability and scheduling
    Enhanced for mobile booking optimization
    """

    AVAILABILITY_TYPE = [
        ('always', _('Always Available')),
        ('scheduled', _('Scheduled Hours')),
        ('appointment_only', _('Appointment Only')),
        ('seasonal', _('Seasonal')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Service Relationship
    service = models.OneToOneField(
        Service,
        on_delete=models.CASCADE,
        related_name='availability',
        help_text=_('Service this availability applies to')
    )

    # Availability Configuration
    availability_type = models.CharField(
        _('availability type'),
        max_length=20,
        choices=AVAILABILITY_TYPE,
        default='always',
        help_text=_('Type of availability scheduling')
    )

    # Advance Booking Settings
    min_advance_booking = models.PositiveIntegerField(
        _('minimum advance booking (hours)'),
        default=2,
        help_text=_('Minimum hours in advance for booking')
    )
    max_advance_booking = models.PositiveIntegerField(
        _('maximum advance booking (days)'),
        default=90,
        help_text=_('Maximum days in advance for booking')
    )

    # Booking Limits
    max_bookings_per_day = models.PositiveIntegerField(
        _('max bookings per day'),
        blank=True,
        null=True,
        help_text=_('Maximum bookings allowed per day')
    )
    max_bookings_per_slot = models.PositiveIntegerField(
        _('max bookings per time slot'),
        default=1,
        help_text=_('Maximum bookings for the same time slot')
    )

    # Cancellation Policy
    cancellation_hours = models.PositiveIntegerField(
        _('cancellation notice (hours)'),
        default=24,
        help_text=_('Hours notice required for cancellation')
    )

    # Special Availability Rules
    weekend_available = models.BooleanField(
        _('available on weekends'),
        default=True,
        help_text=_('Whether service is available on weekends')
    )
    holiday_available = models.BooleanField(
        _('available on holidays'),
        default=False,
        help_text=_('Whether service is available on holidays')
    )

    # Mobile Optimization
    instant_booking = models.BooleanField(
        _('instant booking'),
        default=True,
        help_text=_('Allow instant booking without confirmation')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_service_availability'
        verbose_name = _('Service Availability')
        verbose_name_plural = _('Service Availabilities')
        indexes = [
            models.Index(fields=['service']),
            models.Index(fields=['availability_type']),
            models.Index(fields=['instant_booking']),
        ]

    def __str__(self):
        return f"{self.service.name} - {self.get_availability_type_display()}"

    def is_available_on_date(self, date):
        """Check if service is available on a specific date"""
        # Check if it's a weekend and weekend availability
        if date.weekday() >= 5 and not self.weekend_available:
            return False

        # Add holiday checking logic here if needed
        # if is_holiday(date) and not self.holiday_available:
        #     return False

        return True

    def get_earliest_booking_time(self):
        """Get the earliest possible booking time"""
        now = django_timezone.now()
        return now + django_timezone.timedelta(hours=self.min_advance_booking)

    def get_latest_booking_time(self):
        """Get the latest possible booking time"""
        now = django_timezone.now()
        return now + django_timezone.timedelta(days=self.max_advance_booking)


class ServiceGallery(models.Model):
    """
    Gallery images for service providers and services
    Enhanced with mobile-first design and image optimization
    """

    IMAGE_TYPE = [
        ('provider', _('Provider Gallery')),
        ('service', _('Service Image')),
        ('work_sample', _('Work Sample')),
        ('before_after', _('Before/After')),
        ('certificate', _('Certificate')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Relationships
    provider = models.ForeignKey(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='gallery',
        help_text=_('Service provider this image belongs to')
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='gallery',
        help_text=_('Specific service this image showcases (optional)')
    )

    # Image Information
    image = models.ImageField(
        _('image'),
        upload_to='gallery/%Y/%m/',
        help_text=_('Gallery image file')
    )
    image_type = models.CharField(
        _('image type'),
        max_length=20,
        choices=IMAGE_TYPE,
        default='provider',
        help_text=_('Type of image for categorization')
    )
    caption = models.CharField(
        _('caption'),
        max_length=200,
        blank=True,
        help_text=_('Image caption or description')
    )
    alt_text = models.CharField(
        _('alt text'),
        max_length=255,
        blank=True,
        help_text=_('Alternative text for accessibility')
    )

    # Display Settings
    is_featured = models.BooleanField(
        _('is featured'),
        default=False,
        help_text=_('Whether to feature this image prominently')
    )
    is_cover = models.BooleanField(
        _('is cover image'),
        default=False,
        help_text=_('Whether this is a cover/hero image')
    )
    sort_order = models.PositiveIntegerField(
        _('sort order'),
        default=0,
        help_text=_('Order for displaying images')
    )

    # Mobile Optimization
    mobile_optimized = models.BooleanField(
        _('mobile optimized'),
        default=True,
        help_text=_('Whether image is optimized for mobile display')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_service_gallery'
        verbose_name = _('Service Gallery Image')
        verbose_name_plural = _('Service Gallery Images')
        ordering = ['provider', 'sort_order', '-created_at']
        indexes = [
            models.Index(fields=['provider']),
            models.Index(fields=['service']),
            models.Index(fields=['image_type']),
            models.Index(fields=['is_featured']),
            models.Index(fields=['is_cover']),
            models.Index(fields=['sort_order']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        if self.service:
            return f"{self.provider.business_name} - {self.service.name} - {self.get_image_type_display()}"
        return f"{self.provider.business_name} - {self.get_image_type_display()}"

    def save(self, *args, **kwargs):
        """Override save to ensure only one cover image per provider"""
        if self.is_cover:
            # Remove cover status from other images for this provider
            ServiceGallery.objects.filter(
                provider=self.provider,
                is_cover=True
            ).exclude(id=self.id).update(is_cover=False)

        super().save(*args, **kwargs)

    @property
    def display_name(self):
        """Return display name for the image"""
        if self.caption:
            return self.caption
        elif self.service:
            return f"{self.service.name} - {self.get_image_type_display()}"
        else:
            return f"{self.provider.business_name} - {self.get_image_type_display()}"


class ServiceLocation(models.Model):
    """
    Service location information for mobile location-based search
    Enhanced with geospatial capabilities
    """

    LOCATION_TYPE = [
        ('fixed', _('Fixed Location')),
        ('mobile', _('Mobile Service')),
        ('both', _('Both Fixed and Mobile')),
        ('virtual', _('Virtual/Online')),
    ]

    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False
    )

    # Service Relationship
    service = models.OneToOneField(
        Service,
        on_delete=models.CASCADE,
        related_name='location_info',
        help_text=_('Service this location information applies to')
    )

    # Location Type
    location_type = models.CharField(
        _('location type'),
        max_length=20,
        choices=LOCATION_TYPE,
        default='fixed',
        help_text=_('Type of service location')
    )

    # Mobile Service Settings
    travel_radius = models.PositiveIntegerField(
        _('travel radius (km)'),
        blank=True,
        null=True,
        help_text=_('Maximum travel distance for mobile services')
    )
    travel_fee = models.DecimalField(
        _('travel fee'),
        max_digits=8,
        decimal_places=2,
        blank=True,
        null=True,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text=_('Additional fee for mobile services')
    )

    # Service Area
    service_areas = models.JSONField(
        _('service areas'),
        default=list,
        blank=True,
        help_text=_('List of areas/neighborhoods served')
    )

    # Virtual Service Settings
    virtual_platform = models.CharField(
        _('virtual platform'),
        max_length=100,
        blank=True,
        help_text=_('Platform used for virtual services (Zoom, etc.)')
    )

    # Special Location Notes
    location_notes = models.TextField(
        _('location notes'),
        blank=True,
        help_text=_('Special instructions or notes about service location')
    )

    # Timestamps
    created_at = models.DateTimeField(
        _('created at'),
        default=django_timezone.now
    )
    updated_at = models.DateTimeField(
        _('updated at'),
        auto_now=True
    )

    class Meta:
        db_table = 'catalog_service_locations'
        verbose_name = _('Service Location')
        verbose_name_plural = _('Service Locations')
        indexes = [
            models.Index(fields=['service']),
            models.Index(fields=['location_type']),
            models.Index(fields=['travel_radius']),
        ]

    def __str__(self):
        return f"{self.service.name} - {self.get_location_type_display()}"

    @property
    def is_mobile_service(self):
        """Check if service offers mobile/travel options"""
        return self.location_type in ['mobile', 'both']

    @property
    def is_virtual_service(self):
        """Check if service is virtual/online"""
        return self.location_type == 'virtual'

    @property
    def has_travel_fee(self):
        """Check if mobile service has travel fee"""
        return self.is_mobile_service and self.travel_fee and self.travel_fee > 0

    def can_serve_location(self, latitude, longitude):
        """Check if service can serve a specific location"""
        if not self.is_mobile_service:
            return True  # Fixed location services don't have geographic restrictions

        if not self.travel_radius:
            return True  # No radius limit

        # Calculate distance between provider and requested location
        provider = self.service.provider
        if not provider.has_location:
            return True  # Can't calculate distance without provider coordinates

        # Simple distance calculation (would use PostGIS in production)
        # This is a simplified version for SQLite compatibility
        from math import radians, cos, sin, asin, sqrt

        def haversine(lon1, lat1, lon2, lat2):
            """Calculate the great circle distance between two points"""
            lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
            dlon = lon2 - lon1
            dlat = lat2 - lat1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            c = 2 * asin(sqrt(a))
            r = 6371  # Radius of earth in kilometers
            return c * r

        distance = haversine(
            float(provider.longitude),
            float(provider.latitude),
            longitude,
            latitude
        )

        return distance <= self.travel_radius


class SearchHistory(models.Model):
    """
    Model to store user search history for enhanced search experience
    Supports search suggestions and personalized recommendations
    """

    user = models.ForeignKey(
        get_user_model(), on_delete=models.CASCADE, related_name='search_history'
    )
    query = models.CharField(max_length=255, help_text="Search query text")

    # Search type
    SEARCH_TYPES = [
        ('service', 'Service Search'),
        ('provider', 'Provider Search'),
        ('category', 'Category Search'),
        ('location', 'Location Search'),
    ]
    search_type = models.CharField(
        max_length=20, choices=SEARCH_TYPES, default='service'
    )

    # Search metadata
    results_count = models.IntegerField(default=0, help_text="Number of results returned")
    clicked_result_id = models.CharField(
        max_length=50, blank=True, null=True,
        help_text="ID of the result that was clicked"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'catalog_search_history'
        verbose_name = 'Search History'
        verbose_name_plural = 'Search Histories'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['query', 'search_type']),
            models.Index(fields=['created_at']),
        ]
        # Prevent duplicate entries for same user/query/type within short time
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'query', 'search_type'],
                name='unique_user_query_type'
            )
        ]

    def __str__(self):
        return f"{self.user.email} - {self.query} ({self.search_type})"

    @classmethod
    def get_popular_searches(cls, search_type=None, limit=10):
        """Get popular search queries across all users"""
        from django.db.models import Count

        queryset = cls.objects.all()
        if search_type:
            queryset = queryset.filter(search_type=search_type)

        return queryset.values('query').annotate(
            search_count=Count('query')
        ).order_by('-search_count')[:limit]

    @classmethod
    def get_user_suggestions(cls, user, query_prefix, limit=5):
        """Get search suggestions based on user's history"""
        if not user.is_authenticated:
            return []

        return cls.objects.filter(
            user=user,
            query__istartswith=query_prefix
        ).values_list('query', flat=True).distinct()[:limit]
