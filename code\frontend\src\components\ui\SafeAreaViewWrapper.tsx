/**
 * SafeAreaViewWrapper - Temporary workaround for SafeAreaView TypeError
 * 
 * This component provides a fallback for the SafeAreaView TypeError issue
 * while maintaining the same API as react-native-safe-area-context
 */

import React from 'react';
import { View, Platform, StatusBar, ViewStyle } from 'react-native';

interface SafeAreaViewWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
  testID?: string;
  [key: string]: any; // Allow any additional props
}

// Fallback safe area insets
const getFallbackInsets = () => {
  return {
    top: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 24,
    bottom: Platform.OS === 'ios' ? 34 : 0,
    left: 0,
    right: 0,
  };
};

export const SafeAreaViewWrapper: React.FC<SafeAreaViewWrapperProps> = ({
  children,
  style,
  edges = ['top', 'bottom'],
  testID,
  ...otherProps
}) => {
  try {
    // Ensure edges is always an array
    const safeEdges = Array.isArray(edges) ? edges : ['top', 'bottom'];

    const insets = getFallbackInsets();

    // Ensure insets are valid numbers
    const safeInsets = {
      top: typeof insets.top === 'number' ? insets.top : 0,
      bottom: typeof insets.bottom === 'number' ? insets.bottom : 0,
      left: typeof insets.left === 'number' ? insets.left : 0,
      right: typeof insets.right === 'number' ? insets.right : 0,
    };

    const paddingStyle = {
      paddingTop: safeEdges.includes('top') ? safeInsets.top : 0,
      paddingBottom: safeEdges.includes('bottom') ? safeInsets.bottom : 0,
      paddingLeft: safeEdges.includes('left') ? safeInsets.left : 0,
      paddingRight: safeEdges.includes('right') ? safeInsets.right : 0,
    };

    // Ensure style is safe to spread
    const safeStyle = style || {};

    return (
      <View style={[{ flex: 1 }, paddingStyle, safeStyle]} testID={testID} {...otherProps}>
        {children}
      </View>
    );
  } catch (error) {
    console.warn('SafeAreaViewWrapper error:', error);
    // Fallback to basic View
    return (
      <View style={[{ flex: 1 }, style]} testID={testID}>
        {children}
      </View>
    );
  }
};

// Always use our wrapper to avoid the TypeError issue
export const SafeAreaView: React.FC<SafeAreaViewWrapperProps> = (props) => {
  return <SafeAreaViewWrapper {...props} />;
};

export default SafeAreaView;
