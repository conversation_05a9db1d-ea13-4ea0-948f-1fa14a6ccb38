"""
Tests for test account management system
Tests creation, management, cleanup, and security of test accounts
"""
import os
from unittest.mock import patch
from django.test import TestCase, override_settings
from django.core.management import call_command
from django.core.management.base import CommandError
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from io import StringIO

from catalog.models import ServiceProvider, ServiceCategory, Service
from catalog.security import TestAccountSecurity, TestAccountManager
from catalog.factories import CustomerFactory, ProviderFactory, ServiceFactory

User = get_user_model()


class TestAccountCreationTest(TestCase):
    """Test test account creation functionality"""
    
    def setUp(self):
        self.out = StringIO()
    
    def test_create_test_accounts_command_basic(self):
        """Test basic test account creation command"""
        # Ensure we start with no test accounts
        self.assertEqual(User.objects.filter(is_test_account=True).count(), 0)
        
        # Run the command
        call_command('create_test_accounts', '--quick', stdout=self.out)
        
        # Check that accounts were created
        test_users = User.objects.filter(is_test_account=True)
        self.assertGreater(test_users.count(), 0)
        
        # Check that we have both customers and providers
        customers = test_users.filter(role='customer')
        providers = test_users.filter(role='service_provider')
        
        self.assertGreater(customers.count(), 0)
        self.assertGreater(providers.count(), 0)
        
        # Check output
        output = self.out.getvalue()
        self.assertIn('TEST ACCOUNTS CREATED SUCCESSFULLY', output)
    
    def test_create_test_accounts_with_force(self):
        """Test creating test accounts with force flag"""
        # Create initial accounts
        call_command('create_test_accounts', '--quick', stdout=self.out)
        initial_count = User.objects.filter(is_test_account=True).count()
        
        # Create again with force
        call_command('create_test_accounts', '--quick', '--force', stdout=self.out)
        
        # Should still have accounts (recreated)
        final_count = User.objects.filter(is_test_account=True).count()
        self.assertGreater(final_count, 0)
        
        # Check output mentions force recreation
        output = self.out.getvalue()
        self.assertIn('Deleted existing', output)
    
    def test_create_customers_only(self):
        """Test creating only customer accounts"""
        call_command('create_test_accounts', '--customers-only', '--quick', stdout=self.out)
        
        test_users = User.objects.filter(is_test_account=True)
        customers = test_users.filter(role='customer')
        providers = test_users.filter(role='service_provider')
        
        self.assertGreater(customers.count(), 0)
        self.assertEqual(providers.count(), 0)
    
    def test_create_providers_only(self):
        """Test creating only provider accounts"""
        call_command('create_test_accounts', '--providers-only', '--quick', stdout=self.out)
        
        test_users = User.objects.filter(is_test_account=True)
        customers = test_users.filter(role='customer')
        providers = test_users.filter(role='service_provider')
        
        self.assertEqual(customers.count(), 0)
        self.assertGreater(providers.count(), 0)
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    @override_settings(DEBUG=False, ENVIRONMENT='production')
    def test_create_test_accounts_blocked_in_production(self):
        """Test that test account creation is blocked in production"""
        with self.assertRaises(PermissionDenied):
            call_command('create_test_accounts', '--quick', stdout=self.out)


class TestAccountCleanupTest(TestCase):
    """Test test account cleanup functionality"""
    
    def setUp(self):
        self.out = StringIO()
        # Create some test accounts
        call_command('create_test_accounts', '--quick', stdout=StringIO())
    
    def test_cleanup_test_accounts_dry_run(self):
        """Test cleanup with dry run"""
        initial_count = User.objects.filter(is_test_account=True).count()
        
        call_command('cleanup_test_accounts', '--dry-run', stdout=self.out)
        
        # Should not delete anything
        final_count = User.objects.filter(is_test_account=True).count()
        self.assertEqual(initial_count, final_count)
        
        # Check output
        output = self.out.getvalue()
        self.assertIn('DRY RUN MODE', output)
        self.assertIn('no data was deleted', output)
    
    def test_cleanup_test_accounts_force(self):
        """Test cleanup with force flag"""
        initial_count = User.objects.filter(is_test_account=True).count()
        self.assertGreater(initial_count, 0)
        
        call_command('cleanup_test_accounts', '--force', stdout=self.out)
        
        # Should delete all test accounts
        final_count = User.objects.filter(is_test_account=True).count()
        self.assertEqual(final_count, 0)
        
        # Check output
        output = self.out.getvalue()
        self.assertIn('CLEANUP COMPLETED', output)
    
    def test_cleanup_customers_only(self):
        """Test cleanup of only customer accounts"""
        initial_customers = User.objects.filter(is_test_account=True, role='customer').count()
        initial_providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        
        call_command('cleanup_test_accounts', '--customers-only', '--force', stdout=self.out)
        
        # Should delete only customers
        final_customers = User.objects.filter(is_test_account=True, role='customer').count()
        final_providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        
        self.assertEqual(final_customers, 0)
        self.assertEqual(final_providers, initial_providers)
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    @override_settings(DEBUG=False, ENVIRONMENT='production')
    def test_cleanup_blocked_in_production(self):
        """Test that cleanup is blocked in production"""
        with self.assertRaises(PermissionDenied):
            call_command('cleanup_test_accounts', '--force', stdout=self.out)


class TestAccountSecurityTest(TestCase):
    """Test test account security functionality"""
    
    def test_environment_detection_development(self):
        """Test development environment detection"""
        with override_settings(DEBUG=True, ENVIRONMENT='development'):
            self.assertTrue(TestAccountSecurity.is_development_environment())
            self.assertFalse(TestAccountSecurity.is_production_environment())
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    @override_settings(DEBUG=False, ENVIRONMENT='production')
    def test_environment_detection_production(self):
        """Test production environment detection"""
        self.assertFalse(TestAccountSecurity.is_development_environment())
        self.assertTrue(TestAccountSecurity.is_production_environment())
    
    def test_test_account_email_validation(self):
        """Test test account email validation"""
        # Valid test emails
        self.assertTrue(TestAccountSecurity.is_test_account_email('<EMAIL>'))
        
        # Invalid test emails
        self.assertFalse(TestAccountSecurity.is_test_account_email('<EMAIL>'))
        self.assertFalse(TestAccountSecurity.is_test_account_email('<EMAIL>'))
    
    def test_test_account_manager_create_user(self):
        """Test TestAccountManager user creation"""
        user = TestAccountManager.create_test_user(
            email='<EMAIL>',
            username='<EMAIL>',
            password='TestPass123!',
            first_name='Test',
            last_name='User'
        )
        
        self.assertTrue(user.is_test_account)
        self.assertEqual(user.email, '<EMAIL>')
    
    def test_test_account_manager_invalid_email(self):
        """Test TestAccountManager with invalid email"""
        with self.assertRaises(ValueError):
            TestAccountManager.create_test_user(
                email='<EMAIL>',
                username='<EMAIL>',
                password='TestPass123!'
            )
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    @override_settings(DEBUG=False, ENVIRONMENT='production')
    def test_test_account_manager_blocked_in_production(self):
        """Test TestAccountManager blocked in production"""
        with self.assertRaises(PermissionDenied):
            TestAccountManager.create_test_user(
                email='<EMAIL>',
                username='<EMAIL>',
                password='TestPass123!'
            )


class SampleDataGenerationTest(TestCase):
    """Test sample data generation functionality"""
    
    def setUp(self):
        self.out = StringIO()
    
    def test_generate_sample_data_command(self):
        """Test sample data generation command"""
        call_command('generate_sample_data', '--customers', '3', '--providers', '2', stdout=self.out)
        
        # Check that data was created
        customers = User.objects.filter(is_test_account=True, role='customer').count()
        providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        services = Service.objects.count()
        
        self.assertEqual(customers, 3)
        self.assertEqual(providers, 2)
        self.assertGreater(services, 0)
        
        # Check output
        output = self.out.getvalue()
        self.assertIn('SAMPLE DATA GENERATION COMPLETE', output)
    
    def test_customer_factory(self):
        """Test CustomerFactory"""
        customer = CustomerFactory.create_customer()
        
        self.assertTrue(customer.is_test_account)
        self.assertEqual(customer.role, 'customer')
        self.assertTrue(customer.email.endswith('@test.com'))
        self.assertTrue(customer.is_verified)
    
    def test_provider_factory(self):
        """Test ProviderFactory"""
        # Create a category first
        category = ServiceCategory.objects.create(
            name='Test Category',
            slug='test-category',
            is_active=True
        )
        
        provider = ProviderFactory.create_provider(category_slug='test-category')
        
        self.assertTrue(provider.user.is_test_account)
        self.assertEqual(provider.user.role, 'service_provider')
        self.assertTrue(provider.user.email.endswith('@test.com'))
        self.assertTrue(provider.is_verified)
        self.assertEqual(provider.categories.first(), category)
    
    def test_service_factory(self):
        """Test ServiceFactory"""
        # Create category and provider
        category = ServiceCategory.objects.create(
            name='Test Category',
            slug='test-category',
            is_active=True
        )
        provider = ProviderFactory.create_provider(category_slug='test-category')
        
        service = ServiceFactory.create_service(provider)
        
        self.assertEqual(service.provider, provider)
        self.assertEqual(service.category, category)
        self.assertGreater(service.base_price, 0)
        self.assertGreater(service.duration, 0)
        self.assertTrue(service.is_active)


class DataVerificationTest(TestCase):
    """Test data verification functionality"""
    
    def setUp(self):
        self.out = StringIO()
        # Create some test data
        call_command('generate_sample_data', '--customers', '2', '--providers', '2', stdout=StringIO())
    
    def test_verify_sample_data_command(self):
        """Test sample data verification command"""
        call_command('verify_sample_data', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Verifying sample data integrity', output)
    
    def test_verify_sample_data_detailed(self):
        """Test detailed sample data verification"""
        call_command('verify_sample_data', '--detailed', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Test users:', output)
        self.assertIn('Total categories:', output)
        self.assertIn('Total providers:', output)


class SecurityAuditTest(TestCase):
    """Test security audit functionality"""
    
    def setUp(self):
        self.out = StringIO()
        # Create some test accounts
        call_command('create_test_accounts', '--quick', stdout=StringIO())
    
    def test_audit_test_accounts_command(self):
        """Test security audit command"""
        call_command('audit_test_accounts', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Starting test account security audit', output)
        self.assertIn('Environment Information', output)
        self.assertIn('Security Audit Results', output)
    
    def test_audit_test_accounts_detailed(self):
        """Test detailed security audit"""
        call_command('audit_test_accounts', '--detailed', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Environment Setting:', output)
        self.assertIn('Debug Mode:', output)
        self.assertIn('Test Domain:', output)
    
    @patch.dict(os.environ, {'ENVIRONMENT': 'production'})
    @override_settings(DEBUG=False, ENVIRONMENT='production')
    def test_audit_production_check(self):
        """Test production-specific audit checks"""
        call_command('audit_test_accounts', '--production-check', stdout=self.out)
        
        output = self.out.getvalue()
        self.assertIn('Production Safety Check', output)
