"""
Service Catalog API views for Vierla Beauty Services Marketplace
Enhanced Django REST Framework views with mobile-first design
"""
from rest_framework import viewsets, status, filters, generics
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly, AllowAny
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg, F
from django.db import models
from decimal import Decimal

from .models import (
    ServiceCategory, ServiceProvider, Service, OperatingHours,
    ServiceAvailability, ServiceGallery, ServiceLocation
)
from .serializers import (
    ServiceCategorySerializer, ServiceCategoryListSerializer,
    ServiceProviderSerializer, ServiceProviderListSerializer, ServiceProviderCreateSerializer,
    ServiceSerializer, ServiceListSerializer,
    OperatingHoursSerializer, ServiceAvailabilitySerializer,
    ServiceGallerySerializer, ServiceLocationSerializer
)
from .filters import ServiceProviderFilter, ServiceFilter, ServiceCategoryFilter
from .utils import LocationUtils
from .permissions import (
    IsAdminOrReadOnly, IsProviderOwnerOrReadOnly, IsServiceOwnerOrReadOnly,
    CanManageOwnServices, CanManageGallery, CanManageOperatingHours,
    CanAccessLocationData, IsActiveUser
)


class ServiceCategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for service categories with hierarchical support
    Mobile-optimized with caching and filtering
    """

    queryset = ServiceCategory.objects.filter(is_active=True)
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceCategoryFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceCategoryListSerializer
        return ServiceCategorySerializer

    def get_queryset(self):
        """Optimize queryset with prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'list':
            # For list view, only get root categories with service counts
            queryset = queryset.filter(
                parent__isnull=True).prefetch_related('subcategories')
        else:
            # For detail view, include all related data
            queryset = queryset.select_related('parent').prefetch_related(
                'subcategories', 'services', 'providers'
            )

        return queryset

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular categories"""
        popular_categories = self.get_queryset().filter(is_popular=True)
        serializer = self.get_serializer(popular_categories, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services in this category"""
        category = self.get_object()
        services = Service.objects.filter(
            category=category,
            is_active=True,
            is_available=True
        ).select_related('provider', 'category')

        # Apply pagination
        page = self.paginate_queryset(services)
        if page is not None:
            serializer = ServiceListSerializer(
                page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(
            services, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def providers(self, request, pk=None):
        """Get providers in this category"""
        category = self.get_object()
        providers = ServiceProvider.objects.filter(
            categories=category,
            is_active=True
        ).select_related('user').prefetch_related('categories')

        # Apply pagination
        page = self.paginate_queryset(providers)
        if page is not None:
            serializer = ServiceProviderListSerializer(
                page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ServiceProviderListSerializer(
            providers, many=True, context={'request': request})
        return Response(serializer.data)


class ServiceProviderViewSet(viewsets.ModelViewSet):
    """
    ViewSet for service providers with location-based search
    Mobile-optimized with comprehensive filtering and query optimization
    """

    queryset = ServiceProvider.objects.filter(is_active=True).select_related(
        'user'
    ).prefetch_related(
        'categories',
        'services',
        'services__category'
    )
    permission_classes = [IsProviderOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceProviderFilter
    search_fields = ['business_name', 'business_description', 'city', 'state']
    ordering_fields = ['business_name', 'rating', 'review_count', 'created_at']
    ordering = ['-rating', '-review_count', 'business_name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return ServiceProviderCreateSerializer
        elif self.action == 'list':
            return ServiceProviderListSerializer
        return ServiceProviderSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'list':
            # For list view, optimize for mobile
            queryset = queryset.select_related(
                'user').prefetch_related('categories')
        else:
            # For detail view, include all related data
            queryset = queryset.select_related('user').prefetch_related(
                'categories', 'services', 'operating_hours', 'gallery'
            )

        return queryset

    def create(self, request, *args, **kwargs):
        """Create provider profile for authenticated user"""
        # Check if user already has a provider profile
        if hasattr(request.user, 'provider_profile'):
            return Response(
                {'error': 'User already has a service provider profile'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return super().create(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured providers"""
        featured_providers = self.get_queryset().filter(is_featured=True)
        serializer = self.get_serializer(featured_providers, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def verified(self, request):
        """Get verified providers"""
        verified_providers = self.get_queryset().filter(is_verified=True)
        serializer = self.get_serializer(verified_providers, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def nearby(self, request):
        """Get providers near user location"""
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        radius = request.query_params.get('radius', 25)  # Default 25km

        if not latitude or not longitude:
            return Response(
                {'error': 'Latitude and longitude are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate coordinates
        if not LocationUtils.validate_coordinates(latitude, longitude):
            return Response(
                {'error': 'Invalid coordinates'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            lat = float(latitude)
            lng = float(longitude)
            radius_km = float(radius)
        except ValueError:
            return Response(
                {'error': 'Invalid radius'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use LocationUtils for efficient location-based filtering
        provider_distances = LocationUtils.filter_providers_by_location(
            self.get_queryset(),
            lat, lng, radius_km
        )

        # Extract providers and create distances dict
        nearby_providers = [provider for provider,
                            distance in provider_distances]
        distances = {str(provider.id): round(distance, 2)
                     for provider, distance in provider_distances}

        # Apply pagination
        page = self.paginate_queryset(nearby_providers)
        if page is not None:
            serializer = self.get_serializer(
                page, many=True,
                context={'request': request, 'distances': distances}
            )
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(
            nearby_providers, many=True,
            context={'request': request, 'distances': distances}
        )
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services offered by this provider"""
        provider = self.get_object()
        services = provider.services.filter(is_active=True, is_available=True)

        # Apply pagination
        page = self.paginate_queryset(services)
        if page is not None:
            serializer = ServiceListSerializer(
                page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(
            services, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def availability(self, request, pk=None):
        """Get provider availability for a specific date"""
        provider = self.get_object()
        date_str = request.query_params.get('date')

        if not date_str:
            return Response(
                {'error': 'Date parameter is required (YYYY-MM-DD)'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from datetime import datetime
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
            day_name = date.strftime('%A').lower()

            # Get operating hours for the day
            operating_hours = provider.operating_hours.filter(
                day=day_name).first()

            if not operating_hours or not operating_hours.is_open:
                return Response({
                    'available': False,
                    'message': f'Provider is closed on {day_name.title()}'
                })

            # Get available time slots
            slots = operating_hours.get_available_slots(slot_duration=30)

            return Response({
                'available': True,
                'date': date_str,
                'day': day_name.title(),
                'operating_hours': OperatingHoursSerializer(operating_hours).data,
                'available_slots': slots
            })

        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['patch'], permission_classes=[IsProviderOwnerOrReadOnly])
    def store_customization(self, request, pk=None):
        """Update store customization settings"""
        provider = self.get_object()

        # Define allowed customization fields
        customization_fields = [
            'store_theme_primary_color', 'store_theme_accent_color', 'store_theme_layout',
            'display_show_prices', 'display_show_duration', 'display_show_ratings', 'display_show_availability',
            'portfolio_type', 'portfolio_instagram_max_posts', 'portfolio_show_instagram_captions', 'portfolio_layout'
        ]

        # Filter request data to only include customization fields
        customization_data = {
            field: request.data.get(field)
            for field in customization_fields
            if field in request.data
        }

        if not customization_data:
            return Response(
                {'error': 'No valid customization fields provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the provider with customization data
        serializer = self.get_serializer(
            provider, data=customization_data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for services with comprehensive filtering and search
    Mobile-optimized with provider information and query optimization
    """

    queryset = Service.objects.filter(is_active=True, is_available=True).select_related(
        'provider', 'category'
    ).prefetch_related(
        'provider__categories'
    )
    permission_classes = [IsServiceOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceFilter
    search_fields = ['name', 'description', 'provider__business_name']
    ordering_fields = ['name', 'base_price',
                       'duration', 'created_at', 'booking_count']
    ordering = ['-is_popular', 'base_price', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceListSerializer
        return ServiceSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related for N+1 prevention"""
        queryset = super().get_queryset()

        if self.action == 'list':
            # Enhanced optimization for list view with all necessary relations
            queryset = queryset.select_related(
                'provider',
                'category'
            ).prefetch_related(
                'provider__categories',  # For provider category information
                'gallery',  # For service images
            ).annotate(
                # Add computed fields for sorting and filtering
                provider_rating=F('provider__rating'),
                provider_city=F('provider__city'),
                category_name=F('category__name')
            )
        else:
            # For detail view, include all related data
            queryset = queryset.select_related(
                'provider',
                'category'
            ).prefetch_related(
                'gallery',
                'availability',
                'location_info',
                'provider__categories',
                'provider__operating_hours'
            )

        return queryset

    def perform_create(self, serializer):
        """Associate service with provider on creation"""
        # Ensure user has a provider profile
        if not hasattr(self.request.user, 'provider_profile'):
            from rest_framework import serializers
            raise serializers.ValidationError(
                "User must have a service provider profile to create services"
            )

        serializer.save(provider=self.request.user.provider_profile)

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular services"""
        popular_services = self.get_queryset().filter(is_popular=True)
        serializer = self.get_serializer(popular_services, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Enhanced search with multiple filters and multi-select categories"""
        queryset = self.get_queryset()

        # Text search
        query = request.query_params.get('q')
        if query:
            queryset = queryset.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(provider__business_name__icontains=query) |
                Q(category__name__icontains=query)
            )

        # Multi-select category filter
        categories = request.query_params.getlist('categories')
        if categories:
            # Support both single category and multiple categories
            queryset = queryset.filter(category__id__in=categories)

        # Single category filter (backward compatibility)
        category = request.query_params.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)

        # Price range filter
        min_price = request.query_params.get('min_price')
        max_price = request.query_params.get('max_price')

        if min_price:
            try:
                queryset = queryset.filter(base_price__gte=Decimal(min_price))
            except (ValueError, TypeError):
                pass

        if max_price:
            try:
                queryset = queryset.filter(base_price__lte=Decimal(max_price))
            except (ValueError, TypeError):
                pass

        # Duration filter
        max_duration = request.query_params.get('max_duration')
        if max_duration:
            try:
                queryset = queryset.filter(duration__lte=int(max_duration))
            except (ValueError, TypeError):
                pass

        # Rating filter
        min_rating = request.query_params.get('min_rating')
        if min_rating:
            try:
                queryset = queryset.filter(provider__rating__gte=float(min_rating))
            except (ValueError, TypeError):
                pass

        # Availability filter
        availability = request.query_params.get('availability')
        if availability and availability.lower() == 'true':
            queryset = queryset.filter(is_available=True)

        # Instant booking filter
        instant_booking = request.query_params.get('instant_booking')
        if instant_booking and instant_booking.lower() == 'true':
            queryset = queryset.filter(instant_booking=True)

        # Apply sorting
        sort_by = request.query_params.get('sort_by', 'popularity')
        if sort_by == 'price_low':
            queryset = queryset.order_by('base_price')
        elif sort_by == 'price_high':
            queryset = queryset.order_by('-base_price')
        elif sort_by == 'rating':
            queryset = queryset.order_by('-provider__rating')
        elif sort_by == 'duration':
            queryset = queryset.order_by('duration')
        elif sort_by == 'newest':
            queryset = queryset.order_by('-created_at')
        else:  # Default: popularity
            queryset = queryset.order_by('-is_popular', '-booking_count', 'base_price')

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def toggle_availability(self, request, pk=None):
        """Toggle service availability"""
        service = self.get_object()

        # Check if user owns this service
        if not hasattr(request.user, 'provider_profile') or service.provider != request.user.provider_profile:
            return Response(
                {'error': 'You do not have permission to modify this service'},
                status=status.HTTP_403_FORBIDDEN
            )

        service.is_available = not service.is_available
        service.save()

        serializer = self.get_serializer(service)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def toggle_popular(self, request, pk=None):
        """Toggle service popular status (admin only)"""
        service = self.get_object()

        # Check if user is admin or owns this service
        if not (request.user.is_staff or
                (hasattr(request.user, 'provider_profile') and service.provider == request.user.provider_profile)):
            return Response(
                {'error': 'You do not have permission to modify this service'},
                status=status.HTTP_403_FORBIDDEN
            )

        service.is_popular = not service.is_popular
        service.save()

        serializer = self.get_serializer(service)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def booking_stats(self, request, pk=None):
        """Get booking statistics for a service"""
        service = self.get_object()

        # Basic stats (would be enhanced with actual booking data)
        stats = {
            'total_bookings': service.booking_count,
            'service_name': service.name,
            'provider_name': service.provider.business_name,
            'average_rating': service.provider.rating,
            'created_date': service.created_at,
            'last_updated': service.updated_at,
            'is_popular': service.is_popular,
            'is_available': service.is_available
        }

        return Response(stats)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_services(self, request):
        """Get services owned by the authenticated provider"""
        if not hasattr(request.user, 'provider_profile'):
            return Response(
                {'error': 'User must have a service provider profile'},
                status=status.HTTP_400_BAD_REQUEST
            )

        services = Service.objects.filter(
            provider=request.user.provider_profile
        ).select_related('category').prefetch_related('gallery', 'availability', 'location_info')

        # Apply pagination
        page = self.paginate_queryset(services)
        if page is not None:
            serializer = ServiceSerializer(
                page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ServiceSerializer(
            services, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def nearby_services(self, request):
        """Get services near user location with provider information"""
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        radius = request.query_params.get('radius', 25)  # Default 25km
        category = request.query_params.get('category')

        if not latitude or not longitude:
            return Response(
                {'error': 'Latitude and longitude are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate coordinates
        if not LocationUtils.validate_coordinates(latitude, longitude):
            return Response(
                {'error': 'Invalid coordinates'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            lat = float(latitude)
            lng = float(longitude)
            radius_km = float(radius)
        except ValueError:
            return Response(
                {'error': 'Invalid radius'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Start with base queryset
        services_queryset = self.get_queryset()

        # Filter by category if provided
        if category:
            services_queryset = services_queryset.filter(
                category__slug=category)

        # Use LocationUtils for efficient location-based filtering
        service_distances = LocationUtils.filter_services_by_location(
            services_queryset,
            lat, lng, radius_km
        )

        # Extract services and create distances dict
        nearby_services = [service for service, distance in service_distances]
        distances = {str(service.id): round(distance, 2)
                     for service, distance in service_distances}

        # Apply pagination
        page = self.paginate_queryset(nearby_services)
        if page is not None:
            serializer = ServiceListSerializer(
                page, many=True,
                context={'request': request, 'distances': distances}
            )
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(
            nearby_services, many=True,
            context={'request': request, 'distances': distances}
        )
        return Response(serializer.data)


class OperatingHoursViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing provider operating hours
    """

    serializer_class = OperatingHoursSerializer
    permission_classes = [CanManageOperatingHours]

    def get_queryset(self):
        """Filter operating hours by provider ownership"""
        if hasattr(self.request.user, 'provider_profile'):
            return OperatingHours.objects.filter(provider=self.request.user.provider_profile)
        return OperatingHours.objects.none()

    def perform_create(self, serializer):
        """Associate operating hours with user's provider profile"""
        if not hasattr(self.request.user, 'provider_profile'):
            from rest_framework import serializers
            raise serializers.ValidationError(
                "User must have a service provider profile to manage operating hours"
            )
        serializer.save(provider=self.request.user.provider_profile)


class ServiceAvailabilityViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing service availability settings
    """

    serializer_class = ServiceAvailabilitySerializer
    permission_classes = [CanManageOwnServices]

    def get_queryset(self):
        """Filter availability by service ownership"""
        if hasattr(self.request.user, 'provider_profile'):
            return ServiceAvailability.objects.filter(
                service__provider=self.request.user.provider_profile
            ).select_related('service')
        return ServiceAvailability.objects.none()

    def perform_create(self, serializer):
        """Ensure service belongs to user's provider profile"""
        service = serializer.validated_data['service']
        if not hasattr(self.request.user, 'provider_profile'):
            from rest_framework import serializers
            raise serializers.ValidationError(
                "User must have a service provider profile"
            )

        if service.provider != self.request.user.provider_profile:
            from rest_framework import serializers
            raise serializers.ValidationError(
                "Service must belong to your provider profile"
            )

        serializer.save()


class ServiceGalleryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing service gallery images
    """

    serializer_class = ServiceGallerySerializer
    permission_classes = [CanManageGallery]

    def get_queryset(self):
        """Filter gallery by provider ownership"""
        if hasattr(self.request.user, 'provider_profile'):
            return ServiceGallery.objects.filter(
                provider=self.request.user.provider_profile
            ).select_related('provider', 'service')
        return ServiceGallery.objects.none()

    def perform_create(self, serializer):
        """Associate gallery image with user's provider profile"""
        if not hasattr(self.request.user, 'provider_profile'):
            from rest_framework import serializers
            raise serializers.ValidationError(
                "User must have a service provider profile to upload images"
            )

        # Ensure service belongs to provider if specified
        service = serializer.validated_data.get('service')
        if service and service.provider != self.request.user.provider_profile:
            from rest_framework import serializers
            raise serializers.ValidationError(
                "Service must belong to your provider profile"
            )

        serializer.save(provider=self.request.user.provider_profile)

    @action(detail=True, methods=['post'])
    def set_cover(self, request, pk=None):
        """Set image as cover image for provider"""
        image = self.get_object()

        # Remove cover status from other images
        ServiceGallery.objects.filter(
            provider=image.provider,
            is_cover=True
        ).update(is_cover=False)

        # Set this image as cover
        image.is_cover = True
        image.save()

        serializer = self.get_serializer(image)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """Toggle featured status of image"""
        image = self.get_object()
        image.is_featured = not image.is_featured
        image.save()

        serializer = self.get_serializer(image)
        return Response(serializer.data)


class ServiceLocationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing service location information
    """

    serializer_class = ServiceLocationSerializer
    permission_classes = [CanAccessLocationData]

    def get_queryset(self):
        """Filter location info by service ownership"""
        if hasattr(self.request.user, 'provider_profile'):
            return ServiceLocation.objects.filter(
                service__provider=self.request.user.provider_profile
            ).select_related('service')
        return ServiceLocation.objects.none()

    def perform_create(self, serializer):
        """Ensure service belongs to user's provider profile"""
        service = serializer.validated_data['service']
        if not hasattr(self.request.user, 'provider_profile'):
            from rest_framework import serializers
            raise serializers.ValidationError(
                "User must have a service provider profile"
            )

        if service.provider != self.request.user.provider_profile:
            from rest_framework import serializers
            raise serializers.ValidationError(
                "Service must belong to your provider profile"
            )

        serializer.save()


class SearchView(APIView):
    """
    Unified search endpoint for providers and services
    Mobile-optimized with comprehensive filtering
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get(self, request):
        """Search across providers and services"""
        query = request.query_params.get('q', '').strip()
        category = request.query_params.get('category')
        latitude = request.query_params.get('latitude')
        longitude = request.query_params.get('longitude')
        radius = request.query_params.get('radius', 25)

        if not query and not category:
            return Response(
                {'error': 'Search query or category is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Search providers
        provider_queryset = ServiceProvider.objects.filter(is_active=True)
        if query:
            provider_queryset = provider_queryset.filter(
                Q(business_name__icontains=query) |
                Q(business_description__icontains=query) |
                Q(city__icontains=query)
            )
        if category:
            provider_queryset = provider_queryset.filter(
                categories__slug=category)

        # Search services
        service_queryset = Service.objects.filter(
            is_active=True, is_available=True)
        if query:
            service_queryset = service_queryset.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(provider__business_name__icontains=query)
            )
        if category:
            service_queryset = service_queryset.filter(category__slug=category)

        # Apply location filtering if provided
        if latitude and longitude:
            try:
                lat = float(latitude)
                lng = float(longitude)
                radius_km = float(radius)

                # Filter providers by location
                provider_distances = LocationUtils.filter_providers_by_location(
                    provider_queryset, lat, lng, radius_km
                )
                nearby_providers = [provider for provider,
                                    distance in provider_distances]

                # Filter services by provider location
                nearby_provider_ids = [p.id for p in nearby_providers]
                service_queryset = service_queryset.filter(
                    provider__id__in=nearby_provider_ids)

            except (ValueError, TypeError):
                pass

        # Serialize results
        provider_serializer = ServiceProviderListSerializer(
            provider_queryset[:10], many=True, context={'request': request}
        )
        service_serializer = ServiceListSerializer(
            service_queryset[:20], many=True, context={'request': request}
        )

        return Response({
            'query': query,
            'category': category,
            'providers': {
                'count': provider_queryset.count(),
                'results': provider_serializer.data
            },
            'services': {
                'count': service_queryset.count(),
                'results': service_serializer.data
            }
        })


class SearchProvidersView(generics.ListAPIView):
    """
    Dedicated provider search endpoint with optimized queries
    """
    serializer_class = ServiceProviderListSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceProviderFilter
    search_fields = ['business_name', 'business_description', 'city', 'state']
    ordering_fields = ['business_name', 'rating', 'review_count', 'created_at']
    ordering = ['-rating', '-review_count']

    def get_queryset(self):
        return ServiceProvider.objects.filter(is_active=True).select_related(
            'user'
        ).prefetch_related(
            'categories',
            'services'
        )


class SearchServicesView(generics.ListAPIView):
    """
    Dedicated service search endpoint with optimized queries
    """
    serializer_class = ServiceListSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceFilter
    search_fields = ['name', 'description', 'provider__business_name']
    ordering_fields = ['name', 'base_price', 'duration', 'created_at']
    ordering = ['-is_popular', 'base_price']

    def get_queryset(self):
        return Service.objects.filter(
            is_active=True, is_available=True
        ).select_related('provider', 'category').prefetch_related(
            'provider__categories'
        )


class EnhancedSearchView(APIView):
    """
    Enhanced search endpoint with real-time search, suggestions, and search history
    Optimized for mobile with debouncing support
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get(self, request):
        """
        Enhanced search with multiple features:
        - Real-time search results
        - Search suggestions
        - Search history (for authenticated users)
        - Advanced filtering (categories, price, location, rating, availability)
        - Performance optimized queries
        """
        query = request.GET.get('q', '').strip()
        search_type = request.GET.get('type', 'services')  # services, providers, suggestions
        limit = min(int(request.GET.get('limit', 10)), 50)  # Max 50 results

        # Extract advanced filters
        filters = self._extract_filters(request)

        # Handle different search types
        if search_type == 'suggestions':
            return self._get_search_suggestions(query, limit)
        elif search_type == 'history':
            return self._get_search_history(request.user, limit)
        elif search_type == 'providers':
            return self._search_providers(query, request, limit, filters)
        else:  # Default to services
            return self._search_services(query, request, limit, filters)

    def _extract_filters(self, request):
        """Extract and validate filter parameters from request"""
        filters = {}

        # Category filters (multi-select)
        categories = request.GET.getlist('categories[]') or request.GET.getlist('categories')
        if categories:
            filters['categories'] = categories

        # Price range filters
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        if min_price:
            try:
                filters['min_price'] = float(min_price)
            except ValueError:
                pass
        if max_price:
            try:
                filters['max_price'] = float(max_price)
            except ValueError:
                pass

        # Location-based filters
        latitude = request.GET.get('latitude')
        longitude = request.GET.get('longitude')
        max_distance = request.GET.get('max_distance')

        if latitude and longitude:
            try:
                filters['user_location'] = {
                    'latitude': float(latitude),
                    'longitude': float(longitude)
                }
                if max_distance:
                    filters['max_distance'] = float(max_distance)
            except ValueError:
                pass

        # Rating filter
        min_rating = request.GET.get('min_rating')
        if min_rating:
            try:
                filters['min_rating'] = float(min_rating)
            except ValueError:
                pass

        # Availability filter
        availability = request.GET.get('availability')
        if availability and availability.lower() in ['true', '1', 'yes']:
            filters['availability'] = True

        # Sort options
        sort_by = request.GET.get('sort_by', 'relevance')
        if sort_by in ['relevance', 'price_low', 'price_high', 'rating', 'distance', 'availability']:
            filters['sort_by'] = sort_by

        return filters

    def _search_services(self, query, request, limit, filters=None):
        """Search services with optimized queries and advanced filtering"""
        from django.db.models import Q, Count, Avg, Case, When, IntegerField
        import math

        if filters is None:
            filters = {}

        # Start with base queryset
        services = Service.objects.filter(is_active=True, is_available=True)

        # Apply text search if query provided
        if query:
            services = services.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query) |
                Q(provider__business_name__icontains=query) |
                Q(category__name__icontains=query)
            )

        # Apply category filters
        if filters.get('categories'):
            services = services.filter(category__name__in=filters['categories'])

        # Apply price range filters
        if filters.get('min_price'):
            services = services.filter(base_price__gte=filters['min_price'])
        if filters.get('max_price'):
            services = services.filter(base_price__lte=filters['max_price'])

        # Apply rating filter
        if filters.get('min_rating'):
            services = services.filter(provider__rating__gte=filters['min_rating'])

        # Apply availability filter
        if filters.get('availability'):
            # Filter for services with available time slots
            services = services.filter(
                provider__operating_hours__isnull=False,
                is_available=True
            )

        # Apply location-based filtering (simplified without GIS)
        if filters.get('user_location') and filters.get('max_distance'):
            user_lat = filters['user_location']['latitude']
            user_lng = filters['user_location']['longitude']
            max_distance_km = filters['max_distance']

            # Simple bounding box filter (approximate)
            # 1 degree latitude ≈ 111 km
            lat_delta = max_distance_km / 111.0
            lng_delta = max_distance_km / (111.0 * math.cos(math.radians(user_lat)))

            services = services.filter(
                provider__latitude__gte=user_lat - lat_delta,
                provider__latitude__lte=user_lat + lat_delta,
                provider__longitude__gte=user_lng - lng_delta,
                provider__longitude__lte=user_lng + lng_delta
            )

        # Add annotations for sorting
        services = services.select_related('provider', 'category').annotate(
            search_rank=Case(
                When(name__icontains=query, then=3),
                When(category__name__icontains=query, then=2),
                When(provider__business_name__icontains=query, then=1),
                default=0,
                output_field=IntegerField()
            ) if query else Count('bookings'),
            total_bookings=Count('bookings'),
            avg_rating=Avg('provider__rating')
        )

        # Apply sorting
        sort_by = filters.get('sort_by', 'relevance')
        if sort_by == 'price_low':
            services = services.order_by('base_price', '-is_popular')
        elif sort_by == 'price_high':
            services = services.order_by('-base_price', '-is_popular')
        elif sort_by == 'rating':
            services = services.order_by('-avg_rating', '-is_popular')
        elif sort_by == 'distance' and filters.get('user_location'):
            # Distance sorting handled by location filter
            services = services.order_by('-is_popular', 'base_price')
        else:  # relevance (default)
            if query:
                services = services.order_by('-search_rank', '-is_popular', 'base_price')
            else:
                services = services.filter(is_popular=True).order_by('-is_popular', 'base_price')

        # Limit results
        services = services[:limit]

        # Save search query for authenticated users
        if request.user.is_authenticated and query:
            self._save_search_query(request.user, query, 'service')

        # Serialize results
        serializer = ServiceListSerializer(
            services, many=True, context={'request': request}
        )

        return Response({
            'results': serializer.data,
            'count': len(serializer.data),
            'query': query,
            'type': 'services',
            'filters_applied': filters
        })

    def _search_providers(self, query, request, limit, filters=None):
        """Search providers with optimized queries and advanced filtering"""
        from django.db.models import Q, Count, Avg
        import math

        if filters is None:
            filters = {}

        # Start with base queryset
        providers = ServiceProvider.objects.filter(is_active=True)

        # Apply text search if query provided
        if query:
            providers = providers.filter(
                Q(business_name__icontains=query) |
                Q(business_description__icontains=query) |
                Q(city__icontains=query) |
                Q(services__name__icontains=query) |
                Q(services__category__name__icontains=query)
            ).distinct()

        # Apply category filters (filter by services offered)
        if filters.get('categories'):
            providers = providers.filter(services__category__name__in=filters['categories']).distinct()

        # Apply price range filters (based on services offered)
        if filters.get('min_price'):
            providers = providers.filter(services__base_price__gte=filters['min_price']).distinct()
        if filters.get('max_price'):
            providers = providers.filter(services__base_price__lte=filters['max_price']).distinct()

        # Apply rating filter
        if filters.get('min_rating'):
            providers = providers.filter(rating__gte=filters['min_rating'])

        # Apply availability filter
        if filters.get('availability'):
            providers = providers.filter(
                operating_hours__isnull=False,
                services__is_available=True
            ).distinct()

        # Apply location-based filtering (simplified without GIS)
        if filters.get('user_location') and filters.get('max_distance'):
            user_lat = filters['user_location']['latitude']
            user_lng = filters['user_location']['longitude']
            max_distance_km = filters['max_distance']

            # Simple bounding box filter (approximate)
            # 1 degree latitude ≈ 111 km
            lat_delta = max_distance_km / 111.0
            lng_delta = max_distance_km / (111.0 * math.cos(math.radians(user_lat)))

            providers = providers.filter(
                latitude__gte=user_lat - lat_delta,
                latitude__lte=user_lat + lat_delta,
                longitude__gte=user_lng - lng_delta,
                longitude__lte=user_lng + lng_delta
            )

        # Add annotations for sorting
        providers = providers.select_related('user').annotate(
            service_count=Count('services', distinct=True),
            avg_service_price=Avg('services__base_price'),
            calculated_bookings=Count('services__bookings', distinct=True)
        )

        # Apply sorting
        sort_by = filters.get('sort_by', 'relevance')
        if sort_by == 'rating':
            providers = providers.order_by('-rating', '-is_featured')
        elif sort_by == 'price_low':
            providers = providers.order_by('avg_service_price', '-is_featured')
        elif sort_by == 'price_high':
            providers = providers.order_by('-avg_service_price', '-is_featured')
        else:  # relevance (default)
            providers = providers.order_by('-is_featured', '-rating', '-service_count')

        # Limit results
        providers = providers[:limit]

        # Save search query for authenticated users
        if request.user.is_authenticated and query:
            self._save_search_query(request.user, query, 'provider')

        # Serialize results
        serializer = ServiceProviderListSerializer(
            providers, many=True, context={'request': request}
        )

        return Response({
            'results': serializer.data,
            'count': len(serializer.data),
            'query': query,
            'type': 'providers',
            'filters_applied': filters
        })

    def _get_search_suggestions(self, query, limit):
        """Get search suggestions based on popular searches and existing data"""
        suggestions = []

        if len(query) >= 2:  # Only suggest for 2+ characters
            # Get service name suggestions
            service_suggestions = Service.objects.filter(
                name__icontains=query, is_active=True, is_available=True
            ).values_list('name', flat=True).distinct()[:limit//2]

            # Get category suggestions
            category_suggestions = ServiceCategory.objects.filter(
                name__icontains=query, is_active=True
            ).values_list('name', flat=True).distinct()[:limit//2]

            # Combine and format suggestions
            suggestions.extend([
                {'text': name, 'type': 'service'} for name in service_suggestions
            ])
            suggestions.extend([
                {'text': name, 'type': 'category'} for name in category_suggestions
            ])

        return Response({
            'suggestions': suggestions[:limit],
            'query': query
        })

    def _get_search_history(self, user, limit):
        """Get user's search history"""
        if not user.is_authenticated:
            return Response({'history': [], 'message': 'Authentication required'})

        # Get recent search queries (we'll implement the model next)
        try:
            from .models import SearchHistory
            history = SearchHistory.objects.filter(
                user=user
            ).order_by('-created_at').values_list(
                'query', 'search_type'
            ).distinct()[:limit]

            history_data = [
                {'query': query, 'type': search_type}
                for query, search_type in history
            ]
        except ImportError:
            # Fallback if SearchHistory model doesn't exist yet
            history_data = []

        return Response({
            'history': history_data
        })

    def _save_search_query(self, user, query, search_type):
        """Save search query to user's history"""
        try:
            from django.utils import timezone
            from .models import SearchHistory
            SearchHistory.objects.get_or_create(
                user=user,
                query=query,
                search_type=search_type,
                defaults={'created_at': timezone.now()}
            )
        except ImportError:
            # Silently fail if SearchHistory model doesn't exist yet
            pass
