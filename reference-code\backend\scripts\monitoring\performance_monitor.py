#!/usr/bin/env python
"""
Performance Monitoring Dashboard Terminal
Real-time performance monitoring dashboard for Vierla backend
Food delivery app-analogous metrics monitoring
"""
import os
import sys
import time
import django
from datetime import datetime, timedelta
import psutil
import requests
from decimal import Decimal
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

# Import Django models after setup (simplified for now)
# from apps.analytics.models import (
#     PerformanceMetric, SystemHealthMetric, RealTimeMetric, PerformanceAlert
# )
# from apps.analytics.services import PerformanceMonitoringService

class PerformanceDashboard:
    """
    Real-time performance monitoring dashboard
    """
    
    def __init__(self):
        # self.monitoring_service = PerformanceMonitoringService()
        self.api_base_url = "http://192.168.2.65:8000"
        self.refresh_interval = 5  # seconds
        
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_colored_text(self, text, color):
        """Get colored text for terminal output"""
        colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
        return f"{colors.get(color, '')}{text}{colors['end']}"
    
    def get_status_indicator(self, value, thresholds):
        """Get status indicator based on value and thresholds"""
        if value < thresholds['good']:
            return self.get_colored_text("●", "green")
        elif value < thresholds['warning']:
            return self.get_colored_text("●", "yellow")
        else:
            return self.get_colored_text("●", "red")
    
    def format_bytes(self, bytes_value):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def get_system_metrics(self):
        """Get current system metrics"""
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory Usage
            memory = psutil.virtual_memory()
            
            # Disk Usage
            disk = psutil.disk_usage('/')
            
            # Network Stats
            network = psutil.net_io_counters()
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_used': disk.used,
                'disk_total': disk.total,
                'network_sent': network.bytes_sent,
                'network_recv': network.bytes_recv
            }
        except Exception as e:
            print(f"Error getting system metrics: {e}")
            return {}
    
    def get_api_metrics(self):
        """Get API performance metrics (simplified)"""
        try:
            # Simple API health check
            start_time = time.time()
            response = requests.get(f"{self.api_base_url}/api/health/", timeout=5)
            latency = (time.time() - start_time) * 1000  # Convert to ms

            return {
                'avg_latency': latency,
                'total_requests': 1,
                'error_rate': 0 if response.status_code == 200 else 100,
                'rps': 0.2  # Simulated
            }
        except Exception as e:
            print(f"Error getting API metrics: {e}")
            return {
                'avg_latency': 0,
                'total_requests': 0,
                'error_rate': 100,
                'rps': 0
            }
    
    def get_active_alerts(self):
        """Get active performance alerts (simplified)"""
        try:
            # Simplified - no alerts for now
            return []
        except Exception as e:
            print(f"Error getting active alerts: {e}")
            return []
    
    def test_api_connectivity(self):
        """Test API connectivity"""
        try:
            response = requests.get(f"{self.api_base_url}/api/health/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def display_dashboard(self):
        """Display the performance dashboard"""
        self.clear_screen()
        
        # Header
        print(self.get_colored_text("=" * 80, "cyan"))
        print(self.get_colored_text("🚀 VIERLA PERFORMANCE MONITORING DASHBOARD", "bold"))
        print(self.get_colored_text("Food Delivery App-Analogous Metrics", "cyan"))
        print(self.get_colored_text("=" * 80, "cyan"))
        print()
        
        # Current time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"📊 Last Updated: {self.get_colored_text(current_time, 'white')}")
        print()
        
        # API Connectivity
        api_status = "🟢 ONLINE" if self.test_api_connectivity() else "🔴 OFFLINE"
        print(f"🌐 API Status: {api_status}")
        print()
        
        # System Metrics
        system_metrics = self.get_system_metrics()
        if system_metrics:
            print(self.get_colored_text("📈 SYSTEM HEALTH METRICS", "bold"))
            print("-" * 40)
            
            # CPU
            cpu_status = self.get_status_indicator(system_metrics['cpu_percent'], {'good': 70, 'warning': 85})
            print(f"{cpu_status} CPU Usage: {system_metrics['cpu_percent']:.1f}%")
            
            # Memory
            memory_status = self.get_status_indicator(system_metrics['memory_percent'], {'good': 70, 'warning': 85})
            memory_used = self.format_bytes(system_metrics['memory_used'])
            memory_total = self.format_bytes(system_metrics['memory_total'])
            print(f"{memory_status} Memory: {system_metrics['memory_percent']:.1f}% ({memory_used}/{memory_total})")
            
            # Disk
            disk_status = self.get_status_indicator(system_metrics['disk_percent'], {'good': 80, 'warning': 90})
            disk_used = self.format_bytes(system_metrics['disk_used'])
            disk_total = self.format_bytes(system_metrics['disk_total'])
            print(f"{disk_status} Disk: {system_metrics['disk_percent']:.1f}% ({disk_used}/{disk_total})")
            
            print()
        
        # API Performance Metrics
        api_metrics = self.get_api_metrics()
        if api_metrics:
            print(self.get_colored_text("⚡ API PERFORMANCE METRICS (Last 5 minutes)", "bold"))
            print("-" * 50)
            
            # Latency
            latency_status = self.get_status_indicator(api_metrics['avg_latency'], {'good': 500, 'warning': 1000})
            print(f"{latency_status} Avg Latency: {api_metrics['avg_latency']:.2f}ms")
            
            # RPS
            rps_color = "green" if api_metrics['rps'] > 0 else "yellow"
            print(f"📊 Requests/sec: {self.get_colored_text(f'{api_metrics['rps']:.2f}', rps_color)}")
            
            # Total Requests
            print(f"📈 Total Requests: {api_metrics['total_requests']}")
            
            # Error Rate
            error_status = self.get_status_indicator(api_metrics['error_rate'], {'good': 1, 'warning': 5})
            print(f"{error_status} Error Rate: {api_metrics['error_rate']:.2f}%")
            
            print()
        
        # Active Alerts
        active_alerts = self.get_active_alerts()
        print(self.get_colored_text("🚨 ACTIVE ALERTS", "bold"))
        print("-" * 30)
        
        if active_alerts:
            for alert in active_alerts:
                severity_colors = {
                    'low': 'blue',
                    'medium': 'yellow',
                    'high': 'red',
                    'critical': 'red'
                }
                severity_color = severity_colors.get(alert.severity, 'white')
                severity_text = self.get_colored_text(alert.severity.upper(), severity_color)
                
                alert_time = alert.triggered_at.strftime("%H:%M:%S")
                print(f"[{alert_time}] {severity_text} {alert.alert_type}: {alert.message[:50]}...")
        else:
            print(self.get_colored_text("✅ No active alerts", "green"))
        
        print()
        
        # Footer
        print(self.get_colored_text("=" * 80, "cyan"))
        print(f"🔄 Auto-refresh every {self.refresh_interval} seconds | Press Ctrl+C to exit")
        print(self.get_colored_text("=" * 80, "cyan"))
    
    def run(self):
        """Run the performance monitoring dashboard"""
        print(self.get_colored_text("🚀 Starting Vierla Performance Monitoring Dashboard...", "green"))
        print(f"📊 Monitoring backend at: {self.api_base_url}")
        print(f"🔄 Refresh interval: {self.refresh_interval} seconds")
        print()
        
        # Start the monitoring service (simplified)
        # self.monitoring_service.start_monitoring()

        try:
            while True:
                self.display_dashboard()
                time.sleep(self.refresh_interval)
        except KeyboardInterrupt:
            print(self.get_colored_text("\n\n🛑 Performance monitoring stopped.", "yellow"))
            # self.monitoring_service.stop_monitoring()
            sys.exit(0)
        except Exception as e:
            print(self.get_colored_text(f"\n\n❌ Error: {e}", "red"))
            # self.monitoring_service.stop_monitoring()
            sys.exit(1)

if __name__ == "__main__":
    dashboard = PerformanceDashboard()
    dashboard.run()
