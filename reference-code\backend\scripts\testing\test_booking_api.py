#!/usr/bin/env python3
"""
Test Booking API Endpoints
Simple API test without Django imports
"""
import requests
import json

API_BASE_URL = 'http://192.168.2.65:8000/api'


def get_auth_token():
    """Get authentication token"""
    login_data = {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }

    try:
        response = requests.post(
            f'{API_BASE_URL}/auth/login/', json=login_data, timeout=10)
        print(f'Login response: {response.status_code}')
        if response.status_code == 200:
            return response.json().get('access')
        else:
            print(f'Login failed: {response.text}')
            return None
    except Exception as e:
        print(f'Login error: {e}')
        return None


def test_booking_endpoints():
    """Test booking API endpoints"""
    print('🔖 Testing Booking API Endpoints')
    print('=' * 50)

    # Get auth token
    token = get_auth_token()
    if not token:
        print('❌ Failed to get auth token')
        return

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    # Test endpoints
    endpoints = [
        ('GET', '/bookings/bookings/', 'List bookings'),
        ('GET', '/bookings/bookings/upcoming/', 'Upcoming bookings'),
        ('GET', '/bookings/bookings/today/', 'Today bookings'),
        ('GET', '/bookings/bookings/history/', 'Booking history'),
        ('GET', '/bookings/bookings/stats/', 'Booking stats'),
        ('GET', '/bookings/time-slots/', 'List time slots'),
        ('GET', '/bookings/time-slots/available/', 'Available time slots'),
        ('GET', '/bookings/notifications/', 'Booking notifications'),
    ]

    for method, endpoint, description in endpoints:
        try:
            url = f'{API_BASE_URL}{endpoint}'
            response = requests.get(url, headers=headers)

            status_icon = '✅' if response.status_code in [200, 201] else '❌'
            print(f'{status_icon} {description}: {response.status_code}')

            if response.status_code not in [200, 201]:
                try:
                    error_data = response.json()
                    print(f'   Error: {error_data}')
                except:
                    print(f'   Error: {response.text[:100]}')

        except Exception as e:
            print(f'❌ {description}: Error - {e}')

    print('\n🎯 Booking API Test Complete!')


if __name__ == '__main__':
    test_booking_endpoints()
