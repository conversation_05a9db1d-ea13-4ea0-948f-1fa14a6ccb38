#!/usr/bin/env python
"""
Startup Data Verification Script for Vierla Backend
Verifies that essential data is properly loaded during backend startup
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceCategory
from django.core.management.color import make_style
from django.utils.text import slugify

style = make_style()

def verify_service_categories():
    """Verify that service categories are properly loaded"""
    print(style.HTTP_INFO("🔍 Verifying Service Categories..."))
    
    categories = ServiceCategory.objects.all().order_by('sort_order')
    total_count = categories.count()
    
    if total_count == 0:
        print(style.ERROR("❌ No service categories found!"))
        print(style.WARNING("   Run: python manage.py migrate catalog"))
        return False
    
    print(style.SUCCESS(f"✅ Found {total_count} service categories:"))
    
    # Expected categories (8 required categories)
    expected_categories = [
        'Barbers', 'Makeup', 'Salons', 'Locs',
        'Braids', 'Nails', 'Eyebrows', 'Eyelashes'
    ]
    
    found_categories = []
    for category in categories:
        status = "🔥" if category.is_popular else "📋"
        print(f"   {status} {category.name} ({category.icon}) - {category.color}")
        found_categories.append(category.name)
    
    # Check for missing expected categories and create them
    missing = set(expected_categories) - set(found_categories)
    if missing:
        print(style.WARNING(f"⚠️  Missing expected categories: {', '.join(missing)}"))
        print(style.HTTP_INFO("🔧 Creating missing categories..."))

        # Category data for missing categories
        category_data = {
            'Barbers': {'icon': '✂️', 'color': '#2A4B32', 'description': 'Professional barbering services including haircuts, beard trims, and grooming', 'sort_order': 1},
            'Makeup': {'icon': '💄', 'color': '#E91E63', 'description': 'Professional makeup services for special events, photoshoots, and everyday looks', 'sort_order': 2},
            'Salons': {'icon': '💇‍♀️', 'color': '#8FBC8F', 'description': 'Full-service hair salons offering cuts, styling, coloring, and treatments', 'sort_order': 3},
            'Locs': {'icon': '🌀', 'color': '#43e97b', 'description': 'Loc maintenance, installation, and styling services', 'sort_order': 4},
            'Braids': {'icon': '🤎', 'color': '#4facfe', 'description': 'Professional braiding services including protective styles and creative designs', 'sort_order': 5},
            'Nails': {'icon': '💅', 'color': '#9C27B0', 'description': 'Nail care services including manicures, pedicures, and nail art', 'sort_order': 6},
            'Eyebrows': {'icon': '👁️', 'color': '#673AB7', 'description': 'Eyebrow shaping, threading, waxing, and microblading services', 'sort_order': 7},
            'Eyelashes': {'icon': '👁️‍🗨️', 'color': '#f093fb', 'description': 'Eyelash extensions, lifts, tinting, and enhancement services', 'sort_order': 8}
        }

        created_count = 0
        for category_name in missing:
            if category_name in category_data:
                data = category_data[category_name]
                category = ServiceCategory.objects.create(
                    name=category_name,
                    slug=slugify(category_name),
                    icon=data['icon'],
                    color=data['color'],
                    description=data['description'],
                    sort_order=data['sort_order'],
                    is_active=True,
                    is_popular=True
                )
                created_count += 1
                print(style.SUCCESS(f"   ✨ Created category: {category_name}"))

        print(style.SUCCESS(f"✅ Created {created_count} missing categories"))
    else:
        print(style.SUCCESS("✅ All expected categories are present"))

    return True

def verify_database_connection():
    """Verify database connection is working"""
    print(style.HTTP_INFO("🔍 Verifying Database Connection..."))
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                print(style.SUCCESS("✅ Database connection successful"))
                return True
    except Exception as e:
        print(style.ERROR(f"❌ Database connection failed: {e}"))
        return False

def verify_api_endpoints():
    """Verify that API endpoints are properly configured"""
    print(style.HTTP_INFO("🔍 Verifying API Endpoints..."))

    try:
        from django.test import Client

        client = Client()

        # Test categories endpoint directly
        try:
            response = client.get('/api/catalog/categories/')
            if response.status_code in [200, 401]:  # 401 is OK for auth-required endpoints
                print(style.SUCCESS(f"✅ Categories endpoint accessible: /api/catalog/categories/"))
                print(style.SUCCESS(f"   Response status: {response.status_code}"))
            else:
                print(style.ERROR(f"❌ Categories endpoint returned {response.status_code}"))
                return False
        except Exception as e:
            print(style.ERROR(f"❌ Categories endpoint test failed: {e}"))
            return False

        return True
    except Exception as e:
        print(style.ERROR(f"❌ API endpoint verification failed: {e}"))
        return False

def main():
    """Main verification function"""
    print(style.HTTP_INFO("=" * 60))
    print(style.HTTP_INFO("🚀 Vierla Backend Startup Data Verification"))
    print(style.HTTP_INFO("=" * 60))
    
    checks = [
        ("Database Connection", verify_database_connection),
        ("Service Categories", verify_service_categories),
        ("API Endpoints", verify_api_endpoints),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}:")
        if check_func():
            passed += 1
        print()
    
    print(style.HTTP_INFO("=" * 60))
    if passed == total:
        print(style.SUCCESS(f"🎉 All checks passed! ({passed}/{total})"))
        print(style.SUCCESS("✅ Backend startup data is properly configured"))
    else:
        print(style.ERROR(f"❌ Some checks failed ({passed}/{total})"))
        print(style.WARNING("⚠️  Please review the issues above"))
    print(style.HTTP_INFO("=" * 60))
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
