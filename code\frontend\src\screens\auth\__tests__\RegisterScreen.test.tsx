/**
 * RegisterScreen Test Suite
 * Simplified tests for user registration functionality
 */

import React from 'react';
import { RegisterScreen } from '../RegisterScreen';

// Mock React Native components
jest.mock('react-native', () => ({
  View: 'View',
  Text: 'Text',
  ScrollView: 'ScrollView',
  KeyboardAvoidingView: 'KeyboardAvoidingView',
  Platform: { OS: 'ios' },
  Alert: { alert: jest.fn() },
  StyleSheet: { create: (styles: any) => styles },
}));

// Mock the navigation prop
const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

describe('RegisterScreen', () => {
  it('renders without crashing', () => {
    expect(() => {
      React.createElement(RegisterScreen, { navigation: mockNavigation });
    }).not.toThrow();
  });

  it('accepts navigation prop', () => {
    const element = React.createElement(RegisterScreen, { navigation: mockNavigation });
    expect(element.props.navigation).toBe(mockNavigation);
  });
});
