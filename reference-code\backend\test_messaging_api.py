#!/usr/bin/env python
"""
Script to test messaging API endpoints
"""
import os
import sys
import django
import requests
import json

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def test_messaging_api():
    """Test messaging API endpoints"""
    
    base_url = "http://************:8000/api/messaging"
    
    # First, login to get access token
    login_url = "http://************:8000/api/auth/login/"
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    print("=== MESSAGING API TEST ===\n")
    
    try:
        # Login
        login_response = requests.post(login_url, json=login_data, timeout=10)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
        
        access_token = login_response.json().get('access')
        headers = {'Authorization': f'Bearer {access_token}'}
        
        print("✅ Login successful")
        
        # Test 1: Get conversations (should be empty initially)
        print("\n1. Testing GET /conversations/")
        conversations_response = requests.get(f"{base_url}/conversations/", headers=headers, timeout=10)
        print(f"Status: {conversations_response.status_code}")
        if conversations_response.status_code == 200:
            conversations = conversations_response.json()
            print(f"✅ Conversations retrieved: {len(conversations)} conversations")
        else:
            print(f"❌ Failed to get conversations: {conversations_response.text}")
        
        # Test 2: Create a conversation
        print("\n2. Testing POST /conversations/")
        
        # Get another user to create conversation with
        other_user = User.objects.exclude(email="<EMAIL>").first()
        if not other_user:
            print("❌ No other users found to create conversation with")
            return False
        
        conversation_data = {
            "participants": [other_user.id],
            "conversation_type": "general",
            "title": "Test Conversation"
        }
        
        create_response = requests.post(f"{base_url}/conversations/", 
                                      json=conversation_data, headers=headers, timeout=10)
        print(f"Status: {create_response.status_code}")
        
        if create_response.status_code == 201:
            conversation = create_response.json()
            conversation_id = conversation['id']
            print(f"✅ Conversation created with ID: {conversation_id}")
            
            # Test 3: Send a message
            print(f"\n3. Testing POST /conversations/{conversation_id}/send_message/")
            message_data = {
                "content": "Hello! This is a test message.",
                "message_type": "text"
            }
            
            message_response = requests.post(f"{base_url}/conversations/{conversation_id}/send_message/", 
                                           json=message_data, headers=headers, timeout=10)
            print(f"Status: {message_response.status_code}")
            
            if message_response.status_code == 201:
                message = message_response.json()
                print(f"✅ Message sent with ID: {message['id']}")
                
                # Test 4: Get messages for conversation
                print(f"\n4. Testing GET /conversations/{conversation_id}/messages/")
                messages_response = requests.get(f"{base_url}/conversations/{conversation_id}/messages/", 
                                               headers=headers, timeout=10)
                print(f"Status: {messages_response.status_code}")
                
                if messages_response.status_code == 200:
                    messages_data = messages_response.json()
                    messages = messages_data.get('messages', [])
                    print(f"✅ Retrieved {len(messages)} messages")
                    if messages:
                        print(f"First message: {messages[0]['content']}")
                else:
                    print(f"❌ Failed to get messages: {messages_response.text}")
                
            else:
                print(f"❌ Failed to send message: {message_response.text}")
        else:
            print(f"❌ Failed to create conversation: {create_response.text}")
        
        print("\n=== MESSAGING API TEST COMPLETE ===")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

if __name__ == '__main__':
    test_messaging_api()
