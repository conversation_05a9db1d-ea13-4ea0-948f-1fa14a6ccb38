"""
Performance Monitoring Services
Provides comprehensive performance monitoring and analytics
"""
import time
import psutil
import threading
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.db.models import Avg, Count, Max, Min
from decimal import Decimal
from .models import (
    PerformanceMetric, SystemHealthMetric, BusinessMetric, 
    RealTimeMetric, PerformanceAlert, PerformanceDashboard
)
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitoringService:
    """
    Core service for performance monitoring and metrics collection
    """
    
    def __init__(self):
        self.monitoring_active = True
        self.collection_interval = 10  # seconds
    
    def start_monitoring(self):
        """Start continuous performance monitoring"""
        if self.monitoring_active:
            threading.Thread(target=self._continuous_monitoring, daemon=True).start()
            logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        logger.info("Performance monitoring stopped")
    
    def _continuous_monitoring(self):
        """Continuous monitoring loop"""
        while self.monitoring_active:
            try:
                self.collect_system_metrics()
                self.collect_realtime_metrics()
                self.analyze_performance_trends()
                time.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error in continuous monitoring: {e}")
                time.sleep(self.collection_interval)
    
    def collect_system_metrics(self):
        """Collect system-wide health metrics"""
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)
            SystemHealthMetric.objects.create(
                health_type='cpu_usage',
                value=Decimal(str(cpu_percent)),
                unit='percentage'
            )
            
            # Memory Usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            SystemHealthMetric.objects.create(
                health_type='memory_usage',
                value=Decimal(str(memory_percent)),
                unit='percentage',
                metadata={
                    'total_gb': round(memory.total / (1024**3), 2),
                    'available_gb': round(memory.available / (1024**3), 2),
                    'used_gb': round(memory.used / (1024**3), 2)
                }
            )
            
            # Disk Usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            SystemHealthMetric.objects.create(
                health_type='disk_usage',
                value=Decimal(str(disk_percent)),
                unit='percentage',
                metadata={
                    'total_gb': round(disk.total / (1024**3), 2),
                    'used_gb': round(disk.used / (1024**3), 2),
                    'free_gb': round(disk.free / (1024**3), 2)
                }
            )
            
            # Database Connections
            with connection.cursor() as cursor:
                cursor.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active';")
                active_connections = cursor.fetchone()[0]
                
            SystemHealthMetric.objects.create(
                health_type='active_connections',
                value=Decimal(str(active_connections)),
                unit='count'
            )
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def collect_realtime_metrics(self):
        """Collect real-time performance metrics"""
        try:
            current_time = timezone.now()
            one_minute_ago = current_time - timedelta(minutes=1)
            
            # Calculate 1-minute averages
            recent_metrics = PerformanceMetric.objects.filter(
                timestamp__gte=one_minute_ago,
                metric_type='api_latency'
            )
            
            if recent_metrics.exists():
                avg_latency = recent_metrics.aggregate(avg=Avg('value'))['avg']
                RealTimeMetric.objects.create(
                    metric_type='avg_response_time_1min',
                    value=avg_latency,
                    unit='ms'
                )
                
                # Error rate calculation
                total_requests = recent_metrics.count()
                error_requests = recent_metrics.filter(status_code__gte=400).count()
                error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0
                
                RealTimeMetric.objects.create(
                    metric_type='error_rate_1min',
                    value=Decimal(str(error_rate)),
                    unit='percentage'
                )
                
                # RPS calculation
                rps = total_requests / 60  # requests per second
                RealTimeMetric.objects.create(
                    metric_type='current_rps',
                    value=Decimal(str(rps)),
                    unit='rps'
                )
            
        except Exception as e:
            logger.error(f"Error collecting real-time metrics: {e}")
    
    def analyze_performance_trends(self):
        """Analyze performance trends and generate insights"""
        try:
            current_time = timezone.now()
            one_hour_ago = current_time - timedelta(hours=1)
            
            # Analyze API latency trends
            latency_metrics = PerformanceMetric.objects.filter(
                timestamp__gte=one_hour_ago,
                metric_type='api_latency'
            )
            
            if latency_metrics.exists():
                avg_latency = latency_metrics.aggregate(avg=Avg('value'))['avg']
                max_latency = latency_metrics.aggregate(max=Max('value'))['max']
                
                # Check for performance degradation
                if avg_latency > 1000:  # >1 second average
                    self._create_performance_alert(
                        'high_latency',
                        'high',
                        1000,
                        avg_latency,
                        'ms',
                        f"High average API latency detected: {avg_latency:.2f}ms over the last hour"
                    )
            
        except Exception as e:
            logger.error(f"Error analyzing performance trends: {e}")
    
    def _create_performance_alert(self, alert_type, severity, threshold, actual, unit, message):
        """Create a performance alert"""
        try:
            # Check if similar alert already exists and is active
            existing_alert = PerformanceAlert.objects.filter(
                alert_type=alert_type,
                status='active',
                triggered_at__gte=timezone.now() - timedelta(minutes=30)
            ).first()
            
            if not existing_alert:
                PerformanceAlert.objects.create(
                    alert_type=alert_type,
                    severity=severity,
                    threshold_value=threshold,
                    actual_value=actual,
                    unit=unit,
                    message=message
                )
                logger.warning(f"Performance alert created: {message}")
                
        except Exception as e:
            logger.error(f"Error creating performance alert: {e}")
    
    def get_performance_summary(self, hours=24):
        """Get performance summary for the last N hours"""
        try:
            end_time = timezone.now()
            start_time = end_time - timedelta(hours=hours)
            
            # API Performance Summary
            api_metrics = PerformanceMetric.objects.filter(
                timestamp__gte=start_time,
                metric_type='api_latency'
            )
            
            api_summary = {
                'total_requests': api_metrics.count(),
                'avg_latency_ms': api_metrics.aggregate(avg=Avg('value'))['avg'] or 0,
                'max_latency_ms': api_metrics.aggregate(max=Max('value'))['max'] or 0,
                'min_latency_ms': api_metrics.aggregate(min=Min('value'))['min'] or 0,
                'error_rate': self._calculate_error_rate(api_metrics),
            }
            
            # System Health Summary
            system_metrics = SystemHealthMetric.objects.filter(
                timestamp__gte=start_time
            )
            
            system_summary = {}
            for health_type in ['cpu_usage', 'memory_usage', 'disk_usage']:
                metrics = system_metrics.filter(health_type=health_type)
                if metrics.exists():
                    system_summary[health_type] = {
                        'avg': metrics.aggregate(avg=Avg('value'))['avg'],
                        'max': metrics.aggregate(max=Max('value'))['max'],
                        'current': metrics.latest('timestamp').value
                    }
            
            # Active Alerts
            active_alerts = PerformanceAlert.objects.filter(
                status='active',
                triggered_at__gte=start_time
            ).count()
            
            return {
                'api_performance': api_summary,
                'system_health': system_summary,
                'active_alerts': active_alerts,
                'monitoring_period_hours': hours,
                'generated_at': timezone.now()
            }
            
        except Exception as e:
            logger.error(f"Error generating performance summary: {e}")
            return {}
    
    def _calculate_error_rate(self, metrics):
        """Calculate error rate from metrics"""
        total = metrics.count()
        if total == 0:
            return 0
        errors = metrics.filter(status_code__gte=400).count()
        return (errors / total) * 100
    
    def get_dashboard_data(self, dashboard_type='system_overview'):
        """Get data for performance dashboard"""
        try:
            current_time = timezone.now()
            
            if dashboard_type == 'system_overview':
                return self._get_system_overview_data(current_time)
            elif dashboard_type == 'api_performance':
                return self._get_api_performance_data(current_time)
            elif dashboard_type == 'real_time_monitoring':
                return self._get_realtime_monitoring_data(current_time)
            else:
                return {}
                
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return {}
    
    def _get_system_overview_data(self, current_time):
        """Get system overview dashboard data"""
        one_hour_ago = current_time - timedelta(hours=1)
        
        # Latest system metrics
        latest_cpu = SystemHealthMetric.objects.filter(
            health_type='cpu_usage'
        ).latest('timestamp').value if SystemHealthMetric.objects.filter(health_type='cpu_usage').exists() else 0
        
        latest_memory = SystemHealthMetric.objects.filter(
            health_type='memory_usage'
        ).latest('timestamp').value if SystemHealthMetric.objects.filter(health_type='memory_usage').exists() else 0
        
        # Recent performance
        recent_latency = PerformanceMetric.objects.filter(
            timestamp__gte=one_hour_ago,
            metric_type='api_latency'
        ).aggregate(avg=Avg('value'))['avg'] or 0
        
        # Active alerts
        active_alerts = PerformanceAlert.objects.filter(status='active').count()
        
        return {
            'cpu_usage': float(latest_cpu),
            'memory_usage': float(latest_memory),
            'avg_response_time': float(recent_latency),
            'active_alerts': active_alerts,
            'timestamp': current_time
        }
    
    def _get_api_performance_data(self, current_time):
        """Get API performance dashboard data"""
        one_hour_ago = current_time - timedelta(hours=1)
        
        metrics = PerformanceMetric.objects.filter(
            timestamp__gte=one_hour_ago,
            metric_type='api_latency'
        )
        
        return {
            'total_requests': metrics.count(),
            'avg_latency': float(metrics.aggregate(avg=Avg('value'))['avg'] or 0),
            'error_rate': self._calculate_error_rate(metrics),
            'rps': metrics.count() / 3600,  # requests per second over the hour
            'timestamp': current_time
        }
    
    def _get_realtime_monitoring_data(self, current_time):
        """Get real-time monitoring dashboard data"""
        five_minutes_ago = current_time - timedelta(minutes=5)
        
        realtime_metrics = RealTimeMetric.objects.filter(
            timestamp__gte=five_minutes_ago
        )
        
        data = {}
        for metric in realtime_metrics:
            if metric.metric_type not in data:
                data[metric.metric_type] = []
            data[metric.metric_type].append({
                'value': float(metric.value),
                'timestamp': metric.timestamp
            })
        
        return data
