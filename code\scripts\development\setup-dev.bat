@echo off
REM ============================================================================
REM Vierla Development Environment Setup Script (Windows)
REM ============================================================================
REM This script sets up the development environment for the Vierla application
REM including virtual environment, dependencies, and database initialization.
REM
REM Usage: .\scripts\development\setup-dev.bat
REM ============================================================================

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_NAME=Vierla Development Setup
set SCRIPT_VERSION=1.0.0
set LOG_FILE=logs\setup-dev.log
set BACKEND_DIR=code\backend
set FRONTEND_DIR=code\frontend

REM Colors for output (Windows)
set COLOR_GREEN=[92m
set COLOR_YELLOW=[93m
set COLOR_RED=[91m
set COLOR_BLUE=[94m
set COLOR_RESET=[0m

echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo %COLOR_BLUE%%SCRIPT_NAME% v%SCRIPT_VERSION%%COLOR_RESET%
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo.

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Log function
call :log "INFO" "Starting development environment setup"

REM Check if we're in the correct directory
if not exist "%BACKEND_DIR%" (
    call :log "ERROR" "Backend directory not found. Please run from project root."
    echo %COLOR_RED%Error: Please run this script from the project root directory.%COLOR_RESET%
    exit /b 1
)

REM Check Python installation
call :log "INFO" "Checking Python installation"
echo %COLOR_YELLOW%Checking Python installation...%COLOR_RESET%
python --version >nul 2>&1
if errorlevel 1 (
    call :log "ERROR" "Python not found in PATH"
    echo %COLOR_RED%Error: Python is not installed or not in PATH.%COLOR_RESET%
    echo Please install Python 3.11+ from https://python.org
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
call :log "INFO" "Python version: %PYTHON_VERSION%"
echo %COLOR_GREEN%Python %PYTHON_VERSION% found%COLOR_RESET%

REM Navigate to backend directory
cd %BACKEND_DIR%

REM Create virtual environment
call :log "INFO" "Creating virtual environment"
echo %COLOR_YELLOW%Creating virtual environment...%COLOR_RESET%
if exist venv (
    echo %COLOR_YELLOW%Virtual environment already exists. Removing old one...%COLOR_RESET%
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    call :log "ERROR" "Failed to create virtual environment"
    echo %COLOR_RED%Error: Failed to create virtual environment%COLOR_RESET%
    exit /b 1
)

REM Activate virtual environment
call :log "INFO" "Activating virtual environment"
echo %COLOR_YELLOW%Activating virtual environment...%COLOR_RESET%
call venv\Scripts\activate.bat

REM Upgrade pip
call :log "INFO" "Upgrading pip"
echo %COLOR_YELLOW%Upgrading pip...%COLOR_RESET%
python -m pip install --upgrade pip

REM Install Python dependencies
call :log "INFO" "Installing Python dependencies"
echo %COLOR_YELLOW%Installing Python dependencies...%COLOR_RESET%
pip install -r requirements.txt
if errorlevel 1 (
    call :log "ERROR" "Failed to install Python dependencies"
    echo %COLOR_RED%Error: Failed to install Python dependencies%COLOR_RESET%
    exit /b 1
)

REM Run database migrations
call :log "INFO" "Running database migrations"
echo %COLOR_YELLOW%Running database migrations...%COLOR_RESET%
python manage.py makemigrations
python manage.py migrate
if errorlevel 1 (
    call :log "ERROR" "Failed to run database migrations"
    echo %COLOR_RED%Error: Failed to run database migrations%COLOR_RESET%
    exit /b 1
)

REM Create superuser (optional)
echo %COLOR_YELLOW%Would you like to create a superuser account? (y/n)%COLOR_RESET%
set /p CREATE_SUPERUSER=
if /i "%CREATE_SUPERUSER%"=="y" (
    call :log "INFO" "Creating superuser account"
    echo %COLOR_YELLOW%Creating superuser account...%COLOR_RESET%
    python manage.py createsuperuser
)

REM Navigate back to project root
cd ..\..

REM Setup frontend (if exists)
if exist "%FRONTEND_DIR%" (
    call :log "INFO" "Setting up frontend dependencies"
    echo %COLOR_YELLOW%Setting up frontend dependencies...%COLOR_RESET%
    cd %FRONTEND_DIR%
    
    REM Check if Node.js is installed
    node --version >nul 2>&1
    if errorlevel 1 (
        call :log "WARNING" "Node.js not found, skipping frontend setup"
        echo %COLOR_YELLOW%Warning: Node.js not found. Frontend setup skipped.%COLOR_RESET%
    ) else (
        npm install
        if errorlevel 1 (
            call :log "ERROR" "Failed to install frontend dependencies"
            echo %COLOR_RED%Error: Failed to install frontend dependencies%COLOR_RESET%
        ) else (
            call :log "INFO" "Frontend dependencies installed successfully"
            echo %COLOR_GREEN%Frontend dependencies installed successfully%COLOR_RESET%
        )
    )
    
    cd ..\..
)

REM Create environment file template
call :log "INFO" "Creating environment configuration"
echo %COLOR_YELLOW%Creating environment configuration...%COLOR_RESET%
if not exist "%BACKEND_DIR%\.env" (
    echo # Vierla Development Environment Configuration > "%BACKEND_DIR%\.env"
    echo # Generated by setup-dev.bat on %date% %time% >> "%BACKEND_DIR%\.env"
    echo. >> "%BACKEND_DIR%\.env"
    echo # Django Settings >> "%BACKEND_DIR%\.env"
    echo DEBUG=True >> "%BACKEND_DIR%\.env"
    echo SECRET_KEY=dev-secret-key-change-in-production >> "%BACKEND_DIR%\.env"
    echo ALLOWED_HOSTS=localhost,127.0.0.1 >> "%BACKEND_DIR%\.env"
    echo. >> "%BACKEND_DIR%\.env"
    echo # Database Settings >> "%BACKEND_DIR%\.env"
    echo USE_SQLITE=True >> "%BACKEND_DIR%\.env"
    echo DATABASE_URL=sqlite:///db.sqlite3 >> "%BACKEND_DIR%\.env"
    echo. >> "%BACKEND_DIR%\.env"
    echo # Server Settings >> "%BACKEND_DIR%\.env"
    echo VIERLA_HOST=localhost >> "%BACKEND_DIR%\.env"
    echo VIERLA_PORT=8000 >> "%BACKEND_DIR%\.env"
    
    call :log "INFO" "Environment file created at %BACKEND_DIR%\.env"
    echo %COLOR_GREEN%Environment file created at %BACKEND_DIR%\.env%COLOR_RESET%
)

REM Success message
call :log "INFO" "Development environment setup completed successfully"
echo.
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo %COLOR_GREEN%Development Environment Setup Complete!%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.
echo %COLOR_YELLOW%Next Steps:%COLOR_RESET%
echo 1. Start the backend server: %COLOR_BLUE%.\scripts\development\start-backend.bat%COLOR_RESET%
echo 2. Access the application at: %COLOR_BLUE%http://localhost:8000%COLOR_RESET%
echo 3. Access the admin interface at: %COLOR_BLUE%http://localhost:8000/admin%COLOR_RESET%
echo 4. View API documentation at: %COLOR_BLUE%http://localhost:8000/api/docs%COLOR_RESET%
echo.
echo %COLOR_YELLOW%Configuration:%COLOR_RESET%
echo - Environment file: %COLOR_BLUE%%BACKEND_DIR%\.env%COLOR_RESET%
echo - Virtual environment: %COLOR_BLUE%%BACKEND_DIR%\venv%COLOR_RESET%
echo - Log file: %COLOR_BLUE%%LOG_FILE%%COLOR_RESET%
echo.

exit /b 0

REM ============================================================================
REM Helper Functions
REM ============================================================================

:log
REM Log function: call :log "LEVEL" "MESSAGE"
set LOG_LEVEL=%~1
set LOG_MESSAGE=%~2
set LOG_TIMESTAMP=%date% %time%
echo [%LOG_TIMESTAMP%] [%LOG_LEVEL%] %LOG_MESSAGE% >> %LOG_FILE%
goto :eof
