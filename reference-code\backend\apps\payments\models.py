"""
Payment Models - MVP Critical Feature
Comprehensive payment system with Stripe integration
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid

User = get_user_model()


class PaymentMethod(models.Model):
    """User payment methods (cards, Apple Pay, Google Pay, etc.)"""

    PAYMENT_TYPE_CHOICES = [
        ('card', 'Credit/Debit Card'),
        ('apple_pay', 'Apple Pay'),
        ('google_pay', 'Google Pay'),
        ('paypal', 'PayPal'),
        ('bank_transfer', 'Bank Transfer'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_methods')

    # Payment method details
    type = models.CharField(max_length=20, choices=PAYMENT_TYPE_CHOICES)
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # Card-specific fields
    last_four = models.CharField(max_length=4, blank=True, null=True)
    brand = models.CharField(max_length=20, blank=True, null=True)  # visa, mastercard, amex
    expires_at = models.CharField(max_length=7, blank=True, null=True)  # MM/YY format

    # Stripe integration
    stripe_payment_method_id = models.CharField(max_length=255, blank=True, null=True)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payment_methods'
        ordering = ['-is_default', '-created_at']

    def __str__(self):
        if self.type == 'card' and self.last_four:
            return f"{self.brand} •••• {self.last_four}"
        return f"{self.get_type_display()}"

    def save(self, *args, **kwargs):
        # Ensure only one default payment method per user
        if self.is_default:
            PaymentMethod.objects.filter(
                user=self.user,
                is_default=True
            ).exclude(id=self.id).update(is_default=False)
        super().save(*args, **kwargs)


class PaymentIntent(models.Model):
    """Stripe Payment Intents for processing payments"""

    STATUS_CHOICES = [
        ('requires_payment_method', 'Requires Payment Method'),
        ('requires_confirmation', 'Requires Confirmation'),
        ('requires_action', 'Requires Action'),
        ('processing', 'Processing'),
        ('succeeded', 'Succeeded'),
        ('canceled', 'Canceled'),
        ('requires_capture', 'Requires Capture'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    stripe_payment_intent_id = models.CharField(max_length=255, unique=True)

    # Associated entities
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payment_intents')
    booking = models.ForeignKey('bookings.Booking', on_delete=models.CASCADE,
                               related_name='payment_intents', null=True, blank=True)

    # Payment details
    amount = models.DecimalField(max_digits=10, decimal_places=2,
                                validators=[MinValueValidator(Decimal('0.01'))])
    currency = models.CharField(max_length=3, default='CAD')
    status = models.CharField(max_length=30, choices=STATUS_CHOICES)

    # Stripe details
    client_secret = models.CharField(max_length=255)
    payment_method_id = models.CharField(max_length=255, blank=True, null=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'payment_intents'
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment Intent {self.stripe_payment_intent_id} - ${self.amount}"


class Transaction(models.Model):
    """Payment transactions and history"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
        ('partially_refunded', 'Partially Refunded'),
        ('canceled', 'Canceled'),
    ]

    TRANSACTION_TYPE_CHOICES = [
        ('payment', 'Payment'),
        ('refund', 'Refund'),
        ('partial_refund', 'Partial Refund'),
        ('chargeback', 'Chargeback'),
        ('fee', 'Fee'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Associated entities
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='transactions')
    booking = models.ForeignKey('bookings.Booking', on_delete=models.CASCADE,
                               related_name='transactions')
    payment_intent = models.ForeignKey(PaymentIntent, on_delete=models.CASCADE,
                                     related_name='transactions')
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.SET_NULL,
                                     null=True, blank=True, related_name='transactions')

    # Transaction details
    type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES, default='payment')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Financial details
    amount = models.DecimalField(max_digits=10, decimal_places=2,
                                validators=[MinValueValidator(Decimal('0.01'))])
    service_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2,
                                     validators=[MinValueValidator(Decimal('0.01'))])
    currency = models.CharField(max_length=3, default='CAD')

    # Stripe details
    stripe_charge_id = models.CharField(max_length=255, blank=True, null=True)
    stripe_refund_id = models.CharField(max_length=255, blank=True, null=True)

    # Processing details
    processed_at = models.DateTimeField(null=True, blank=True)
    failure_reason = models.TextField(blank=True, null=True)

    # Metadata
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'transactions'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['booking', 'type']),
            models.Index(fields=['stripe_charge_id']),
        ]

    def __str__(self):
        return f"Transaction {self.id} - {self.type} ${self.total_amount}"
