/**
 * API Client Tests
 * Tests for authentication API client configuration and interceptors
 */

import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient, API_BASE_URL } from '../../services/api/client';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock AsyncStorage
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.getItem.mockClear();
    mockAsyncStorage.setItem.mockClear();
    mockAsyncStorage.multiRemove.mockClear();
  });

  describe('Configuration', () => {
    it('should have correct base URL for development', () => {
      expect(API_BASE_URL).toBe('http://192.168.2.65:8000/api');
    });

    it('should have correct timeout configuration', () => {
      expect(apiClient.defaults.timeout).toBe(10000);
    });

    it('should have correct default headers', () => {
      expect(apiClient.defaults.headers['Content-Type']).toBe('application/json');
    });
  });

  describe('Request Interceptor', () => {
    it('should add authorization header when token exists', async () => {
      const mockToken = 'test-access-token';
      mockAsyncStorage.getItem.mockResolvedValue(mockToken);

      const config = {
        headers: {},
        url: '/test',
        method: 'get',
      };

      // Get the request interceptor
      const requestInterceptor = apiClient.interceptors.request.handlers[0];
      const result = await requestInterceptor.fulfilled(config);

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('access_token');
      expect(result.headers.Authorization).toBe(`Bearer ${mockToken}`);
    });

    it('should not add authorization header when token does not exist', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const config = {
        headers: {},
        url: '/test',
        method: 'get',
      };

      const requestInterceptor = apiClient.interceptors.request.handlers[0];
      const result = await requestInterceptor.fulfilled(config);

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('access_token');
      expect(result.headers.Authorization).toBeUndefined();
    });

    it('should handle AsyncStorage errors gracefully', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const config = {
        headers: {},
        url: '/test',
        method: 'get',
      };

      const requestInterceptor = apiClient.interceptors.request.handlers[0];
      const result = await requestInterceptor.fulfilled(config);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Error getting auth token:', expect.any(Error));
      expect(result.headers.Authorization).toBeUndefined();

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Response Interceptor', () => {
    it('should pass through successful responses', async () => {
      const mockResponse = {
        data: { message: 'success' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      };

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      const result = await responseInterceptor.fulfilled(mockResponse);

      expect(result).toBe(mockResponse);
    });

    it('should handle 401 errors with token refresh', async () => {
      const mockRefreshToken = 'test-refresh-token';
      const mockNewTokens = {
        access: 'new-access-token',
        refresh: 'new-refresh-token',
      };

      mockAsyncStorage.getItem.mockResolvedValue(mockRefreshToken);
      mockedAxios.post.mockResolvedValue({ data: mockNewTokens });

      const originalRequest = {
        headers: {},
        url: '/test',
        method: 'get',
        _retry: undefined,
      };

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      // Mock apiClient call for retry
      const mockRetryResponse = { data: { message: 'success' } };
      jest.spyOn(apiClient, 'request').mockResolvedValue(mockRetryResponse);

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      const result = await responseInterceptor.rejected(error);

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('refresh_token');
      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_BASE_URL}/auth/refresh/`,
        { refresh: mockRefreshToken }
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('access_token', mockNewTokens.access);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('refresh_token', mockNewTokens.refresh);
      expect(originalRequest.headers.Authorization).toBe(`Bearer ${mockNewTokens.access}`);
      expect(originalRequest._retry).toBe(true);
    });

    it('should handle refresh token failure', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockAsyncStorage.getItem.mockResolvedValue('invalid-refresh-token');
      mockedAxios.post.mockRejectedValue(new Error('Refresh failed'));

      const originalRequest = {
        headers: {},
        url: '/test',
        method: 'get',
        _retry: undefined,
      };

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      
      try {
        await responseInterceptor.rejected(error);
      } catch (rejectedError) {
        expect(rejectedError).toBe(error);
      }

      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Token refresh failed:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });

    it('should not retry 401 errors that have already been retried', async () => {
      const originalRequest = {
        headers: {},
        url: '/test',
        method: 'get',
        _retry: true, // Already retried
      };

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      
      try {
        await responseInterceptor.rejected(error);
      } catch (rejectedError) {
        expect(rejectedError).toBe(error);
      }

      expect(mockAsyncStorage.getItem).not.toHaveBeenCalled();
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('should pass through non-401 errors', async () => {
      const error = {
        response: { status: 500 },
        config: { url: '/test' },
      };

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      
      try {
        await responseInterceptor.rejected(error);
      } catch (rejectedError) {
        expect(rejectedError).toBe(error);
      }

      expect(mockAsyncStorage.getItem).not.toHaveBeenCalled();
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('should handle missing refresh token', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const originalRequest = {
        headers: {},
        url: '/test',
        method: 'get',
        _retry: undefined,
      };

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      const responseInterceptor = apiClient.interceptors.response.handlers[0];
      
      try {
        await responseInterceptor.rejected(error);
      } catch (rejectedError) {
        expect(rejectedError).toBe(error);
      }

      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('refresh_token');
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });
  });
});
