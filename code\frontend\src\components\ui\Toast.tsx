/**
 * Toast Component
 * shadcn/ui inspired toast notification system
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  ViewStyle,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { Text } from './Text';
import { colors, spacing, borderRadius } from '../../theme';
import { cn } from '../../lib/utils';

export interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
  onDismiss?: () => void;
  position?: 'top' | 'bottom';
  style?: ViewStyle;
  testID?: string;
}

export interface ToastContextType {
  toasts: ToastProps[];
  showToast: (toast: Omit<ToastProps, 'id'>) => string;
  dismissToast: (id: string) => void;
  dismissAllToasts: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const Toast: React.FC<ToastProps & { onAnimationComplete?: () => void }> = ({
  id,
  title,
  description,
  variant = 'default',
  duration = 4000,
  action,
  onDismiss,
  position = 'top',
  style,
  onAnimationComplete,
  testID = 'toast',
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(position === 'top' ? -100 : 100));

  useEffect(() => {
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, []);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: position === 'top' ? -100 : 100,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss?.();
      onAnimationComplete?.();
    });
  };

  const getIcon = () => {
    switch (variant) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'warning':
        return 'warning';
      case 'info':
        return 'information-circle';
      default:
        return 'notifications';
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'success':
        return colors.success;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      case 'info':
        return colors.info;
      default:
        return colors.primary;
    }
  };

  const toastStyle = cn(
    styles.toast,
    styles[variant],
    style
  );

  return (
    <Animated.View
      style={[
        toastStyle,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
      testID={testID}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={getIcon()}
            size={20}
            color={getIconColor()}
          />
        </View>
        
        <View style={styles.textContainer}>
          {title && (
            <Text variant="body" style={styles.title}>
              {title}
            </Text>
          )}
          {description && (
            <Text variant="caption" style={styles.description}>
              {description}
            </Text>
          )}
        </View>

        <View style={styles.actions}>
          {action && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={action.onPress}
            >
              <Text variant="caption" style={styles.actionText}>
                {action.label}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleDismiss}
            testID="toast-close-button"
          >
            <Ionicons
              name="close"
              size={16}
              color={colors.text.secondary}
            />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

export const ToastContainer: React.FC<{
  toasts: ToastProps[];
  onDismiss: (id: string) => void;
  position?: 'top' | 'bottom';
}> = ({
  toasts,
  onDismiss,
  position = 'top',
}) => {
  if (toasts.length === 0) return null;

  return (
    <View style={[styles.container, styles[`container_${position}`]]}>
      <SafeAreaView>
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            position={position}
            onDismiss={() => onDismiss(toast.id)}
            style={styles.toastSpacing}
          />
        ))}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 1000,
    paddingHorizontal: spacing.md,
  },
  container_top: {
    top: 0,
  },
  container_bottom: {
    bottom: 0,
  },
  toast: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.primaryLight,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxWidth: screenWidth - (spacing.md * 2),
  },
  default: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  success: {
    borderLeftWidth: 4,
    borderLeftColor: colors.success,
  },
  error: {
    borderLeftWidth: 4,
    borderLeftColor: colors.error,
  },
  warning: {
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  info: {
    borderLeftWidth: 4,
    borderLeftColor: colors.info,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: spacing.md,
  },
  iconContainer: {
    marginRight: spacing.sm,
    marginTop: 2,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing.xs / 2,
  },
  description: {
    color: colors.text.secondary,
    lineHeight: 18,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  actionButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    marginRight: spacing.xs,
  },
  actionText: {
    color: colors.primary,
    fontWeight: '600',
  },
  closeButton: {
    padding: spacing.xs,
  },
  toastSpacing: {
    marginBottom: spacing.sm,
  },
});
