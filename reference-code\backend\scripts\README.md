# Backend Scripts Organization

This directory contains organized scripts for various backend operations and testing.

## Directory Structure

### 📁 authentication/
Scripts related to user authentication and role management:
- `check_provider.py` - Check provider account status and permissions
- `check_role.py` - Verify user roles and access levels

### 📁 data_management/
Scripts for creating and managing test data:
- `create_comprehensive_test_data.py` - Create complete test dataset
- `create_provider_profile.py` - Create service provider profiles
- `create_services.py` - Create service catalog entries
- `enhance_providers.py` - Enhance existing provider data

### 📁 database/
Database-related scripts and utilities (currently empty - ready for future scripts)

### 📁 monitoring/
Performance and database monitoring scripts:
- `database_monitor.py` - Advanced database monitoring
- `performance_monitor.py` - Application performance monitoring
- `simple_db_monitor.py` - Basic database monitoring
- `simple_performance_monitor.py` - Basic performance monitoring

### 📁 testing/
Comprehensive test scripts for API endpoints and functionality:
- `test_auth_connection.py` - Test authentication connectivity
- `test_auth_quick.py` - Quick authentication tests
- `test_auth_simple.py` - Simple authentication tests
- `test_authentication.py` - Full authentication system tests
- `test_booking_system.py` - Booking system API tests
- `test_catalog_integration.py` - Service catalog integration tests
- `test_frontend_auth_flow.py` - Frontend-backend auth flow tests
- `test_location_search.py` - Location-based search tests
- `test_provider_access.py` - Provider access and permissions tests

## Usage

### Running Test Scripts
```bash
# From backend directory
cd scripts/testing
python test_authentication.py
python test_booking_system.py
```

### Running Monitoring Scripts
```bash
# From backend directory
cd scripts/monitoring
python performance_monitor.py
python database_monitor.py
```

### Creating Test Data
```bash
# From backend directory
cd scripts/data_management
python create_comprehensive_test_data.py
```

## Requirements

All scripts require the Django environment to be properly configured:
1. Virtual environment activated
2. Dependencies installed (`pip install -r requirements/development.txt`)
3. Django server accessible (for API testing scripts)

## Notes

- Test scripts assume the backend server is running on `http://localhost:8000` or `http://************:8000`
- Monitoring scripts can be run independently or integrated into CI/CD pipelines
- Data management scripts should be run with caution in production environments
