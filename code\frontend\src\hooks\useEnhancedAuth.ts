/**
 * Enhanced Authentication Hook
 * Improved authentication hook with better state management and error handling
 * Part of EPIC-05-CRITICAL: Authentication & UI Enhancement
 */

import { useState, useEffect, useCallback } from 'react';
import { authService, AuthState, LoginResult } from '../services/authService';
import { LoginRequest } from '../services/api/auth';

export interface UseEnhancedAuthReturn {
  // State
  isAuthenticated: boolean;
  user: AuthState['user'];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<LoginResult>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  
  // Utilities
  hasRole: (role: 'customer' | 'service_provider' | 'admin') => boolean;
  isCustomer: boolean;
  isProvider: boolean;
  isAdmin: boolean;
}

/**
 * Enhanced authentication hook with improved error handling and state management
 */
export const useEnhancedAuth = (): UseEnhancedAuthReturn => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    tokens: { access: null, refresh: null },
    isLoading: true,
    error: null,
  });

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const state = await authService.initialize();
        setAuthState(state);
      } catch (error) {
        console.error('Auth initialization error:', error);
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to initialize authentication',
        }));
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = useCallback(async (credentials: LoginRequest): Promise<LoginResult> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await authService.login(credentials);
      
      if (result.success) {
        const newState = authService.getAuthState();
        setAuthState(newState);
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Login failed',
        }));
      }
      
      return result;
    } catch (error) {
      const errorMessage = 'An unexpected error occurred during login';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }, []);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    
    try {
      await authService.logout();
      const newState = authService.getAuthState();
      setAuthState(newState);
    } catch (error) {
      console.error('Logout error:', error);
      // Still update state even if logout fails
      const newState = authService.getAuthState();
      setAuthState(newState);
    }
  }, []);

  // Refresh user data
  const refreshUser = useCallback(async (): Promise<void> => {
    try {
      // This would typically refresh user data from the server
      // For now, we'll just get the current state
      const currentState = authService.getAuthState();
      setAuthState(currentState);
    } catch (error) {
      console.error('Refresh user error:', error);
      setAuthState(prev => ({
        ...prev,
        error: 'Failed to refresh user data',
      }));
    }
  }, []);

  // Clear error
  const clearError = useCallback((): void => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // Check if user has specific role
  const hasRole = useCallback((role: 'customer' | 'service_provider' | 'admin'): boolean => {
    return authService.hasRole(role);
  }, [authState.user]);

  // Role-specific computed properties
  const isCustomer = authState.user?.role === 'customer';
  const isProvider = authState.user?.role === 'service_provider';
  const isAdmin = authState.user?.role === 'admin';

  return {
    // State
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    isLoading: authState.isLoading,
    error: authState.error,
    
    // Actions
    login,
    logout,
    refreshUser,
    clearError,
    
    // Utilities
    hasRole,
    isCustomer,
    isProvider,
    isAdmin,
  };
};

/**
 * Hook for authentication status only (lighter version)
 */
export const useAuthStatus = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const state = await authService.initialize();
        setIsAuthenticated(state.isAuthenticated);
      } catch (error) {
        console.error('Auth status check error:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  return { isAuthenticated, isLoading };
};

/**
 * Hook for user role information
 */
export const useUserRole = () => {
  const [role, setRole] = useState<'customer' | 'service_provider' | 'admin' | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getUserRole = async () => {
      try {
        const state = await authService.initialize();
        setRole(state.user?.role || null);
      } catch (error) {
        console.error('User role check error:', error);
        setRole(null);
      } finally {
        setIsLoading(false);
      }
    };

    getUserRole();
  }, []);

  return {
    role,
    isLoading,
    isCustomer: role === 'customer',
    isProvider: role === 'service_provider',
    isAdmin: role === 'admin',
  };
};

/**
 * Hook for authentication errors
 */
export const useAuthError = () => {
  const [error, setError] = useState<string | null>(null);

  const setAuthError = useCallback((errorMessage: string) => {
    setError(errorMessage);
  }, []);

  const clearAuthError = useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    setError: setAuthError,
    clearError: clearAuthError,
    hasError: !!error,
  };
};

export default useEnhancedAuth;
