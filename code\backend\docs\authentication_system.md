# Vierla Authentication System Documentation

## Overview

The Vierla authentication system provides comprehensive user authentication and authorization functionality for both customers and service providers. It includes traditional email/password authentication, social authentication (Google/Apple), email verification, password reset, and profile management.

## Architecture

### Backend Components

#### Models (`authentication/models.py`)
- **User**: Extended Django user model with additional fields
  - `role`: Customer or Provider
  - `phone`: Phone number with validation
  - `is_verified`: Email verification status
  - `account_status`: Active, suspended, pending_verification
  - `failed_login_attempts`: Security tracking
  - `last_failed_login`: Timestamp of last failed attempt

- **PasswordResetToken**: Secure password reset tokens
  - `token`: UUID-based reset token
  - `expires_at`: Token expiration timestamp
  - `is_used`: Usage tracking

#### API Endpoints (`authentication/urls.py`)

**Authentication Endpoints:**
- `POST /auth/login/` - Email/password login
- `POST /auth/register/` - User registration
- `POST /auth/logout/` - User logout
- `POST /auth/social/` - Social authentication (Google/Apple)
- `POST /auth/token/refresh/` - JWT token refresh

**Profile Management:**
- `GET /auth/profile/` - Get user profile
- `PATCH /auth/profile/update/` - Update user profile
- `GET /auth/status/` - Check authentication status

**Email Verification:**
- `POST /auth/verify-email/` - Verify email with token
- `POST /auth/resend-verification/` - Resend verification email

**Password Management:**
- `POST /auth/change-password/` - Change password (authenticated)
- `POST /auth/password/reset/` - Request password reset
- `POST /auth/password/reset/confirm/` - Confirm password reset

#### Security Features

1. **Account Lockout**: Automatic lockout after 5 failed login attempts
2. **JWT Authentication**: Secure token-based authentication
3. **Password Validation**: Django's built-in password validators
4. **Rate Limiting**: Protection against brute force attacks
5. **Secure Token Generation**: UUID-based tokens for password reset
6. **Email Verification**: Required for account activation

### Frontend Components

#### Navigation Structure
- **AppNavigator**: Root navigation with authentication state management
- **AuthNavigator**: Handles login/register screens
- **MainNavigator**: Tab-based navigation for authenticated users

#### Authentication Screens
- **LoginScreen**: Email/password login with social auth options
- **RegisterScreen**: User registration with validation
- **ForgotPasswordScreen**: Password reset request

#### Reusable Components
- **Button**: Styled button with loading states
- **Input**: Form input with validation and error display
- **Text**: Typography component with consistent styling
- **SocialButton**: Social authentication buttons

#### API Integration
- **authAPI**: Centralized authentication API service
- **React Query**: State management for API calls
- **AsyncStorage**: Secure token storage

## API Reference

### Authentication Endpoints

#### Login
```http
POST /auth/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "user": {
    "id": "1",
    "email": "<EMAIL>",
    "username": "user",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "role": "customer",
    "is_verified": true,
    "account_status": "active",
    "phone": "+**********",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

#### Register
```http
POST /auth/register/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "newuser",
  "password": "password123",
  "password_confirm": "password123",
  "first_name": "Jane",
  "last_name": "Smith",
  "phone": "+**********"
}
```

#### Social Authentication
```http
POST /auth/social/
Content-Type: application/json

{
  "provider": "google",
  "identity_token": "google_id_token",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe"
}
```

### Error Handling

#### Common Error Responses

**400 Bad Request:**
```json
{
  "email": ["This field is required."],
  "password": ["This field is required."]
}
```

**401 Unauthorized:**
```json
{
  "detail": "Invalid credentials"
}
```

**423 Locked:**
```json
{
  "detail": "Account locked due to multiple failed login attempts"
}
```

## Frontend Integration

### Authentication Flow

1. **App Initialization**: Check for stored tokens in AsyncStorage
2. **Login Process**: 
   - User enters credentials
   - API call to `/auth/login/`
   - Store tokens and user data
   - Navigate to main app
3. **Token Refresh**: Automatic refresh using stored refresh token
4. **Logout**: Clear stored data and navigate to auth screens

### State Management

The app uses React Query for API state management and AsyncStorage for persistent authentication state:

```typescript
// Check authentication status
const checkAuthStatus = async () => {
  const accessToken = await AsyncStorage.getItem('access_token');
  const user = await AsyncStorage.getItem('user');
  return accessToken && user;
};

// Store authentication data
const storeAuthData = async (response) => {
  await AsyncStorage.multiSet([
    ['access_token', response.access],
    ['refresh_token', response.refresh],
    ['user', JSON.stringify(response.user)],
  ]);
};
```

## Testing

### Backend Tests
- **68 test cases** covering all authentication functionality
- **Unit tests** for models, serializers, and views
- **Integration tests** for complete authentication flows
- **Security tests** for account lockout and token validation

### Frontend Tests
- **Jest configuration** for React Native testing
- **API service tests** for authentication endpoints
- **Component tests** for UI elements
- **Mock implementations** for external dependencies

## Security Considerations

1. **Password Security**: Strong password requirements and hashing
2. **Token Security**: JWT tokens with appropriate expiration
3. **Account Protection**: Automatic lockout after failed attempts
4. **Data Validation**: Comprehensive input validation and sanitization
5. **CORS Configuration**: Proper cross-origin request handling
6. **HTTPS Only**: All authentication endpoints require HTTPS in production

## Deployment Notes

### Environment Variables
- `SECRET_KEY`: Django secret key
- `DEBUG`: Debug mode (False in production)
- `ALLOWED_HOSTS`: Allowed hostnames
- `DATABASE_URL`: Database connection string
- `CORS_ALLOWED_ORIGINS`: Frontend URLs

### Database Migrations
```bash
python manage.py makemigrations authentication
python manage.py migrate
```

### Frontend Build
```bash
npm run build
expo build:web
```

## Future Enhancements

1. **Two-Factor Authentication**: SMS/TOTP support
2. **OAuth Providers**: Additional social login options
3. **Session Management**: Advanced session handling
4. **Audit Logging**: Comprehensive authentication logging
5. **Password Policies**: Configurable password requirements
