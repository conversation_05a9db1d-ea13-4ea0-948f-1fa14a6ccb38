/**
 * Utility functions for component styling and class management
 * Based on shadcn/ui patterns for React Native
 */

/**
 * Simple class name utility function
 * Combines multiple class names and filters out falsy values
 */
export function cn(...inputs: (string | undefined | null | false)[]): string {
  return inputs.filter(Boolean).join(' ');
}

/**
 * Merge style objects with proper precedence
 * Later styles override earlier ones
 */
export function mergeStyles<T>(...styles: (T | undefined | null | false)[]): T {
  return Object.assign({}, ...styles.filter(Boolean)) as T;
}

/**
 * Create variant-based style function
 * Similar to class-variance-authority but for React Native styles
 */
export function createVariants<T extends Record<string, any>>(config: {
  base: T;
  variants: Record<string, Record<string, Partial<T>>>;
  defaultVariants?: Record<string, string>;
}) {
  return function getVariantStyles(props: Record<string, string> = {}): T {
    const mergedProps = { ...config.defaultVariants, ...props };

    let variantStyles = {};

    Object.entries(mergedProps).forEach(([key, value]) => {
      if (config.variants[key] && config.variants[key][value]) {
        variantStyles = { ...variantStyles, ...config.variants[key][value] };
      }
    });

    return { ...config.base, ...variantStyles } as T;
  };
}

/**
 * Conditional style application
 */
export function conditionalStyle<T>(condition: boolean, trueStyle: T, falseStyle?: T): T | undefined {
  return condition ? trueStyle : falseStyle;
}

/**
 * Color utilities for consistent theming
 */
export const colorUtils = {
  /**
   * Add opacity to a hex color
   */
  addOpacity: (color: string, opacity: number): string => {
    const opacityHex = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return `${color}${opacityHex}`;
  },
  
  /**
   * Lighten a color (simple implementation)
   */
  lighten: (color: string, amount: number): string => {
    // Simple implementation - in a real app you'd use a proper color library
    return color;
  },
  
  /**
   * Darken a color (simple implementation)
   */
  darken: (color: string, amount: number): string => {
    // Simple implementation - in a real app you'd use a proper color library
    return color;
  },
};

/**
 * Animation utilities
 */
export const animationUtils = {
  /**
   * Standard easing curves
   */
  easing: {
    easeInOut: 'ease-in-out',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    linear: 'linear',
  },
  
  /**
   * Standard durations
   */
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
};

/**
 * Spacing utilities
 */
export const spacingUtils = {
  /**
   * Get spacing value by size
   */
  getSpacing: (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'): number => {
    const spacingMap = {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    };
    return spacingMap[size];
  },
};

/**
 * Typography utilities
 */
export const typographyUtils = {
  /**
   * Get font size by variant
   */
  getFontSize: (variant: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | 'xxl'): number => {
    const sizeMap = {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
    };
    return sizeMap[variant];
  },
  
  /**
   * Get line height by variant
   */
  getLineHeight: (variant: 'tight' | 'normal' | 'relaxed'): number => {
    const lineHeightMap = {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    };
    return lineHeightMap[variant];
  },
};

/**
 * Platform utilities
 */
export const platformUtils = {
  /**
   * Check if running on iOS
   */
  isIOS: () => {
    // In a real React Native app, you'd use Platform.OS === 'ios'
    return false;
  },
  
  /**
   * Check if running on Android
   */
  isAndroid: () => {
    // In a real React Native app, you'd use Platform.OS === 'android'
    return true;
  },
  
  /**
   * Check if running on web
   */
  isWeb: () => {
    // In a real React Native app, you'd use Platform.OS === 'web'
    return false;
  },
};
