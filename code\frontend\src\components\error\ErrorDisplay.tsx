/**
 * ErrorDisplay Component - Standardized Error Display for Vierla Application
 * 
 * This component provides a consistent way to display errors across the application
 * with support for different variants, severities, and interactive actions.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../Text';
import { Button } from '../Button';
import {
  ErrorDisplayProps,
  AppError,
  ErrorType,
  ErrorSeverity,
  ErrorVariant,
  ERROR_ICONS,
  SEVERITY_COLORS,
  createAppError,
} from '../../utils/errorTypes';
import {
  formatErrorMessage,
  getErrorTitle,
  isRetryableError,
} from '../../utils/errorUtils';

/**
 * Get error configuration based on severity
 */
const getErrorConfig = (severity: ErrorSeverity) => {
  const color = SEVERITY_COLORS[severity];
  return {
    color,
    textColor: '#374151', // Gray-700
    backgroundColor: `${color}10`, // 10% opacity
    borderColor: `${color}30`, // 30% opacity
    icon: severity === ErrorSeverity.CRITICAL ? 'alert-circle' : 'information-circle',
  };
};

/**
 * ErrorDisplay Component
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  title,
  description,
  severity = ErrorSeverity.MEDIUM,
  variant = ErrorVariant.INLINE,
  showIcon = true,
  dismissible = false,
  onDismiss,
  onRetry,
  retryLabel = 'Try Again',
  testID = 'error-display',
  style,
}) => {
  // Convert error to AppError if needed
  const appError: AppError = React.useMemo(() => {
    if (typeof error === 'string') {
      return createAppError(error, ErrorType.UNKNOWN, severity);
    }
    if (error instanceof Error) {
      return createAppError(error, ErrorType.UNKNOWN, severity);
    }
    return error as AppError;
  }, [error, severity]);

  // Don't render if no error
  if (!error) return null;

  const config = getErrorConfig(appError.severity || severity);
  const errorMessage = formatErrorMessage(appError);
  const errorTitle = title || getErrorTitle(appError.type, appError.severity || severity);
  const canRetry = onRetry && isRetryableError(appError);
  const iconName = ERROR_ICONS[appError.type] || config.icon;

  const renderContent = () => (
    <>
      {showIcon && (
        <Ionicons
          name={iconName as any}
          size={24}
          color={config.color}
          style={styles.icon}
        />
      )}
      <View style={styles.content}>
        {title && (
          <Text
            style={[styles.title, { color: config.color }]}
            accessibilityRole="header"
          >
            {errorTitle}
          </Text>
        )}
        <Text
          style={[styles.message, { color: config.textColor }]}
          accessibilityRole="text"
        >
          {errorMessage}
        </Text>
        {description && (
          <Text
            style={[styles.description, { color: config.textColor }]}
            accessibilityRole="text"
          >
            {description}
          </Text>
        )}
        {canRetry && (
          <Button
            title={retryLabel}
            onPress={onRetry}
            style={styles.retryButton}
            testID={`${testID}-retry`}
          />
        )}
      </View>
      {dismissible && onDismiss && (
        <TouchableOpacity
          style={styles.dismissButton}
          onPress={onDismiss}
          accessibilityLabel="Dismiss error"
          accessibilityRole="button"
          testID={`${testID}-dismiss`}
        >
          <Ionicons
            name="close"
            size={20}
            color={config.textColor}
          />
        </TouchableOpacity>
      )}
    </>
  );

  const containerStyle: ViewStyle = [
    styles.container,
    {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    },
    variant === ErrorVariant.BANNER && styles.banner,
    variant === ErrorVariant.MODAL && styles.modal,
    style,
  ];

  return (
    <View
      style={containerStyle}
      testID={testID}
      accessibilityRole="alert"
      accessibilityLabel={`Error: ${errorMessage}`}
    >
      {renderContent()}
    </View>
  );
};

/**
 * Validation Error Component - Specialized for form validation
 */
export interface ValidationErrorProps {
  errors: string[];
  field?: string;
  style?: ViewStyle;
  testID?: string;
}

export const ValidationError: React.FC<ValidationErrorProps> = ({
  errors,
  field,
  style,
  testID = 'validation-error',
}) => {
  if (!errors || errors.length === 0) return null;

  return (
    <View style={[styles.validationContainer, style]} testID={testID}>
      {errors.map((error, index) => (
        <View key={index} style={styles.validationItem}>
          <Ionicons
            name="alert-circle"
            size={16}
            color={SEVERITY_COLORS[ErrorSeverity.LOW]}
            style={styles.validationIcon}
          />
          <Text style={styles.validationText} accessibilityRole="text">
            {field ? `${field}: ${error}` : error}
          </Text>
        </View>
      ))}
    </View>
  );
};

/**
 * Success Display Component - For positive feedback
 */
export interface SuccessDisplayProps {
  message: string;
  title?: string;
  visible: boolean;
  onAction?: () => void;
  actionLabel?: string;
  style?: ViewStyle;
  testID?: string;
}

export const SuccessDisplay: React.FC<SuccessDisplayProps> = ({
  message,
  title,
  visible,
  onAction,
  actionLabel,
  style,
  testID = 'success-display',
}) => {
  if (!visible) return null;

  return (
    <View
      style={[styles.container, styles.successContainer, style]}
      testID={testID}
    >
      <Ionicons
        name="checkmark-circle"
        size={24}
        color="#10B981" // Green
        style={styles.icon}
      />
      <View style={styles.content}>
        {title && (
          <Text
            style={[styles.title, { color: '#10B981' }]}
            accessibilityRole="header"
          >
            {title}
          </Text>
        )}
        <Text
          style={[styles.message, { color: '#374151' }]}
          accessibilityRole="text"
        >
          {message}
        </Text>
        {onAction && actionLabel && (
          <Button
            title={actionLabel}
            onPress={onAction}
            style={styles.retryButton}
            testID={`${testID}-action`}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginVertical: 8,
  },
  banner: {
    borderRadius: 0,
    marginVertical: 0,
    paddingVertical: 12,
  },
  modal: {
    borderRadius: 12,
    margin: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  icon: {
    marginRight: 12,
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  description: {
    fontSize: 12,
    lineHeight: 16,
    opacity: 0.8,
    marginBottom: 8,
  },
  retryButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
  validationContainer: {
    marginTop: 4,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  validationIcon: {
    marginRight: 6,
  },
  validationText: {
    fontSize: 12,
    color: SEVERITY_COLORS[ErrorSeverity.LOW],
    flex: 1,
  },
  successContainer: {
    backgroundColor: '#10B98110', // Green with 10% opacity
    borderColor: '#10B98130', // Green with 30% opacity
  },
});
