"""
Unit tests for Authentication app
"""
import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from rest_framework import status
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from apps.authentication.models import UserProfile, PasswordResetToken, EmailVerificationToken
from apps.authentication.serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer, UserSerializer
)

User = get_user_model()


@pytest.mark.unit
@pytest.mark.auth
class TestUserModel:
    """Test User model functionality"""

    def test_create_user_success(self, db):
        """Test creating a regular user"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        assert user.email == '<EMAIL>'
        assert user.first_name == 'Test'
        assert user.last_name == 'User'
        assert user.is_active is True
        assert user.is_staff is False
        assert user.is_superuser is False
        assert user.role == 'customer'
        assert user.check_password('testpass123')

    def test_create_superuser_success(self, db):
        """Test creating a superuser"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User',
            role='admin'  # Explicitly set role for superuser
        )

        assert user.email == '<EMAIL>'
        assert user.is_active is True
        assert user.is_staff is True
        assert user.is_superuser is True
        assert user.role == 'admin'

    @pytest.mark.django_db
    def test_user_string_representation(self, customer_user):
        """Test user string representation"""
        expected = f"{customer_user.first_name} {customer_user.last_name} ({customer_user.email})"
        assert str(customer_user) == expected

    def test_user_email_unique(self, db):
        """Test that user email must be unique"""
        User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        with pytest.raises(IntegrityError):
            User.objects.create_user(
                email='<EMAIL>',
                password='anotherpass123'
            )

    def test_user_email_normalization(self, db):
        """Test that email is normalized"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Django's normalize_email only lowercases the domain part
        assert user.email == '<EMAIL>'

    def test_user_role_choices(self, db):
        """Test user role validation"""
        # Valid roles
        for role in ['customer', 'service_provider', 'admin']:
            user = User.objects.create_user(
                email=f'{role}@example.com',
                password='testpass123',
                role=role
            )
            assert user.role == role

    @pytest.mark.django_db
    def test_user_profile_creation(self, customer_user):
        """Test that user profile is created automatically"""
        assert hasattr(customer_user, 'profile')
        assert isinstance(customer_user.profile, UserProfile)


@pytest.mark.unit
@pytest.mark.auth
class TestUserProfileModel:
    """Test UserProfile model functionality"""

    @pytest.mark.django_db
    def test_profile_creation(self, customer_user):
        """Test profile is created with user"""
        profile = customer_user.profile
        assert profile.user == customer_user
        assert profile.business_description == ''
        assert profile.country == 'Canada'

    @pytest.mark.django_db
    def test_profile_string_representation(self, customer_user):
        """Test profile string representation"""
        profile = customer_user.profile
        expected = f"{customer_user.get_full_name()}'s Profile"
        assert str(profile) == expected


@pytest.mark.unit
@pytest.mark.auth
class TestAuthenticationSerializers:
    """Test authentication serializers"""

    def test_user_registration_serializer_valid(self, db):
        """Test valid user registration data"""
        data = {
            'email': '<EMAIL>',
            'password': 'strongpass123',
            'password_confirm': 'strongpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer',
            'terms_accepted': True  # Required field
        }

        serializer = UserRegistrationSerializer(data=data)
        assert serializer.is_valid(), f"Serializer errors: {serializer.errors}"

        user = serializer.save()
        assert user.email == '<EMAIL>'
        assert user.first_name == 'New'
        assert user.last_name == 'User'
        assert user.role == 'customer'

    def test_user_registration_serializer_password_mismatch(self, db):
        """Test registration with password mismatch"""
        data = {
            'email': '<EMAIL>',
            'password': 'strongpass123',
            'password_confirm': 'differentpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer',
            'terms_accepted': True
        }

        serializer = UserRegistrationSerializer(data=data)
        assert not serializer.is_valid()
        # Check for password validation errors (could be in non_field_errors or password_confirm)
        assert ('password_confirm' in serializer.errors or
                'non_field_errors' in serializer.errors)

    @pytest.mark.django_db
    def test_user_registration_serializer_duplicate_email(self, customer_user):
        """Test registration with duplicate email"""
        data = {
            'email': customer_user.email,
            'password': 'strongpass123',
            'password_confirm': 'strongpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer'
        }

        serializer = UserRegistrationSerializer(data=data)
        assert not serializer.is_valid()
        assert 'email' in serializer.errors

    @pytest.mark.django_db
    def test_user_login_serializer_valid(self, customer_user):
        """Test valid login data"""
        # Set the password properly for the test user
        customer_user.set_password('testpass123')
        customer_user.save()

        data = {
            'email': customer_user.email,
            'password': 'testpass123'
        }

        serializer = UserLoginSerializer(data=data)
        assert serializer.is_valid()

    @pytest.mark.django_db
    def test_user_login_serializer_invalid_credentials(self, customer_user):
        """Test login with invalid credentials"""
        data = {
            'email': customer_user.email,
            'password': 'wrongpassword'
        }

        serializer = UserLoginSerializer(data=data)
        assert not serializer.is_valid()
        assert 'non_field_errors' in serializer.errors

    @pytest.mark.django_db
    def test_user_serializer(self, customer_user):
        """Test user serializer"""
        serializer = UserSerializer(customer_user)
        data = serializer.data

        assert data['email'] == customer_user.email
        assert data['first_name'] == customer_user.first_name
        assert data['last_name'] == customer_user.last_name
        assert data['role'] == customer_user.role
        assert data['full_name'] == customer_user.full_name


@pytest.mark.unit
@pytest.mark.auth
class TestPasswordResetToken:
    """Test password reset token functionality"""

    @pytest.mark.django_db
    def test_create_password_reset_token(self, customer_user):
        """Test creating password reset token"""
        token = PasswordResetToken.objects.create(user=customer_user)

        assert token.user == customer_user
        assert token.token is not None
        assert len(token.token) == 32  # UUID4 hex length
        assert token.is_used is False
        assert token.created_at is not None

    @pytest.mark.django_db
    def test_password_reset_token_string_representation(self, customer_user):
        """Test token string representation"""
        token = PasswordResetToken.objects.create(user=customer_user)
        expected = f"Password reset token for {customer_user.email}"
        assert str(token) == expected


@pytest.mark.unit
@pytest.mark.auth
class TestEmailVerificationToken:
    """Test email verification token functionality"""

    @pytest.mark.django_db
    def test_create_email_verification_token(self, customer_user):
        """Test creating email verification token"""
        token = EmailVerificationToken.objects.create(user=customer_user)

        assert token.user == customer_user
        assert token.token is not None
        assert len(token.token) == 32  # UUID4 hex length
        assert token.is_used is False
        assert token.created_at is not None

    @pytest.mark.django_db
    def test_email_verification_token_string_representation(self, customer_user):
        """Test token string representation"""
        token = EmailVerificationToken.objects.create(user=customer_user)
        expected = f"Email verification token for {customer_user.email}"
        assert str(token) == expected
