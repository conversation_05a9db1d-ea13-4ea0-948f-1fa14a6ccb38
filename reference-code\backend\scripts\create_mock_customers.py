#!/usr/bin/env python
"""
Create Mock Customer Database Seeder
Creates 25 diverse customer accounts with realistic profiles and preferences
"""

import os
import sys
import django
import random
from decimal import Decimal
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from apps.authentication.models import UserProfile

User = get_user_model()

# Mock customer data based on diverse demographics
MOCK_CUSTOMERS_DATA = [
    {
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 32,
        'occupation': 'Marketing Manager',
        'address': '45 King Street West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5H 1J1',
        'latitude': Decimal('43.6532'),
        'longitude': Decima<PERSON>('-79.3832'),
        'bio': 'Busy marketing professional who values quality beauty services and convenience. Regular client for hair, nails, and skincare.',
        'preferred_language': 'en',
        'budget_range': 'high',  # $200-300/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Alex Rodriguez',
        'email': '<EMAIL>',
        'phone': '+14165552002',
        'age': 28,
        'occupation': 'Software Developer',
        'address': '123 Richmond Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5H 2L3',
        'latitude': Decimal('43.6548'),
        'longitude': Decimal('-79.3832'),
        'bio': 'Tech professional who appreciates modern styling and online booking convenience. Prefers trendy, low-maintenance looks.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $100-150/month
        'service_frequency': 'occasional'  # 1-2 times per month
    },
    {
        'name': 'Maria Garcia',
        'email': '<EMAIL>',
        'phone': '+14165552003',
        'age': 38,
        'occupation': 'Teacher',
        'address': '567 Bloor Street West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6G 1K5',
        'latitude': Decimal('43.6677'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Mother of two who seeks relaxing beauty treatments as self-care time. Values family-friendly services and weekend availability.',
        'preferred_language': 'es',
        'budget_range': 'medium',  # $150-200/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Jordan Kim',
        'email': '<EMAIL>',
        'phone': '+14165552004',
        'age': 21,
        'occupation': 'College Student',
        'address': '89 College Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5S 3M2',
        'latitude': Decimal('43.6577'),
        'longitude': Decimal('-79.3957'),
        'bio': 'Fashion-forward student who loves trying new trends and styles. Budget-conscious but willing to splurge for special occasions.',
        'preferred_language': 'en',
        'budget_range': 'low',  # $50-80/month
        'service_frequency': 'occasional'  # Special events only
    },
    {
        'name': 'Robert Johnson',
        'email': '<EMAIL>',
        'phone': '+14165552005',
        'age': 62,
        'occupation': 'Retired Engineer',
        'address': '234 Avenue Road',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5R 2J6',
        'latitude': Decimal('43.6777'),
        'longitude': Decimal('-79.3948'),
        'bio': 'Traditional gentleman who values consistent quality and personal relationships with service providers. Prefers classic styles.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $80-120/month
        'service_frequency': 'regular'  # Monthly maintenance
    },
    {
        'name': 'Priya Patel',
        'email': '<EMAIL>',
        'phone': '+14165552006',
        'age': 29,
        'occupation': 'Nurse',
        'address': '456 Danforth Avenue',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M4K 1P1',
        'latitude': Decimal('43.6779'),
        'longitude': Decimal('-79.3499'),
        'bio': 'Healthcare worker who needs stress-relief treatments and professional appearance maintenance. Values hygiene and safety.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $120-180/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Zoe Chen',
        'email': '<EMAIL>',
        'phone': '+14165552007',
        'age': 26,
        'occupation': 'Graphic Designer',
        'address': '789 Queen Street East',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M4M 1J8',
        'latitude': Decimal('43.6579'),
        'longitude': Decimal('-79.3499'),
        'bio': 'Creative professional who loves artistic nail designs and bold hair colors. Appreciates unique and Instagram-worthy looks.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $150-200/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Marcus Williams',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 35,
        'occupation': 'Financial Advisor',
        'address': '321 Bay Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5H 2R2',
        'latitude': Decimal('43.6532'),
        'longitude': Decimal('-79.3832'),
        'bio': 'Professional who requires polished appearance for client meetings. Values efficiency and premium grooming services.',
        'preferred_language': 'en',
        'budget_range': 'high',  # $200-250/month
        'service_frequency': 'regular'  # Weekly grooming
    },
    {
        'name': 'Fatima Al-Rashid',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 31,
        'occupation': 'Doctor',
        'address': '654 University Avenue',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5G 1X8',
        'latitude': Decimal('43.6548'),
        'longitude': Decimal('-79.3900'),
        'bio': 'Medical professional who appreciates cultural sensitivity and modest styling options. Values quality and professionalism.',
        'preferred_language': 'ar',
        'budget_range': 'high',  # $180-250/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Tyler Brooks',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 24,
        'occupation': 'Personal Trainer',
        'address': '987 Spadina Avenue',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5T 2C7',
        'latitude': Decimal('43.6548'),
        'longitude': Decimal('-79.3957'),
        'bio': 'Fitness enthusiast who needs grooming services that work with an active lifestyle. Prefers quick, efficient appointments.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $100-140/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Isabella Santos',
        'email': '<EMAIL>',
        'phone': '+14165552011',
        'age': 27,
        'occupation': 'Event Planner',
        'address': '147 Ossington Avenue',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6J 2Z8',
        'latitude': Decimal('43.6548'),
        'longitude': Decimal('-79.4200'),
        'bio': 'Event professional who needs to look perfect for client events. Requires flexible scheduling and last-minute appointments.',
        'preferred_language': 'es',
        'budget_range': 'high',  # $220-300/month
        'service_frequency': 'frequent'  # 3-4 times per month
    },
    {
        'name': 'David Park',
        'email': '<EMAIL>',
        'phone': '+14165552012',
        'age': 33,
        'occupation': 'Architect',
        'address': '258 Harbord Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5S 1G6',
        'latitude': Decimal('43.6577'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Design-conscious professional who appreciates precision and attention to detail in grooming services. Values modern techniques.',
        'preferred_language': 'ko',
        'budget_range': 'medium',  # $140-180/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Aisha Johnson',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 30,
        'occupation': 'Lawyer',
        'address': '369 King Street East',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5A 1L1',
        'latitude': Decimal('43.6532'),
        'longitude': Decimal('-79.3600'),
        'bio': 'Legal professional who needs polished, professional appearance for court and client meetings. Values reliability and quality.',
        'preferred_language': 'en',
        'budget_range': 'high',  # $200-280/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Carlos Mendoza',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 41,
        'occupation': 'Restaurant Owner',
        'address': '741 College Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6G 1C4',
        'latitude': Decimal('43.6577'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Business owner who values personal grooming for professional image. Prefers early morning or late evening appointments.',
        'preferred_language': 'es',
        'budget_range': 'medium',  # $120-160/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Sophie Dubois',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 25,
        'occupation': 'Fashion Blogger',
        'address': '852 Queen Street West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6J 1G4',
        'latitude': Decimal('43.6426'),
        'longitude': Decimal('-79.4000'),
        'bio': 'Fashion influencer who needs cutting-edge styles for content creation. Values trendy, photogenic looks and social media presence.',
        'preferred_language': 'fr',
        'budget_range': 'high',  # $250-350/month
        'service_frequency': 'frequent'  # 4-5 times per month
    },
    {
        'name': 'James Mitchell',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 45,
        'occupation': 'Sales Director',
        'address': '963 Yonge Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M4W 2H2',
        'latitude': Decimal('43.6777'),
        'longitude': Decimal('-79.3832'),
        'bio': 'Senior executive who requires consistent, professional grooming for business meetings and presentations. Values premium service.',
        'preferred_language': 'en',
        'budget_range': 'high',  # $180-240/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Keisha Washington',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 34,
        'occupation': 'Social Worker',
        'address': '174 Jane Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6S 3Y3',
        'latitude': Decimal('43.6629'),
        'longitude': Decimal('-79.4900'),
        'bio': 'Community-focused professional who values natural hair care and protective styling. Appreciates cultural competency in services.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $100-150/month
        'service_frequency': 'regular'  # Monthly protective styles
    },
    {
        'name': 'Raj Sharma',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 39,
        'occupation': 'IT Manager',
        'address': '285 Gerrard Street East',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5A 2G2',
        'latitude': Decimal('43.6579'),
        'longitude': Decimal('-79.3499'),
        'bio': 'Technology professional who prefers efficient, no-fuss grooming services. Values online booking and minimal wait times.',
        'preferred_language': 'hi',
        'budget_range': 'medium',  # $80-120/month
        'service_frequency': 'regular'  # Monthly maintenance
    },
    {
        'name': 'Luna Rodriguez',
        'email': '<EMAIL>',
        'phone': '+14165552019',
        'age': 23,
        'occupation': 'Art Student',
        'address': '396 Bathurst Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5T 2S6',
        'latitude': Decimal('43.6577'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Creative student who loves experimenting with bold colors and artistic nail designs. Budget-conscious but values artistic expression.',
        'preferred_language': 'es',
        'budget_range': 'low',  # $60-100/month
        'service_frequency': 'occasional'  # Special projects and events
    },
    {
        'name': 'Michael O\'Connor',
        'email': '<EMAIL>',
        'phone': '+14165552020',
        'age': 52,
        'occupation': 'University Professor',
        'address': '507 St. Clair Avenue West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6C 1A3',
        'latitude': Decimal('43.6777'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Academic who values traditional grooming and intellectual conversation. Prefers established providers with consistent quality.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $100-140/month
        'service_frequency': 'regular'  # Monthly maintenance
    },
    {
        'name': 'Amara Okafor',
        'email': '<EMAIL>',
        'phone': '+14165552021',
        'age': 28,
        'occupation': 'Journalist',
        'address': '618 Dundas Street West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5T 1H6',
        'latitude': Decimal('43.6548'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Media professional who needs camera-ready appearance for TV appearances. Values quick touch-ups and emergency appointments.',
        'preferred_language': 'en',
        'budget_range': 'high',  # $180-250/month
        'service_frequency': 'frequent'  # 3-4 times per month
    },
    {
        'name': 'Elena Rossi',
        'email': '<EMAIL>',
        'phone': '+14165552022',
        'age': 36,
        'occupation': 'Interior Designer',
        'address': '729 Mount Pleasant Road',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M4S 2N1',
        'latitude': Decimal('43.6900'),
        'longitude': Decimal('-79.3900'),
        'bio': 'Design professional who appreciates aesthetic excellence and attention to detail. Values luxury treatments and spa experiences.',
        'preferred_language': 'it',
        'budget_range': 'high',  # $200-300/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Hassan Ali',
        'email': '<EMAIL>',
        'phone': '+14165552023',
        'age': 29,
        'occupation': 'Pharmacist',
        'address': '840 Eglinton Avenue West',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M6C 2B6',
        'latitude': Decimal('43.7001'),
        'longitude': Decimal('-79.4103'),
        'bio': 'Healthcare professional who values hygiene standards and professional appearance. Prefers providers with medical-grade cleanliness.',
        'preferred_language': 'ar',
        'budget_range': 'medium',  # $120-160/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Chloe Williams',
        'email': '<EMAIL>',
        'phone': '+14165552024',
        'age': 22,
        'occupation': 'Yoga Instructor',
        'address': '951 Broadview Avenue',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M4K 2R1',
        'latitude': Decimal('43.6779'),
        'longitude': Decimal('-79.3499'),
        'bio': 'Wellness professional who prefers natural, organic beauty treatments. Values eco-friendly products and holistic approaches.',
        'preferred_language': 'en',
        'budget_range': 'medium',  # $100-150/month
        'service_frequency': 'regular'  # 2-3 times per month
    },
    {
        'name': 'Victor Petrov',
        'email': '<EMAIL>',
        'phone': '+***********',
        'age': 48,
        'occupation': 'Investment Banker',
        'address': '162 Bay Street',
        'city': 'Toronto',
        'state': 'ON',
        'zip_code': 'M5J 2R8',
        'latitude': Decimal('43.6532'),
        'longitude': Decimal('-79.3832'),
        'bio': 'High-powered executive who demands premium grooming services and flexible scheduling. Values discretion and luxury experience.',
        'preferred_language': 'ru',
        'budget_range': 'premium',  # $300-500/month
        'service_frequency': 'frequent'  # 4-5 times per month
    }
]

def generate_customer_preferences(customer_data):
    """Generate realistic customer preferences based on profile"""
    preferences = {
        'preferred_time_slots': [],
        'preferred_days': [],
        'max_travel_distance': 25,  # km
        'price_sensitivity': 'medium',
        'booking_lead_time': 'flexible'
    }
    
    # Set preferences based on occupation and lifestyle
    occupation = customer_data['occupation'].lower()
    
    if 'student' in occupation or 'blogger' in occupation:
        preferences['preferred_time_slots'] = ['afternoon', 'evening']
        preferences['preferred_days'] = ['weekday', 'weekend']
        preferences['price_sensitivity'] = 'high'
        preferences['booking_lead_time'] = 'flexible'
    elif 'teacher' in occupation or 'professor' in occupation:
        preferences['preferred_time_slots'] = ['afternoon', 'evening']
        preferences['preferred_days'] = ['weekend', 'weekday_evening']
        preferences['price_sensitivity'] = 'medium'
        preferences['booking_lead_time'] = 'advance'
    elif any(word in occupation for word in ['manager', 'director', 'executive', 'lawyer', 'doctor', 'banker']):
        preferences['preferred_time_slots'] = ['early_morning', 'lunch', 'evening']
        preferences['preferred_days'] = ['weekday', 'weekend']
        preferences['price_sensitivity'] = 'low'
        preferences['booking_lead_time'] = 'last_minute'
    elif 'trainer' in occupation or 'instructor' in occupation:
        preferences['preferred_time_slots'] = ['morning', 'afternoon']
        preferences['preferred_days'] = ['weekday', 'weekend']
        preferences['price_sensitivity'] = 'medium'
        preferences['booking_lead_time'] = 'flexible'
    else:
        preferences['preferred_time_slots'] = ['afternoon', 'evening']
        preferences['preferred_days'] = ['weekend', 'weekday_evening']
        preferences['price_sensitivity'] = 'medium'
        preferences['booking_lead_time'] = 'advance'
    
    return preferences

def create_customer_account(customer_data):
    """Create a complete customer account with user and profile"""
    try:
        # Create user account
        user = User.objects.create_user(
            email=customer_data['email'],
            password='VierlaTest123!',  # Standard test password
            first_name=customer_data['name'].split()[0],
            last_name=customer_data['name'].split()[-1],
            role='customer',
            phone=customer_data['phone'],
            is_verified=True,
            is_active=True,
            preferred_language=customer_data['preferred_language']
        )
        
        # Update user profile
        profile = user.profile
        profile.address = customer_data['address']
        profile.city = customer_data['city']
        profile.state = customer_data['state']
        profile.zip_code = customer_data['zip_code']
        profile.country = 'Canada'
        profile.latitude = customer_data['latitude']
        profile.longitude = customer_data['longitude']
        profile.save()
        
        # Set user bio
        user.bio = customer_data['bio']
        user.save()
        
        print(f"✅ Created customer: {customer_data['name']} ({customer_data['email']})")
        return user
        
    except Exception as e:
        print(f"❌ Error creating customer {customer_data['name']}: {e}")
        return None

def main():
    """Main function to create all mock customers"""
    print("🚀 Creating Mock Customer Accounts")
    print("=" * 60)
    
    created_count = 0
    total_count = len(MOCK_CUSTOMERS_DATA)
    
    for customer_data in MOCK_CUSTOMERS_DATA:
        customer = create_customer_account(customer_data)
        if customer:
            created_count += 1
    
    print(f"\n📊 Summary")
    print("=" * 60)
    print(f"Total customers processed: {total_count}")
    print(f"Successfully created: {created_count}")
    print(f"Failed: {total_count - created_count}")
    
    if created_count > 0:
        print(f"\n🎉 Successfully created {created_count} customer accounts!")
        print("📝 Test login credentials:")
        print("   Password: VierlaTest123!")
        print("   Example: <EMAIL> / VierlaTest123!")
    else:
        print("\n⚠️ No customers were created. Please check the errors above.")

if __name__ == "__main__":
    main()
