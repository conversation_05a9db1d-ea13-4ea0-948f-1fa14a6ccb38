import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ServiceList, { ServiceListProps } from '../ServiceList';
import { Service } from '../ServiceCard';

// Mock ServiceCard component
jest.mock('../ServiceCard', () => {
  const MockServiceCard = ({ service, onPress, onFavorite, testID }: any) => {
    const React = require('react');
    const { TouchableOpacity, Text } = require('react-native');
    
    return (
      <TouchableOpacity testID={testID} onPress={onPress}>
        <Text testID={`${testID}-name`}>{service.name}</Text>
        {onFavorite && (
          <TouchableOpacity testID={`${testID}-favorite`} onPress={onFavorite}>
            <Text>Favorite</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };
  
  return {
    __esModule: true,
    default: MockServiceCard,
  };
});

// Mock services data
const mockServices: Service[] = [
  {
    id: 'service-1',
    name: 'Premium Haircut',
    short_description: 'Professional haircut',
    base_price: 75.00,
    price_type: 'fixed',
    display_price: '$75.00',
    duration: 60,
    display_duration: '1h',
    is_popular: true,
    is_available: true,
    booking_count: 25,
    provider_name: 'Hair Studio Elite',
    provider_rating: 4.8,
    provider_city: 'Toronto',
    category_name: 'Hair Services',
    category_icon: '💇‍♀️',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'service-2',
    name: 'Luxury Manicure',
    short_description: 'Premium nail care',
    base_price: 55.00,
    price_type: 'fixed',
    display_price: '$55.00',
    duration: 45,
    display_duration: '45m',
    is_popular: false,
    is_available: true,
    booking_count: 18,
    provider_name: 'Nail Boutique',
    provider_rating: 4.6,
    provider_city: 'Vancouver',
    category_name: 'Nail Services',
    category_icon: '💅',
    created_at: '2024-01-02T00:00:00Z',
  },
  {
    id: 'service-3',
    name: 'Relaxing Massage',
    short_description: 'Full body massage',
    base_price: 120.00,
    price_type: 'fixed',
    display_price: '$120.00',
    duration: 90,
    display_duration: '1h 30m',
    is_popular: false,
    is_available: false,
    booking_count: 12,
    provider_name: 'Spa Wellness',
    provider_rating: 4.9,
    provider_city: 'Montreal',
    category_name: 'Spa Services',
    category_icon: '💆‍♀️',
    created_at: '2024-01-03T00:00:00Z',
  },
];

const defaultProps: ServiceListProps = {
  services: mockServices,
  onServicePress: jest.fn(),
  testID: 'service-list',
};

describe('ServiceList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders correctly with services', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      
      expect(getByTestId('service-list')).toBeTruthy();
      expect(getByTestId('service-list-flatlist')).toBeTruthy();
      expect(getByTestId('service-list-service-service-1')).toBeTruthy();
      expect(getByTestId('service-list-service-service-2')).toBeTruthy();
      expect(getByTestId('service-list-service-service-3')).toBeTruthy();
    });

    it('renders all service items', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      
      mockServices.forEach(service => {
        expect(getByTestId(`service-list-service-${service.id}`)).toBeTruthy();
        expect(getByTestId(`service-list-service-${service.id}-name`)).toBeTruthy();
      });
    });

    it('calls onServicePress when service is pressed', () => {
      const onServicePress = jest.fn();
      const props = { ...defaultProps, onServicePress };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      fireEvent.press(getByTestId('service-list-service-service-1'));
      expect(onServicePress).toHaveBeenCalledWith(mockServices[0]);
    });
  });

  describe('Empty State', () => {
    it('renders empty state when no services', () => {
      const props = { ...defaultProps, services: [] };
      const { getByTestId, getByText } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-empty-state')).toBeTruthy();
      expect(getByText('No services found')).toBeTruthy();
      expect(getByText('Try adjusting your search or filters')).toBeTruthy();
    });

    it('renders custom empty message', () => {
      const props = {
        ...defaultProps,
        services: [],
        emptyMessage: 'Custom empty message',
        emptySubtitle: 'Custom subtitle',
      };
      const { getByText } = render(<ServiceList {...props} />);
      
      expect(getByText('Custom empty message')).toBeTruthy();
      expect(getByText('Custom subtitle')).toBeTruthy();
    });
  });

  describe('Loading States', () => {
    it('renders initial loading state', () => {
      const props = { ...defaultProps, services: [], loading: true };
      const { getByTestId, getByText } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-initial-loading')).toBeTruthy();
      expect(getByText('Loading services...')).toBeTruthy();
    });

    it('renders loading footer when loading more', () => {
      const props = { ...defaultProps, loading: true };
      const { getByTestId, getByText } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-loading-footer')).toBeTruthy();
      expect(getByText('Loading more services...')).toBeTruthy();
    });

    it('does not render loading footer when not loading', () => {
      const { queryByTestId } = render(<ServiceList {...defaultProps} />);
      expect(queryByTestId('service-list-loading-footer')).toBeNull();
    });
  });

  describe('Refresh Control', () => {
    it('renders refresh control when onRefresh is provided', () => {
      const props = { ...defaultProps, onRefresh: jest.fn() };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-refresh-control')).toBeTruthy();
    });

    it('does not render refresh control when onRefresh is not provided', () => {
      const { queryByTestId } = render(<ServiceList {...defaultProps} />);
      expect(queryByTestId('service-list-refresh-control')).toBeNull();
    });

    it('calls onRefresh when pull to refresh is triggered', () => {
      const onRefresh = jest.fn();
      const props = { ...defaultProps, onRefresh };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      const refreshControl = getByTestId('service-list-refresh-control');
      fireEvent(refreshControl, 'refresh');
      expect(onRefresh).toHaveBeenCalledTimes(1);
    });
  });

  describe('Favorite Functionality', () => {
    it('renders favorite buttons when onFavoriteToggle is provided', () => {
      const props = { ...defaultProps, onFavoriteToggle: jest.fn() };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-service-service-1-favorite')).toBeTruthy();
      expect(getByTestId('service-list-service-service-2-favorite')).toBeTruthy();
    });

    it('calls onFavoriteToggle when favorite button is pressed', () => {
      const onFavoriteToggle = jest.fn();
      const props = { ...defaultProps, onFavoriteToggle };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      fireEvent.press(getByTestId('service-list-service-service-1-favorite'));
      expect(onFavoriteToggle).toHaveBeenCalledWith('service-1');
    });

    it('passes favorite status correctly', () => {
      const props = {
        ...defaultProps,
        onFavoriteToggle: jest.fn(),
        favoriteServices: ['service-1', 'service-3'],
      };
      render(<ServiceList {...props} />);
      
      // The favorite status is passed to ServiceCard components
      // This is tested through the mock implementation
    });
  });

  describe('Variants', () => {
    it('renders in list variant by default', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      expect(getByTestId('service-list-flatlist')).toBeTruthy();
    });

    it('renders in grid variant when specified', () => {
      const props = { ...defaultProps, variant: 'grid' as const };
      const { getByTestId } = render(<ServiceList {...props} />);
      expect(getByTestId('service-list-flatlist')).toBeTruthy();
    });
  });

  describe('Load More Functionality', () => {
    it('calls onLoadMore when end is reached', () => {
      const onLoadMore = jest.fn();
      const props = {
        ...defaultProps,
        onLoadMore,
        hasMore: true,
        loading: false,
      };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      const flatList = getByTestId('service-list-flatlist');
      fireEvent(flatList, 'endReached');
      expect(onLoadMore).toHaveBeenCalledTimes(1);
    });

    it('does not call onLoadMore when loading', () => {
      const onLoadMore = jest.fn();
      const props = {
        ...defaultProps,
        onLoadMore,
        hasMore: true,
        loading: true,
      };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      const flatList = getByTestId('service-list-flatlist');
      fireEvent(flatList, 'endReached');
      expect(onLoadMore).not.toHaveBeenCalled();
    });

    it('does not call onLoadMore when no more items', () => {
      const onLoadMore = jest.fn();
      const props = {
        ...defaultProps,
        onLoadMore,
        hasMore: false,
        loading: false,
      };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      const flatList = getByTestId('service-list-flatlist');
      fireEvent(flatList, 'endReached');
      expect(onLoadMore).not.toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('has proper testID for accessibility testing', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      expect(getByTestId('service-list')).toBeTruthy();
    });

    it('handles custom testID prop', () => {
      const props = { ...defaultProps, testID: 'custom-service-list' };
      const { getByTestId } = render(<ServiceList {...props} />);
      expect(getByTestId('custom-service-list')).toBeTruthy();
    });

    it('provides proper testIDs for child components', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      
      mockServices.forEach(service => {
        expect(getByTestId(`service-list-service-${service.id}`)).toBeTruthy();
      });
    });
  });

  describe('Performance', () => {
    it('handles large lists efficiently', () => {
      const largeServiceList = Array.from({ length: 100 }, (_, index) => ({
        ...mockServices[0],
        id: `service-${index}`,
        name: `Service ${index}`,
      }));
      
      const props = { ...defaultProps, services: largeServiceList };
      const { getByTestId } = render(<ServiceList {...props} />);
      
      expect(getByTestId('service-list-flatlist')).toBeTruthy();
    });

    it('uses keyExtractor for performance', () => {
      const { getByTestId } = render(<ServiceList {...defaultProps} />);
      expect(getByTestId('service-list-flatlist')).toBeTruthy();
    });
  });
});
