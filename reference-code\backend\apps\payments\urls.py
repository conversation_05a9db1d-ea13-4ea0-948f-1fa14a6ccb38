"""
Payment URLs Configuration
MVP Critical Feature - Payment Processing API Endpoints
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'payments'

# Create router for ViewSets
router = DefaultRouter()

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Payment configuration
    path('config/', views.PaymentConfigView.as_view(), name='payment-config'),
    
    # Payment methods
    path('methods/', views.PaymentMethodListView.as_view(), name='payment-methods'),
    path('methods/<str:payment_method_id>/', views.PaymentMethodDetailView.as_view(), name='payment-method-detail'),
    path('methods/<str:payment_method_id>/set-default/', views.PaymentMethodDetailView.as_view(), name='set-default-payment-method'),
    
    # Payment processing
    path('process/', views.ProcessPaymentView.as_view(), name='process-payment'),
    path('intents/', views.CreatePaymentIntentView.as_view(), name='create-payment-intent'),
    path('intents/<str:payment_intent_id>/confirm/', views.ConfirmPaymentIntentView.as_view(), name='confirm-payment-intent'),
    
    # Apple Pay / Google Pay
    path('apple-pay/', views.ProcessApplePayView.as_view(), name='apple-pay'),
    path('google-pay/', views.ProcessGooglePayView.as_view(), name='google-pay'),
    
    # Transaction history
    path('transactions/', views.TransactionListView.as_view(), name='transactions'),
    path('transactions/<str:transaction_id>/refund/', views.RefundRequestView.as_view(), name='request-refund'),
    
    # Webhooks
    path('webhooks/stripe/', views.StripeWebhookView.as_view(), name='stripe-webhook'),
]
