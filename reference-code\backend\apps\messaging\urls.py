"""
Messaging URLs for Services Marketplace App
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'messaging'

# Create router for ViewSets
router = DefaultRouter()
router.register(r'conversations', views.ConversationViewSet, basename='conversation')
router.register(r'messages', views.MessageViewSet, basename='message')

urlpatterns = [
    # ViewSet routes
    path('', include(router.urls)),
]
