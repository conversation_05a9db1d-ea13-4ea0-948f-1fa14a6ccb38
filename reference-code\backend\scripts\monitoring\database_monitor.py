#!/usr/bin/env python
"""
Database Monitoring Script for Vierla Backend
Monitors SQLite database operations and provides real-time insights
"""

import os
import sys
import time
import sqlite3
from datetime import datetime
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

# Import Django models after setup
from apps.bookings.models import Booking
from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.authentication.models import User, UserProfile
from django.db import connection


def get_database_stats():
    """Get current database statistics"""
    with connection.cursor() as cursor:
        stats = {}

        # Get table counts
        stats['users'] = User.objects.count()
        stats['user_profiles'] = UserProfile.objects.count()
        stats['service_categories'] = ServiceCategory.objects.count()
        stats['service_providers'] = ServiceProvider.objects.count()
        stats['services'] = Service.objects.count()
        stats['bookings'] = Booking.objects.count()

        # Get database size
        db_path = connection.settings_dict['NAME']
        if os.path.exists(db_path):
            stats['db_size_mb'] = round(
                os.path.getsize(db_path) / (1024 * 1024), 2)
        else:
            stats['db_size_mb'] = 0

        return stats


def monitor_database():
    """Main monitoring loop"""
    print("🔍 Vierla Database Monitor Started")
    print("=" * 50)

    while True:
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            stats = get_database_stats()

            print(f"\n📊 Database Status - {timestamp}")
            print("-" * 40)
            print(f"👥 Users: {stats['users']}")
            print(f"📋 User Profiles: {stats['user_profiles']}")
            print(f"🏷️  Service Categories: {stats['service_categories']}")
            print(f"🏪 Service Providers: {stats['service_providers']}")
            print(f"⚡ Services: {stats['services']}")
            print(f"📅 Bookings: {stats['bookings']}")
            print(f"💾 Database Size: {stats['db_size_mb']} MB")

            # Check for recent activity
            recent_bookings = Booking.objects.filter(
                created_at__gte=datetime.now().replace(hour=0, minute=0, second=0)
            ).count()

            if recent_bookings > 0:
                print(f"🔥 Recent Activity: {recent_bookings} bookings today")

            time.sleep(30)  # Update every 30 seconds

        except KeyboardInterrupt:
            print("\n\n🛑 Database monitoring stopped")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            time.sleep(5)


if __name__ == "__main__":
    monitor_database()
