<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- V<PERSON><PERSON> - Optimized SVG Version -->
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C9A85;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5A7A63;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.1)"/>
    </filter>
  </defs>
  
  <!-- Logo background -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" filter="url(#shadow)"/>
  
  <!-- Stylized "V" icon -->
  <path d="M12 12 L20 28 L28 12" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- Company name -->
  <text x="45" y="16" font-family="system-ui, -apple-system, sans-serif" font-size="14" font-weight="600" fill="#1F2937">
    Vierla
  </text>
  
  <!-- Tagline -->
  <text x="45" y="28" font-family="system-ui, -apple-system, sans-serif" font-size="10" font-weight="400" fill="#6B7280">
    Self-care, simplified
  </text>
  
  <!-- Accessibility -->
  <title>Vierla - Self-care, simplified</title>
  <desc>Vierla company logo featuring a stylized V icon with company name and tagline</desc>
</svg>
