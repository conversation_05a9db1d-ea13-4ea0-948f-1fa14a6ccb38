/**
 * View Toggle Component
 * Toggle between list and grid view modes
 */

import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, spacing } from '../../theme';

export type ViewMode = 'list' | 'grid';

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  style?: any;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.toggleButton,
          styles.leftButton,
          viewMode === 'list' && styles.activeButton,
        ]}
        onPress={() => onViewModeChange('list')}
        activeOpacity={0.7}
      >
        <Icon
          name="view-list"
          size={20}
          color={viewMode === 'list' ? colors.white : colors.primary}
        />
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.toggleButton,
          styles.rightButton,
          viewMode === 'grid' && styles.activeButton,
        ]}
        onPress={() => onViewModeChange('grid')}
        activeOpacity={0.7}
      >
        <Icon
          name="view-module"
          size={20}
          color={viewMode === 'grid' ? colors.white : colors.primary}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: 2,
  },
  toggleButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 44,
  },
  leftButton: {
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
  },
  rightButton: {
    borderTopRightRadius: 6,
    borderBottomRightRadius: 6,
  },
  activeButton: {
    backgroundColor: colors.primary,
  },
});
