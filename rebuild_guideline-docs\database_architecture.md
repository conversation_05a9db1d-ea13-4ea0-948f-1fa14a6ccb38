# Database Architecture: The Data Backbone for On-Demand Services

This document provides the detailed database schema for the on-demand beauty service application. The schema is designed for performance, data integrity, and scalability, and will be implemented using PostgreSQL.

## Section 1: Logical and Relational Model

The foundation of a robust database is a well-designed logical model that accurately represents the business domain's entities and their relationships.

### 1.1. Entity-Relationship Diagram (ERD)

An Entity-Relationship Diagram (ERD) serves as the high-level visual blueprint for the database. It illustrates the key tables (entities) and the relationships between them. A detailed ERD corresponding to the tables described below will be created as a formal artifact for the development team to provide a visual guide for understanding the overall structure and flow of data.[59, 60]

### 1.2. Core Database Entities and Attributes

A detailed table defining every entity, column, data type, and constraint is essential for providing a clear and unambiguous specification for the database. This table acts as the single source of truth for developers.[59, 61, 60]

| Table Name | Column Name | Data Type | Constraints/Notes |
| :--- | :--- | :--- | :--- |
| **users** | `id` | `UUID` | Primary Key, default `gen_random_uuid()` |
| | `email` | `VARCHAR(254)` | `UNIQUE`, `NOT NULL` |
| | `password` | `VARCHAR(128)` | `NOT NULL` (Stores hashed password) |
| | `first_name` | `VARCHAR(150)` | Optional |
| | `last_name` | `VARCHAR(150)` | Optional |
| | `phone_number` | `VARCHAR(20)` | `UNIQUE`, Optional |
| | `is_active` | `BOOLEAN` | Default `True`, for soft deletes or disabling accounts |
| | `is_staff` | `BOOLEAN` | Default `False`, for Django Admin access |
| | `is_professional` | `BOOLEAN` | Default `False`, flag to indicate if user has a professional profile |
| | `created_at` | `TIMESTAMPTZ` | `NOT NULL`, Default `NOW()` |
| | `updated_at` | `TIMESTAMPTZ` | `NOT NULL`, Default `NOW()` (Updated via trigger) |
| **professionals** | `user_id` | `UUID` | Primary Key, Foreign Key to `users.id` on delete cascade |
| | `bio` | `TEXT` | A short biography for the professional's profile |
| | `profile_picture_url` | `VARCHAR(255)` | URL to the profile picture stored in S3 |
| | `average_rating` | `DECIMAL(3, 2)` | Default `0.00`, calculated field updated by a trigger or task |
| | `total_ratings` | `INTEGER` | Default `0`, calculated field |
| | `is_verified` | `BOOLEAN` | Default `False`, indicates if the professional is verified by an admin |
| **service_categories** | `id` | `SERIAL` | Primary Key |
| | `name` | `VARCHAR(100)` | `UNIQUE`, `NOT NULL` (e.g., "Hair Styling", "Nail Care") |
| | `slug` | `VARCHAR(100)` | `UNIQUE`, `NOT NULL`, for use in URLs |
| **services** | `id` | `SERIAL` | Primary Key |
| | `category_id` | `INTEGER` | Foreign Key to `service_categories.id` on delete set null |
| | `name` | `VARCHAR(255)` | `NOT NULL` (e.g., "Manicure", "Haircut") |
| | `description` | `TEXT` | Detailed description of the service |
| | `duration_minutes` | `INTEGER` | `NOT NULL`, duration of the service in minutes |
| | `price` | `DECIMAL(10, 2)` | `NOT NULL`, base price of the service |
| **professional_services** | `professional_id` | `UUID` | Foreign Key to `professionals.user_id` on delete cascade |
| | `service_id` | `INTEGER` | Foreign Key to `services.id` on delete cascade |
| | | | Composite Primary Key (`professional_id`, `service_id`) |
| **availability_schedules** | `id` | `SERIAL` | Primary Key |
| | `professional_id` | `UUID` | Foreign Key to `professionals.user_id` on delete cascade |
| | `day_of_week` | `INTEGER` | `NOT NULL`, `CHECK (day_of_week BETWEEN 0 AND 6)` (0=Sun, 1=Mon,..., 6=Sat) |
| | `start_time` | `TIME` | `NOT NULL` (e.g., '09:00:00') |
| | `end_time` | `TIME` | `NOT NULL` (e.g., '17:00:00') |
| | | | `UNIQUE` constraint on (`professional_id`, `day_of_week`) |
| **availability_overrides** | `id` | `SERIAL` | Primary Key |
| | `professional_id` | `UUID` | Foreign Key to `professionals.user_id` on delete cascade |
| | `date` | `DATE` | `NOT NULL`, the specific date for the override |
| | `start_time` | `TIME` | Optional, start time for a specific day |
| | `end_time` | `TIME` | Optional, end time for a specific day |
| | `is_available` | `BOOLEAN` | `NOT NULL`, `False` for a day off, `True` for special hours |
| **bookings** | `id` | `UUID` | Primary Key, default `gen_random_uuid()` |
| | `customer_id` | `UUID` | Foreign Key to `users.id` on delete cascade |
| | `professional_id` | `UUID` | Foreign Key to `professionals.user_id` on delete cascade |
| | `service_id` | `INTEGER` | Foreign Key to `services.id` on delete restrict |
| | `start_time` | `TIMESTAMPTZ` | `NOT NULL` |
| | `end_time` | `TIMESTAMPTZ` | `NOT NULL` |
| | `status` | `VARCHAR(20)` | `NOT NULL`, `CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled_by_user', 'cancelled_by_pro'))` |
| | `total_price` | `DECIMAL(10, 2)` | `NOT NULL`, final price at time of booking |
| | `created_at` | `TIMESTAMPTZ` | `NOT NULL`, Default `NOW()` |
| **payments** | `id` | `UUID` | Primary Key, default `gen_random_uuid()` |
| | `booking_id` | `UUID` | Foreign Key to `bookings.id` on delete cascade |
| | `amount` | `DECIMAL(10, 2)` | `NOT NULL` |
| | `status` | `VARCHAR(20)` | `NOT NULL` (e.g., 'succeeded', 'pending', 'failed') |
| | `provider` | `VARCHAR(50)` | e.g., 'Stripe', 'PayPal' |
| | `transaction_id` | `VARCHAR(255)` | `UNIQUE`, from the payment provider |
| | `created_at` | `TIMESTAMPTZ` | `NOT NULL`, Default `NOW()` |
| **reviews** | `id` | `SERIAL` | Primary Key |
| | `booking_id` | `UUID` | `UNIQUE`, Foreign Key to `bookings.id` on delete cascade (one review per booking) |
| | `customer_id` | `UUID` | Foreign Key to `users.id` on delete cascade |
| | `professional_id` | `UUID` | Foreign Key to `professionals.user_id` on delete cascade |
| | `rating` | `INTEGER` | `NOT NULL`, `CHECK (rating BETWEEN 1 AND 5)` |
| | `comment` | `TEXT` | Optional |
| | `created_at` | `TIMESTAMPTZ` | `NOT NULL`, Default `NOW()` |

This schema is designed to be comprehensive, covering user management, service catalogs, complex availability scheduling, bookings, payments, and reviews.[62, 63, 64, 65, 66]

## Section 2: Schema Implementation and Optimization

A logical model must be translated into a physical implementation that is both correct and performant.

### 2.1. SQL `CREATE TABLE` Statements

This subsection will contain the complete, ready-to-execute PostgreSQL DDL (`CREATE TABLE`) statements for every table defined in the logical model above. These statements will include all primary keys, foreign keys with specified `ON DELETE` behavior, `UNIQUE` constraints, `NOT NULL` constraints, and `CHECK` constraints, providing a script that can be used to instantiate the database schema directly.[61]

### 2.2. Indexing Strategy for Performance

Database performance is critically dependent on a well-designed indexing strategy. Indexes allow the database to find data quickly without having to scan entire tables.

**Recommendation:** In addition to the primary key indexes created automatically, explicit indexes will be created on all foreign key columns. Furthermore, composite indexes will be created on columns that are frequently used together in `WHERE`, `JOIN`, and `ORDER BY` clauses.

**Analysis & Justification:** The performance of an on-demand application hinges on the speed of key operations like checking availability and fetching booking history. Without proper indexing, these operations would require slow, resource-intensive full table scans.[60]

**Critical Indexes to be Created:**
*   `CREATE INDEX ON bookings (professional_id, start_time);`
    *   **Purpose:** This is the most critical index. It allows for extremely fast lookups of a specific professional's appointments within a given time range, essential for checking for booking conflicts.
*   `CREATE INDEX ON bookings (customer_id, start_time);`
    *   **Purpose:** Efficiently retrieves a specific customer's list of appointments, ordered by date.
*   `CREATE INDEX ON availability_overrides (professional_id, date);`
    *   **Purpose:** Allows for rapid lookups of any exceptions to a professional's regular weekly schedule.
*   `CREATE INDEX ON reviews (professional_id);`
    *   **Purpose:** Speeds up the retrieval of all reviews for a specific professional.

### 2.3. Normalization and Data Integrity

The structure of the schema is designed to ensure data integrity and minimize redundancy.

**Analysis & Justification:** The proposed schema is designed in **Third Normal Form (3NF)**. This is a database normalization standard that aims to reduce data redundancy and improve data integrity by ensuring that all attributes in a table are dependent only on the primary key.[60]

For example, instead of storing service details directly in the `bookings` table, we store only a `service_id`. The `services` table becomes the single source of truth for service information. The `bookings` table stores the `total_price` at the time of the transaction, preserving historical accuracy, while the `services` table can be updated independently. This design prevents data anomalies and makes the database far easier to maintain.[61]
