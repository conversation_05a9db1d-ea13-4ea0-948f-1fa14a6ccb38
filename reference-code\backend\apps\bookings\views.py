"""
Booking views for Vierla Beauty Services Marketplace
Comprehensive booking management with mobile-first design
"""
from rest_framework import viewsets, status, filters, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg
from django.utils import timezone as django_timezone
from datetime import timedelta, date

from .models import Booking, BookingStateChange, TimeSlot, BookingNotification
from .serializers import (
    BookingListSerializer, BookingDetailSerializer, BookingCreateSerializer,
    BookingStateChangeSerializer, TimeSlotSerializer, BookingNotificationSerializer
)
from .filters import BookingFilter, TimeSlotFilter
from .permissions import IsBookingOwnerOrProvider, CanManageBookings, CanViewBookings
from apps.core.pagination import StandardResultsSetPagination
from apps.core.permissions import IsActiveUser


class BookingViewSet(viewsets.ModelViewSet):
    """
    Comprehensive booking management viewset
    Supports mobile-first design with optimized queries
    """

    permission_classes = [permissions.IsAuthenticated,
                          IsActiveUser, CanViewBookings]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend,
                       filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BookingFilter
    search_fields = ['booking_number', 'customer__first_name',
                     'customer__last_name', 'service__name']
    ordering_fields = ['scheduled_datetime',
                       'created_at', 'total_amount', 'status']
    ordering = ['-scheduled_datetime']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return BookingCreateSerializer
        elif self.action in ['retrieve', 'update', 'partial_update']:
            return BookingDetailSerializer
        return BookingListSerializer

    def get_queryset(self):
        """Get bookings based on user role and permissions"""
        user = self.request.user

        # Base queryset with optimized joins
        queryset = Booking.objects.select_related(
            'customer', 'provider', 'service', 'cancelled_by'
        ).prefetch_related(
            'state_changes__changed_by'
        )

        # Filter based on user role
        if user.role == 'customer':
            return queryset.filter(customer=user)
        elif user.role == 'service_provider':
            # Check if user has a provider profile
            if hasattr(user, 'provider_profile'):
                return queryset.filter(provider=user.provider_profile)
            return queryset.none()
        elif user.role == 'admin':
            return queryset

        return queryset.none()

    def perform_create(self, serializer):
        """Create booking with proper user assignment"""
        booking = serializer.save()

        # Create initial state change record
        BookingStateChange.objects.create(
            booking=booking,
            from_status='',
            to_status=booking.status,
            changed_by=self.request.user,
            notes="Booking created"
        )

        # Create notification for provider
        BookingNotification.objects.create(
            booking=booking,
            recipient=booking.provider.user,
            notification_type=BookingNotification.NotificationType.BOOKING_CREATED,
            channel=BookingNotification.NotificationChannel.PUSH,
            title="New Booking Request",
            message=f"New booking request from {booking.customer.get_full_name()} for {booking.service.name}"
        )

    @action(detail=True, methods=['post'], permission_classes=[CanManageBookings])
    def confirm(self, request, pk=None):
        """Confirm a booking"""
        booking = self.get_object()

        try:
            booking.confirm_booking(confirmed_by=request.user)

            # Create notification for customer
            BookingNotification.objects.create(
                booking=booking,
                recipient=booking.customer,
                notification_type=BookingNotification.NotificationType.BOOKING_CONFIRMED,
                channel=BookingNotification.NotificationChannel.PUSH,
                title="Booking Confirmed",
                message=f"Your booking for {booking.service.name} has been confirmed"
            )

            serializer = self.get_serializer(booking)
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[CanManageBookings])
    def start(self, request, pk=None):
        """Start a booking (service in progress)"""
        booking = self.get_object()

        try:
            booking.start_booking(started_by=request.user)

            # Create notification for customer
            BookingNotification.objects.create(
                booking=booking,
                recipient=booking.customer,
                notification_type=BookingNotification.NotificationType.BOOKING_STARTED,
                channel=BookingNotification.NotificationChannel.PUSH,
                title="Service Started",
                message=f"Your {booking.service.name} service has started"
            )

            serializer = self.get_serializer(booking)
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[CanManageBookings])
    def complete(self, request, pk=None):
        """Complete a booking"""
        booking = self.get_object()

        try:
            booking.complete_booking(completed_by=request.user)

            # Create notification for customer
            BookingNotification.objects.create(
                booking=booking,
                recipient=booking.customer,
                notification_type=BookingNotification.NotificationType.BOOKING_COMPLETED,
                channel=BookingNotification.NotificationChannel.PUSH,
                title="Service Completed",
                message=f"Your {booking.service.name} service has been completed"
            )

            serializer = self.get_serializer(booking)
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a booking"""
        booking = self.get_object()
        reason = request.data.get('reason', '')

        # Check permissions - customer can cancel their own, provider can cancel theirs
        if not (booking.customer == request.user or
                (hasattr(request.user, 'provider_profile') and
                 booking.provider == request.user.provider_profile)):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            booking.cancel_booking(cancelled_by=request.user, reason=reason)

            # Create notification for the other party
            if booking.customer == request.user:
                # Customer cancelled, notify provider
                recipient = booking.provider.user
                message = f"Booking for {booking.service.name} has been cancelled by the customer"
            else:
                # Provider cancelled, notify customer
                recipient = booking.customer
                message = f"Your booking for {booking.service.name} has been cancelled by the provider"

            BookingNotification.objects.create(
                booking=booking,
                recipient=recipient,
                notification_type=BookingNotification.NotificationType.BOOKING_CANCELLED,
                channel=BookingNotification.NotificationChannel.PUSH,
                title="Booking Cancelled",
                message=message
            )

            serializer = self.get_serializer(booking)
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'], permission_classes=[CanManageBookings])
    def mark_no_show(self, request, pk=None):
        """Mark booking as no show"""
        booking = self.get_object()

        try:
            booking.mark_no_show(marked_by=request.user)
            serializer = self.get_serializer(booking)
            return Response(serializer.data)

        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """Get upcoming bookings for the user"""
        queryset = self.get_queryset().filter(
            scheduled_datetime__gte=django_timezone.now(),
            status__in=[Booking.Status.PENDING, Booking.Status.CONFIRMED]
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def today(self, request):
        """Get today's bookings for the user"""
        today = django_timezone.now().date()
        queryset = self.get_queryset().filter(
            scheduled_datetime__date=today
        )

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def history(self, request):
        """Get booking history (completed/cancelled)"""
        queryset = self.get_queryset().filter(
            status__in=[Booking.Status.COMPLETED,
                        Booking.Status.CANCELLED, Booking.Status.NO_SHOW]
        )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get booking statistics for the user"""
        queryset = self.get_queryset()

        # Enhanced stats calculation
        from django.db.models import Sum, Avg
        from datetime import datetime, timedelta

        now = django_timezone.now()

        # Basic counts
        total_bookings = queryset.count()
        upcoming_bookings = queryset.filter(
            scheduled_datetime__gte=now,
            status__in=[Booking.Status.PENDING, Booking.Status.CONFIRMED]
        ).count()
        completed_bookings = queryset.filter(
            status=Booking.Status.COMPLETED).count()
        cancelled_bookings = queryset.filter(
            status=Booking.Status.CANCELLED).count()

        # Revenue calculation
        completed_queryset = queryset.filter(status=Booking.Status.COMPLETED)
        total_revenue = completed_queryset.aggregate(
            total=Sum('total_amount')
        )['total'] or 0

        average_booking_value = completed_queryset.aggregate(
            avg=Avg('total_amount')
        )['avg'] or 0

        # Popular services (for providers)
        popular_services = []
        if hasattr(request.user, 'provider_profile'):
            popular_services = list(
                queryset.values('service__name')
                .annotate(booking_count=Count('id'))
                .order_by('-booking_count')[:5]
            )
            popular_services = [
                {'service_name': item['service__name'],
                    'booking_count': item['booking_count']}
                for item in popular_services if item['service__name']
            ]

        # Booking trends (last 30 days)
        booking_trends = []
        for i in range(30):
            date = (now - timedelta(days=i)).date()
            day_bookings = queryset.filter(scheduled_datetime__date=date)
            booking_trends.append({
                'date': date.isoformat(),
                'booking_count': day_bookings.count(),
                'revenue': float(day_bookings.filter(
                    status=Booking.Status.COMPLETED
                ).aggregate(total=Sum('total_amount'))['total'] or 0)
            })

        booking_trends.reverse()  # Chronological order

        stats = {
            'total_bookings': total_bookings,
            'upcoming_bookings': upcoming_bookings,
            'completed_bookings': completed_bookings,
            'cancelled_bookings': cancelled_bookings,
            'total_revenue': float(total_revenue),
            'average_booking_value': float(average_booking_value),
            'popular_services': popular_services,
            'booking_trends': booking_trends,
        }

        return Response(stats)


class TimeSlotViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing time slots and availability
    """

    serializer_class = TimeSlotSerializer
    permission_classes = [permissions.IsAuthenticated, IsActiveUser]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = TimeSlotFilter
    ordering_fields = ['date', 'start_time']
    ordering = ['date', 'start_time']

    def get_queryset(self):
        """Get time slots based on user role"""
        user = self.request.user

        queryset = TimeSlot.objects.select_related('provider', 'service')

        if user.role == 'service_provider':
            if hasattr(user, 'provider_profile'):
                return queryset.filter(provider=user.provider_profile)
            return queryset.none()
        elif user.role == 'admin':
            return queryset
        else:
            # Customers can view available slots
            return queryset.filter(is_available=True, is_break=False)

    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available time slots for booking"""
        provider_id = request.query_params.get('provider_id')
        service_id = request.query_params.get('service_id')
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')

        queryset = self.get_queryset().filter(
            is_available=True,
            is_break=False
        )

        if provider_id:
            queryset = queryset.filter(provider_id=provider_id)

        if service_id:
            queryset = queryset.filter(
                Q(service_id=service_id) | Q(service__isnull=True))

        if date_from:
            queryset = queryset.filter(date__gte=date_from)

        if date_to:
            queryset = queryset.filter(date__lte=date_to)

        # Only show slots that can be booked
        available_slots = [slot for slot in queryset if slot.can_be_booked()]

        serializer = self.get_serializer(available_slots, many=True)
        return Response(serializer.data)


class BookingNotificationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing booking notifications
    """

    serializer_class = BookingNotificationSerializer
    permission_classes = [permissions.IsAuthenticated, IsActiveUser]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['notification_type', 'channel', 'status']
    ordering_fields = ['created_at', 'scheduled_for']
    ordering = ['-created_at']

    def get_queryset(self):
        """Get notifications for the current user"""
        return BookingNotification.objects.filter(
            recipient=self.request.user
        ).select_related('booking', 'recipient')

    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark notification as read"""
        notification = self.get_object()
        notification.mark_as_read()

        serializer = self.get_serializer(notification)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read"""
        notifications = self.get_queryset().filter(
            status__in=[
                BookingNotification.NotificationStatus.PENDING,
                BookingNotification.NotificationStatus.SENT,
                BookingNotification.NotificationStatus.DELIVERED
            ]
        )

        for notification in notifications:
            notification.mark_as_read()

        return Response({'message': f'Marked {notifications.count()} notifications as read'})
