#!/usr/bin/env python3
"""
Test script to verify authentication API connectivity
"""
import requests
import json

# API Configuration
API_BASE_URL = 'http://************:8000/api'


def test_auth_endpoints():
    """Test authentication endpoints"""
    print("Testing Vierla Authentication API...")
    print(f"Base URL: {API_BASE_URL}")
    print("-" * 50)

    # Test 1: Check if API is accessible
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        print(f"✅ API Root accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ API Root failed: {e}")
        return

    # Test 2: Check authentication endpoints
    try:
        response = requests.get(f"{API_BASE_URL}/auth/", timeout=10)
        print(f"✅ Auth endpoints accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ Auth endpoints failed: {e}")

    # Test 3: Try login with actual test credentials
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "remember_me": True
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        print(f"✅ Login endpoint accessible: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(
                f"✅ Login successful! User: {data.get('user', {}).get('email', 'Unknown')}")
        elif response.status_code == 400:
            print(
                f"⚠️  Login failed (expected for test credentials): {response.json()}")
        else:
            print(
                f"⚠️  Unexpected response: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"❌ Login test failed: {e}")

    print("-" * 50)
    print("Authentication API test completed!")


if __name__ == "__main__":
    test_auth_endpoints()
