"""
Analytics URLs
URL configuration for performance monitoring and analytics
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    PerformanceMetricViewSet, SystemHealthMetricViewSet,
    RealTimeMetricViewSet, PerformanceAlertViewSet,
    PerformanceDashboardViewSet
)

# Create router for API endpoints
router = DefaultRouter()
router.register(r'performance-metrics', PerformanceMetricViewSet, basename='performance-metrics')
router.register(r'system-health', SystemHealthMetricViewSet, basename='system-health')
router.register(r'realtime-metrics', RealTimeMetricViewSet, basename='realtime-metrics')
router.register(r'alerts', PerformanceAlertViewSet, basename='alerts')
router.register(r'dashboards', PerformanceDashboardViewSet, basename='dashboards')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    
    # Additional custom endpoints can be added here
]
