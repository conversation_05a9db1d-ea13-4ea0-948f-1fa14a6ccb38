"""
Service Catalog serializers for Vierla Beauty Services Marketplace
Enhanced Django REST Framework serializers with mobile-first design
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import ServiceCategory, ServiceProvider, Service

User = get_user_model()


class ServiceCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for ServiceCategory with hierarchical support
    """
    has_subcategories = serializers.ReadOnlyField()
    service_count = serializers.ReadOnlyField()
    subcategories = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'slug', 'description', 'icon', 'color', 'image',
            'parent', 'is_popular', 'is_active', 'sort_order', 'mobile_icon',
            'has_subcategories', 'service_count', 'subcategories',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_subcategories(self, obj):
        """Get subcategories for hierarchical display"""
        if obj.has_subcategories:
            subcategories = obj.subcategories.filter(is_active=True)
            return ServiceCategoryListSerializer(subcategories, many=True).data
        return []


class ServiceCategoryListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for category lists (no nested subcategories)
    """
    service_count = serializers.ReadOnlyField()
    
    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'slug', 'icon', 'color', 'mobile_icon',
            'is_popular', 'service_count'
        ]


class ServiceProviderListSerializer(serializers.ModelSerializer):
    """
    Serializer for ServiceProvider list view with essential information
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_first_name = serializers.CharField(source='user.first_name', read_only=True)
    user_last_name = serializers.CharField(source='user.last_name', read_only=True)
    full_address = serializers.ReadOnlyField()
    has_location = serializers.ReadOnlyField()
    category_names = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceProvider
        fields = [
            'id', 'user_email', 'user_first_name', 'user_last_name',
            'business_name', 'business_description', 'business_phone',
            'city', 'state', 'country', 'full_address', 'has_location',
            'profile_image', 'is_verified', 'is_featured', 'is_active',
            'rating', 'review_count', 'total_bookings', 'years_of_experience',
            'category_names', 'created_at'
        ]
    
    def get_category_names(self, obj):
        """Get list of category names for this provider"""
        return [category.name for category in obj.categories.filter(is_active=True)]


class ServiceProviderSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for ServiceProvider with full information
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_first_name = serializers.CharField(source='user.first_name', read_only=True)
    user_last_name = serializers.CharField(source='user.last_name', read_only=True)
    full_address = serializers.ReadOnlyField()
    has_location = serializers.ReadOnlyField()
    categories_detail = ServiceCategoryListSerializer(source='categories', many=True, read_only=True)
    
    class Meta:
        model = ServiceProvider
        fields = [
            'id', 'user_email', 'user_first_name', 'user_last_name',
            'business_name', 'business_description', 'business_phone', 'business_email',
            'address', 'city', 'state', 'zip_code', 'country', 'full_address',
            'latitude', 'longitude', 'has_location', 'website', 'instagram_handle',
            'facebook_url', 'profile_image', 'cover_image', 'is_verified',
            'is_featured', 'is_active', 'categories', 'categories_detail',
            'rating', 'review_count', 'total_bookings', 'years_of_experience',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user_email', 'user_first_name', 'user_last_name', 'created_at', 'updated_at']


class ServiceListSerializer(serializers.ModelSerializer):
    """
    Serializer for Service list view with essential information
    """
    provider_name = serializers.CharField(source='provider.business_name', read_only=True)
    provider_rating = serializers.DecimalField(source='provider.rating', max_digits=3, decimal_places=2, read_only=True)
    provider_city = serializers.CharField(source='provider.city', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_icon = serializers.CharField(source='category.icon', read_only=True)
    display_price = serializers.ReadOnlyField()
    display_duration = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'short_description', 'base_price', 'price_type',
            'max_price', 'display_price', 'duration', 'display_duration',
            'image', 'is_popular', 'is_available', 'booking_count',
            'provider_name', 'provider_rating', 'provider_city',
            'category_name', 'category_icon', 'created_at'
        ]


class ServiceSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for Service with full information
    """
    provider_detail = ServiceProviderListSerializer(source='provider', read_only=True)
    category_detail = ServiceCategoryListSerializer(source='category', read_only=True)
    display_price = serializers.ReadOnlyField()
    display_duration = serializers.ReadOnlyField()
    total_duration_with_buffer = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'provider', 'category', 'name', 'description', 'short_description',
            'mobile_description', 'base_price', 'price_type', 'max_price',
            'display_price', 'duration', 'buffer_time', 'display_duration',
            'total_duration_with_buffer', 'image', 'is_popular', 'is_available',
            'is_active', 'requirements', 'preparation_instructions', 'booking_count',
            'provider_detail', 'category_detail', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'booking_count', 'created_at', 'updated_at']


class ServiceCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating services
    """
    
    class Meta:
        model = Service
        fields = [
            'provider', 'category', 'name', 'description', 'short_description',
            'mobile_description', 'base_price', 'price_type', 'max_price',
            'duration', 'buffer_time', 'image', 'is_popular', 'is_available',
            'is_active', 'requirements', 'preparation_instructions'
        ]
    
    def validate_name(self, value):
        """Validate service name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Service name is required")

        if len(value.strip()) < 3:
            raise serializers.ValidationError("Service name must be at least 3 characters long")

        if len(value) > 100:
            raise serializers.ValidationError("Service name cannot exceed 100 characters")

        return value.strip()

    def validate_description(self, value):
        """Validate service description"""
        if not value or not value.strip():
            raise serializers.ValidationError("Service description is required")

        if len(value.strip()) < 10:
            raise serializers.ValidationError("Description must be at least 10 characters long")

        if len(value) > 1000:
            raise serializers.ValidationError("Description cannot exceed 1000 characters")

        return value.strip()

    def validate_base_price(self, value):
        """Validate base price"""
        if value is None:
            raise serializers.ValidationError("Base price is required")

        if value <= 0:
            raise serializers.ValidationError("Base price must be greater than 0")

        if value > 10000:
            raise serializers.ValidationError("Base price cannot exceed $10,000")

        return value

    def validate_duration(self, value):
        """Validate service duration"""
        if value is None:
            raise serializers.ValidationError("Duration is required")

        if value <= 0:
            raise serializers.ValidationError("Duration must be greater than 0 minutes")

        if value > 480:  # 8 hours
            raise serializers.ValidationError("Duration cannot exceed 8 hours (480 minutes)")

        return value

    def validate(self, data):
        """Custom validation for service data"""
        # Validate price range
        if data.get('price_type') == 'range':
            if not data.get('max_price'):
                raise serializers.ValidationError({
                    'max_price': "max_price is required when price_type is 'range'"
                })
            if data.get('max_price') <= data.get('base_price', 0):
                raise serializers.ValidationError({
                    'max_price': "max_price must be greater than base_price"
                })

        # Validate category exists and is active
        category = data.get('category')
        if category and not category.is_active:
            raise serializers.ValidationError({
                'category': "Selected category is not available"
            })

        return data


class ProviderServiceCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for provider service creation (excludes provider field)
    """

    class Meta:
        model = Service
        fields = [
            'category', 'name', 'description', 'short_description',
            'mobile_description', 'base_price', 'price_type', 'max_price',
            'duration', 'buffer_time', 'image', 'is_popular', 'is_available',
            'requirements', 'preparation_instructions'
        ]

    def validate_name(self, value):
        """Validate service name"""
        if not value or not value.strip():
            raise serializers.ValidationError("Service name is required")

        if len(value.strip()) < 3:
            raise serializers.ValidationError("Service name must be at least 3 characters long")

        if len(value) > 100:
            raise serializers.ValidationError("Service name cannot exceed 100 characters")

        # Check for duplicate names for the same provider (during creation)
        if self.instance is None:  # Creating new service
            request = self.context.get('request')
            if request and hasattr(request.user, 'provider_profile'):
                provider = request.user.provider_profile
                if Service.objects.filter(
                    provider=provider,
                    name__iexact=value.strip(),
                    is_active=True
                ).exists():
                    raise serializers.ValidationError(
                        "You already have a service with this name"
                    )

        return value.strip()

    def validate_description(self, value):
        """Validate service description"""
        if not value or not value.strip():
            raise serializers.ValidationError("Service description is required")

        if len(value.strip()) < 10:
            raise serializers.ValidationError("Description must be at least 10 characters long")

        if len(value) > 1000:
            raise serializers.ValidationError("Description cannot exceed 1000 characters")

        return value.strip()

    def validate_base_price(self, value):
        """Validate base price"""
        if value is None:
            raise serializers.ValidationError("Base price is required")

        if value <= 0:
            raise serializers.ValidationError("Base price must be greater than 0")

        if value > 10000:
            raise serializers.ValidationError("Base price cannot exceed $10,000")

        return value

    def validate_duration(self, value):
        """Validate service duration"""
        if value is None:
            raise serializers.ValidationError("Duration is required")

        if value <= 0:
            raise serializers.ValidationError("Duration must be greater than 0 minutes")

        if value > 480:  # 8 hours
            raise serializers.ValidationError("Duration cannot exceed 8 hours (480 minutes)")

        return value

    def validate_buffer_time(self, value):
        """Validate buffer time"""
        if value is not None and value < 0:
            raise serializers.ValidationError("Buffer time cannot be negative")

        if value is not None and value > 120:  # 2 hours
            raise serializers.ValidationError("Buffer time cannot exceed 2 hours (120 minutes)")

        return value

    def validate(self, data):
        """Custom validation for service data"""
        # Validate price range
        if data.get('price_type') == 'range':
            if not data.get('max_price'):
                raise serializers.ValidationError({
                    'max_price': "max_price is required when price_type is 'range'"
                })
            if data.get('max_price') <= data.get('base_price', 0):
                raise serializers.ValidationError({
                    'max_price': "max_price must be greater than base_price"
                })

        # Validate service limit for unverified providers
        if self.instance is None:  # Creating new service
            request = self.context.get('request')
            if request and hasattr(request.user, 'provider_profile'):
                provider = request.user.provider_profile
                if not provider.is_verified:
                    active_services_count = Service.objects.filter(
                        provider=provider,
                        is_active=True
                    ).count()
                    if active_services_count >= 3:
                        raise serializers.ValidationError(
                            "Unverified providers are limited to 3 active services. "
                            "Please verify your account to add more services."
                        )

        # Validate category exists and is active
        category = data.get('category')
        if category and not category.is_active:
            raise serializers.ValidationError({
                'category': "Selected category is not available"
            })

        return data


class ServiceSearchSerializer(serializers.Serializer):
    """
    Serializer for service search parameters
    """
    search = serializers.CharField(required=False, allow_blank=True)
    category = serializers.UUIDField(required=False)
    categories = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True
    )
    min_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, min_value=0)
    max_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, min_value=0)
    min_duration = serializers.IntegerField(required=False, min_value=1)
    max_duration = serializers.IntegerField(required=False, min_value=1)
    min_rating = serializers.DecimalField(max_digits=3, decimal_places=2, required=False, min_value=0, max_value=5)
    city = serializers.CharField(required=False, allow_blank=True)
    is_popular = serializers.BooleanField(required=False)
    is_available = serializers.BooleanField(required=False)
    provider_verified = serializers.BooleanField(required=False)
    sort_by = serializers.ChoiceField(
        choices=[
            'relevance', 'price_low', 'price_high', 'rating',
            'popularity', 'duration', 'newest', 'distance'
        ],
        required=False,
        default='relevance'
    )
    
    def validate(self, data):
        """Custom validation for search parameters"""
        # Validate price range
        min_price = data.get('min_price')
        max_price = data.get('max_price')
        if min_price and max_price and min_price > max_price:
            raise serializers.ValidationError(
                "min_price cannot be greater than max_price"
            )
        
        # Validate duration range
        min_duration = data.get('min_duration')
        max_duration = data.get('max_duration')
        if min_duration and max_duration and min_duration > max_duration:
            raise serializers.ValidationError(
                "min_duration cannot be greater than max_duration"
            )
        
        return data
