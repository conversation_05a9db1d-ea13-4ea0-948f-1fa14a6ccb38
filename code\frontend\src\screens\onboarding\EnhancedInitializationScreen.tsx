/**
 * Enhanced InitializationScreen Component
 * Rebuilt with shadcn/ui design patterns for better styling and user experience
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  Animated,
  Dimensions,
  } from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button, Card, Progress } from '../../components/ui';
import { colors, spacing } from '../../theme';

type InitializationScreenNavigationProp = StackNavigationProp<any, 'Initialization'>;

interface InitializationState {
  isLoading: boolean;
  error: string | null;
  progress: number;
  currentStep: string;
  stepIndex: number;
}

interface InitializationScreenProps {
  onComplete?: () => void;
  onBack?: () => void;
  isLoading?: boolean;
}

const { width, height } = Dimensions.get('window');

const INITIALIZATION_STEPS = [
  { message: 'Initializing app...', duration: 800 },
  { message: 'Checking user data...', duration: 600 },
  { message: 'Loading preferences...', duration: 400 },
  { message: 'Finalizing setup...', duration: 500 },
];

export const EnhancedInitializationScreen: React.FC<InitializationScreenProps> = ({
  onComplete,
  onBack,
  isLoading: externalLoading,
}) => {
  const navigation = useNavigation<InitializationScreenNavigationProp>();
  const [state, setState] = useState<InitializationState>({
    isLoading: true,
    error: null,
    progress: 0,
    currentStep: INITIALIZATION_STEPS[0].message,
    stepIndex: 0,
  });

  // Animation values
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const contentOpacity = useRef(new Animated.Value(0)).current;
  const slideUpAnimation = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    startAnimations();
    initializeApp();
  }, []);

  const startAnimations = () => {
    // Logo entrance animation
    Animated.parallel([
      Animated.timing(logoOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Content slide up animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(slideUpAnimation, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    }, 300);
  };

  const updateProgress = (stepIndex: number, message: string) => {
    const progress = ((stepIndex + 1) / INITIALIZATION_STEPS.length) * 100;
    setState(prev => ({
      ...prev,
      progress,
      currentStep: message,
      stepIndex,
    }));
  };

  const initializeApp = async () => {
    try {
      for (let i = 0; i < INITIALIZATION_STEPS.length; i++) {
        const step = INITIALIZATION_STEPS[i];
        updateProgress(i, step.message);
        
        // Perform actual initialization logic based on step
        if (i === 1) {
          await checkExistingUserData();
        } else if (i === 2) {
          await checkOnboardingStatus();
        }
        
        await new Promise(resolve => setTimeout(resolve, step.duration));
      }

      // Navigation logic
      if (onComplete) {
        onComplete();
      } else {
        const existingUserData = await checkExistingUserData();
        const onboardingCompleted = await checkOnboardingStatus();

        if (existingUserData && onboardingCompleted) {
          navigation.reset({
            index: 0,
            routes: [{ name: 'Auth' }],
          });
        } else if (onboardingCompleted) {
          navigation.reset({
            index: 0,
            routes: [{ name: 'Auth' }],
          });
        } else {
          navigation.navigate('RoleSelection');
        }
      }

    } catch (error) {
      console.error('Initialization error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to initialize app. Please try again.',
      }));
    }
  };

  const checkExistingUserData = async (): Promise<boolean> => {
    try {
      const accessToken = await AsyncStorage.getItem('access_token');
      const user = await AsyncStorage.getItem('user');
      return !!(accessToken && user);
    } catch (error) {
      console.error('Error checking user data:', error);
      return false;
    }
  };

  const checkOnboardingStatus = async (): Promise<boolean> => {
    try {
      const onboardingStatus = await AsyncStorage.getItem('onboarding_completed');
      return onboardingStatus === 'true';
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      return false;
    }
  };

  const handleRetry = () => {
    setState({
      isLoading: true,
      error: null,
      progress: 0,
      currentStep: INITIALIZATION_STEPS[0].message,
      stepIndex: 0,
    });
    initializeApp();
  };

  if (state.error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
        
        <View style={styles.content}>
          <Card variant="elevated" style={styles.errorCard}>
            <View style={styles.errorContent}>
              <Text variant="h3" color="destructive" align="center" style={styles.errorTitle}>
                Initialization Failed
              </Text>
              
              <Text variant="body" color="secondary" align="center" style={styles.errorMessage}>
                {state.error}
              </Text>
              
              <Button
                variant="outline"
                onPress={handleRetry}
                style={styles.retryButton}
              >
                Try Again
              </Button>
            </View>
          </Card>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />

      <View style={styles.content}>
        {/* Animated Logo Section */}
        <Animated.View
          style={[
            styles.logoSection,
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }]
            }
          ]}
        >
          <View style={styles.logoContainer}>
            <View style={styles.logo}>
              <Text variant="h1" color="accent" style={styles.logoText}>
                V
              </Text>
            </View>
            <Text variant="h2" weight="bold" align="center" style={styles.appName}>
              Vierla
            </Text>
            <Text variant="caption" color="secondary" align="center">
              Your trusted service marketplace
            </Text>
          </View>
        </Animated.View>

        {/* Animated Content Section */}
        <Animated.View
          style={[
            styles.loadingSection,
            {
              opacity: contentOpacity,
              transform: [{ translateY: slideUpAnimation }]
            }
          ]}
        >
          <Card variant="elevated" style={styles.progressCard}>
            <View style={styles.progressContent}>
              <Progress
                value={state.progress}
                variant="default"
                size="default"
                animated={true}
                style={styles.progressBar}
                testID="progress-bar"
              />
              
              <Text variant="body" color="secondary" align="center" style={styles.progressText}>
                {state.currentStep}
              </Text>
              
              <View style={styles.stepsIndicator}>
                {INITIALIZATION_STEPS.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.stepDot,
                      index <= state.stepIndex ? styles.stepDotActive : styles.stepDotInactive
                    ]}
                  />
                ))}
              </View>
            </View>
          </Card>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: spacing.xl * 2,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  logoText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: colors.white,
  },
  appName: {
    marginBottom: spacing.xs,
  },
  loadingSection: {
    width: '100%',
    maxWidth: 320,
  },
  progressCard: {
    padding: spacing.lg,
  },
  progressContent: {
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    marginBottom: spacing.md,
  },
  progressText: {
    marginBottom: spacing.lg,
  },
  stepsIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: spacing.xs,
  },
  stepDotActive: {
    backgroundColor: colors.primary,
  },
  stepDotInactive: {
    backgroundColor: colors.border,
  },
  errorCard: {
    maxWidth: 320,
    padding: spacing.lg,
  },
  errorContent: {
    alignItems: 'center',
  },
  errorTitle: {
    marginBottom: spacing.md,
  },
  errorMessage: {
    marginBottom: spacing.xl,
    lineHeight: 22,
  },
  retryButton: {
    minWidth: 120,
  },
});

export default EnhancedInitializationScreen;
