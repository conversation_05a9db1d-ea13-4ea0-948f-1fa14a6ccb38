#!/usr/bin/env python3
"""
Service Categories Verification Script
Verifies that all required service categories exist in the database
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceCategory
from django.utils.text import slugify

# Required service categories
REQUIRED_CATEGORIES = [
    {
        'name': 'Barber<PERSON>',
        'icon': '✂️',
        'color': '#2A4B32',
        'description': 'Professional barbering services including haircuts, beard trims, and grooming',
        'sort_order': 1
    },
    {
        'name': 'Makeup',
        'icon': '💄',
        'color': '#E91E63',
        'description': 'Professional makeup services for special events, photoshoots, and everyday looks',
        'sort_order': 2
    },
    {
        'name': 'Salons',
        'icon': '💇‍♀️',
        'color': '#8FBC8F',
        'description': 'Full-service hair salons offering cuts, styling, coloring, and treatments',
        'sort_order': 3
    },
    {
        'name': 'Locs',
        'icon': '🌀',
        'color': '#43e97b',
        'description': 'Loc maintenance, installation, and styling services',
        'sort_order': 4
    },
    {
        'name': 'Braids',
        'icon': '🤎',
        'color': '#4facfe',
        'description': 'Professional braiding services including protective styles and creative designs',
        'sort_order': 5
    },
    {
        'name': 'Nails',
        'icon': '💅',
        'color': '#9C27B0',
        'description': 'Nail care services including manicures, pedicures, and nail art',
        'sort_order': 6
    },
    {
        'name': 'Eyebrows',
        'icon': '👁️',
        'color': '#673AB7',
        'description': 'Eyebrow shaping, threading, waxing, and microblading services',
        'sort_order': 7
    },
    {
        'name': 'Eyelashes',
        'icon': '👁️‍🗨️',
        'color': '#f093fb',
        'description': 'Eyelash extensions, lifts, tinting, and enhancement services',
        'sort_order': 8
    }
]

def verify_and_create_categories():
    """Verify all required categories exist and create missing ones"""
    print("🔍 Verifying service categories...")
    
    existing_categories = {cat.name: cat for cat in ServiceCategory.objects.all()}
    print(f"📊 Found {len(existing_categories)} existing categories")
    
    created_count = 0
    updated_count = 0
    
    for category_data in REQUIRED_CATEGORIES:
        name = category_data['name']
        
        if name in existing_categories:
            # Update existing category if needed
            category = existing_categories[name]
            updated = False
            
            if category.icon != category_data['icon']:
                category.icon = category_data['icon']
                updated = True
            
            if category.color != category_data['color']:
                category.color = category_data['color']
                updated = True
                
            if category.description != category_data['description']:
                category.description = category_data['description']
                updated = True
                
            if category.sort_order != category_data['sort_order']:
                category.sort_order = category_data['sort_order']
                updated = True
            
            if updated:
                category.save()
                updated_count += 1
                print(f"✅ Updated category: {name}")
            else:
                print(f"✓ Category exists: {name}")
        else:
            # Create new category
            category = ServiceCategory.objects.create(
                name=name,
                slug=slugify(name),
                icon=category_data['icon'],
                color=category_data['color'],
                description=category_data['description'],
                sort_order=category_data['sort_order'],
                is_active=True,
                is_popular=True
            )
            created_count += 1
            print(f"✨ Created category: {name}")
    
    print(f"\n📈 Summary:")
    print(f"   Created: {created_count} categories")
    print(f"   Updated: {updated_count} categories")
    print(f"   Total categories: {ServiceCategory.objects.count()}")
    
    # List all categories
    print(f"\n📋 All service categories:")
    for cat in ServiceCategory.objects.filter(name__in=[c['name'] for c in REQUIRED_CATEGORIES]).order_by('sort_order'):
        print(f"   {cat.sort_order}. {cat.name} ({cat.icon}) - {cat.color}")

if __name__ == '__main__':
    verify_and_create_categories()
