{"authentication/tests.py": true, "debug_test.py::test_registration": true, "authentication/test_acceptance.py::TestCase": true, "authentication/test_acceptance.py::TransactionTestCase": true, "authentication/test_acceptance.py::APITestCase": true, "authentication/test_acceptance.py::UserRegistrationAcceptanceTests": true, "authentication/test_acceptance.py::UserLoginAcceptanceTests": true, "authentication/test_acceptance.py::EmailVerificationAcceptanceTests": true, "authentication/test_acceptance.py::SocialAuthenticationAcceptanceTests": true, "authentication/test_urls.py::TestCase": true, "authentication/test_urls.py::APITestCase": true, "authentication/test_urls.py::AuthenticationURLTests": true, "authentication/test_urls.py::AuthenticationURLAccessTests": true, "catalog/tests/test_account_management.py::TestCase": true, "catalog/tests/test_account_management.py::TestAccountSecurity": true, "catalog/tests/test_account_management.py::TestAccountManager": true, "catalog/tests/test_account_management.py::TestAccountCreationTest": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest": true, "catalog/tests/test_account_management.py::TestAccountSecurityTest": true, "catalog/tests/test_account_management.py::SampleDataGenerationTest": true, "catalog/tests/test_account_management.py::DataVerificationTest": true, "catalog/tests/test_account_management.py::SecurityAuditTest": true, "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_blocked_in_production": true}