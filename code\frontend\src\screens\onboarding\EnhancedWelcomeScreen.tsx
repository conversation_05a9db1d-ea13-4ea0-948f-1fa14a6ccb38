/**
 * Enhanced WelcomeScreen Component
 * Rebuilt with shadcn/ui design patterns for better styling and user experience
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  StatusBar,
  Animated,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';

import { Text, Button, Card } from '../../components/ui';
import { colors, spacing } from '../../theme';

const { width, height } = Dimensions.get('window');

type WelcomeScreenNavigationProp = StackNavigationProp<any, 'Welcome'>;

interface FeatureItem {
  icon: string;
  title: string;
  description: string;
}

const features: FeatureItem[] = [
  {
    icon: '🔍',
    title: 'Discover Services',
    description: 'Find trusted professionals in your area for any service you need',
  },
  {
    icon: '⭐',
    title: 'Verified Reviews',
    description: 'Read authentic reviews from real customers to make informed decisions',
  },
  {
    icon: '💬',
    title: 'Easy Communication',
    description: 'Connect directly with service providers through our secure platform',
  },
  {
    icon: '🔒',
    title: 'Secure Payments',
    description: 'Safe and secure payment processing with buyer protection',
  },
];

export const EnhancedWelcomeScreen: React.FC = () => {
  const navigation = useNavigation<WelcomeScreenNavigationProp>();
  
  // Animation values
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const contentOpacity = useRef(new Animated.Value(0)).current;
  const slideUpAnimation = useRef(new Animated.Value(30)).current;
  const featureAnimations = useRef(
    features.map(() => new Animated.Value(0))
  ).current;

  useEffect(() => {
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Logo entrance animation
    Animated.parallel([
      Animated.timing(logoOpacity, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Content slide up animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideUpAnimation, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }, 400);

    // Staggered feature animations
    setTimeout(() => {
      const featureAnimationSequence = featureAnimations.map((animation, index) =>
        Animated.timing(animation, {
          toValue: 1,
          duration: 400,
          delay: index * 100,
          useNativeDriver: true,
        })
      );

      Animated.stagger(100, featureAnimationSequence).start();
    }, 800);
  };

  const handleGetStarted = () => {
    navigation.navigate('Initialization');
  };

  const renderFeature = (feature: FeatureItem, index: number) => {
    const animatedStyle = {
      opacity: featureAnimations[index],
      transform: [
        {
          translateY: featureAnimations[index].interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0],
          }),
        },
      ],
    };

    return (
      <Animated.View key={index} style={animatedStyle}>
        <Card variant="elevated" style={styles.featureCard}>
          <View style={styles.featureContent}>
            <View style={styles.featureIcon}>
              <Text variant="h2" style={styles.featureEmoji}>
                {feature.icon}
              </Text>
            </View>
            <View style={styles.featureText}>
              <Text variant="h4" weight="semibold" style={styles.featureTitle}>
                {feature.title}
              </Text>
              <Text variant="caption" color="secondary" style={styles.featureDescription}>
                {feature.description}
              </Text>
            </View>
          </View>
        </Card>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      {/* Header Section with Logo */}
      <View style={styles.header}>
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }]
            }
          ]}
          testID="logo-container"
        >
          <View style={styles.logo}>
            <Text variant="h1" style={styles.logoText}>
              V
            </Text>
          </View>
          
          <Text variant="h1" style={styles.appName}>
            Vierla
          </Text>
          
          <Text variant="body" style={styles.tagline}>
            Your trusted service marketplace
          </Text>
        </Animated.View>
      </View>

      {/* Content Section */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: contentOpacity,
            transform: [{ translateY: slideUpAnimation }]
          }
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.welcomeSection}>
            <Text variant="h2" weight="bold" align="center" style={styles.welcomeTitle}>
              Welcome to Vierla
            </Text>
            
            <Text variant="body" color="secondary" align="center" style={styles.welcomeDescription}>
              Connect with trusted service providers in your area. From home repairs to personal services, find exactly what you need.
            </Text>
          </View>

          {/* Features Grid */}
          <View style={styles.featuresSection}>
            <Text variant="h3" weight="semibold" style={styles.featuresTitle}>
              Why Choose Vierla?
            </Text>
            
            <View style={styles.featuresGrid}>
              {features.map(renderFeature)}
            </View>
          </View>

          {/* Action Section */}
          <View style={styles.actionSection}>
            <Button
              variant="default"
              size="lg"
              onPress={handleGetStarted}
              style={styles.getStartedButton}
            >
              Get Started
            </Button>
            
            <Text variant="caption" color="secondary" align="center" style={styles.version}>
              Version 1.0.0 • Made with ❤️
            </Text>
          </View>
        </ScrollView>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  header: {
    flex: 0.4,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  logoContainer: {
    alignItems: 'center',
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  logoText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: colors.primary,
  },
  appName: {
    fontSize: 36,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  tagline: {
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 0.6,
    backgroundColor: colors.white,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
  },
  welcomeSection: {
    marginBottom: spacing.xl,
  },
  welcomeTitle: {
    marginBottom: spacing.md,
  },
  welcomeDescription: {
    lineHeight: 24,
    marginBottom: spacing.lg,
  },
  featuresSection: {
    marginBottom: spacing.xl,
  },
  featuresTitle: {
    marginBottom: spacing.lg,
  },
  featuresGrid: {
    gap: spacing.md,
  },
  featureCard: {
    padding: spacing.md,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  featureEmoji: {
    fontSize: 24,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    marginBottom: spacing.xs,
  },
  featureDescription: {
    lineHeight: 18,
  },
  actionSection: {
    alignItems: 'center',
  },
  getStartedButton: {
    width: '100%',
    marginBottom: spacing.md,
  },
  version: {
    opacity: 0.7,
  },
});

export default EnhancedWelcomeScreen;
