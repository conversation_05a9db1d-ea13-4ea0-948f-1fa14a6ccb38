@echo off
REM ============================================================================
REM Vierla Database Startup Script (Windows)
REM ============================================================================
REM This script starts the database service for the Vierla application.
REM Supports PostgreSQL, MySQL, and SQLite configurations.
REM
REM Usage: .\scripts\development\start-database.bat [options]
REM Options:
REM   --type <type>     Database type (postgresql, mysql, sqlite)
REM   --port <port>     Database port (default: 5432 for PostgreSQL, 3306 for MySQL)
REM   --help            Show this help message
REM ============================================================================

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_NAME=Vierla Database Service
set SCRIPT_VERSION=1.0.0
set LOG_FILE=logs\database-service.log
set BACKEND_DIR=code\backend

REM Default configuration
set DB_TYPE=sqlite
set DB_PORT=5432
set DB_HOST=localhost

REM Colors for output
set COLOR_GREEN=[92m
set COLOR_YELLOW=[93m
set COLOR_RED=[91m
set COLOR_BLUE=[94m
set COLOR_CYAN=[96m
set COLOR_RESET=[0m

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_database
if "%~1"=="--type" (
    set DB_TYPE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo %COLOR_RED%Unknown option: %~1%COLOR_RESET%
goto :show_help

:show_help
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo %COLOR_BLUE%%SCRIPT_NAME% v%SCRIPT_VERSION%%COLOR_RESET%
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo.
echo Usage: %COLOR_CYAN%.\scripts\development\start-database.bat [options]%COLOR_RESET%
echo.
echo Options:
echo   %COLOR_YELLOW%--type ^<type^>%COLOR_RESET%     Database type (postgresql, mysql, sqlite)
echo   %COLOR_YELLOW%--port ^<port^>%COLOR_RESET%     Database port (default: 5432 for PostgreSQL, 3306 for MySQL)
echo   %COLOR_YELLOW%--help%COLOR_RESET%            Show this help message
echo.
echo Examples:
echo   %COLOR_CYAN%.\scripts\development\start-database.bat%COLOR_RESET%
echo   %COLOR_CYAN%.\scripts\development\start-database.bat --type postgresql%COLOR_RESET%
echo   %COLOR_CYAN%.\scripts\development\start-database.bat --type mysql --port 3306%COLOR_RESET%
echo.
echo Supported Database Types:
echo   %COLOR_YELLOW%sqlite%COLOR_RESET%       - SQLite (file-based, no service required)
echo   %COLOR_YELLOW%postgresql%COLOR_RESET%   - PostgreSQL (requires PostgreSQL installation)
echo   %COLOR_YELLOW%mysql%COLOR_RESET%        - MySQL (requires MySQL installation)
echo.
exit /b 0

:start_database
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo %COLOR_BLUE%%SCRIPT_NAME% v%SCRIPT_VERSION%%COLOR_RESET%
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo.

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Log function
call :log "INFO" "Starting database service startup sequence"

REM Load environment configuration if available
if exist "%BACKEND_DIR%\.env" (
    call :log "INFO" "Loading database configuration from .env"
    echo %COLOR_YELLOW%Loading database configuration...%COLOR_RESET%
    for /f "usebackq tokens=1,2 delims==" %%a in ("%BACKEND_DIR%\.env") do (
        if not "%%a"=="" if not "%%a:~0,1%"=="#" (
            if "%%a"=="USE_SQLITE" if "%%b"=="True" set DB_TYPE=sqlite
            if "%%a"=="DATABASE_URL" (
                echo %%b | findstr /i "postgresql" >nul && set DB_TYPE=postgresql
                echo %%b | findstr /i "mysql" >nul && set DB_TYPE=mysql
            )
        )
    )
)

REM Set default port based on database type
if "%DB_TYPE%"=="mysql" set DB_PORT=3306

echo %COLOR_CYAN%Database Configuration:%COLOR_RESET%
echo   Type: %COLOR_YELLOW%!DB_TYPE!%COLOR_RESET%
echo   Host: %COLOR_YELLOW%!DB_HOST!%COLOR_RESET%
echo   Port: %COLOR_YELLOW%!DB_PORT!%COLOR_RESET%
echo.

REM Handle different database types
if /i "%DB_TYPE%"=="sqlite" goto :start_sqlite
if /i "%DB_TYPE%"=="postgresql" goto :start_postgresql
if /i "%DB_TYPE%"=="mysql" goto :start_mysql

echo %COLOR_RED%Error: Unsupported database type: %DB_TYPE%%COLOR_RESET%
exit /b 1

:start_sqlite
call :log "INFO" "Using SQLite database (no service required)"
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo %COLOR_GREEN%SQLite Database Configuration%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.
echo %COLOR_YELLOW%SQLite is a file-based database and doesn't require a separate service.%COLOR_RESET%
echo %COLOR_YELLOW%The database file will be created automatically when you start the backend.%COLOR_RESET%
echo.
echo %COLOR_CYAN%Database Information:%COLOR_RESET%
echo   Database File: %COLOR_BLUE%code\backend\db.sqlite3%COLOR_RESET%
echo   No service startup required
echo.
echo %COLOR_GREEN%SQLite is ready! You can now start the backend server.%COLOR_RESET%
echo Run: %COLOR_CYAN%.\scripts\development\start-backend.bat%COLOR_RESET%
echo.
goto :end

:start_postgresql
call :log "INFO" "Starting PostgreSQL database service"
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo %COLOR_GREEN%Starting PostgreSQL Database Service%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.

REM Check if PostgreSQL is installed
pg_ctl --version >nul 2>&1
if errorlevel 1 (
    call :log "ERROR" "PostgreSQL not found"
    echo %COLOR_RED%Error: PostgreSQL is not installed or not in PATH.%COLOR_RESET%
    echo.
    echo %COLOR_YELLOW%To install PostgreSQL:%COLOR_RESET%
    echo 1. Download from: %COLOR_BLUE%https://www.postgresql.org/download/windows/%COLOR_RESET%
    echo 2. Install and add to PATH
    echo 3. Initialize database cluster
    echo 4. Start PostgreSQL service
    echo.
    exit /b 1
)

REM Check if PostgreSQL service is running
sc query postgresql-x64-14 >nul 2>&1
if errorlevel 1 (
    echo %COLOR_YELLOW%PostgreSQL service not found. Attempting to start...%COLOR_RESET%
    net start postgresql-x64-14 >nul 2>&1
    if errorlevel 1 (
        call :log "ERROR" "Failed to start PostgreSQL service"
        echo %COLOR_RED%Error: Failed to start PostgreSQL service.%COLOR_RESET%
        echo %COLOR_YELLOW%Please start PostgreSQL manually or check your installation.%COLOR_RESET%
        exit /b 1
    )
)

echo %COLOR_GREEN%PostgreSQL service is running!%COLOR_RESET%
echo.
echo %COLOR_CYAN%Connection Information:%COLOR_RESET%
echo   Host: %COLOR_BLUE%!DB_HOST!:!DB_PORT!%COLOR_RESET%
echo   Default Database: %COLOR_BLUE%postgres%COLOR_RESET%
echo   Default User: %COLOR_BLUE%postgres%COLOR_RESET%
echo.
echo %COLOR_YELLOW%Make sure to create the Vierla database and configure DATABASE_URL in .env%COLOR_RESET%
echo.
goto :end

:start_mysql
call :log "INFO" "Starting MySQL database service"
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo %COLOR_GREEN%Starting MySQL Database Service%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.

REM Check if MySQL is installed
mysql --version >nul 2>&1
if errorlevel 1 (
    call :log "ERROR" "MySQL not found"
    echo %COLOR_RED%Error: MySQL is not installed or not in PATH.%COLOR_RESET%
    echo.
    echo %COLOR_YELLOW%To install MySQL:%COLOR_RESET%
    echo 1. Download from: %COLOR_BLUE%https://dev.mysql.com/downloads/mysql/%COLOR_RESET%
    echo 2. Install and add to PATH
    echo 3. Configure MySQL service
    echo 4. Start MySQL service
    echo.
    exit /b 1
)

REM Check if MySQL service is running
sc query MySQL80 >nul 2>&1
if errorlevel 1 (
    echo %COLOR_YELLOW%MySQL service not found. Attempting to start...%COLOR_RESET%
    net start MySQL80 >nul 2>&1
    if errorlevel 1 (
        call :log "ERROR" "Failed to start MySQL service"
        echo %COLOR_RED%Error: Failed to start MySQL service.%COLOR_RESET%
        echo %COLOR_YELLOW%Please start MySQL manually or check your installation.%COLOR_RESET%
        exit /b 1
    )
)

echo %COLOR_GREEN%MySQL service is running!%COLOR_RESET%
echo.
echo %COLOR_CYAN%Connection Information:%COLOR_RESET%
echo   Host: %COLOR_BLUE%!DB_HOST!:!DB_PORT!%COLOR_RESET%
echo   Default Database: %COLOR_BLUE%mysql%COLOR_RESET%
echo   Default User: %COLOR_BLUE%root%COLOR_RESET%
echo.
echo %COLOR_YELLOW%Make sure to create the Vierla database and configure DATABASE_URL in .env%COLOR_RESET%
echo.
goto :end

:end
call :log "INFO" "Database startup sequence completed"
echo %COLOR_GREEN%Database service is ready! You can now start the backend server.%COLOR_RESET%
echo Run: %COLOR_CYAN%.\scripts\development\start-backend.bat%COLOR_RESET%
echo.

exit /b 0

REM ============================================================================
REM Helper Functions
REM ============================================================================

:log
REM Log function: call :log "LEVEL" "MESSAGE"
set LOG_LEVEL=%~1
set LOG_MESSAGE=%~2
set LOG_TIMESTAMP=%date% %time%
echo [%LOG_TIMESTAMP%] [%LOG_LEVEL%] %LOG_MESSAGE% >> %LOG_FILE%
goto :eof
