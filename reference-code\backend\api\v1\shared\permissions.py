# Shared API Permissions
from rest_framework import permissions


class IsAuthenticatedUser(permissions.BasePermission):
    """
    Custom permission to only allow authenticated users.
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        return request.user and request.user.is_authenticated
