/**
 * Modal Component
 * shadcn/ui inspired modal component with overlay and animations
 */

import React, { useEffect } from 'react';
import {
  View,
  Modal as RNModal,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  Dimensions,
  ViewStyle,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { Text } from './Text';
import { Button } from './Button';
import { useTheme } from '../../contexts/ThemeContext';
import { cn } from '../../lib/utils';

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  position?: 'center' | 'bottom' | 'top';
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  animationType?: 'fade' | 'slide' | 'none';
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  overlayStyle?: ViewStyle;
  testID?: string;
}

export interface ModalHeaderProps {
  title?: string;
  onClose?: () => void;
  showCloseButton?: boolean;
  children?: React.ReactNode;
  style?: ViewStyle;
}

export interface ModalContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface ModalFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  size = 'md',
  position = 'center',
  showCloseButton = true,
  closeOnBackdropPress = true,
  animationType = 'fade',
  style,
  contentStyle,
  overlayStyle,
  testID = 'modal',
}) => {
  const { colors, spacing, borderRadius } = useTheme();
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.9);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleBackdropPress = () => {
    if (closeOnBackdropPress) {
      onClose();
    }
  };

  const styles = createModalStyles(colors, spacing, borderRadius);

  const modalStyle = cn(
    styles.modal,
    styles[position],
    styles[size]
  );

  const contentContainerStyle = cn(
    styles.content,
    styles[`content_${size}`]
  );

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
      testID={testID}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <Animated.View
          style={[
            styles.overlay,
            overlayStyle,
            { opacity: fadeAnim }
          ]}
        >
          <SafeAreaView style={styles.safeArea}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  modalStyle,
                  style,
                  {
                    opacity: fadeAnim,
                    transform: [{ scale: scaleAnim }]
                  }
                ]}
              >
                <View style={[contentContainerStyle, contentStyle]}>
                  {(title || showCloseButton) && (
                    <ModalHeader
                      title={title}
                      onClose={onClose}
                      showCloseButton={showCloseButton}
                    />
                  )}
                  {children}
                </View>
              </Animated.View>
            </TouchableWithoutFeedback>
          </SafeAreaView>
        </Animated.View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
};

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  title,
  onClose,
  showCloseButton = true,
  children,
  style,
}) => {
  const { colors, spacing } = useTheme();
  const styles = createModalStyles(colors, spacing, { lg: 12 });

  return (
    <View style={[styles.header, style]}>
      <View style={styles.headerContent}>
        {title && (
          <Text variant="h3" style={styles.title}>
            {title}
          </Text>
        )}
        {children}
      </View>
      {showCloseButton && onClose && (
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          testID="modal-close-button"
        >
          <Ionicons
            name="close"
            size={24}
            color={colors.text.secondary}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export const ModalContent: React.FC<ModalContentProps> = ({
  children,
  style,
}) => {
  const modalContentStyle = {
    flex: 1,
  };

  return (
    <View style={[modalContentStyle, style]}>
      {children}
    </View>
  );
};

export const ModalFooter: React.FC<ModalFooterProps> = ({
  children,
  style,
}) => {
  const { colors, spacing } = useTheme();
  const footerStyle = {
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
    alignItems: 'center' as const,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.text.tertiary,
    marginTop: spacing.md,
    gap: spacing.sm,
  };

  return (
    <View style={[footerStyle, style]}>
      {children}
    </View>
  );
};

const createModalStyles = (colors: any, spacing: any, borderRadius: any) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  safeArea: {
    flex: 1,
  },
  modal: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.lg || 12,
    shadowColor: colors.black || '#000000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  // Positions
  center: {
    alignSelf: 'center',
    marginVertical: 'auto',
  },
  bottom: {
    alignSelf: 'stretch',
    marginTop: 'auto',
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  top: {
    alignSelf: 'stretch',
    marginBottom: 'auto',
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  // Sizes
  sm: {
    maxWidth: screenWidth * 0.8,
    maxHeight: screenHeight * 0.6,
  },
  md: {
    maxWidth: screenWidth * 0.9,
    maxHeight: screenHeight * 0.8,
  },
  lg: {
    maxWidth: screenWidth * 0.95,
    maxHeight: screenHeight * 0.9,
  },
  xl: {
    maxWidth: screenWidth * 0.98,
    maxHeight: screenHeight * 0.95,
  },
  full: {
    width: screenWidth,
    height: screenHeight,
    borderRadius: 0,
  },
  content: {
    flex: 1,
  },
  content_sm: {
    padding: spacing.md,
  },
  content_md: {
    padding: spacing.lg,
  },
  content_lg: {
    padding: spacing.xl,
  },
  content_xl: {
    padding: spacing.xl,
  },
  content_full: {
    padding: spacing.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.text.tertiary,
    marginBottom: spacing.md,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontWeight: '600',
    color: colors.text.primary,
  },
  closeButton: {
    padding: spacing.xs,
    marginLeft: spacing.md,
  },
  modalContent: {
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.text.tertiary,
    marginTop: spacing.md,
    gap: spacing.sm,
  },
});
