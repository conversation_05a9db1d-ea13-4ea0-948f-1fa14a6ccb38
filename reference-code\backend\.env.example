# Vierla Backend Environment Configuration

# Django Settings
SECRET_KEY=your-super-secret-key-here-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://vierla_user:secure_password@localhost:5432/vierla_db
DB_NAME=vierla_db
DB_USER=vierla_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432
DB_SSLMODE=prefer

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_PRIVATE_KEY=your-jwt-private-key
JWT_PUBLIC_KEY=your-jwt-public-key

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# AWS S3 Configuration (for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=vierla-media
AWS_S3_REGION_NAME=us-east-1

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Sentry Configuration (for error tracking)
SENTRY_DSN=your-sentry-dsn

# Mobile Optimization
MOBILE_CACHE_TIMEOUT=300
MOBILE_MAX_PAYLOAD_SIZE=1048576
BATTERY_AWARE_PROCESSING=True

# Development Settings
DJANGO_LOG_LEVEL=INFO
SQL_DEBUG=False
