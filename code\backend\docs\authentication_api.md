# Authentication API Documentation

## Overview

The Vierla authentication system provides secure user registration, login, and profile management with support for traditional email/password authentication and social authentication (Google, Apple).

## Base URL

- **Development**: `http://localhost:8000/api/auth/`
- **Production**: `https://api.vierla.com/api/auth/`

## Authentication

Most endpoints require authentication via JWT tokens. Include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## User Model

```json
{
  "id": "string",
  "email": "string",
  "username": "string", 
  "first_name": "string",
  "last_name": "string",
  "full_name": "string",
  "role": "customer|service_provider|admin",
  "is_verified": "boolean",
  "account_status": "string",
  "avatar": "string|null",
  "phone": "string|null",
  "created_at": "string"
}
```

## Endpoints

### POST /auth/register/

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "password_confirm": "password123",
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "phone": "+**********"
}
```

**Response (201):**
```json
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "user": { /* User object */ },
  "message": "Registration successful"
}
```

### POST /auth/login/

Authenticate user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "access": "jwt_access_token",
  "refresh": "jwt_refresh_token",
  "user": { /* User object */ }
}
```

### POST /auth/social/

Authenticate user with social provider token.

**Request Body:**
```json
{
  "provider": "google|apple",
  "identity_token": "provider_token",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "user_id": "provider_user_id"
}
```

**Response (200):**
```json
{
  "access": "jwt_access_token", 
  "refresh": "jwt_refresh_token",
  "user": { /* User object */ }
}
```

### POST /auth/logout/

Logout user and invalidate refresh token.

**Request Body:**
```json
{
  "refresh": "jwt_refresh_token"
}
```

**Response (200):**
```json
{
  "message": "Logout successful"
}
```

### GET /auth/profile/

Get current user profile (requires authentication).

**Response (200):**
```json
{
  /* User object */
}
```

### PATCH /auth/profile/update/

Update user profile (requires authentication).

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+**********"
}
```

**Response (200):**
```json
{
  /* Updated User object */
}
```

### POST /auth/change-password/

Change user password (requires authentication).

**Request Body:**
```json
{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

**Response (200):**
```json
{
  "message": "Password changed successfully"
}
```

### POST /auth/password-reset/

Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "message": "Password reset email sent"
}
```

### POST /auth/password-reset/confirm/

Confirm password reset with token.

**Request Body:**
```json
{
  "token": "reset_token",
  "password": "newpassword123"
}
```

**Response (200):**
```json
{
  "message": "Password reset successful"
}
```

### POST /auth/verify-email/

Verify email address with token.

**Request Body:**
```json
{
  "token": "verification_token"
}
```

**Response (200):**
```json
{
  "message": "Email verified successfully"
}
```

### POST /auth/resend-verification/

Resend email verification.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "message": "Verification email sent"
}
```

### GET /auth/status/

Check authentication status (requires authentication).

**Response (200):**
```json
{
  /* User object */
}
```

## Error Responses

### 400 Bad Request
```json
{
  "field_name": ["Error message"],
  "detail": "General error message"
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 423 Locked
```json
{
  "detail": "Account temporarily locked due to multiple failed login attempts."
}
```

## Rate Limiting

- Login attempts: 5 per minute per IP
- Registration: 3 per hour per IP
- Password reset: 3 per hour per email

## Security Features

- JWT token authentication with refresh tokens
- Password hashing with Django's PBKDF2
- Account lockout after failed login attempts
- Email verification for new accounts
- Secure password reset flow
- Social authentication token verification
