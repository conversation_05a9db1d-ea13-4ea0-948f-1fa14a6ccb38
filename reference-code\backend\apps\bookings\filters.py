"""
Booking filters for Vierla Beauty Services Marketplace
Advanced filtering with mobile-first design
"""
import django_filters
from django.db.models import Q
from django.utils import timezone as django_timezone
from datetime import timedel<PERSON>

from .models import Booking, TimeSlot, BookingNotification


class BookingFilter(django_filters.FilterSet):
    """
    Advanced filtering for bookings with mobile optimization
    """
    
    # Status filtering
    status = django_filters.MultipleChoiceFilter(
        choices=Booking.Status.choices,
        help_text="Filter by booking status (can select multiple)"
    )
    payment_status = django_filters.MultipleChoiceFilter(
        choices=Booking.PaymentStatus.choices,
        help_text="Filter by payment status"
    )
    location_type = django_filters.MultipleChoiceFilter(
        choices=Booking.LocationType.choices,
        help_text="Filter by location type"
    )
    
    # Date filtering
    scheduled_date = django_filters.DateFilter(
        field_name='scheduled_datetime__date',
        help_text="Filter by specific date (YYYY-MM-DD)"
    )
    scheduled_date_from = django_filters.DateFilter(
        field_name='scheduled_datetime__date',
        lookup_expr='gte',
        help_text="Filter bookings from this date onwards"
    )
    scheduled_date_to = django_filters.DateFilter(
        field_name='scheduled_datetime__date',
        lookup_expr='lte',
        help_text="Filter bookings up to this date"
    )
    
    # Time-based filters
    is_upcoming = django_filters.BooleanFilter(
        method='filter_upcoming',
        help_text="Filter upcoming bookings"
    )
    is_today = django_filters.BooleanFilter(
        method='filter_today',
        help_text="Filter today's bookings"
    )
    is_this_week = django_filters.BooleanFilter(
        method='filter_this_week',
        help_text="Filter this week's bookings"
    )
    
    # Provider/Service filtering
    provider = django_filters.UUIDFilter(
        field_name='provider__id',
        help_text="Filter by provider ID"
    )
    service = django_filters.UUIDFilter(
        field_name='service__id',
        help_text="Filter by service ID"
    )
    service_category = django_filters.UUIDFilter(
        field_name='service__category__id',
        help_text="Filter by service category ID"
    )
    
    # Price filtering
    min_amount = django_filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='gte',
        help_text="Minimum total amount"
    )
    max_amount = django_filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='lte',
        help_text="Maximum total amount"
    )
    
    # Search functionality
    search = django_filters.CharFilter(
        method='filter_search',
        help_text="Search in booking number, customer name, service name"
    )
    
    class Meta:
        model = Booking
        fields = [
            'status', 'payment_status', 'location_type',
            'scheduled_date', 'scheduled_date_from', 'scheduled_date_to',
            'is_upcoming', 'is_today', 'is_this_week',
            'provider', 'service', 'service_category',
            'min_amount', 'max_amount', 'search'
        ]
    
    def filter_upcoming(self, queryset, name, value):
        """Filter upcoming bookings"""
        if value:
            return queryset.filter(
                scheduled_datetime__gte=django_timezone.now(),
                status__in=[Booking.Status.PENDING, Booking.Status.CONFIRMED]
            )
        return queryset
    
    def filter_today(self, queryset, name, value):
        """Filter today's bookings"""
        if value:
            today = django_timezone.now().date()
            return queryset.filter(scheduled_datetime__date=today)
        return queryset
    
    def filter_this_week(self, queryset, name, value):
        """Filter this week's bookings"""
        if value:
            today = django_timezone.now().date()
            start_of_week = today - timedelta(days=today.weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return queryset.filter(
                scheduled_datetime__date__range=[start_of_week, end_of_week]
            )
        return queryset
    
    def filter_search(self, queryset, name, value):
        """Search across multiple fields"""
        if value:
            return queryset.filter(
                Q(booking_number__icontains=value) |
                Q(customer__first_name__icontains=value) |
                Q(customer__last_name__icontains=value) |
                Q(service__name__icontains=value) |
                Q(provider__business_name__icontains=value)
            )
        return queryset


class TimeSlotFilter(django_filters.FilterSet):
    """
    Filtering for time slots and availability
    """
    
    # Provider/Service filtering
    provider = django_filters.UUIDFilter(
        field_name='provider__id',
        help_text="Filter by provider ID"
    )
    service = django_filters.UUIDFilter(
        field_name='service__id',
        help_text="Filter by service ID"
    )
    
    # Date filtering
    date = django_filters.DateFilter(
        help_text="Filter by specific date (YYYY-MM-DD)"
    )
    date_from = django_filters.DateFilter(
        field_name='date',
        lookup_expr='gte',
        help_text="Filter slots from this date onwards"
    )
    date_to = django_filters.DateFilter(
        field_name='date',
        lookup_expr='lte',
        help_text="Filter slots up to this date"
    )
    
    # Availability filtering
    is_available = django_filters.BooleanFilter(
        help_text="Filter by availability"
    )
    is_break = django_filters.BooleanFilter(
        help_text="Filter break slots"
    )
    can_be_booked = django_filters.BooleanFilter(
        method='filter_can_be_booked',
        help_text="Filter slots that can be booked"
    )
    
    # Time filtering
    start_time_from = django_filters.TimeFilter(
        field_name='start_time',
        lookup_expr='gte',
        help_text="Filter slots starting from this time"
    )
    start_time_to = django_filters.TimeFilter(
        field_name='start_time',
        lookup_expr='lte',
        help_text="Filter slots starting up to this time"
    )
    
    # Duration filtering
    min_duration = django_filters.NumberFilter(
        field_name='duration_minutes',
        lookup_expr='gte',
        help_text="Minimum duration in minutes"
    )
    max_duration = django_filters.NumberFilter(
        field_name='duration_minutes',
        lookup_expr='lte',
        help_text="Maximum duration in minutes"
    )
    
    class Meta:
        model = TimeSlot
        fields = [
            'provider', 'service', 'date', 'date_from', 'date_to',
            'is_available', 'is_break', 'can_be_booked',
            'start_time_from', 'start_time_to',
            'min_duration', 'max_duration'
        ]
    
    def filter_can_be_booked(self, queryset, name, value):
        """Filter slots that can actually be booked"""
        if value:
            # This will be further filtered in the view to check can_be_booked() method
            return queryset.filter(
                is_available=True,
                is_break=False
            ).exclude(
                current_bookings__gte=django_filters.F('max_bookings')
            )
        return queryset


class BookingNotificationFilter(django_filters.FilterSet):
    """
    Filtering for booking notifications
    """
    
    # Notification type filtering
    notification_type = django_filters.MultipleChoiceFilter(
        choices=BookingNotification.NotificationType.choices,
        help_text="Filter by notification type"
    )
    channel = django_filters.MultipleChoiceFilter(
        choices=BookingNotification.NotificationChannel.choices,
        help_text="Filter by notification channel"
    )
    status = django_filters.MultipleChoiceFilter(
        choices=BookingNotification.NotificationStatus.choices,
        help_text="Filter by notification status"
    )
    
    # Date filtering
    created_date = django_filters.DateFilter(
        field_name='created_at__date',
        help_text="Filter by creation date"
    )
    scheduled_date = django_filters.DateFilter(
        field_name='scheduled_for__date',
        help_text="Filter by scheduled date"
    )
    
    # Status-based filters
    is_unread = django_filters.BooleanFilter(
        method='filter_unread',
        help_text="Filter unread notifications"
    )
    is_pending = django_filters.BooleanFilter(
        method='filter_pending',
        help_text="Filter pending notifications"
    )
    
    # Booking filtering
    booking = django_filters.UUIDFilter(
        field_name='booking__id',
        help_text="Filter by booking ID"
    )
    
    class Meta:
        model = BookingNotification
        fields = [
            'notification_type', 'channel', 'status',
            'created_date', 'scheduled_date',
            'is_unread', 'is_pending', 'booking'
        ]
    
    def filter_unread(self, queryset, name, value):
        """Filter unread notifications"""
        if value:
            return queryset.filter(read_at__isnull=True)
        return queryset
    
    def filter_pending(self, queryset, name, value):
        """Filter pending notifications"""
        if value:
            return queryset.filter(status=BookingNotification.NotificationStatus.PENDING)
        return queryset
