#!/usr/bin/env python
"""
Create Service Listings Database Seeder
Creates comprehensive service listings for all 40 service providers
"""

import os
import sys
import django
import random
from decimal import Decimal
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import Service, ServiceProvider, ServiceCategory

# Service templates by category
SERVICE_TEMPLATES = {
    'hair-styling': [
        {
            'name': 'Classic Haircut',
            'description': 'Professional haircut with consultation, wash, cut, and basic styling. Perfect for maintaining your current style or making subtle changes.',
            'short_description': 'Professional haircut with wash and style',
            'mobile_description': 'Haircut + wash + style',
            'base_price': Decimal('35.00'),
            'max_price': Decimal('55.00'),
            'duration': 45,
            'buffer_time': 15,
            'requirements': ['Please arrive with clean hair', 'Bring reference photos if desired'],
            'preparation_instructions': 'Wash hair 24 hours before appointment for best results',
            'is_popular': True
        },
        {
            'name': 'Hair Color (Single Process)',
            'description': 'Full head single-process hair color with professional consultation, color application, and styling. Includes toner if needed.',
            'short_description': 'Full head single-process color',
            'mobile_description': 'Single process color',
            'base_price': Decimal('75.00'),
            'max_price': Decimal('120.00'),
            'duration': 120,
            'buffer_time': 30,
            'requirements': ['Patch test required 48 hours prior', 'No recent chemical treatments'],
            'preparation_instructions': 'Do not wash hair 24 hours before appointment',
            'is_popular': True
        },
        {
            'name': 'Highlights (Full Head)',
            'description': 'Full head highlights with foil technique, toner, cut, and style. Creates dimension and brightness throughout your hair.',
            'short_description': 'Full head highlights with cut and style',
            'mobile_description': 'Full head highlights',
            'base_price': Decimal('95.00'),
            'max_price': Decimal('180.00'),
            'duration': 180,
            'buffer_time': 30,
            'requirements': ['Consultation required', 'No recent bleaching'],
            'preparation_instructions': 'Arrive with unwashed hair for better color adhesion',
            'is_popular': True
        },
        {
            'name': 'Balayage',
            'description': 'Hand-painted highlights for a natural, sun-kissed look. Includes consultation, color application, toner, and styling.',
            'short_description': 'Hand-painted highlights for natural look',
            'mobile_description': 'Balayage highlights',
            'base_price': Decimal('120.00'),
            'max_price': Decimal('250.00'),
            'duration': 210,
            'buffer_time': 30,
            'requirements': ['Consultation required', 'Healthy hair condition'],
            'preparation_instructions': 'Come with day-old hair for best results',
            'is_popular': True
        },
        {
            'name': 'Deep Conditioning Treatment',
            'description': 'Intensive hair treatment to restore moisture, shine, and manageability. Perfect for damaged or dry hair.',
            'short_description': 'Intensive moisture and repair treatment',
            'mobile_description': 'Deep conditioning',
            'base_price': Decimal('25.00'),
            'max_price': Decimal('60.00'),
            'duration': 30,
            'buffer_time': 15,
            'requirements': ['Clean hair preferred'],
            'preparation_instructions': 'Wash hair before appointment',
            'is_popular': False
        },
        {
            'name': 'Blowout & Style',
            'description': 'Professional wash, blow-dry, and styling for special events or everyday glamour. Includes heat protection.',
            'short_description': 'Professional wash and blow-dry styling',
            'mobile_description': 'Blowout & style',
            'base_price': Decimal('30.00'),
            'max_price': Decimal('50.00'),
            'duration': 45,
            'buffer_time': 15,
            'requirements': ['Clean hair preferred'],
            'preparation_instructions': 'Arrive with clean, towel-dried hair',
            'is_popular': True
        }
    ],
    'nail-care': [
        {
            'name': 'Classic Manicure',
            'description': 'Traditional manicure with nail shaping, cuticle care, hand massage, and regular polish application.',
            'short_description': 'Traditional manicure with regular polish',
            'mobile_description': 'Classic manicure',
            'base_price': Decimal('25.00'),
            'max_price': Decimal('40.00'),
            'duration': 45,
            'buffer_time': 15,
            'requirements': ['Remove existing polish before appointment'],
            'preparation_instructions': 'Moisturize hands daily leading up to appointment',
            'is_popular': True
        },
        {
            'name': 'Gel Manicure',
            'description': 'Long-lasting gel manicure with nail shaping, cuticle care, and UV-cured gel polish that lasts 2-3 weeks.',
            'short_description': 'Long-lasting gel polish manicure',
            'mobile_description': 'Gel manicure',
            'base_price': Decimal('35.00'),
            'max_price': Decimal('55.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['No recent gel removal', 'Healthy nail beds'],
            'preparation_instructions': 'Remove old polish and moisturize cuticles',
            'is_popular': True
        },
        {
            'name': 'Nail Art Design',
            'description': 'Custom nail art with intricate designs, patterns, or themes. Price varies by complexity and detail level.',
            'short_description': 'Custom artistic nail designs',
            'mobile_description': 'Nail art',
            'base_price': Decimal('10.00'),
            'max_price': Decimal('25.00'),
            'duration': 30,
            'buffer_time': 15,
            'requirements': ['Base manicure required', 'Design consultation'],
            'preparation_instructions': 'Bring inspiration photos for design ideas',
            'is_popular': True
        },
        {
            'name': 'Acrylic Full Set',
            'description': 'Complete acrylic nail extension set with shaping, length customization, and polish or design of choice.',
            'short_description': 'Full set acrylic nail extensions',
            'mobile_description': 'Acrylic full set',
            'base_price': Decimal('45.00'),
            'max_price': Decimal('75.00'),
            'duration': 90,
            'buffer_time': 30,
            'requirements': ['Healthy natural nails', 'No recent infections'],
            'preparation_instructions': 'Remove any existing enhancements',
            'is_popular': True
        },
        {
            'name': 'Pedicure',
            'description': 'Relaxing pedicure with foot soak, exfoliation, nail care, massage, and polish application.',
            'short_description': 'Complete foot care and polish',
            'mobile_description': 'Pedicure',
            'base_price': Decimal('30.00'),
            'max_price': Decimal('50.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['No open wounds on feet', 'Remove existing polish'],
            'preparation_instructions': 'Avoid shaving legs 24 hours before',
            'is_popular': True
        }
    ],
    'lash-extensions': [
        {
            'name': 'Classic Lash Extensions',
            'description': 'Natural-looking lash extensions with one extension per natural lash. Perfect for everyday enhancement.',
            'short_description': 'Natural one-to-one lash extensions',
            'mobile_description': 'Classic lashes',
            'base_price': Decimal('80.00'),
            'max_price': Decimal('150.00'),
            'duration': 120,
            'buffer_time': 30,
            'requirements': ['No eye makeup', 'No caffeine 2 hours prior', 'Patch test recommended'],
            'preparation_instructions': 'Remove all eye makeup and cleanse lashes thoroughly',
            'is_popular': True
        },
        {
            'name': 'Volume Lash Extensions',
            'description': 'Dramatic volume lashes with multiple lightweight extensions per natural lash for fuller, more glamorous look.',
            'short_description': 'Multiple extensions for dramatic volume',
            'mobile_description': 'Volume lashes',
            'base_price': Decimal('120.00'),
            'max_price': Decimal('200.00'),
            'duration': 150,
            'buffer_time': 30,
            'requirements': ['Healthy natural lashes', 'No recent lash treatments', 'Patch test required'],
            'preparation_instructions': 'Arrive with clean lashes and no eye makeup',
            'is_popular': True
        },
        {
            'name': 'Lash Lift & Tint',
            'description': 'Natural lash enhancement that lifts and curls your natural lashes, with optional tinting for definition.',
            'short_description': 'Lift and curl natural lashes with tint',
            'mobile_description': 'Lash lift & tint',
            'base_price': Decimal('60.00'),
            'max_price': Decimal('100.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['No eye makeup', 'Patch test for tint', 'No recent eye treatments'],
            'preparation_instructions': 'Clean lashes and avoid waterproof mascara for 48 hours',
            'is_popular': True
        },
        {
            'name': 'Lash Extension Fill',
            'description': 'Maintenance appointment to replace grown-out extensions and maintain fullness. Recommended every 2-3 weeks.',
            'short_description': 'Maintenance fill for existing extensions',
            'mobile_description': 'Lash fill',
            'base_price': Decimal('40.00'),
            'max_price': Decimal('80.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['Existing extensions from same technician', 'At least 40% retention'],
            'preparation_instructions': 'Clean lashes with lash cleanser before appointment',
            'is_popular': True
        }
    ],
    'braiding': [
        {
            'name': 'Box Braids',
            'description': 'Protective box braids in various sizes. Includes hair washing, sectioning, and braiding with synthetic or human hair.',
            'short_description': 'Protective box braids with extensions',
            'mobile_description': 'Box braids',
            'base_price': Decimal('150.00'),
            'max_price': Decimal('300.00'),
            'duration': 240,
            'buffer_time': 60,
            'requirements': ['Hair must be detangled', 'Bring desired hair extensions', 'Scalp must be healthy'],
            'preparation_instructions': 'Wash and deep condition hair 1-2 days before',
            'is_popular': True
        },
        {
            'name': 'Cornrows',
            'description': 'Traditional cornrow braiding in various patterns and designs. Can be styled up or down depending on preference.',
            'short_description': 'Traditional cornrow braiding patterns',
            'mobile_description': 'Cornrows',
            'base_price': Decimal('80.00'),
            'max_price': Decimal('150.00'),
            'duration': 120,
            'buffer_time': 30,
            'requirements': ['Clean, detangled hair', 'Healthy scalp condition'],
            'preparation_instructions': 'Wash hair and apply leave-in conditioner',
            'is_popular': True
        },
        {
            'name': 'Knotless Braids',
            'description': 'Gentle knotless braiding technique that reduces tension on the scalp while providing beautiful protective styling.',
            'short_description': 'Gentle knotless protective braids',
            'mobile_description': 'Knotless braids',
            'base_price': Decimal('180.00'),
            'max_price': Decimal('350.00'),
            'duration': 300,
            'buffer_time': 60,
            'requirements': ['Healthy hair and scalp', 'Hair extensions provided or brought', 'Patience for longer process'],
            'preparation_instructions': 'Deep condition hair and ensure scalp is moisturized',
            'is_popular': True
        },
        {
            'name': 'Goddess Braids',
            'description': 'Large, elegant braids that can be styled in various updos and patterns. Perfect for special occasions.',
            'short_description': 'Large decorative braids for special styling',
            'mobile_description': 'Goddess braids',
            'base_price': Decimal('120.00'),
            'max_price': Decimal('200.00'),
            'duration': 150,
            'buffer_time': 30,
            'requirements': ['Medium to long hair length', 'Healthy hair condition'],
            'preparation_instructions': 'Moisturize hair and scalp thoroughly',
            'is_popular': False
        }
    ],
    'makeup': [
        {
            'name': 'Bridal Makeup',
            'description': 'Complete bridal makeup including trial session, long-lasting application, and touch-up kit for your special day.',
            'short_description': 'Complete bridal makeup with trial',
            'mobile_description': 'Bridal makeup',
            'base_price': Decimal('150.00'),
            'max_price': Decimal('300.00'),
            'duration': 90,
            'buffer_time': 30,
            'requirements': ['Trial session required', 'Skin prep consultation', 'Bring inspiration photos'],
            'preparation_instructions': 'Moisturize skin and arrive with clean face',
            'is_popular': True
        },
        {
            'name': 'Special Event Makeup',
            'description': 'Professional makeup for special occasions, parties, photoshoots, or date nights. Customized to your style and event.',
            'short_description': 'Professional makeup for special events',
            'mobile_description': 'Event makeup',
            'base_price': Decimal('60.00'),
            'max_price': Decimal('120.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['Clean face', 'Specify event type'],
            'preparation_instructions': 'Exfoliate and moisturize skin 24 hours before',
            'is_popular': True
        },
        {
            'name': 'Makeup Lesson',
            'description': 'Personal makeup lesson teaching techniques for everyday or special occasion looks using your own products.',
            'short_description': 'Personal makeup instruction and tips',
            'mobile_description': 'Makeup lesson',
            'base_price': Decimal('80.00'),
            'max_price': Decimal('150.00'),
            'duration': 90,
            'buffer_time': 15,
            'requirements': ['Bring your makeup collection', 'Clean brushes preferred'],
            'preparation_instructions': 'Come with clean face and bring all makeup tools',
            'is_popular': False
        },
        {
            'name': 'Airbrush Makeup',
            'description': 'Flawless airbrush makeup application for photography, special events, or long-lasting coverage.',
            'short_description': 'Professional airbrush makeup application',
            'mobile_description': 'Airbrush makeup',
            'base_price': Decimal('100.00'),
            'max_price': Decimal('180.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['Skin consultation', 'Allergy disclosure'],
            'preparation_instructions': 'Well-moisturized skin for best application',
            'is_popular': True
        }
    ],
    'skincare': [
        {
            'name': 'European Facial',
            'description': 'Classic European facial with deep cleansing, exfoliation, extractions, mask, and moisturizing treatment.',
            'short_description': 'Classic deep cleansing facial treatment',
            'mobile_description': 'European facial',
            'base_price': Decimal('75.00'),
            'max_price': Decimal('120.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['No recent chemical peels', 'Skin consultation'],
            'preparation_instructions': 'Avoid sun exposure and retinoids 48 hours before',
            'is_popular': True
        },
        {
            'name': 'Hydrating Facial',
            'description': 'Intensive hydration treatment for dry or dehydrated skin with hyaluronic acid and nourishing masks.',
            'short_description': 'Deep hydration treatment for dry skin',
            'mobile_description': 'Hydrating facial',
            'base_price': Decimal('85.00'),
            'max_price': Decimal('140.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['Skin type assessment', 'Allergy disclosure'],
            'preparation_instructions': 'Avoid harsh exfoliants for 3 days prior',
            'is_popular': True
        },
        {
            'name': 'Anti-Aging Facial',
            'description': 'Advanced anti-aging treatment with peptides, antioxidants, and firming techniques to reduce fine lines.',
            'short_description': 'Advanced anti-aging treatment',
            'mobile_description': 'Anti-aging facial',
            'base_price': Decimal('100.00'),
            'max_price': Decimal('180.00'),
            'duration': 90,
            'buffer_time': 15,
            'requirements': ['Skin consultation required', 'Medical history disclosure'],
            'preparation_instructions': 'Discontinue retinoids 1 week before treatment',
            'is_popular': True
        },
        {
            'name': 'Acne Treatment Facial',
            'description': 'Specialized facial for acne-prone skin with deep pore cleansing, extractions, and antibacterial treatment.',
            'short_description': 'Specialized treatment for acne-prone skin',
            'mobile_description': 'Acne facial',
            'base_price': Decimal('80.00'),
            'max_price': Decimal('130.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['Current skincare routine disclosure', 'No recent extractions'],
            'preparation_instructions': 'Avoid picking at skin for 48 hours before',
            'is_popular': True
        },
        {
            'name': 'Chemical Peel',
            'description': 'Professional chemical peel to improve skin texture, reduce hyperpigmentation, and promote cell renewal.',
            'short_description': 'Professional chemical exfoliation treatment',
            'mobile_description': 'Chemical peel',
            'base_price': Decimal('90.00'),
            'max_price': Decimal('200.00'),
            'duration': 45,
            'buffer_time': 30,
            'requirements': ['Patch test required', 'No sun exposure planned', 'Medical clearance if needed'],
            'preparation_instructions': 'Prepare skin with recommended products 2 weeks prior',
            'is_popular': False
        }
    ],
    'massage': [
        {
            'name': 'Swedish Massage',
            'description': 'Classic relaxation massage with long, flowing strokes to reduce tension and promote overall wellness.',
            'short_description': 'Classic relaxation massage therapy',
            'mobile_description': 'Swedish massage',
            'base_price': Decimal('80.00'),
            'max_price': Decimal('120.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['Health questionnaire', 'No recent injuries'],
            'preparation_instructions': 'Hydrate well and avoid large meals 2 hours before',
            'is_popular': True
        },
        {
            'name': 'Deep Tissue Massage',
            'description': 'Therapeutic massage targeting deeper muscle layers to relieve chronic tension and muscle knots.',
            'short_description': 'Therapeutic deep muscle treatment',
            'mobile_description': 'Deep tissue massage',
            'base_price': Decimal('90.00'),
            'max_price': Decimal('140.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['Medical clearance if needed', 'Pain tolerance discussion'],
            'preparation_instructions': 'Communicate any problem areas and pain levels',
            'is_popular': True
        },
        {
            'name': 'Hot Stone Massage',
            'description': 'Relaxing massage using heated stones to warm muscles and enhance the therapeutic benefits of massage.',
            'short_description': 'Heated stone relaxation therapy',
            'mobile_description': 'Hot stone massage',
            'base_price': Decimal('100.00'),
            'max_price': Decimal('160.00'),
            'duration': 90,
            'buffer_time': 15,
            'requirements': ['Temperature sensitivity check', 'No circulation issues'],
            'preparation_instructions': 'Arrive hydrated and inform of any heat sensitivities',
            'is_popular': True
        },
        {
            'name': 'Prenatal Massage',
            'description': 'Specialized massage for expecting mothers to relieve pregnancy-related discomfort and promote relaxation.',
            'short_description': 'Specialized massage for expecting mothers',
            'mobile_description': 'Prenatal massage',
            'base_price': Decimal('85.00'),
            'max_price': Decimal('130.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['Doctor approval required', 'Second trimester or later', 'Pregnancy stage disclosure'],
            'preparation_instructions': 'Bring doctor clearance and comfortable clothing',
            'is_popular': False
        },
        {
            'name': 'Sports Massage',
            'description': 'Performance-focused massage for athletes to prevent injury, enhance performance, and aid recovery.',
            'short_description': 'Athletic performance and recovery massage',
            'mobile_description': 'Sports massage',
            'base_price': Decimal('95.00'),
            'max_price': Decimal('150.00'),
            'duration': 75,
            'buffer_time': 15,
            'requirements': ['Athletic activity disclosure', 'Injury history'],
            'preparation_instructions': 'Discuss training schedule and any current discomfort',
            'is_popular': False
        }
    ],
    'barbering': [
        {
            'name': 'Classic Haircut',
            'description': 'Traditional mens haircut with consultation, wash, cut, and styling. Includes beard trim if desired.',
            'short_description': 'Traditional mens haircut and styling',
            'mobile_description': 'Classic haircut',
            'base_price': Decimal('25.00'),
            'max_price': Decimal('45.00'),
            'duration': 45,
            'buffer_time': 15,
            'requirements': ['Bring reference photos if desired'],
            'preparation_instructions': 'Come with clean hair for best results',
            'is_popular': True
        },
        {
            'name': 'Fade Haircut',
            'description': 'Modern fade haircut with precise blending and styling. Various fade styles available (high, mid, low).',
            'short_description': 'Modern fade with precise blending',
            'mobile_description': 'Fade haircut',
            'base_price': Decimal('30.00'),
            'max_price': Decimal('50.00'),
            'duration': 60,
            'buffer_time': 15,
            'requirements': ['Specify fade type preference'],
            'preparation_instructions': 'Arrive with clean, dry hair',
            'is_popular': True
        },
        {
            'name': 'Beard Trim & Shape',
            'description': 'Professional beard trimming and shaping with hot towel treatment and moisturizing balm application.',
            'short_description': 'Professional beard trimming and care',
            'mobile_description': 'Beard trim',
            'base_price': Decimal('20.00'),
            'max_price': Decimal('35.00'),
            'duration': 30,
            'buffer_time': 15,
            'requirements': ['Minimum 2 weeks beard growth'],
            'preparation_instructions': 'Clean beard and bring style preferences',
            'is_popular': True
        },
        {
            'name': 'Hot Towel Shave',
            'description': 'Traditional hot towel straight razor shave with pre-shave oil, multiple hot towels, and aftershave.',
            'short_description': 'Traditional straight razor hot towel shave',
            'mobile_description': 'Hot towel shave',
            'base_price': Decimal('35.00'),
            'max_price': Decimal('60.00'),
            'duration': 45,
            'buffer_time': 15,
            'requirements': ['No recent skin irritation', 'Allergy disclosure'],
            'preparation_instructions': 'Avoid shaving for 24 hours before appointment',
            'is_popular': True
        },
        {
            'name': 'Head Shave',
            'description': 'Complete head shave with hot towel treatment, precision shaving, and scalp moisturizing.',
            'short_description': 'Complete head shave with hot towel',
            'mobile_description': 'Head shave',
            'base_price': Decimal('25.00'),
            'max_price': Decimal('40.00'),
            'duration': 30,
            'buffer_time': 15,
            'requirements': ['Scalp condition assessment'],
            'preparation_instructions': 'Inform of any scalp sensitivities',
            'is_popular': False
        }
    ]
}

def get_random_price_variation(base_price, max_price):
    """Generate realistic price variation based on provider experience and location"""
    variation = random.uniform(0.8, 1.2)  # ±20% variation
    new_base = base_price * Decimal(str(variation))
    new_max = max_price * Decimal(str(variation)) if max_price else None
    
    # Round to nearest $5
    new_base = (new_base / 5).quantize(Decimal('1')) * 5
    if new_max:
        new_max = (new_max / 5).quantize(Decimal('1')) * 5
    
    return new_base, new_max

def get_duration_variation(base_duration):
    """Generate realistic duration variation"""
    variation = random.choice([0.9, 1.0, 1.1, 1.2])  # -10% to +20%
    new_duration = int(base_duration * variation)
    
    # Round to nearest 15 minutes
    return ((new_duration + 7) // 15) * 15

def create_services_for_provider(provider, category_slug):
    """Create services for a specific provider based on their category"""
    try:
        category = ServiceCategory.objects.get(slug=category_slug)
        
        if category_slug not in SERVICE_TEMPLATES:
            print(f"⚠️ No service templates for category: {category_slug}")
            return 0
        
        templates = SERVICE_TEMPLATES[category_slug]
        created_count = 0
        
        # Create 3-5 services per provider
        num_services = random.randint(3, min(5, len(templates)))
        selected_templates = random.sample(templates, num_services)
        
        for template in selected_templates:
            # Apply variations based on provider
            base_price, max_price = get_random_price_variation(
                template['base_price'], 
                template.get('max_price')
            )
            duration = get_duration_variation(template['duration'])
            
            # Determine price type
            if max_price and max_price > base_price:
                price_type = 'range'
            else:
                price_type = 'fixed'
                max_price = None
            
            service = Service.objects.create(
                provider=provider,
                category=category,
                name=template['name'],
                description=template['description'],
                short_description=template['short_description'],
                mobile_description=template['mobile_description'],
                base_price=base_price,
                price_type=price_type,
                max_price=max_price,
                duration=duration,
                buffer_time=template['buffer_time'],
                requirements=template['requirements'],
                preparation_instructions=template['preparation_instructions'],
                is_popular=template['is_popular'],
                is_available=True,
                is_active=True,
                booking_count=random.randint(5, 50)  # Random booking history
            )
            
            created_count += 1
            print(f"   ✅ Created service: {service.name} (${base_price})")
        
        return created_count
        
    except ServiceCategory.DoesNotExist:
        print(f"❌ Category '{category_slug}' not found")
        return 0
    except Exception as e:
        print(f"❌ Error creating services for {provider.business_name}: {e}")
        return 0

def main():
    """Main function to create service listings for all providers"""
    print("🚀 Creating Service Listings for All Providers")
    print("=" * 60)
    
    total_services = 0
    total_providers = 0
    
    # Get all service providers grouped by category
    categories = ServiceCategory.objects.filter(is_active=True)
    
    for category in categories:
        providers = ServiceProvider.objects.filter(
            categories=category,
            is_active=True
        )
        
        if providers.exists():
            print(f"\n📂 Creating services for category: {category.name}")
            print("-" * 40)
            
            for provider in providers:
                print(f"🏢 Provider: {provider.business_name}")
                services_created = create_services_for_provider(provider, category.slug)
                total_services += services_created
                total_providers += 1
    
    print(f"\n📊 Summary")
    print("=" * 60)
    print(f"Providers processed: {total_providers}")
    print(f"Total services created: {total_services}")
    print(f"Average services per provider: {total_services / total_providers if total_providers > 0 else 0:.1f}")
    
    if total_services > 0:
        print(f"\n🎉 Successfully created {total_services} service listings!")
        print("📝 Services are now available for booking through the platform")
    else:
        print("\n⚠️ No services were created. Please check the errors above.")

if __name__ == "__main__":
    main()
