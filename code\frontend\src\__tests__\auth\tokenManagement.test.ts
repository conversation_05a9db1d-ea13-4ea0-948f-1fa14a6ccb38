/**
 * Token Management Tests
 * Tests for EPIC-05-CRITICAL: Authentication & UI Enhancement
 * 
 * This test suite covers:
 * 1. Token storage and retrieval
 * 2. Token refresh mechanism
 * 3. API client token interceptors
 * 4. Token expiration handling
 * 5. Authentication header management
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { apiClient } from '../../services/api/client';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockedAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('Token Management', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedAsyncStorage.getItem.mockResolvedValue(null);
    mockedAsyncStorage.setItem.mockResolvedValue();
    mockedAsyncStorage.multiRemove.mockResolvedValue();
  });

  describe('Token Storage', () => {
    it('should store tokens correctly after login', async () => {
      const tokens = {
        access: 'new_access_token',
        refresh: 'new_refresh_token',
      };

      await AsyncStorage.multiSet([
        ['access_token', tokens.access],
        ['refresh_token', tokens.refresh],
      ]);

      expect(mockedAsyncStorage.multiSet).toHaveBeenCalledWith([
        ['access_token', tokens.access],
        ['refresh_token', tokens.refresh],
      ]);
    });

    it('should retrieve stored tokens', async () => {
      mockedAsyncStorage.getItem
        .mockResolvedValueOnce('stored_access_token')
        .mockResolvedValueOnce('stored_refresh_token');

      const accessToken = await AsyncStorage.getItem('access_token');
      const refreshToken = await AsyncStorage.getItem('refresh_token');

      expect(accessToken).toBe('stored_access_token');
      expect(refreshToken).toBe('stored_refresh_token');
    });

    it('should clear tokens on logout', async () => {
      await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);

      expect(mockedAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
    });

    it('should handle storage errors gracefully', async () => {
      mockedAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const token = await AsyncStorage.getItem('access_token').catch(() => null);

      expect(token).toBe(null);
    });
  });

  describe('API Client Token Interceptors', () => {
    it('should add authorization header when token exists', async () => {
      mockedAsyncStorage.getItem.mockResolvedValue('valid_token');

      const config = {
        headers: {},
        url: '/test',
        method: 'GET',
      };

      // Simulate request interceptor
      const token = await AsyncStorage.getItem('access_token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      expect(config.headers.Authorization).toBe('Bearer valid_token');
    });

    it('should not add authorization header when no token exists', async () => {
      mockedAsyncStorage.getItem.mockResolvedValue(null);

      const config = {
        headers: {},
        url: '/test',
        method: 'GET',
      };

      // Simulate request interceptor
      const token = await AsyncStorage.getItem('access_token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      expect(config.headers.Authorization).toBeUndefined();
    });

    it('should handle token retrieval errors in interceptor', async () => {
      mockedAsyncStorage.getItem.mockRejectedValue(new Error('Token error'));

      const config = {
        headers: {},
        url: '/test',
        method: 'GET',
      };

      // Simulate request interceptor with error handling
      try {
        const token = await AsyncStorage.getItem('access_token');
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Error getting auth token:', error);
      }

      expect(config.headers.Authorization).toBeUndefined();
    });
  });

  describe('Token Refresh Mechanism', () => {
    it('should refresh token on 401 response', async () => {
      const originalRequest = {
        headers: {},
        _retry: false,
      };

      const refreshResponse = {
        data: {
          access: 'new_access_token',
          refresh: 'new_refresh_token',
        },
      };

      mockedAsyncStorage.getItem.mockResolvedValue('valid_refresh_token');
      mockedAxios.post.mockResolvedValue(refreshResponse);

      // Simulate 401 error and refresh logic
      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        const refreshToken = await AsyncStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post('/api/auth/refresh/', {
            refresh: refreshToken,
          });

          const { access, refresh } = response.data;
          await AsyncStorage.setItem('access_token', access);
          await AsyncStorage.setItem('refresh_token', refresh);

          originalRequest.headers.Authorization = `Bearer ${access}`;
        }
      }

      expect(mockedAxios.post).toHaveBeenCalledWith('/api/auth/refresh/', {
        refresh: 'valid_refresh_token',
      });
      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith(
        'access_token',
        'new_access_token'
      );
      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith(
        'refresh_token',
        'new_refresh_token'
      );
      expect(originalRequest.headers.Authorization).toBe('Bearer new_access_token');
    });

    it('should clear tokens when refresh fails', async () => {
      const originalRequest = {
        headers: {},
        _retry: false,
      };

      mockedAsyncStorage.getItem.mockResolvedValue('invalid_refresh_token');
      mockedAxios.post.mockRejectedValue(new Error('Refresh failed'));

      // Simulate 401 error and failed refresh
      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = await AsyncStorage.getItem('refresh_token');
          if (refreshToken) {
            await axios.post('/api/auth/refresh/', {
              refresh: refreshToken,
            });
          }
        } catch (refreshError) {
          await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
          console.error('Token refresh failed:', refreshError);
        }
      }

      expect(mockedAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
    });

    it('should not retry refresh if already attempted', async () => {
      const originalRequest = {
        headers: {},
        _retry: true, // Already retried
      };

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      // Simulate 401 error with already retried request
      let refreshAttempted = false;
      if (error.response?.status === 401 && !originalRequest._retry) {
        refreshAttempted = true;
      }

      expect(refreshAttempted).toBe(false);
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('should handle missing refresh token', async () => {
      const originalRequest = {
        headers: {},
        _retry: false,
      };

      mockedAsyncStorage.getItem.mockResolvedValue(null); // No refresh token

      const error = {
        response: { status: 401 },
        config: originalRequest,
      };

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        const refreshToken = await AsyncStorage.getItem('refresh_token');
        if (refreshToken) {
          // This won't execute because refreshToken is null
          await axios.post('/api/auth/refresh/', {
            refresh: refreshToken,
          });
        }
      }

      expect(mockedAxios.post).not.toHaveBeenCalled();
    });
  });

  describe('Token Validation', () => {
    it('should validate token format', () => {
      const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      const invalidToken = 'invalid.token';

      const isValidTokenFormat = (token: string): boolean => {
        return token.split('.').length === 3;
      };

      expect(isValidTokenFormat(validToken)).toBe(true);
      expect(isValidTokenFormat(invalidToken)).toBe(false);
    });

    it('should check token expiration', () => {
      const createMockToken = (exp: number) => {
        const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
        const payload = btoa(JSON.stringify({ exp, sub: '123' }));
        const signature = 'mock_signature';
        return `${header}.${payload}.${signature}`;
      };

      const isTokenExpired = (token: string): boolean => {
        try {
          const parts = token.split('.');
          if (parts.length !== 3) return true;

          const payload = JSON.parse(atob(parts[1]));
          const currentTime = Math.floor(Date.now() / 1000);
          
          return payload.exp < currentTime;
        } catch {
          return true;
        }
      };

      const expiredToken = createMockToken(Math.floor(Date.now() / 1000) - 3600); // 1 hour ago
      const validToken = createMockToken(Math.floor(Date.now() / 1000) + 3600); // 1 hour from now

      expect(isTokenExpired(expiredToken)).toBe(true);
      expect(isTokenExpired(validToken)).toBe(false);
    });
  });

  describe('Security Considerations', () => {
    it('should not log sensitive token data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const token = 'sensitive_token_data';

      // Simulate logging without exposing token
      console.log('Token operation completed', { tokenLength: token.length });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Token operation completed',
        { tokenLength: 19 }
      );
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('sensitive_token_data')
      );

      consoleSpy.mockRestore();
    });

    it('should handle token storage securely', async () => {
      const sensitiveToken = 'very_sensitive_token';

      // Simulate secure storage (in real app, this would use secure storage)
      await AsyncStorage.setItem('access_token', sensitiveToken);

      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith(
        'access_token',
        sensitiveToken
      );
    });
  });
});
