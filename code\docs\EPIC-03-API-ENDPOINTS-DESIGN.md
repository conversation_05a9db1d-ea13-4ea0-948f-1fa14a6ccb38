# EPIC-03: Service Management API Endpoints Design

## Overview
This document outlines the REST API endpoints for service creation and management functionality for providers in the Vierla application. The API follows RESTful principles with proper authentication, authorization, and validation.

## Current Implementation Status

### Existing API Structure ✅
- **Base URL**: `/api/provider/`
- **Authentication**: Required for all endpoints
- **Permission Classes**: `IsServiceProvider`, `IsServiceOwner`, `ServiceLimitCheck`
- **ViewSet**: `ProviderServiceViewSet` with full CRUD operations

## API Endpoint Specifications

### 1. Service Management Endpoints

#### 1.1 List Provider Services
```http
GET /api/provider/services/
```

**Purpose**: Retrieve all services belonging to the authenticated provider

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`

**Query Parameters**:
```typescript
{
  page?: number;           // Pagination page number
  page_size?: number;      // Items per page (default: 20)
  ordering?: string;       // Sort field: name, base_price, duration, created_at, booking_count
  search?: string;         // Search in name and description
  is_active?: boolean;     // Filter by active status
  is_available?: boolean;  // Filter by availability
  category?: string;       // Filter by category ID
  price_min?: number;      // Minimum price filter
  price_max?: number;      // Maximum price filter
}
```

**Response**:
```typescript
{
  count: number;
  next: string | null;
  previous: string | null;
  results: ServiceListItem[];
  provider_summary: {
    total_services: number;
    active_services: number;
    inactive_services: number;
    is_verified: boolean;
    service_limit: number | null;
  };
}

interface ServiceListItem {
  id: string;
  name: string;
  base_price: string;
  duration: number;
  is_active: boolean;
  is_available: boolean;
  booking_count: number;
  category: {
    id: string;
    name: string;
  };
  created_at: string;
  updated_at: string;
}
```

#### 1.2 Create New Service
```http
POST /api/provider/services/
```

**Purpose**: Create a new service for the authenticated provider

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `ServiceLimitCheck`

**Request Body**:
```typescript
{
  name: string;                    // Required, max 200 chars
  description: string;             // Required
  short_description?: string;      // Optional, max 255 chars
  mobile_description?: string;     // Optional
  category: string;                // Required, category UUID
  base_price: string;              // Required, decimal
  price_type: 'fixed' | 'hourly' | 'range' | 'consultation';
  max_price?: string;              // Required if price_type is 'range'
  duration: number;                // Required, minutes
  buffer_time?: number;            // Optional, default 15 minutes
  requirements?: string;           // Optional
  preparation_instructions?: string; // Optional
  is_active?: boolean;             // Optional, default true
  is_available?: boolean;          // Optional, default true
  image?: File;                    // Optional, multipart/form-data
}
```

**Response**:
```typescript
{
  id: string;
  name: string;
  description: string;
  short_description: string;
  mobile_description: string;
  category: CategoryDetail;
  base_price: string;
  price_type: string;
  max_price: string | null;
  duration: number;
  buffer_time: number;
  requirements: string;
  preparation_instructions: string;
  is_active: boolean;
  is_available: boolean;
  booking_count: number;
  image: string | null;
  created_at: string;
  updated_at: string;
}
```

#### 1.3 Retrieve Service Details
```http
GET /api/provider/services/{id}/
```

**Purpose**: Get detailed information about a specific service

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `IsServiceOwner`

**Response**: Same as create response with full service details

#### 1.4 Update Service
```http
PUT /api/provider/services/{id}/
PATCH /api/provider/services/{id}/
```

**Purpose**: Update an existing service (full or partial update)

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `IsServiceOwner`

**Request Body**: Same as create (all fields for PUT, partial for PATCH)
**Response**: Updated service details

#### 1.5 Delete Service (Soft Delete)
```http
DELETE /api/provider/services/{id}/
```

**Purpose**: Soft delete a service (sets is_active to false)

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `IsServiceOwner`

**Response**: `204 No Content`

### 2. Service Action Endpoints

#### 2.1 Toggle Service Status
```http
POST /api/provider/services/{id}/toggle_status/
```

**Purpose**: Toggle service availability (is_available field)

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `IsServiceOwner`

**Response**: Updated service details

#### 2.2 Bulk Update Services
```http
POST /api/provider/services/bulk_update/
```

**Purpose**: Perform bulk operations on multiple services

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`

**Request Body**:
```typescript
{
  service_ids: string[];           // Array of service UUIDs
  action: 'activate' | 'deactivate' | 'delete';
}
```

**Response**:
```typescript
{
  message: string;
  updated_count: number;
}
```

#### 2.3 Service Analytics
```http
GET /api/provider/services/{id}/analytics/
```

**Purpose**: Get analytics data for a specific service

**Authentication**: Required (Bearer Token)
**Permissions**: `IsServiceProvider`, `IsServiceOwner`

**Response**:
```typescript
{
  service_id: string;
  total_bookings: number;
  revenue: number;
  average_rating: number;
  conversion_rate: number;
  popular_times: string[];
  customer_insights: {
    new_customers: number;
    repeat_customers: number;
    total_customers: number;
  };
}
```

## Authentication & Authorization

### Authentication Method
- **Type**: Bearer Token (JWT)
- **Header**: `Authorization: Bearer <token>`
- **Endpoint**: `/api/auth/login/` (returns access token)

### Permission Classes

#### IsServiceProvider
```python
def has_permission(self, request, view):
    return (
        request.user and 
        request.user.is_authenticated and 
        request.user.role == 'service_provider' and
        hasattr(request.user, 'provider_profile')
    )
```

#### IsServiceOwner
```python
def has_object_permission(self, request, view, obj):
    return obj.provider.user == request.user
```

#### ServiceLimitCheck
```python
def has_permission(self, request, view):
    if request.method != 'POST':
        return True
    
    provider = request.user.provider_profile
    if provider.is_verified:
        return True
    
    # Unverified providers limited to 3 active services
    active_services_count = Service.objects.filter(
        provider=provider, is_active=True
    ).count()
    
    return active_services_count < 3
```

## Error Handling

### Standard Error Responses

#### 400 Bad Request
```typescript
{
  error: string;
  details?: {
    field_name: string[];
  };
}
```

#### 401 Unauthorized
```typescript
{
  detail: "Authentication credentials were not provided.";
}
```

#### 403 Forbidden
```typescript
{
  detail: "You do not have permission to perform this action.";
}
```

#### 404 Not Found
```typescript
{
  detail: "Not found.";
}
```

#### 429 Too Many Requests
```typescript
{
  detail: "Request was throttled. Expected available in X seconds.";
}
```

### Validation Errors

#### Service Creation Validation
- **Name**: Required, max 200 characters
- **Description**: Required
- **Category**: Must exist and be active
- **Base Price**: Must be positive decimal
- **Duration**: Must be positive integer (minutes)
- **Price Type**: Must be valid choice
- **Max Price**: Required if price_type is 'range', must be > base_price

#### Service Limit Validation
- Unverified providers: Maximum 3 active services
- Verified providers: No limit

## Rate Limiting

### Throttle Classes
- **Anonymous**: 100 requests/hour
- **Authenticated**: 1000 requests/hour
- **Provider**: 2000 requests/hour

### Specific Limits
- **Service Creation**: 10 requests/hour
- **Bulk Operations**: 5 requests/hour
- **Analytics**: 100 requests/hour

## API Versioning

### Current Version: v1
- **URL Pattern**: `/api/provider/services/`
- **Future Versions**: `/api/v2/provider/services/`

### Backward Compatibility
- Maintain v1 for 12 months after v2 release
- Deprecation warnings in response headers
- Migration guides for breaking changes

## Implementation Status

### ✅ Completed Features
- Full CRUD operations
- Authentication and authorization
- Service ownership validation
- Soft delete functionality
- Bulk operations
- Service status toggle
- Provider service limits
- Filtering and search
- Pagination and ordering

### 🔄 Ready for Enhancement
- Analytics endpoint (currently returns mock data)
- Image upload handling
- Advanced filtering options
- Rate limiting configuration
- API documentation (OpenAPI/Swagger)

This API design provides a comprehensive, secure, and scalable foundation for service management functionality while maintaining consistency with the existing Vierla API architecture.
