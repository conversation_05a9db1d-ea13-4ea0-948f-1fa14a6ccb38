"""
Service Catalog filters for Vierla Beauty Services Marketplace
Enhanced Django Filter integration for mobile-optimized filtering
"""
import django_filters
from django.db.models import Q
from .models import ServiceCategory, ServiceProvider, Service


class ServiceCategoryFilter(django_filters.FilterSet):
    """
    Filter for service categories with hierarchical support
    """
    
    name = django_filters.CharFilter(lookup_expr='icontains')
    is_popular = django_filters.BooleanFilter()
    parent = django_filters.ModelChoiceFilter(queryset=ServiceCategory.objects.all())
    has_subcategories = django_filters.BooleanFilter(method='filter_has_subcategories')
    
    class Meta:
        model = ServiceCategory
        fields = ['name', 'is_popular', 'parent', 'has_subcategories']
    
    def filter_has_subcategories(self, queryset, name, value):
        """Filter categories that have or don't have subcategories"""
        if value:
            return queryset.filter(subcategories__isnull=False).distinct()
        else:
            return queryset.filter(subcategories__isnull=True)


class ServiceProviderFilter(django_filters.FilterSet):
    """
    Filter for service providers with location and rating filters
    """
    
    business_name = django_filters.CharFilter(lookup_expr='icontains')
    city = django_filters.CharFilter(lookup_expr='icontains')
    state = django_filters.CharFilter(lookup_expr='icontains')
    is_verified = django_filters.BooleanFilter()
    is_featured = django_filters.BooleanFilter()
    
    # Rating filters
    min_rating = django_filters.NumberFilter(field_name='rating', lookup_expr='gte')
    max_rating = django_filters.NumberFilter(field_name='rating', lookup_expr='lte')
    
    # Experience filter
    min_experience = django_filters.NumberFilter(field_name='years_of_experience', lookup_expr='gte')
    
    # Category filter
    category = django_filters.ModelMultipleChoiceFilter(
        field_name='categories',
        queryset=ServiceCategory.objects.filter(is_active=True),
        conjoined=False  # OR logic for multiple categories
    )
    
    # Location filters
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    
    class Meta:
        model = ServiceProvider
        fields = [
            'business_name', 'city', 'state', 'is_verified', 'is_featured',
            'min_rating', 'max_rating', 'min_experience', 'category', 'has_location'
        ]
    
    def filter_has_location(self, queryset, name, value):
        """Filter providers that have or don't have location coordinates"""
        if value:
            return queryset.filter(latitude__isnull=False, longitude__isnull=False)
        else:
            return queryset.filter(Q(latitude__isnull=True) | Q(longitude__isnull=True))


class ServiceFilter(django_filters.FilterSet):
    """
    Enhanced filter for services with comprehensive filtering options
    Supports multi-select categories and advanced search capabilities
    """

    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')

    # Provider filters
    provider = django_filters.ModelChoiceFilter(queryset=ServiceProvider.objects.filter(is_active=True))
    provider_name = django_filters.CharFilter(field_name='provider__business_name', lookup_expr='icontains')
    provider_city = django_filters.CharFilter(field_name='provider__city', lookup_expr='icontains')
    provider_verified = django_filters.BooleanFilter(field_name='provider__is_verified')

    # Enhanced multi-select category filter
    categories = django_filters.ModelMultipleChoiceFilter(
        field_name='category',
        queryset=ServiceCategory.objects.filter(is_active=True),
        conjoined=False,  # OR logic for multiple categories
        method='filter_categories'
    )
    
    # Category filters
    category = django_filters.ModelChoiceFilter(queryset=ServiceCategory.objects.filter(is_active=True))
    category_name = django_filters.CharFilter(field_name='category__name', lookup_expr='icontains')
    
    # Price filters
    min_price = django_filters.NumberFilter(field_name='base_price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='base_price', lookup_expr='lte')
    price_type = django_filters.ChoiceFilter(choices=Service._meta.get_field('price_type').choices)
    
    # Duration filters
    min_duration = django_filters.NumberFilter(field_name='duration', lookup_expr='gte')
    max_duration = django_filters.NumberFilter(field_name='duration', lookup_expr='lte')
    
    # Status filters
    is_popular = django_filters.BooleanFilter()
    is_available = django_filters.BooleanFilter()
    
    # Booking filters
    min_bookings = django_filters.NumberFilter(field_name='booking_count', lookup_expr='gte')

    # Enhanced filters for advanced search
    instant_booking = django_filters.BooleanFilter(field_name='instant_booking')
    min_rating = django_filters.NumberFilter(field_name='provider__rating', lookup_expr='gte')
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    search = django_filters.CharFilter(method='filter_search')
    sort_by = django_filters.CharFilter(method='filter_sort')

    class Meta:
        model = Service
        fields = [
            'name', 'description', 'provider', 'provider_name', 'provider_city',
            'provider_verified', 'category', 'categories', 'category_name', 'min_price', 'max_price',
            'price_type', 'min_duration', 'max_duration', 'is_popular', 'is_available',
            'min_bookings', 'instant_booking', 'min_rating', 'has_location', 'search', 'sort_by'
        ]

    def filter_categories(self, queryset, name, value):
        """Filter services by multiple categories (OR logic)"""
        if value:
            return queryset.filter(category__in=value).distinct()
        return queryset

    def filter_has_location(self, queryset, name, value):
        """Filter services that have location information"""
        if value:
            return queryset.filter(
                provider__latitude__isnull=False,
                provider__longitude__isnull=False
            )
        return queryset.filter(
            Q(provider__latitude__isnull=True) | Q(provider__longitude__isnull=True)
        )

    def filter_search(self, queryset, name, value):
        """Advanced search across multiple fields"""
        if value:
            return queryset.filter(
                Q(name__icontains=value) |
                Q(description__icontains=value) |
                Q(provider__business_name__icontains=value) |
                Q(category__name__icontains=value) |
                Q(provider__city__icontains=value)
            ).distinct()
        return queryset

    def filter_sort(self, queryset, name, value):
        """Apply custom sorting to queryset"""
        if value == 'price_low':
            return queryset.order_by('base_price')
        elif value == 'price_high':
            return queryset.order_by('-base_price')
        elif value == 'rating':
            return queryset.order_by('-provider__rating')
        elif value == 'popularity':
            return queryset.order_by('-is_popular', '-booking_count')
        elif value == 'duration':
            return queryset.order_by('duration')
        elif value == 'newest':
            return queryset.order_by('-created_at')
        # Default sorting (distance will be handled in view)
        return queryset.order_by('-is_popular', 'base_price', 'name')
