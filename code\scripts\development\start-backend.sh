#!/bin/bash
# ============================================================================
# Vierla Backend Server Startup Script (Unix/Linux/macOS)
# ============================================================================
# This script starts the Django backend server for the Vierla application
# with proper environment configuration and error handling.
#
# Usage: ./scripts/development/start-backend.sh [options]
# Options:
#   --host <host>     Server host (default: localhost)
#   --port <port>     Server port (default: 8000)
#   --debug           Enable debug mode
#   --help            Show this help message
# ============================================================================

set -e  # Exit on any error

# Script configuration
SCRIPT_NAME="Vierla Backend Server"
SCRIPT_VERSION="1.0.0"
LOG_FILE="logs/backend-server.log"
BACKEND_DIR="code/backend"

# Default configuration
DEFAULT_HOST="localhost"
DEFAULT_PORT="8000"
SERVER_HOST="$DEFAULT_HOST"
SERVER_PORT="$DEFAULT_PORT"
DEBUG_MODE="false"

# Colors for output
COLOR_GREEN='\033[0;32m'
COLOR_YELLOW='\033[1;33m'
COLOR_RED='\033[0;31m'
COLOR_BLUE='\033[0;34m'
COLOR_CYAN='\033[0;36m'
COLOR_RESET='\033[0m'

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${COLOR_RESET}"
}

# Error handling
error_exit() {
    local message=$1
    log "ERROR" "$message"
    print_color "$COLOR_RED" "Error: $message"
    exit 1
}

# Show help message
show_help() {
    print_color "$COLOR_BLUE" "============================================================================"
    print_color "$COLOR_BLUE" "$SCRIPT_NAME v$SCRIPT_VERSION"
    print_color "$COLOR_BLUE" "============================================================================"
    echo
    print_color "$COLOR_CYAN" "Usage: ./scripts/development/start-backend.sh [options]"
    echo
    echo "Options:"
    echo "  ${COLOR_YELLOW}--host <host>${COLOR_RESET}     Server host (default: $DEFAULT_HOST)"
    echo "  ${COLOR_YELLOW}--port <port>${COLOR_RESET}     Server port (default: $DEFAULT_PORT)"
    echo "  ${COLOR_YELLOW}--debug${COLOR_RESET}           Enable debug mode"
    echo "  ${COLOR_YELLOW}--help${COLOR_RESET}            Show this help message"
    echo
    echo "Examples:"
    echo "  ${COLOR_CYAN}./scripts/development/start-backend.sh${COLOR_RESET}"
    echo "  ${COLOR_CYAN}./scripts/development/start-backend.sh --host 0.0.0.0 --port 8080${COLOR_RESET}"
    echo "  ${COLOR_CYAN}./scripts/development/start-backend.sh --debug${COLOR_RESET}"
    echo
    exit 0
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --host)
                SERVER_HOST="$2"
                shift 2
                ;;
            --port)
                SERVER_PORT="$2"
                shift 2
                ;;
            --debug)
                DEBUG_MODE="true"
                shift
                ;;
            --help)
                show_help
                ;;
            *)
                print_color "$COLOR_RED" "Unknown option: $1"
                show_help
                ;;
        esac
    done
}

# Load environment variables from .env file
load_env() {
    if [ -f "$BACKEND_DIR/.env" ]; then
        log "INFO" "Loading environment configuration from .env"
        print_color "$COLOR_YELLOW" "Loading environment configuration..."
        
        # Export variables from .env file
        set -a
        source "$BACKEND_DIR/.env"
        set +a
    else
        log "WARNING" "No .env file found, using defaults"
        print_color "$COLOR_YELLOW" "Warning: No .env file found, using default configuration"
    fi
}

# Check if port is available
check_port() {
    if command -v lsof &> /dev/null; then
        if lsof -i ":$SERVER_PORT" &> /dev/null; then
            log "WARNING" "Port $SERVER_PORT is already in use"
            print_color "$COLOR_YELLOW" "Warning: Port $SERVER_PORT is already in use"
            print_color "$COLOR_YELLOW" "The server may fail to start or another instance may be running"
        fi
    fi
}

# Main server startup function
start_server() {
    print_color "$COLOR_BLUE" "============================================================================"
    print_color "$COLOR_BLUE" "$SCRIPT_NAME v$SCRIPT_VERSION"
    print_color "$COLOR_BLUE" "============================================================================"
    echo

    # Create logs directory if it doesn't exist
    mkdir -p logs

    log "INFO" "Starting backend server startup sequence"

    # Check if backend directory exists
    if [ ! -d "$BACKEND_DIR" ]; then
        error_exit "Backend directory not found. Please run setup first.\nRun: ./scripts/development/setup-dev.sh"
    fi

    # Navigate to backend directory
    cd "$BACKEND_DIR"

    # Check if virtual environment exists
    if [ ! -f "venv/bin/activate" ]; then
        error_exit "Virtual environment not found. Please run setup first.\nRun: ./scripts/development/setup-dev.sh"
    fi

    # Load environment configuration
    load_env

    # Override with command line arguments
    if [ -n "$VIERLA_HOST" ] && [ "$SERVER_HOST" = "$DEFAULT_HOST" ]; then
        SERVER_HOST="$VIERLA_HOST"
    fi
    if [ -n "$VIERLA_PORT" ] && [ "$SERVER_PORT" = "$DEFAULT_PORT" ]; then
        SERVER_PORT="$VIERLA_PORT"
    fi

    # Activate virtual environment
    log "INFO" "Activating virtual environment"
    print_color "$COLOR_YELLOW" "Activating virtual environment..."
    source venv/bin/activate

    # Check if Django is installed
    if ! python -c "import django" &> /dev/null; then
        error_exit "Django not found in virtual environment. Please run setup first.\nRun: ./scripts/development/setup-dev.sh"
    fi

    # Run database migrations (in case there are new ones)
    log "INFO" "Checking for database migrations"
    print_color "$COLOR_YELLOW" "Checking for database migrations..."
    
    if ! python manage.py migrate --check &> /dev/null; then
        print_color "$COLOR_YELLOW" "Running pending migrations..."
        python manage.py migrate || error_exit "Failed to run database migrations"
    fi

    # Collect static files (if needed)
    if [ "$DEBUG_MODE" = "false" ]; then
        log "INFO" "Collecting static files"
        print_color "$COLOR_YELLOW" "Collecting static files..."
        python manage.py collectstatic --noinput
    fi

    # Check if port is available
    check_port

    # Display server information
    log "INFO" "Starting Django development server"
    print_color "$COLOR_GREEN" "============================================================================"
    print_color "$COLOR_GREEN" "Starting Vierla Backend Server"
    print_color "$COLOR_GREEN" "============================================================================"
    echo
    print_color "$COLOR_CYAN" "Server Configuration:"
    echo "  Host: ${COLOR_YELLOW}http://$SERVER_HOST:$SERVER_PORT${COLOR_RESET}"
    echo "  Debug Mode: ${COLOR_YELLOW}${DEBUG:-$DEBUG_MODE}${COLOR_RESET}"
    echo "  Environment: ${COLOR_YELLOW}Development${COLOR_RESET}"
    echo "  Log File: ${COLOR_YELLOW}../../$LOG_FILE${COLOR_RESET}"
    echo
    print_color "$COLOR_CYAN" "Available Endpoints:"
    echo "  Application: ${COLOR_BLUE}http://$SERVER_HOST:$SERVER_PORT/${COLOR_RESET}"
    echo "  Admin Panel: ${COLOR_BLUE}http://$SERVER_HOST:$SERVER_PORT/admin/${COLOR_RESET}"
    echo "  API Root: ${COLOR_BLUE}http://$SERVER_HOST:$SERVER_PORT/api/${COLOR_RESET}"
    echo "  API Docs: ${COLOR_BLUE}http://$SERVER_HOST:$SERVER_PORT/api/docs/${COLOR_RESET}"
    echo
    print_color "$COLOR_YELLOW" "Press Ctrl+C to stop the server"
    print_color "$COLOR_GREEN" "============================================================================"
    echo

    # Start the Django development server
    log "INFO" "Django server starting on $SERVER_HOST:$SERVER_PORT"
    python manage.py runserver "$SERVER_HOST:$SERVER_PORT"

    # Log server shutdown
    log "INFO" "Django server stopped"
    echo
    print_color "$COLOR_YELLOW" "Server stopped. Thank you for using Vierla!"

    # Navigate back to project root
    cd ../..
}

# Make script executable and run
chmod +x "$0"
parse_args "$@"
start_server
