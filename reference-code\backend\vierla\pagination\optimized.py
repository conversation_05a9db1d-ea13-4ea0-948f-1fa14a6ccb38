"""
Optimized Pagination Classes for Vierla API
Based on Backend Agent Consultation for API Response Optimization
"""

from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination
from rest_framework.response import Response
from django.core.paginator import Paginator
from django.conf import settings
from collections import OrderedDict
import math
from typing import Dict, Any, Optional

class OptimizedPageNumberPagination(PageNumberPagination):
    """
    Optimized page number pagination with performance enhancements.
    """
    page_size = getattr(settings, 'DEFAULT_PAGE_SIZE', 20)
    page_size_query_param = 'page_size'
    max_page_size = getattr(settings, 'MAX_PAGE_SIZE', 100)
    
    def get_paginated_response(self, data):
        """Return optimized paginated response with performance metadata."""
        # Calculate pagination metadata efficiently
        total_pages = math.ceil(self.page.paginator.count / self.page_size) if self.page_size else 1
        current_page = self.page.number
        
        # Determine if there are next/previous pages without additional queries
        has_next = current_page < total_pages
        has_previous = current_page > 1
        
        # Build response with performance optimizations
        response_data = OrderedDict([
            ('count', self.page.paginator.count),
            ('total_pages', total_pages),
            ('current_page', current_page),
            ('page_size', self.page_size),
            ('has_next', has_next),
            ('has_previous', has_previous),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('results', data)
        ])
        
        # Add performance metadata
        response_data['_meta'] = {
            'pagination_type': 'page_number',
            'optimized': True,
            'cache_hint': f'page_{current_page}_size_{self.page_size}',
        }
        
        return Response(response_data)
    
    def get_next_link(self):
        """Get next page link with optimization."""
        if not self.page.has_next():
            return None
        
        url = self.request.build_absolute_uri()
        page_number = self.page.next_page_number()
        return self.replace_query_param(url, self.page_query_param, page_number)
    
    def get_previous_link(self):
        """Get previous page link with optimization."""
        if not self.page.has_previous():
            return None
        
        url = self.request.build_absolute_uri()
        page_number = self.page.previous_page_number()
        return self.replace_query_param(url, self.page_query_param, page_number)


class OptimizedLimitOffsetPagination(LimitOffsetPagination):
    """
    Optimized limit/offset pagination for better performance on large datasets.
    """
    default_limit = getattr(settings, 'DEFAULT_PAGE_SIZE', 20)
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = getattr(settings, 'MAX_PAGE_SIZE', 100)
    
    def get_paginated_response(self, data):
        """Return optimized paginated response with cursor hints."""
        total_count = self.count
        current_offset = self.offset
        current_limit = self.limit
        
        # Calculate pagination metadata
        has_next = current_offset + current_limit < total_count
        has_previous = current_offset > 0
        
        # Calculate next/previous offsets
        next_offset = current_offset + current_limit if has_next else None
        previous_offset = max(0, current_offset - current_limit) if has_previous else None
        
        response_data = OrderedDict([
            ('count', total_count),
            ('limit', current_limit),
            ('offset', current_offset),
            ('has_next', has_next),
            ('has_previous', has_previous),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('results', data)
        ])
        
        # Add performance metadata
        response_data['_meta'] = {
            'pagination_type': 'limit_offset',
            'optimized': True,
            'next_offset': next_offset,
            'previous_offset': previous_offset,
            'performance_hint': 'Use cursor pagination for better performance on large datasets'
        }
        
        return Response(response_data)


class CursorPaginationOptimized(PageNumberPagination):
    """
    Cursor-based pagination for optimal performance on large datasets.
    """
    page_size = getattr(settings, 'DEFAULT_PAGE_SIZE', 20)
    page_size_query_param = 'page_size'
    max_page_size = getattr(settings, 'MAX_PAGE_SIZE', 100)
    cursor_query_param = 'cursor'
    ordering = '-created_at'  # Default ordering
    
    def paginate_queryset(self, queryset, request, view=None):
        """Paginate queryset using cursor-based approach."""
        self.request = request
        self.view = view
        
        # Get page size
        page_size = self.get_page_size(request)
        if not page_size:
            return None
        
        # Get cursor from request
        cursor = request.query_params.get(self.cursor_query_param)
        
        if cursor:
            # Decode cursor and filter queryset
            try:
                cursor_value = self.decode_cursor(cursor)
                if self.ordering.startswith('-'):
                    # Descending order
                    field_name = self.ordering[1:]
                    queryset = queryset.filter(**{f'{field_name}__lt': cursor_value})
                else:
                    # Ascending order
                    queryset = queryset.filter(**{f'{self.ordering}__gt': cursor_value})
            except (ValueError, TypeError):
                # Invalid cursor, start from beginning
                pass
        
        # Apply ordering
        queryset = queryset.order_by(self.ordering)
        
        # Get one extra item to determine if there's a next page
        items = list(queryset[:page_size + 1])
        
        # Check if there are more items
        self.has_next = len(items) > page_size
        if self.has_next:
            items = items[:-1]  # Remove the extra item
        
        # Store items and metadata
        self.items = items
        self.page_size = page_size
        
        return items
    
    def get_paginated_response(self, data):
        """Return cursor-paginated response."""
        next_cursor = None
        if self.has_next and self.items:
            # Get cursor value from last item
            last_item = self.items[-1]
            cursor_field = self.ordering.lstrip('-')
            cursor_value = getattr(last_item, cursor_field)
            next_cursor = self.encode_cursor(cursor_value)
        
        response_data = OrderedDict([
            ('has_next', self.has_next),
            ('next_cursor', next_cursor),
            ('page_size', self.page_size),
            ('results', data)
        ])
        
        # Add performance metadata
        response_data['_meta'] = {
            'pagination_type': 'cursor',
            'optimized': True,
            'ordering': self.ordering,
            'performance': 'Optimal for large datasets'
        }
        
        return Response(response_data)
    
    def encode_cursor(self, value) -> str:
        """Encode cursor value."""
        import base64
        import json
        cursor_data = {'value': str(value), 'ordering': self.ordering}
        cursor_json = json.dumps(cursor_data)
        return base64.b64encode(cursor_json.encode()).decode()
    
    def decode_cursor(self, cursor: str):
        """Decode cursor value."""
        import base64
        import json
        cursor_json = base64.b64decode(cursor.encode()).decode()
        cursor_data = json.loads(cursor_json)
        return cursor_data['value']


class SmartPagination:
    """
    Smart pagination that chooses the best pagination strategy based on dataset size.
    """
    
    SMALL_DATASET_THRESHOLD = 1000
    MEDIUM_DATASET_THRESHOLD = 10000
    
    @classmethod
    def get_pagination_class(cls, queryset_count: int):
        """Choose optimal pagination class based on dataset size."""
        if queryset_count <= cls.SMALL_DATASET_THRESHOLD:
            # Use page number pagination for small datasets
            return OptimizedPageNumberPagination
        elif queryset_count <= cls.MEDIUM_DATASET_THRESHOLD:
            # Use limit/offset for medium datasets
            return OptimizedLimitOffsetPagination
        else:
            # Use cursor pagination for large datasets
            return CursorPaginationOptimized
    
    @classmethod
    def get_optimized_paginator(cls, queryset, request, view=None):
        """Get optimized paginator instance."""
        # Estimate count efficiently
        try:
            count = queryset.count()
        except Exception:
            # Fallback to default pagination
            count = cls.SMALL_DATASET_THRESHOLD
        
        # Choose pagination class
        pagination_class = cls.get_pagination_class(count)
        paginator = pagination_class()
        
        # Paginate the queryset
        page = paginator.paginate_queryset(queryset, request, view)
        return paginator, page


class PaginationMetrics:
    """
    Utility class for pagination performance metrics.
    """
    
    @staticmethod
    def calculate_pagination_efficiency(total_items: int, page_size: int, current_page: int) -> Dict[str, Any]:
        """Calculate pagination efficiency metrics."""
        total_pages = math.ceil(total_items / page_size)
        items_on_current_page = min(page_size, total_items - (current_page - 1) * page_size)
        
        return {
            'total_items': total_items,
            'total_pages': total_pages,
            'current_page': current_page,
            'page_size': page_size,
            'items_on_current_page': items_on_current_page,
            'efficiency_score': items_on_current_page / page_size,
            'memory_usage_estimate': items_on_current_page * 1024,  # Rough estimate in bytes
            'recommended_page_size': cls._get_recommended_page_size(total_items),
        }
    
    @staticmethod
    def _get_recommended_page_size(total_items: int) -> int:
        """Get recommended page size based on total items."""
        if total_items <= 100:
            return min(20, total_items)
        elif total_items <= 1000:
            return 25
        elif total_items <= 10000:
            return 50
        else:
            return 100
