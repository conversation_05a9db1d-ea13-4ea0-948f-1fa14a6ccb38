"""
Django management command to test the booking system
"""
from django.core.management.base import BaseCommand
from django.utils import timezone as django_timezone
from datetime import timedelta, time
from decimal import Decimal

from apps.bookings.models import Booking, BookingStateChange, TimeSlot, BookingNotification
from apps.catalog.models import ServiceProvider, Service, ServiceCategory
from apps.authentication.models import User


class Command(BaseCommand):
    help = 'Test the comprehensive booking system functionality'

    def handle(self, *args, **options):
        """Test the booking system"""
        
        self.stdout.write(self.style.SUCCESS("🎯 Testing Vierla Booking System"))
        self.stdout.write("=" * 60)
        
        # Test 1: Database Models and Relationships
        self.stdout.write(f"\n📊 Test 1: Database Models and Relationships")
        self.stdout.write("-" * 40)
        
        try:
            # Check existing data
            users = User.objects.count()
            providers = ServiceProvider.objects.count()
            services = Service.objects.count()
            bookings = Booking.objects.count()
            
            self.stdout.write(f"✅ Users: {users}")
            self.stdout.write(f"✅ Providers: {providers}")
            self.stdout.write(f"✅ Services: {services}")
            self.stdout.write(f"✅ Existing Bookings: {bookings}")
            
            # Get test data
            customer = User.objects.filter(role='customer').first()
            provider_obj = ServiceProvider.objects.first()
            service = Service.objects.first()
            
            if not customer or not provider_obj or not service:
                self.stdout.write(self.style.ERROR("❌ Missing test data. Please run create_services.py first."))
                return
                
            self.stdout.write(f"✅ Test customer: {customer.email}")
            self.stdout.write(f"✅ Test provider: {provider_obj.business_name}")
            self.stdout.write(f"✅ Test service: {service.name}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Database test failed: {e}"))
            return
        
        # Test 2: Create a Test Booking
        self.stdout.write(f"\n📝 Test 2: Create Test Booking")
        self.stdout.write("-" * 40)
        
        try:
            # Create a booking for tomorrow
            scheduled_time = django_timezone.now() + timedelta(days=1)
            
            booking = Booking.objects.create(
                customer=customer,
                provider=provider_obj,
                service=service,
                scheduled_datetime=scheduled_time,
                duration_minutes=service.duration,
                base_price=service.base_price,
                total_amount=service.base_price,
                location_type=Booking.LocationType.SALON,
                customer_notes="Test booking for system validation"
            )
            
            self.stdout.write(f"✅ Created booking: {booking.booking_number}")
            self.stdout.write(f"✅ Status: {booking.status}")
            self.stdout.write(f"✅ Scheduled: {booking.scheduled_datetime}")
            self.stdout.write(f"✅ Total: ${booking.total_amount}")
            self.stdout.write(f"✅ Duration: {booking.get_duration_display()}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Booking creation failed: {e}"))
            return
        
        # Test 3: Booking State Machine
        self.stdout.write(f"\n🔄 Test 3: Booking State Machine")
        self.stdout.write("-" * 40)
        
        try:
            # Test state transitions
            self.stdout.write(f"Initial state: {booking.status}")
            
            # Test confirmation
            if booking.can_be_confirmed():
                booking.confirm_booking(confirmed_by=provider_obj.user)
                self.stdout.write(f"✅ Confirmed booking: {booking.status}")
            else:
                self.stdout.write(f"❌ Cannot confirm booking in state: {booking.status}")
            
            # Test starting
            if booking.can_be_started():
                booking.start_booking(started_by=provider_obj.user)
                self.stdout.write(f"✅ Started booking: {booking.status}")
            else:
                self.stdout.write(f"❌ Cannot start booking in state: {booking.status}")
            
            # Test completion
            if booking.can_be_completed():
                booking.complete_booking(completed_by=provider_obj.user)
                self.stdout.write(f"✅ Completed booking: {booking.status}")
            else:
                self.stdout.write(f"❌ Cannot complete booking in state: {booking.status}")
            
            # Check state changes
            state_changes = BookingStateChange.objects.filter(booking=booking).count()
            self.stdout.write(f"✅ State changes recorded: {state_changes}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ State machine test failed: {e}"))
            return
        
        # Test 4: Time Slots
        self.stdout.write(f"\n⏰ Test 4: Time Slots Management")
        self.stdout.write("-" * 40)
        
        try:
            # Create test time slots
            tomorrow = (django_timezone.now() + timedelta(days=1)).date()
            
            time_slot = TimeSlot.objects.create(
                provider=provider_obj,
                service=service,
                date=tomorrow,
                start_time=time(9, 0),
                end_time=time(10, 0),
                is_available=True,
                max_bookings=1,
                current_bookings=0
            )
            
            self.stdout.write(f"✅ Created time slot: {time_slot}")
            self.stdout.write(f"✅ Available: {time_slot.is_available}")
            self.stdout.write(f"✅ Can be booked: {time_slot.can_be_booked()}")
            self.stdout.write(f"✅ Available spots: {time_slot.get_available_spots()}")
            
            # Test booking the slot
            if time_slot.can_be_booked():
                time_slot.book_slot()
                self.stdout.write(f"✅ Booked slot - Current bookings: {time_slot.current_bookings}")
                self.stdout.write(f"✅ Still available: {time_slot.can_be_booked()}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Time slots test failed: {e}"))
            return
        
        # Test 5: Notifications
        self.stdout.write(f"\n🔔 Test 5: Booking Notifications")
        self.stdout.write("-" * 40)
        
        try:
            # Create test notification
            notification = BookingNotification.objects.create(
                booking=booking,
                recipient=customer,
                notification_type=BookingNotification.NotificationType.BOOKING_COMPLETED,
                channel=BookingNotification.NotificationChannel.PUSH,
                title="Service Completed",
                message=f"Your {service.name} service has been completed successfully!"
            )
            
            self.stdout.write(f"✅ Created notification: {notification.title}")
            self.stdout.write(f"✅ Type: {notification.get_notification_type_display()}")
            self.stdout.write(f"✅ Channel: {notification.get_channel_display()}")
            self.stdout.write(f"✅ Status: {notification.get_status_display()}")
            
            # Test notification state changes
            notification.mark_as_sent(external_id="test-123")
            self.stdout.write(f"✅ Marked as sent: {notification.status}")
            
            notification.mark_as_read()
            self.stdout.write(f"✅ Marked as read: {notification.status}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Notifications test failed: {e}"))
            return
        
        # Test 6: Business Logic Validation
        self.stdout.write(f"\n💼 Test 6: Business Logic Validation")
        self.stdout.write("-" * 40)
        
        try:
            # Test cancellation deadline
            booking.scheduled_datetime = django_timezone.now() + timedelta(hours=48)  # 48 hours from now
            booking.save()
            
            can_cancel = booking.can_be_cancelled()
            deadline = booking.get_cancellation_deadline()
            self.stdout.write(f"✅ Can cancel (48h notice): {can_cancel}")
            self.stdout.write(f"✅ Cancellation deadline: {deadline}")
            
            # Test with short notice
            booking.scheduled_datetime = django_timezone.now() + timedelta(hours=12)  # 12 hours from now
            booking.save()
            
            can_cancel_short = booking.can_be_cancelled()
            self.stdout.write(f"✅ Can cancel (12h notice): {can_cancel_short}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Business logic test failed: {e}"))
            return
        
        # Summary
        self.stdout.write(f"\n🎉 Test Summary")
        self.stdout.write("=" * 60)
        self.stdout.write(f"✅ Database Models: Working")
        self.stdout.write(f"✅ Booking Creation: Working")
        self.stdout.write(f"✅ State Machine: Working")
        self.stdout.write(f"✅ Time Slots: Working")
        self.stdout.write(f"✅ Notifications: Working")
        self.stdout.write(f"✅ Business Logic: Working")
        
        self.stdout.write(f"\n📊 Final Statistics:")
        self.stdout.write(f"   Total Bookings: {Booking.objects.count()}")
        self.stdout.write(f"   Total State Changes: {BookingStateChange.objects.count()}")
        self.stdout.write(f"   Total Time Slots: {TimeSlot.objects.count()}")
        self.stdout.write(f"   Total Notifications: {BookingNotification.objects.count()}")
        
        self.stdout.write(self.style.SUCCESS(f"\n🎯 Booking System Test: PASSED ✅"))
