#!/usr/bin/env python
"""
Create Test Accounts <PERSON><PERSON><PERSON>
<PERSON><PERSON>s all test accounts documented in CONSOLIDATED_TEST_ACCOUNTS.md
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings')
django.setup()

from django.contrib.auth import get_user_model
from authentication.models import User
from catalog.models import ServiceCategory, ServiceProvider

def create_test_accounts():
    """Create all test accounts from consolidated documentation"""
    
    print("Creating test accounts from CONSOLIDATED_TEST_ACCOUNTS.md...")
    
    # Test accounts from consolidated documentation
    test_accounts = [
        {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'first_name': 'Test',
            'last_name': 'Customer',
            'role': 'customer',
            'is_email_verified': True,
            'is_test_account': True,
            'expected_id': 27
        },
        {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'first_name': 'Test',
            'last_name': 'Provider',
            'role': 'service_provider',
            'is_email_verified': True,
            'is_test_account': True,
            'expected_id': 30
        },
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'customer',
            'is_email_verified': False,
            'is_test_account': True,
            'expected_id': 1
        },
        {
            'email': '<EMAIL>',
            'password': 'demo123',
            'first_name': 'Demo',
            'last_name': 'User',
            'role': 'customer',
            'is_email_verified': False,
            'is_test_account': True,
            'expected_id': 33
        },
        {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Debug',
            'last_name': 'User',
            'role': 'customer',
            'is_email_verified': False,
            'is_test_account': True,
            'expected_id': 32
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for account_data in test_accounts:
        email = account_data['email']
        
        try:
            # Check if user already exists
            user = User.objects.get(email=email)
            print(f"✓ User {email} already exists (ID: {user.id})")
            
            # Update user data if needed
            user.first_name = account_data['first_name']
            user.last_name = account_data['last_name']
            user.role = account_data['role']
            user.is_email_verified = account_data['is_email_verified']
            user.is_test_account = account_data['is_test_account']
            user.set_password(account_data['password'])
            user.save()
            updated_count += 1
            print(f"  → Updated user data for {email}")
            
        except User.DoesNotExist:
            # Create new user
            user_data = {
                'email': email,
                'first_name': account_data['first_name'],
                'last_name': account_data['last_name'],
                'role': account_data['role'],
                'is_email_verified': account_data['is_email_verified'],
                'is_test_account': account_data['is_test_account']
            }
            
            user = User.objects.create_user(
                email=email,
                password=account_data['password'],
                **{k: v for k, v in user_data.items() if k != 'email'}
            )
            
            created_count += 1
            print(f"✓ Created user {email} (ID: {user.id})")
    
    # Create service provider profile for provider account
    try:
        provider_user = User.objects.get(email='<EMAIL>')
        
        # Create test category if it doesn't exist
        category, created = ServiceCategory.objects.get_or_create(
            slug='test-category',
            defaults={
                'name': 'Test Category',
                'description': 'Test category for service provider testing'
            }
        )
        
        if created:
            print(f"✓ Created test category: {category.name}")
        
        # Create service provider profile if it doesn't exist
        provider_profile, created = ServiceProvider.objects.get_or_create(
            user=provider_user,
            defaults={
                'business_name': 'Test Business',
                'description': 'Test service provider business',
                'phone': '+**********',
                'address': '123 Test Street, Test City, TC 12345'
            }
        )
        
        if created:
            print(f"✓ Created service provider profile for {provider_user.email}")
        else:
            print(f"✓ Service provider profile already exists for {provider_user.email}")
            
    except User.DoesNotExist:
        print("⚠ Provider user not found - will be created when script runs")
    
    print(f"\n📊 Summary:")
    print(f"   Created: {created_count} users")
    print(f"   Updated: {updated_count} users")
    print(f"   Total test accounts: {len(test_accounts)}")
    print(f"\n✅ Test accounts setup complete!")

if __name__ == '__main__':
    create_test_accounts()
