import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ServiceCard, { Service, ServiceCardProps } from '../ServiceCard';

// Mock service data
const mockService: Service = {
  id: 'service-1',
  name: 'Premium Haircut',
  short_description: 'Professional haircut with styling consultation',
  base_price: 75.00,
  price_type: 'fixed',
  max_price: undefined,
  display_price: '$75.00',
  duration: 60,
  display_duration: '1h',
  image: 'https://example.com/service-image.jpg',
  is_popular: true,
  is_available: true,
  booking_count: 25,
  provider_name: 'Hair Studio Elite',
  provider_rating: 4.8,
  provider_city: 'Toronto',
  category_name: 'Hair Services',
  category_icon: '💇‍♀️',
  created_at: '2024-01-01T00:00:00Z',
};

const mockServiceUnavailable: Service = {
  ...mockService,
  id: 'service-2',
  name: 'Unavailable Service',
  is_available: false,
  is_popular: false,
  image: undefined,
};

const defaultProps: ServiceCardProps = {
  service: mockService,
  onPress: jest.fn(),
  testID: 'service-card',
};

describe('ServiceCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders correctly with all service information', () => {
      const { getByTestId, getByText } = render(<ServiceCard {...defaultProps} />);

      expect(getByTestId('service-card')).toBeTruthy();
      expect(getByTestId('service-card-name')).toBeTruthy();
      expect(getByTestId('service-card-description')).toBeTruthy();
      expect(getByTestId('service-card-provider-info')).toBeTruthy();
      expect(getByTestId('service-card-details')).toBeTruthy();
      expect(getByTestId('service-card-price')).toBeTruthy();
      expect(getByTestId('service-card-duration')).toBeTruthy();
      expect(getByTestId('service-card-rating')).toBeTruthy();

      expect(getByText('Premium Haircut')).toBeTruthy();
      expect(getByText('Professional haircut with styling consultation')).toBeTruthy();
      expect(getByText('Hair Studio Elite')).toBeTruthy();
      expect(getByText('• Toronto')).toBeTruthy();
      expect(getByText('$75.00')).toBeTruthy();
      expect(getByText('1h')).toBeTruthy();
      expect(getByText('4.8')).toBeTruthy();
    });

    it('renders service image when provided', () => {
      const { getByTestId } = render(<ServiceCard {...defaultProps} />);
      expect(getByTestId('service-card-image')).toBeTruthy();
    });

    it('renders placeholder when no image provided', () => {
      const props = {
        ...defaultProps,
        service: { ...mockService, image: undefined },
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      expect(getByTestId('service-card-placeholder')).toBeTruthy();
    });

    it('renders popular badge for popular services', () => {
      const { getByTestId, getByText } = render(<ServiceCard {...defaultProps} />);
      expect(getByTestId('service-card-popular-badge')).toBeTruthy();
      expect(getByText('Popular')).toBeTruthy();
    });

    it('does not render popular badge for non-popular services', () => {
      const props = {
        ...defaultProps,
        service: { ...mockService, is_popular: false },
      };
      const { queryByTestId } = render(<ServiceCard {...props} />);
      expect(queryByTestId('service-card-popular-badge')).toBeNull();
    });

    it('renders unavailable badge for unavailable services', () => {
      const props = {
        ...defaultProps,
        service: mockServiceUnavailable,
      };
      const { getByTestId, getByText } = render(<ServiceCard {...props} />);
      expect(getByTestId('service-card-unavailable')).toBeTruthy();
      expect(getByText('Currently Unavailable')).toBeTruthy();
    });

    it('does not render unavailable badge for available services', () => {
      const { queryByTestId } = render(<ServiceCard {...defaultProps} />);
      expect(queryByTestId('service-card-unavailable')).toBeNull();
    });
  });

  describe('Favorite Functionality', () => {
    it('renders favorite button when onFavorite is provided', () => {
      const props = {
        ...defaultProps,
        onFavorite: jest.fn(),
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      expect(getByTestId('service-card-favorite-button')).toBeTruthy();
    });

    it('does not render favorite button when onFavorite is not provided', () => {
      const { queryByTestId } = render(<ServiceCard {...defaultProps} />);
      expect(queryByTestId('service-card-favorite-button')).toBeNull();
    });

    it('calls onFavorite when favorite button is pressed', () => {
      const onFavorite = jest.fn();
      const props = {
        ...defaultProps,
        onFavorite,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      
      fireEvent.press(getByTestId('service-card-favorite-button'));
      expect(onFavorite).toHaveBeenCalledTimes(1);
    });

    it('shows filled heart when service is favorited', () => {
      const props = {
        ...defaultProps,
        onFavorite: jest.fn(),
        isFavorite: true,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      const favoriteButton = getByTestId('service-card-favorite-button');
      expect(favoriteButton).toBeTruthy();
    });

    it('shows outline heart when service is not favorited', () => {
      const props = {
        ...defaultProps,
        onFavorite: jest.fn(),
        isFavorite: false,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      const favoriteButton = getByTestId('service-card-favorite-button');
      expect(favoriteButton).toBeTruthy();
    });
  });

  describe('Variants', () => {
    it('applies default styling for default variant', () => {
      const { getByTestId } = render(<ServiceCard {...defaultProps} />);
      const card = getByTestId('service-card');
      expect(card).toBeTruthy();
    });

    it('applies compact styling for compact variant', () => {
      const props = {
        ...defaultProps,
        variant: 'compact' as const,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      const card = getByTestId('service-card');
      expect(card).toBeTruthy();
    });

    it('applies featured styling for featured variant', () => {
      const props = {
        ...defaultProps,
        variant: 'featured' as const,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      const card = getByTestId('service-card');
      expect(card).toBeTruthy();
    });
  });

  describe('Interaction', () => {
    it('calls onPress when card is pressed', () => {
      const onPress = jest.fn();
      const props = {
        ...defaultProps,
        onPress,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      
      fireEvent.press(getByTestId('service-card'));
      expect(onPress).toHaveBeenCalledTimes(1);
    });

    it('handles text truncation for long service names', () => {
      const longNameService = {
        ...mockService,
        name: 'This is a very long service name that should be truncated when displayed in the card component',
      };
      const props = {
        ...defaultProps,
        service: longNameService,
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      expect(getByTestId('service-card-name')).toBeTruthy();
    });

    it('handles missing short description gracefully', () => {
      const noDescriptionService = {
        ...mockService,
        short_description: undefined,
      };
      const props = {
        ...defaultProps,
        service: noDescriptionService,
      };
      const { queryByTestId } = render(<ServiceCard {...props} />);
      expect(queryByTestId('service-card-description')).toBeNull();
    });
  });

  describe('Price Display', () => {
    it('displays fixed price correctly', () => {
      const { getByText } = render(<ServiceCard {...defaultProps} />);
      expect(getByText('$75.00')).toBeTruthy();
    });

    it('displays hourly price correctly', () => {
      const hourlyService = {
        ...mockService,
        price_type: 'hourly' as const,
        display_price: '$75.00/hour',
      };
      const props = {
        ...defaultProps,
        service: hourlyService,
      };
      const { getByText } = render(<ServiceCard {...props} />);
      expect(getByText('$75.00/hour')).toBeTruthy();
    });

    it('displays price range correctly', () => {
      const rangeService = {
        ...mockService,
        price_type: 'range' as const,
        max_price: 125.00,
        display_price: '$75.00 - $125.00',
      };
      const props = {
        ...defaultProps,
        service: rangeService,
      };
      const { getByText } = render(<ServiceCard {...props} />);
      expect(getByText('$75.00 - $125.00')).toBeTruthy();
    });

    it('displays consultation price correctly', () => {
      const consultationService = {
        ...mockService,
        price_type: 'consultation' as const,
        display_price: 'Consultation Required',
      };
      const props = {
        ...defaultProps,
        service: consultationService,
      };
      const { getByText } = render(<ServiceCard {...props} />);
      expect(getByText('Consultation Required')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('has proper testID for accessibility testing', () => {
      const { getByTestId } = render(<ServiceCard {...defaultProps} />);
      expect(getByTestId('service-card')).toBeTruthy();
    });

    it('handles custom testID prop', () => {
      const props = {
        ...defaultProps,
        testID: 'custom-service-card',
      };
      const { getByTestId } = render(<ServiceCard {...props} />);
      expect(getByTestId('custom-service-card')).toBeTruthy();
    });
  });
});
