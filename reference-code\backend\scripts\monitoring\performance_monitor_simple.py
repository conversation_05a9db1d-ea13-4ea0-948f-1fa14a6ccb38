#!/usr/bin/env python
"""
Simple Performance Monitoring Script for Vierla Backend
Monitors system performance metrics without Django dependencies
"""

import os
import sys
import time
import psutil
import requests
from datetime import datetime


class SimplePerformanceMonitor:
    """Simple performance monitoring without Django dependencies"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.refresh_interval = 10  # seconds
        
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_colored_text(self, text, color):
        """Get colored text for terminal output"""
        colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'magenta': '\033[95m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
        return f"{colors.get(color, '')}{text}{colors['end']}"
    
    def get_status_indicator(self, value, thresholds):
        """Get status indicator based on value and thresholds"""
        if value < thresholds['good']:
            return self.get_colored_text("●", "green")
        elif value < thresholds['warning']:
            return self.get_colored_text("●", "yellow")
        else:
            return self.get_colored_text("●", "red")
    
    def format_bytes(self, bytes_value):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def get_system_metrics(self):
        """Get current system metrics"""
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory Usage
            memory = psutil.virtual_memory()
            
            # Disk Usage (use current drive on Windows)
            disk = psutil.disk_usage('C:' if os.name == 'nt' else '/')
            
            # Network Stats
            network = psutil.net_io_counters()
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_used': disk.used,
                'disk_total': disk.total,
                'network_sent': network.bytes_sent,
                'network_recv': network.bytes_recv
            }
        except Exception as e:
            print(f"Error getting system metrics: {e}")
            return {}
    
    def test_api_connectivity(self):
        """Test API connectivity"""
        try:
            response = requests.get(f"{self.api_base_url}/admin/", timeout=5)
            return response.status_code in [200, 302]  # 302 for redirect to login
        except:
            return False
    
    def get_process_info(self):
        """Get Django process information"""
        django_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower() and 'manage.py' in ' '.join(proc.info['cmdline']):
                    django_processes.append({
                        'pid': proc.info['pid'],
                        'cpu': proc.info['cpu_percent'],
                        'memory': proc.info['memory_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        return django_processes
    
    def display_dashboard(self):
        """Display the performance dashboard"""
        self.clear_screen()
        
        # Header
        print(self.get_colored_text("=" * 70, "cyan"))
        print(self.get_colored_text("🚀 VIERLA PERFORMANCE MONITOR", "bold"))
        print(self.get_colored_text("=" * 70, "cyan"))
        print()
        
        # Current time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"📊 Last Updated: {self.get_colored_text(current_time, 'white')}")
        print()
        
        # API Connectivity
        api_status = "🟢 ONLINE" if self.test_api_connectivity() else "🔴 OFFLINE"
        print(f"🌐 Django Server: {api_status}")
        print()
        
        # System Metrics
        system_metrics = self.get_system_metrics()
        if system_metrics:
            print(self.get_colored_text("📈 SYSTEM HEALTH METRICS", "bold"))
            print("-" * 40)
            
            # CPU
            cpu_status = self.get_status_indicator(system_metrics['cpu_percent'], {'good': 70, 'warning': 85})
            print(f"{cpu_status} CPU Usage: {system_metrics['cpu_percent']:.1f}%")
            
            # Memory
            memory_status = self.get_status_indicator(system_metrics['memory_percent'], {'good': 70, 'warning': 85})
            memory_used = self.format_bytes(system_metrics['memory_used'])
            memory_total = self.format_bytes(system_metrics['memory_total'])
            print(f"{memory_status} Memory: {system_metrics['memory_percent']:.1f}% ({memory_used}/{memory_total})")
            
            # Disk
            disk_status = self.get_status_indicator(system_metrics['disk_percent'], {'good': 80, 'warning': 90})
            disk_used = self.format_bytes(system_metrics['disk_used'])
            disk_total = self.format_bytes(system_metrics['disk_total'])
            print(f"{disk_status} Disk: {system_metrics['disk_percent']:.1f}% ({disk_used}/{disk_total})")
            
            print()
        
        # Django Process Info
        django_processes = self.get_process_info()
        print(self.get_colored_text("🐍 DJANGO PROCESSES", "bold"))
        print("-" * 30)
        
        if django_processes:
            for proc in django_processes:
                print(f"PID {proc['pid']}: CPU {proc['cpu']:.1f}%, Memory {proc['memory']:.1f}%")
        else:
            print(self.get_colored_text("⚠️  No Django processes found", "yellow"))
        
        print()
        
        # Footer
        print(self.get_colored_text("=" * 70, "cyan"))
        print(f"🔄 Auto-refresh every {self.refresh_interval} seconds | Press Ctrl+C to exit")
        print(self.get_colored_text("=" * 70, "cyan"))
    
    def run(self):
        """Run the performance monitoring dashboard"""
        print(self.get_colored_text("🚀 Starting Vierla Performance Monitor...", "green"))
        print(f"📊 Monitoring backend at: {self.api_base_url}")
        print(f"🔄 Refresh interval: {self.refresh_interval} seconds")
        print()
        
        try:
            while True:
                self.display_dashboard()
                time.sleep(self.refresh_interval)
        except KeyboardInterrupt:
            print(self.get_colored_text("\n\n🛑 Performance monitoring stopped.", "yellow"))
            sys.exit(0)
        except Exception as e:
            print(self.get_colored_text(f"\n\n❌ Error: {e}", "red"))
            sys.exit(1)


if __name__ == "__main__":
    monitor = SimplePerformanceMonitor()
    monitor.run()
