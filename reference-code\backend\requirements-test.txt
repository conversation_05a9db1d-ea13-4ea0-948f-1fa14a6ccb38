# Testing Dependencies for Vierla Backend
# Install with: pip install -r requirements-test.txt

# Core testing frameworks
pytest>=7.4.0
pytest-django>=4.7.0
pytest-cov>=4.1.0
pytest-xdist>=3.5.0

# Test utilities
factory-boy>=3.3.0
faker>=20.1.0
freezegun>=1.2.0
responses>=0.24.0

# Code coverage
coverage>=7.3.0

# Code quality and linting
flake8>=6.1.0
black>=23.11.0
isort>=5.12.0

# Security testing
bandit>=1.7.0
safety>=3.0.0

# API testing
requests>=2.31.0
requests-mock>=1.11.0

# Performance testing
locust>=2.17.0

# Django testing utilities
model-bakery>=1.17.0

# Mock and fixtures
pytest-mock>=3.12.0
