"""
Provider API URLs - Enhanced based on Backend Agent feedback
Custom URL patterns for service provider endpoints
"""

from django.urls import path
from . import views

app_name = 'provider'

urlpatterns = [
    # Business management endpoints
    path('profile/', views.ProviderProfileView.as_view(), name='profile'),
    path('business-settings/', views.BusinessSettingsView.as_view(), name='business-settings'),
    path('dashboard/overview/', views.ProviderDashboardViewSet.as_view({'get': 'list'}), name='dashboard-overview'),
    
    # Service management endpoints
    path('services/', views.ProviderServicesView.as_view(), name='services'),
    path('services/bulk-update/', views.BulkUpdateServicesView.as_view(), name='bulk-update-services'),
    path('services/<int:service_id>/toggle-active/', views.ToggleServiceActiveView.as_view(), name='toggle-service-active'),
    path('services/pricing-optimization/', views.PricingOptimizationView.as_view(), name='pricing-optimization'),
    
    # Advanced booking management
    path('bookings/calendar/', views.BookingCalendarView.as_view(), name='booking-calendar'),
    path('bookings/availability/', views.AvailabilityManagementView.as_view(), name='availability-management'),
    path('bookings/bulk-actions/', views.BulkBookingActionsView.as_view(), name='bulk-booking-actions'),
    
    # Customer communication
    path('customers/messages/', views.CustomerMessagesView.as_view(), name='customer-messages'),
    path('customers/<int:customer_id>/history/', views.CustomerHistoryView.as_view(), name='customer-history'),
    
    # Financial management
    path('finances/earnings/', views.EarningsView.as_view(), name='earnings'),
    path('finances/payouts/', views.PayoutsView.as_view(), name='payouts'),
    path('finances/tax-reports/', views.TaxReportsView.as_view(), name='tax-reports'),
    
    # Marketing and promotion
    path('marketing/promotions/', views.PromotionsView.as_view(), name='promotions'),
    path('marketing/social-media/', views.SocialMediaIntegrationView.as_view(), name='social-media'),
    
    # Business intelligence
    path('insights/customer-behavior/', views.CustomerBehaviorInsightsView.as_view(), name='customer-behavior'),
    path('insights/market-trends/', views.MarketTrendsView.as_view(), name='market-trends'),
    path('insights/competitor-analysis/', views.CompetitorAnalysisView.as_view(), name='competitor-analysis'),
    
    # Verification and compliance
    path('verification/status/', views.VerificationStatusView.as_view(), name='verification-status'),
    path('verification/documents/', views.VerificationDocumentsView.as_view(), name='verification-documents'),
    
    # Performance optimization
    path('optimization/recommendations/', views.OptimizationRecommendationsView.as_view(), name='optimization-recommendations'),
    path('optimization/ab-testing/', views.ABTestingView.as_view(), name='ab-testing'),
]
