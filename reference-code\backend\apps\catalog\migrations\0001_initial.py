# Generated by Django 4.2.16 on 2025-06-17 22:28

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Service",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the service",
                        max_length=200,
                        verbose_name="service name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the service",
                        verbose_name="service description",
                    ),
                ),
                (
                    "short_description",
                    models.CharField(
                        blank=True,
                        help_text="Brief description for mobile display",
                        max_length=255,
                        verbose_name="short description",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base price for the service",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="base price",
                    ),
                ),
                (
                    "price_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Price"),
                            ("hourly", "Hourly Rate"),
                            ("range", "Price Range"),
                            ("consultation", "Consultation Required"),
                        ],
                        default="fixed",
                        help_text="Type of pricing structure",
                        max_length=20,
                        verbose_name="price type",
                    ),
                ),
                (
                    "max_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum price for range pricing",
                        max_digits=10,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="maximum price",
                    ),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(
                        help_text="Service duration in minutes",
                        verbose_name="duration (minutes)",
                    ),
                ),
                (
                    "buffer_time",
                    models.PositiveIntegerField(
                        default=15,
                        help_text="Buffer time between appointments",
                        verbose_name="buffer time (minutes)",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Main service image",
                        null=True,
                        upload_to="services/%Y/%m/",
                        verbose_name="service image",
                    ),
                ),
                (
                    "is_popular",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as popular service for featured display",
                        verbose_name="is popular",
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether service is currently available for booking",
                        verbose_name="is available",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether service is active in the system",
                        verbose_name="is active",
                    ),
                ),
                (
                    "requirements",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of requirements or preparations needed",
                        verbose_name="service requirements",
                    ),
                ),
                (
                    "preparation_instructions",
                    models.TextField(
                        blank=True,
                        help_text="Instructions for client preparation",
                        verbose_name="preparation instructions",
                    ),
                ),
                (
                    "mobile_description",
                    models.CharField(
                        blank=True,
                        help_text="Optimized description for mobile display",
                        max_length=100,
                        verbose_name="mobile description",
                    ),
                ),
                (
                    "booking_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of bookings for this service",
                        verbose_name="booking count",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
            ],
            options={
                "verbose_name": "Service",
                "verbose_name_plural": "Services",
                "db_table": "catalog_services",
                "ordering": ["provider", "category", "name"],
            },
        ),
        migrations.CreateModel(
            name="ServiceCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the service category",
                        max_length=100,
                        unique=True,
                        verbose_name="category name",
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="URL-friendly version of the name",
                        max_length=120,
                        unique=True,
                        verbose_name="slug",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the category",
                        verbose_name="description",
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        help_text="Icon identifier (emoji or icon name)",
                        max_length=50,
                        verbose_name="icon",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#8FBC8F",
                        help_text="Hex color code for the category",
                        max_length=7,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Color must be a valid hex color code",
                                regex="^#[0-9A-Fa-f]{6}$",
                            )
                        ],
                        verbose_name="color",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Category banner image",
                        null=True,
                        upload_to="categories/%Y/%m/",
                        verbose_name="category image",
                    ),
                ),
                (
                    "is_popular",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as popular category for featured display",
                        verbose_name="is popular",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this category is active and visible",
                        verbose_name="is active",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Order for displaying categories",
                        verbose_name="sort order",
                    ),
                ),
                (
                    "mobile_icon",
                    models.CharField(
                        blank=True,
                        help_text="Optimized icon for mobile display",
                        max_length=50,
                        verbose_name="mobile icon",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent category for hierarchical organization",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="catalog.servicecategory",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Category",
                "verbose_name_plural": "Service Categories",
                "db_table": "catalog_service_categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="ServiceProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        help_text="Official business name",
                        max_length=255,
                        verbose_name="business name",
                    ),
                ),
                (
                    "business_description",
                    models.TextField(
                        help_text="Detailed description of services and business",
                        verbose_name="business description",
                    ),
                ),
                (
                    "business_phone",
                    models.CharField(
                        help_text="Business contact phone number",
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be in valid format",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="business phone",
                    ),
                ),
                (
                    "business_email",
                    models.EmailField(
                        help_text="Business contact email address",
                        max_length=254,
                        verbose_name="business email",
                    ),
                ),
                (
                    "address",
                    models.TextField(
                        help_text="Full business address",
                        verbose_name="business address",
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        help_text="Business city", max_length=100, verbose_name="city"
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        help_text="State or province",
                        max_length=100,
                        verbose_name="state/province",
                    ),
                ),
                (
                    "zip_code",
                    models.CharField(
                        help_text="Zip or postal code",
                        max_length=20,
                        verbose_name="zip/postal code",
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        default="Canada",
                        help_text="Country",
                        max_length=100,
                        verbose_name="country",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        help_text="Latitude coordinate for location-based services",
                        max_digits=10,
                        null=True,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        help_text="Longitude coordinate for location-based services",
                        max_digits=11,
                        null=True,
                        verbose_name="longitude",
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True,
                        help_text="Business website URL",
                        verbose_name="website",
                    ),
                ),
                (
                    "instagram_handle",
                    models.CharField(
                        blank=True,
                        help_text="Instagram username without @",
                        max_length=100,
                        verbose_name="Instagram handle",
                    ),
                ),
                (
                    "facebook_url",
                    models.URLField(
                        blank=True,
                        help_text="Facebook business page URL",
                        verbose_name="Facebook page",
                    ),
                ),
                (
                    "profile_image",
                    models.ImageField(
                        blank=True,
                        help_text="Business profile picture",
                        null=True,
                        upload_to="providers/profiles/%Y/%m/",
                        verbose_name="profile image",
                    ),
                ),
                (
                    "cover_image",
                    models.ImageField(
                        blank=True,
                        help_text="Business cover/banner image",
                        null=True,
                        upload_to="providers/covers/%Y/%m/",
                        verbose_name="cover image",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the business is verified by admin",
                        verbose_name="is verified",
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False,
                        help_text="Whether to feature this provider prominently",
                        verbose_name="is featured",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the provider is active and accepting bookings",
                        verbose_name="is active",
                    ),
                ),
                (
                    "rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Average rating from customer reviews",
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("5.00")),
                        ],
                        verbose_name="average rating",
                    ),
                ),
                (
                    "review_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of reviews received",
                        verbose_name="review count",
                    ),
                ),
                (
                    "total_bookings",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of completed bookings",
                        verbose_name="total bookings",
                    ),
                ),
                (
                    "years_of_experience",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Years of professional experience",
                        null=True,
                        verbose_name="years of experience",
                    ),
                ),
                (
                    "mobile_optimized",
                    models.BooleanField(
                        default=True,
                        help_text="Whether provider profile is optimized for mobile",
                        verbose_name="mobile optimized",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "categories",
                    models.ManyToManyField(
                        help_text="Service categories offered by this provider",
                        related_name="providers",
                        to="catalog.servicecategory",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="Associated user account",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="provider_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Provider",
                "verbose_name_plural": "Service Providers",
                "db_table": "catalog_service_providers",
                "ordering": ["-rating", "-review_count", "business_name"],
            },
        ),
        migrations.CreateModel(
            name="ServiceLocation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "location_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Location"),
                            ("mobile", "Mobile Service"),
                            ("both", "Both Fixed and Mobile"),
                            ("virtual", "Virtual/Online"),
                        ],
                        default="fixed",
                        help_text="Type of service location",
                        max_length=20,
                        verbose_name="location type",
                    ),
                ),
                (
                    "travel_radius",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum travel distance for mobile services",
                        null=True,
                        verbose_name="travel radius (km)",
                    ),
                ),
                (
                    "travel_fee",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Additional fee for mobile services",
                        max_digits=8,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="travel fee",
                    ),
                ),
                (
                    "service_areas",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of areas/neighborhoods served",
                        verbose_name="service areas",
                    ),
                ),
                (
                    "virtual_platform",
                    models.CharField(
                        blank=True,
                        help_text="Platform used for virtual services (Zoom, etc.)",
                        max_length=100,
                        verbose_name="virtual platform",
                    ),
                ),
                (
                    "location_notes",
                    models.TextField(
                        blank=True,
                        help_text="Special instructions or notes about service location",
                        verbose_name="location notes",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "service",
                    models.OneToOneField(
                        help_text="Service this location information applies to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="location_info",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Location",
                "verbose_name_plural": "Service Locations",
                "db_table": "catalog_service_locations",
            },
        ),
        migrations.CreateModel(
            name="ServiceGallery",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        help_text="Gallery image file",
                        upload_to="gallery/%Y/%m/",
                        verbose_name="image",
                    ),
                ),
                (
                    "image_type",
                    models.CharField(
                        choices=[
                            ("provider", "Provider Gallery"),
                            ("service", "Service Image"),
                            ("work_sample", "Work Sample"),
                            ("before_after", "Before/After"),
                            ("certificate", "Certificate"),
                        ],
                        default="provider",
                        help_text="Type of image for categorization",
                        max_length=20,
                        verbose_name="image type",
                    ),
                ),
                (
                    "caption",
                    models.CharField(
                        blank=True,
                        help_text="Image caption or description",
                        max_length=200,
                        verbose_name="caption",
                    ),
                ),
                (
                    "alt_text",
                    models.CharField(
                        blank=True,
                        help_text="Alternative text for accessibility",
                        max_length=255,
                        verbose_name="alt text",
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False,
                        help_text="Whether to feature this image prominently",
                        verbose_name="is featured",
                    ),
                ),
                (
                    "is_cover",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is a cover/hero image",
                        verbose_name="is cover image",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Order for displaying images",
                        verbose_name="sort order",
                    ),
                ),
                (
                    "mobile_optimized",
                    models.BooleanField(
                        default=True,
                        help_text="Whether image is optimized for mobile display",
                        verbose_name="mobile optimized",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider this image belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gallery",
                        to="catalog.serviceprovider",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        blank=True,
                        help_text="Specific service this image showcases (optional)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="gallery",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Gallery Image",
                "verbose_name_plural": "Service Gallery Images",
                "db_table": "catalog_service_gallery",
                "ordering": ["provider", "sort_order", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ServiceAvailability",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "availability_type",
                    models.CharField(
                        choices=[
                            ("always", "Always Available"),
                            ("scheduled", "Scheduled Hours"),
                            ("appointment_only", "Appointment Only"),
                            ("seasonal", "Seasonal"),
                        ],
                        default="always",
                        help_text="Type of availability scheduling",
                        max_length=20,
                        verbose_name="availability type",
                    ),
                ),
                (
                    "min_advance_booking",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="Minimum hours in advance for booking",
                        verbose_name="minimum advance booking (hours)",
                    ),
                ),
                (
                    "max_advance_booking",
                    models.PositiveIntegerField(
                        default=90,
                        help_text="Maximum days in advance for booking",
                        verbose_name="maximum advance booking (days)",
                    ),
                ),
                (
                    "max_bookings_per_day",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum bookings allowed per day",
                        null=True,
                        verbose_name="max bookings per day",
                    ),
                ),
                (
                    "max_bookings_per_slot",
                    models.PositiveIntegerField(
                        default=1,
                        help_text="Maximum bookings for the same time slot",
                        verbose_name="max bookings per time slot",
                    ),
                ),
                (
                    "cancellation_hours",
                    models.PositiveIntegerField(
                        default=24,
                        help_text="Hours notice required for cancellation",
                        verbose_name="cancellation notice (hours)",
                    ),
                ),
                (
                    "weekend_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether service is available on weekends",
                        verbose_name="available on weekends",
                    ),
                ),
                (
                    "holiday_available",
                    models.BooleanField(
                        default=False,
                        help_text="Whether service is available on holidays",
                        verbose_name="available on holidays",
                    ),
                ),
                (
                    "instant_booking",
                    models.BooleanField(
                        default=True,
                        help_text="Allow instant booking without confirmation",
                        verbose_name="instant booking",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "service",
                    models.OneToOneField(
                        help_text="Service this availability applies to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="availability",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Availability",
                "verbose_name_plural": "Service Availabilities",
                "db_table": "catalog_service_availability",
            },
        ),
        migrations.AddField(
            model_name="service",
            name="category",
            field=models.ForeignKey(
                help_text="Category this service belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="services",
                to="catalog.servicecategory",
            ),
        ),
        migrations.AddField(
            model_name="service",
            name="provider",
            field=models.ForeignKey(
                help_text="Service provider offering this service",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="services",
                to="catalog.serviceprovider",
            ),
        ),
        migrations.CreateModel(
            name="OperatingHours",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "day",
                    models.CharField(
                        choices=[
                            ("monday", "Monday"),
                            ("tuesday", "Tuesday"),
                            ("wednesday", "Wednesday"),
                            ("thursday", "Thursday"),
                            ("friday", "Friday"),
                            ("saturday", "Saturday"),
                            ("sunday", "Sunday"),
                        ],
                        help_text="Day of the week",
                        max_length=10,
                        verbose_name="day of week",
                    ),
                ),
                (
                    "is_open",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the provider is open on this day",
                        verbose_name="is open",
                    ),
                ),
                (
                    "open_time",
                    models.TimeField(
                        help_text="Time when provider opens",
                        verbose_name="opening time",
                    ),
                ),
                (
                    "close_time",
                    models.TimeField(
                        help_text="Time when provider closes",
                        verbose_name="closing time",
                    ),
                ),
                (
                    "break_start",
                    models.TimeField(
                        blank=True,
                        help_text="Start time of lunch/break period",
                        null=True,
                        verbose_name="break start time",
                    ),
                ),
                (
                    "break_end",
                    models.TimeField(
                        blank=True,
                        help_text="End time of lunch/break period",
                        null=True,
                        verbose_name="break end time",
                    ),
                ),
                (
                    "notes",
                    models.CharField(
                        blank=True,
                        help_text="Special notes for this day",
                        max_length=255,
                        verbose_name="notes",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider these hours belong to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="operating_hours",
                        to="catalog.serviceprovider",
                    ),
                ),
            ],
            options={
                "verbose_name": "Operating Hours",
                "verbose_name_plural": "Operating Hours",
                "db_table": "catalog_operating_hours",
                "ordering": ["provider", "day"],
            },
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["business_name"], name="catalog_ser_busines_77fdd5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["city", "state"], name="catalog_ser_city_f67aa1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["latitude", "longitude"], name="catalog_ser_latitud_b1ddfd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(fields=["rating"], name="catalog_ser_rating_351bd3_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["is_active", "is_verified"],
                name="catalog_ser_is_acti_471907_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["created_at"], name="catalog_ser_created_4b97be_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicelocation",
            index=models.Index(
                fields=["service"], name="catalog_ser_service_7b3a27_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicelocation",
            index=models.Index(
                fields=["location_type"], name="catalog_ser_locatio_126449_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicelocation",
            index=models.Index(
                fields=["travel_radius"], name="catalog_ser_travel__dac552_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["provider"], name="catalog_ser_provide_e82b8e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["service"], name="catalog_ser_service_bdac94_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["image_type"], name="catalog_ser_image_t_2f12b2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["is_featured"], name="catalog_ser_is_feat_95adf8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["is_cover"], name="catalog_ser_is_cove_29c666_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["sort_order"], name="catalog_ser_sort_or_07ffc1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicegallery",
            index=models.Index(
                fields=["created_at"], name="catalog_ser_created_d352e9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(fields=["name"], name="catalog_ser_name_b76acd_idx"),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(fields=["slug"], name="catalog_ser_slug_fb1757_idx"),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["is_active", "is_popular"],
                name="catalog_ser_is_acti_22bb90_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["sort_order"], name="catalog_ser_sort_or_8a3779_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["parent"], name="catalog_ser_parent__b8f200_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["service"], name="catalog_ser_service_1893dd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["availability_type"], name="catalog_ser_availab_328422_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceavailability",
            index=models.Index(
                fields=["instant_booking"], name="catalog_ser_instant_6ff5a7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["provider"], name="catalog_ser_provide_2ce47a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["category"], name="catalog_ser_categor_a222c3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(fields=["name"], name="catalog_ser_name_049ed0_idx"),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["base_price"], name="catalog_ser_base_pr_fbbffb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["duration"], name="catalog_ser_duratio_f96efe_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["is_active", "is_available"],
                name="catalog_ser_is_acti_78638e_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["is_popular"], name="catalog_ser_is_popu_ce052b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["created_at"], name="catalog_ser_created_cfdce9_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="service",
            unique_together={("provider", "name")},
        ),
        migrations.AddIndex(
            model_name="operatinghours",
            index=models.Index(
                fields=["provider", "day"], name="catalog_ope_provide_e93f25_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="operatinghours",
            index=models.Index(
                fields=["is_open"], name="catalog_ope_is_open_150843_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="operatinghours",
            unique_together={("provider", "day")},
        ),
    ]
