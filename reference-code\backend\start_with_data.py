#!/usr/bin/env python
"""
Vierla Backend Startup Script with Automatic Data Seeding
Starts the Django development server with comprehensive mock data
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print startup banner"""
    print("\n" + "="*70)
    print("🚀 VIERLA BACKEND DEVELOPMENT SERVER")
    print("="*70)
    print("🎯 Starting with comprehensive mock data seeding")
    print("📱 Mobile-ready API server for frontend development")
    print("="*70)

def check_environment():
    """Check if environment is properly set up"""
    print("\n📋 Checking environment...")
    
    # Check if we're in the right directory
    if not Path('manage.py').exists():
        print("❌ Error: manage.py not found. Please run from backend directory.")
        return False
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment may not be activated")
        print("   Consider running: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)")
    
    # Check Django installation
    try:
        import django
        print(f"✅ Django {django.get_version()} detected")
    except ImportError:
        print("❌ Error: Django not installed. Run: pip install -r requirements/development.txt")
        return False
    
    return True

def run_migrations():
    """Run database migrations"""
    print("\n🔄 Running database migrations...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate'
        ], check=True, capture_output=True, text=True)
        print("✅ Database migrations completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def seed_startup_data():
    """Seed startup data using management command"""
    print("\n🌱 Seeding startup data...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'seed_startup_data', '--force'
        ], check=True, capture_output=True, text=True)
        print("✅ Startup data seeding completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Seeding failed, but continuing: {e}")
        print("You can manually run: python manage.py seed_startup_data")
        return False

def create_superuser():
    """Create superuser if it doesn't exist"""
    print("\n👑 Checking admin user...")
    try:
        # Check if superuser exists
        result = subprocess.run([
            sys.executable, 'manage.py', 'shell', '-c',
            "from django.contrib.auth import get_user_model; User = get_user_model(); print('exists' if User.objects.filter(is_superuser=True).exists() else 'none')"
        ], capture_output=True, text=True)
        
        if 'none' in result.stdout:
            print("🔧 Creating admin superuser...")
            # Create superuser non-interactively
            env = os.environ.copy()
            env.update({
                'DJANGO_SUPERUSER_EMAIL': '<EMAIL>',
                'DJANGO_SUPERUSER_PASSWORD': 'VierlaAdmin123!',
                'DJANGO_SUPERUSER_FIRST_NAME': 'Admin',
                'DJANGO_SUPERUSER_LAST_NAME': 'User'
            })
            
            subprocess.run([
                sys.executable, 'manage.py', 'createsuperuser', '--noinput'
            ], env=env, check=True)
            print("✅ Admin user created: <EMAIL> / VierlaAdmin123!")
        else:
            print("✅ Admin user already exists")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Superuser creation failed: {e}")
        return False

def start_server():
    """Start the Django development server"""
    print("\n🌐 Starting Django development server...")
    print("📱 Server will be accessible at:")
    print("   - Local: http://127.0.0.1:8000/")
    print("   - Network: http://192.168.2.65:8000/")
    print("   - API: http://192.168.2.65:8000/api/")
    print("   - Admin: http://192.168.2.65:8000/admin/")
    print("   - Docs: http://192.168.2.65:8000/api/docs/")
    
    print("\n🔑 Test Credentials:")
    print("   👤 Customer: <EMAIL> / VierlaTest123!")
    print("   🏢 Provider: <EMAIL> / VierlaTest123!")
    print("   👑 Admin: <EMAIL> / VierlaAdmin123!")
    
    print("\n" + "="*70)
    print("🎉 VIERLA BACKEND READY FOR DEVELOPMENT!")
    print("="*70)
    print("Press Ctrl+C to stop the server")
    print("="*70 + "\n")
    
    try:
        # Start server on network interface for mobile access
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '192.168.2.65:8000'
        ], check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Server failed to start: {e}")

def main():
    """Main startup function"""
    print_banner()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Run setup steps
    steps = [
        ("Database Migrations", run_migrations),
        ("Startup Data Seeding", seed_startup_data),
        ("Admin User Setup", create_superuser),
    ]
    
    for step_name, step_func in steps:
        if not step_func():
            response = input(f"\n⚠️  {step_name} failed. Continue anyway? (y/n): ").lower().strip()
            if response != 'y':
                print("❌ Startup cancelled")
                sys.exit(1)
    
    # Start the server
    start_server()

if __name__ == "__main__":
    main()
