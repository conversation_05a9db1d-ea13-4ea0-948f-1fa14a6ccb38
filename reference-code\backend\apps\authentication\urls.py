"""
Authentication URL configuration for Vierla Beauty Services Marketplace
Enhanced JWT authentication endpoints with mobile-first design
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView

from . import views

app_name = 'authentication'

urlpatterns = [
    # Authentication endpoints
    path('login/', views.CustomTokenObtainPairView.as_view(), name='login'),
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('social/', views.SocialAuthView.as_view(), name='social_auth'),

    # User profile endpoints
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('profile/details/', views.UserProfileDetailView.as_view(),
         name='profile_details'),
    path('status/', views.auth_status, name='auth_status'),

    # Password management
    path('password/change/', views.PasswordChangeView.as_view(),
         name='change_password'),
    path('password/reset/', views.PasswordResetRequestView.as_view(),
         name='password_reset_request'),
    path('password/reset/confirm/', views.PasswordResetConfirmView.as_view(),
         name='password_reset_confirm'),

    # Email verification
    path('email/verify/', views.EmailVerificationView.as_view(), name='email_verify'),
    path('email/resend/', views.resend_verification_email, name='email_resend'),

    # Role switching endpoints
    path('role/switch/', views.RoleSwitchView.as_view(), name='role_switch'),
    path('role/available/', views.AvailableRolesView.as_view(), name='available_roles'),
]
