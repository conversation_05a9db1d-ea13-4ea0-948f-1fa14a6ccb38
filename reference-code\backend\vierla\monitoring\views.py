"""
Performance Monitoring Dashboard Views for Vierla Backend
Based on Backend Agent Consultation for Production Monitoring
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.cache import cache
from django.conf import settings
from django.utils.decorators import method_decorator
from django.views import View
import json
import time
from datetime import datetime, timedelta
import psutil
from django.db import connection

@require_http_methods(["GET"])
def performance_dashboard(request):
    """
    Main performance dashboard endpoint providing comprehensive metrics.
    """
    # Get aggregated performance statistics
    stats = cache.get('performance_stats', {})
    
    # Get recent performance metrics (last hour)
    current_time = int(time.time())
    recent_metrics = []
    
    for i in range(60):  # Last 60 minutes
        timestamp = current_time - (i * 60)
        metrics_key = f"performance_metrics:{timestamp}"
        metrics = cache.get(metrics_key)
        if metrics:
            recent_metrics.append(metrics)
    
    # Get recent alerts
    recent_alerts = []
    for i in range(60):  # Last 60 minutes
        timestamp = current_time - (i * 60)
        alert_key = f"performance_alerts:{timestamp}"
        alerts = cache.get(alert_key)
        if alerts:
            recent_alerts.append(alerts)
    
    # Calculate endpoint usage statistics
    endpoint_stats = _get_endpoint_usage_stats()
    
    # Get system health
    system_health = _get_system_health()
    
    dashboard_data = {
        'timestamp': datetime.now().isoformat(),
        'overview': stats,
        'recent_metrics': recent_metrics[:20],  # Last 20 requests
        'recent_alerts': recent_alerts[:10],    # Last 10 alerts
        'endpoint_usage': endpoint_stats,
        'system_health': system_health,
        'performance_trends': _calculate_performance_trends(recent_metrics),
    }
    
    return JsonResponse(dashboard_data)

@require_http_methods(["GET"])
def api_metrics(request):
    """
    Detailed API performance metrics endpoint.
    """
    # Get query parameters
    timeframe = request.GET.get('timeframe', '1h')  # 1h, 6h, 24h
    endpoint = request.GET.get('endpoint', None)
    
    # Calculate time range
    if timeframe == '1h':
        time_range = 3600
    elif timeframe == '6h':
        time_range = 21600
    elif timeframe == '24h':
        time_range = 86400
    else:
        time_range = 3600
    
    current_time = int(time.time())
    start_time = current_time - time_range
    
    # Collect metrics for the specified timeframe
    metrics = []
    for timestamp in range(start_time, current_time, 60):  # Every minute
        metrics_key = f"performance_metrics:{timestamp}"
        metric = cache.get(metrics_key)
        if metric and (not endpoint or metric.get('path') == endpoint):
            metrics.append(metric)
    
    # Aggregate metrics
    aggregated = _aggregate_metrics(metrics)
    
    return JsonResponse({
        'timeframe': timeframe,
        'endpoint': endpoint,
        'total_requests': len(metrics),
        'aggregated_metrics': aggregated,
        'detailed_metrics': metrics[-50:] if len(metrics) > 50 else metrics,  # Last 50 requests
    })

@require_http_methods(["GET"])
def database_metrics(request):
    """
    Database performance metrics endpoint.
    """
    # Get database connection statistics
    db_stats = {
        'total_queries': len(connection.queries),
        'connection_info': {
            'vendor': connection.vendor,
            'settings': {
                'NAME': connection.settings_dict.get('NAME'),
                'HOST': connection.settings_dict.get('HOST'),
                'PORT': connection.settings_dict.get('PORT'),
            }
        }
    }
    
    # Get recent slow queries
    slow_queries = []
    for query in connection.queries[-100:]:  # Last 100 queries
        if float(query['time']) > 0.1:  # Queries taking more than 100ms
            slow_queries.append({
                'sql': query['sql'][:200] + '...' if len(query['sql']) > 200 else query['sql'],
                'time': float(query['time']),
            })
    
    # Sort by execution time
    slow_queries.sort(key=lambda x: x['time'], reverse=True)
    
    return JsonResponse({
        'database_stats': db_stats,
        'slow_queries': slow_queries[:20],  # Top 20 slow queries
        'query_analysis': _analyze_query_patterns(connection.queries[-100:]),
    })

@require_http_methods(["GET"])
def system_resources(request):
    """
    System resource monitoring endpoint.
    """
    # CPU metrics
    cpu_percent = psutil.cpu_percent(interval=1)
    cpu_count = psutil.cpu_count()
    
    # Memory metrics
    memory = psutil.virtual_memory()
    
    # Disk metrics
    disk = psutil.disk_usage('/')
    
    # Network metrics (if available)
    try:
        network = psutil.net_io_counters()
        network_stats = {
            'bytes_sent': network.bytes_sent,
            'bytes_recv': network.bytes_recv,
            'packets_sent': network.packets_sent,
            'packets_recv': network.packets_recv,
        }
    except:
        network_stats = None
    
    return JsonResponse({
        'timestamp': datetime.now().isoformat(),
        'cpu': {
            'percent': cpu_percent,
            'count': cpu_count,
        },
        'memory': {
            'total_gb': memory.total / 1024 / 1024 / 1024,
            'available_gb': memory.available / 1024 / 1024 / 1024,
            'percent': memory.percent,
            'used_gb': memory.used / 1024 / 1024 / 1024,
        },
        'disk': {
            'total_gb': disk.total / 1024 / 1024 / 1024,
            'free_gb': disk.free / 1024 / 1024 / 1024,
            'used_gb': disk.used / 1024 / 1024 / 1024,
            'percent': (disk.used / disk.total) * 100,
        },
        'network': network_stats,
    })

def _get_endpoint_usage_stats():
    """Get endpoint usage statistics from cache."""
    endpoint_stats = {}
    
    # Get all endpoint usage keys from cache
    # Note: This is a simplified implementation
    # In production, you might want to use Redis SCAN or similar
    common_endpoints = [
        'GET:/api/auth/me/',
        'POST:/api/auth/login/',
        'GET:/api/services/',
        'GET:/api/providers/',
        'POST:/api/bookings/',
        'GET:/api/bookings/',
    ]
    
    for endpoint in common_endpoints:
        usage_count = cache.get(f"endpoint_usage:{endpoint}", 0)
        if usage_count > 0:
            endpoint_stats[endpoint] = usage_count
    
    return endpoint_stats

def _get_system_health():
    """Get current system health status."""
    try:
        # Database health check
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            db_healthy = True
    except Exception:
        db_healthy = False
    
    # Cache health check
    try:
        cache.set('health_test', 'ok', timeout=10)
        cache_healthy = cache.get('health_test') == 'ok'
    except Exception:
        cache_healthy = False
    
    return {
        'database': 'healthy' if db_healthy else 'unhealthy',
        'cache': 'healthy' if cache_healthy else 'unhealthy',
        'overall': 'healthy' if db_healthy and cache_healthy else 'unhealthy',
    }

def _calculate_performance_trends(metrics):
    """Calculate performance trends from recent metrics."""
    if not metrics:
        return {}
    
    # Calculate averages for different time periods
    response_times = [m['response_time_ms'] for m in metrics]
    query_counts = [m['query_count'] for m in metrics]
    
    return {
        'avg_response_time': sum(response_times) / len(response_times),
        'max_response_time': max(response_times),
        'min_response_time': min(response_times),
        'avg_query_count': sum(query_counts) / len(query_counts),
        'total_requests': len(metrics),
        'error_rate': len([m for m in metrics if m['status_code'] >= 400]) / len(metrics) * 100,
    }

def _aggregate_metrics(metrics):
    """Aggregate performance metrics for analysis."""
    if not metrics:
        return {}
    
    response_times = [m['response_time_ms'] for m in metrics]
    query_counts = [m['query_count'] for m in metrics]
    status_codes = [m['status_code'] for m in metrics]
    
    return {
        'response_time': {
            'avg': sum(response_times) / len(response_times),
            'min': min(response_times),
            'max': max(response_times),
            'p95': sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0,
        },
        'query_count': {
            'avg': sum(query_counts) / len(query_counts),
            'min': min(query_counts),
            'max': max(query_counts),
        },
        'status_codes': {
            '2xx': len([s for s in status_codes if 200 <= s < 300]),
            '3xx': len([s for s in status_codes if 300 <= s < 400]),
            '4xx': len([s for s in status_codes if 400 <= s < 500]),
            '5xx': len([s for s in status_codes if 500 <= s < 600]),
        },
        'error_rate': len([s for s in status_codes if s >= 400]) / len(status_codes) * 100,
    }

def _analyze_query_patterns(queries):
    """Analyze database query patterns for optimization insights."""
    if not queries:
        return {}
    
    # Analyze query types
    query_types = {}
    total_time = 0
    
    for query in queries:
        sql = query['sql'].strip().upper()
        query_type = sql.split()[0] if sql else 'UNKNOWN'
        
        if query_type not in query_types:
            query_types[query_type] = {'count': 0, 'total_time': 0}
        
        query_types[query_type]['count'] += 1
        query_types[query_type]['total_time'] += float(query['time'])
        total_time += float(query['time'])
    
    return {
        'query_types': query_types,
        'total_queries': len(queries),
        'total_time': total_time,
        'avg_time_per_query': total_time / len(queries) if queries else 0,
    }
