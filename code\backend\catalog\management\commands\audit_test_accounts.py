"""
Django management command to audit test account security
Performs security checks and provides recommendations for test account management
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings

from catalog.security import (
    TestAccountSecurity,
    audit_test_accounts,
    production_safety_check,
    validate_test_account_settings
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Audit test account security and provide recommendations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix-issues',
            action='store_true',
            help='Attempt to fix found security issues automatically',
        )
        parser.add_argument(
            '--production-check',
            action='store_true',
            help='Perform production-specific security checks',
        )
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed audit information',
        )

    def handle(self, *args, **options):
        self.fix_issues = options['fix_issues']
        self.production_check = options['production_check']
        self.detailed = options['detailed']

        self.stdout.write(
            self.style.SUCCESS('🔒 Starting test account security audit...')
        )

        # Determine environment
        environment = self.get_environment_info()
        self.print_environment_info(environment)

        # Run audit
        audit_results = audit_test_accounts()
        self.print_audit_results(audit_results)

        # Run production-specific checks if requested
        if self.production_check:
            production_results = production_safety_check()
            self.print_production_results(production_results)

        # Validate settings
        settings_issues = validate_test_account_settings()
        if settings_issues:
            self.print_settings_issues(settings_issues)

        # Fix issues if requested
        if self.fix_issues and (audit_results['issues'] or settings_issues):
            self.attempt_fixes(audit_results, settings_issues)

        # Final summary
        self.print_final_summary(audit_results, settings_issues)

    def get_environment_info(self):
        """Get current environment information"""
        return {
            'is_production': TestAccountSecurity.is_production_environment(),
            'is_development': TestAccountSecurity.is_development_environment(),
            'debug_mode': getattr(settings, 'DEBUG', False),
            'environment_setting': getattr(settings, 'ENVIRONMENT', 'unknown'),
            'test_domain': TestAccountSecurity.get_test_account_domain()
        }

    def print_environment_info(self, env_info):
        """Print environment information"""
        self.stdout.write('\n🌍 Environment Information:')
        self.stdout.write('-' * 40)
        
        if env_info['is_production']:
            self.stdout.write(
                self.style.ERROR('   🚨 PRODUCTION ENVIRONMENT DETECTED')
            )
        elif env_info['is_development']:
            self.stdout.write(
                self.style.SUCCESS('   🛠️  Development Environment')
            )
        else:
            self.stdout.write(
                self.style.WARNING('   ❓ Unknown Environment')
            )
        
        if self.detailed:
            self.stdout.write(f'   📊 Environment Setting: {env_info["environment_setting"]}')
            self.stdout.write(f'   🐛 Debug Mode: {env_info["debug_mode"]}')
            self.stdout.write(f'   📧 Test Domain: {env_info["test_domain"]}')

    def print_audit_results(self, results):
        """Print audit results"""
        self.stdout.write('\n🔍 Security Audit Results:')
        self.stdout.write('-' * 40)
        
        self.stdout.write(f'   📊 Test Accounts Found: {results["test_accounts_count"]}')
        
        if results['issues']:
            self.stdout.write(f'   ❌ Security Issues: {len(results["issues"])}')
            for issue in results['issues']:
                self.stdout.write(f'      • {issue}')
        else:
            self.stdout.write('   ✅ No security issues found')
        
        if results['recommendations']:
            self.stdout.write(f'\n   💡 Recommendations:')
            for rec in results['recommendations']:
                self.stdout.write(f'      • {rec}')

    def print_production_results(self, results):
        """Print production-specific check results"""
        self.stdout.write('\n🏭 Production Safety Check:')
        self.stdout.write('-' * 40)
        
        if results['status'] == 'skipped':
            self.stdout.write(f'   ⏭️  Skipped: {results["reason"]}')
        else:
            if results['test_accounts_found']:
                self.stdout.write(
                    self.style.ERROR('   🚨 CRITICAL: Test accounts found in production!')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('   ✅ No test accounts found in production')
                )
            
            if results['issues']:
                for issue in results['issues']:
                    self.stdout.write(f'      ❌ {issue}')
            
            if results['actions_taken']:
                self.stdout.write('   🔧 Actions Taken:')
                for action in results['actions_taken']:
                    self.stdout.write(f'      • {action}')

    def print_settings_issues(self, issues):
        """Print settings validation issues"""
        if not issues:
            return
        
        self.stdout.write('\n⚙️  Settings Issues:')
        self.stdout.write('-' * 40)
        
        for issue in issues:
            self.stdout.write(f'   ⚠️  {issue}')

    def attempt_fixes(self, audit_results, settings_issues):
        """Attempt to fix found issues"""
        self.stdout.write('\n🔧 Attempting to fix issues...')
        self.stdout.write('-' * 40)
        
        fixed_count = 0
        
        # Fix unverified test accounts
        if any('unverified test accounts' in issue for issue in audit_results['issues']):
            from django.utils import timezone
            updated = User.objects.filter(
                is_test_account=True,
                is_verified=False
            ).update(
                is_verified=True,
                email_verified_at=timezone.now()
            )
            if updated > 0:
                self.stdout.write(f'   ✅ Verified {updated} test accounts')
                fixed_count += 1
        
        # Fix test accounts with bad email domains
        bad_email_accounts = User.objects.filter(
            is_test_account=True
        ).exclude(
            email__endswith=f'@{TestAccountSecurity.get_test_account_domain()}'
        )
        
        if bad_email_accounts.exists():
            # This is more complex to fix automatically, so just report
            self.stdout.write(
                f'   ⚠️  Cannot automatically fix {bad_email_accounts.count()} accounts with bad email domains'
            )
            self.stdout.write('      Manual intervention required')
        
        # Clean up test accounts in production (if enabled)
        if TestAccountSecurity.is_production_environment():
            if getattr(settings, 'AUTO_CLEANUP_TEST_ACCOUNTS_IN_PRODUCTION', False):
                cleaned = TestAccountSecurity.cleanup_test_accounts_on_production()
                if cleaned > 0:
                    self.stdout.write(f'   ✅ Cleaned up {cleaned} test accounts from production')
                    fixed_count += 1
            else:
                self.stdout.write('   ⚠️  Auto-cleanup disabled for production')
        
        if fixed_count > 0:
            self.stdout.write(f'\n   🎉 Fixed {fixed_count} issues automatically')
        else:
            self.stdout.write('   ℹ️  No issues could be fixed automatically')

    def print_final_summary(self, audit_results, settings_issues):
        """Print final audit summary"""
        total_issues = len(audit_results['issues']) + len(settings_issues)
        
        self.stdout.write('\n' + '='*60)
        
        if total_issues == 0:
            self.stdout.write(
                self.style.SUCCESS('🎉 SECURITY AUDIT PASSED')
            )
            self.stdout.write('✅ No security issues found')
            self.stdout.write('✅ Test account security is properly configured')
        else:
            self.stdout.write(
                self.style.WARNING('⚠️  SECURITY AUDIT COMPLETED WITH ISSUES')
            )
            self.stdout.write(f'📊 Total Issues Found: {total_issues}')
            
            if TestAccountSecurity.is_production_environment():
                self.stdout.write(
                    self.style.ERROR('🚨 PRODUCTION ENVIRONMENT - IMMEDIATE ACTION REQUIRED')
                )
        
        self.stdout.write('\n💡 Security Recommendations:')
        
        if TestAccountSecurity.is_production_environment():
            self.stdout.write('   🚨 Remove ALL test accounts from production immediately')
            self.stdout.write('   🔒 Enable AUTO_CLEANUP_TEST_ACCOUNTS_IN_PRODUCTION setting')
            self.stdout.write('   📊 Monitor for test accounts in production regularly')
        else:
            self.stdout.write('   🛡️  Keep test accounts isolated to development/testing')
            self.stdout.write('   📧 Use dedicated test email domains')
            self.stdout.write('   🧹 Regular cleanup of unused test accounts')
        
        self.stdout.write('\n🔧 Management Commands:')
        self.stdout.write('   🔍 Audit: python manage.py audit_test_accounts')
        self.stdout.write('   🧹 Cleanup: python manage.py cleanup_test_accounts')
        self.stdout.write('   🔧 Fix Issues: python manage.py audit_test_accounts --fix-issues')
        
        self.stdout.write('\n🔒 Security audit completed!')
        
        if total_issues > 0 and TestAccountSecurity.is_production_environment():
            self.stdout.write(
                self.style.ERROR('\n⚠️  CRITICAL: Security issues found in production!')
            )
            self.stdout.write(
                self.style.ERROR('Take immediate action to resolve these issues.')
            )
