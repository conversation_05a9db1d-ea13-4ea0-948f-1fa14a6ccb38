# Test Credentials for Vierla Application

**Last Updated:** August 6, 2025
**Status:** ✅ Verified Working Credentials

This document contains the **verified and working** test user credentials for development and testing purposes.

## ✅ Verified Working Test Users

### Primary Test User
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: Customer
- **Status**: ✅ VERIFIED WORKING
- **User ID**: 1

### Customer Test Account
- **Email**: `<EMAIL>`
- **Password**: `TestPass123!`
- **Role**: Customer
- **Status**: ✅ VERIFIED WORKING
- **User ID**: 27

### Demo User (Recommended for testing)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Role**: Customer
- **Status**: ✅ VERIFIED WORKING
- **User ID**: 33

### ✅ Additional Verified Test Users
- **Debug User**: `<EMAIL>` / `testpass123` (Customer) - User ID: 32
- **Provider Test Account**: `<EMAIL>` / `TestPass123!` (Service Provider) - User ID: 30

### Sample Users with Realistic Data
- **<PERSON>**: `<EMAIL>` (Customer)
- **Emma Thompson**: `<EMAIL>` (Customer)
- **Sophia Martinez**: `<EMAIL>` (Service Provider)
- **Nicole Anderson** (Luxe Hair Studio): `<EMAIL>` (Service Provider)

*Note: All sample users use password `password123` unless otherwise specified*

## Usage Instructions

### Frontend Testing
Use any of the above credentials to test the login functionality in the mobile app:

1. Open the Vierla app
2. Navigate to the login screen
3. Enter one of the email/password combinations above
4. Tap "Sign In"

### API Testing
You can test the login API directly with verified credentials:

```bash
# Using PowerShell (Recommended)
$body = @{email='<EMAIL>'; password='password123'} | ConvertTo-Json
Invoke-WebRequest -Uri 'http://************:8000/api/auth/login/' -Method POST -Body $body -ContentType 'application/json'

# Alternative with customer account
$body = @{email='<EMAIL>'; password='TestPass123!'} | ConvertTo-Json
Invoke-WebRequest -Uri 'http://************:8000/api/auth/login/' -Method POST -Body $body -ContentType 'application/json'

# Using curl (if available)
curl -X POST http://************:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### Backend Configuration
- **Backend URL**: `http://************:8000`
- **API Base**: `http://************:8000/api/`
- **Admin Panel**: `http://************:8000/admin/`

## Notes

- ✅ All verified test users are configured with the SQLite database
- ✅ Passwords are properly hashed using Django's authentication system
- ✅ Backend is accessible on `************:8000` for frontend connectivity
- ✅ These credentials are for development/testing only
- ✅ The backend supports case-insensitive email authentication
- ⚠️ Some documented credentials in other files may not work with current database

## Related Documentation

- **Consolidated Credentials**: `/code/docs/TEST_CREDENTIALS_CONSOLIDATED.md`
- **Account Specification**: `/code/docs/TEST-ACCOUNTS-SPECIFICATION.md` (contains non-working accounts)
- **Backend Documentation**: `/code/backend/docs/TEST_ACCOUNTS_README.md`

## Creating Additional Test Users

To create additional test users, use the Django shell:

```python
# Access Django shell
python manage.py shell

# Create a new user
from authentication.models import User

user = User.objects.create_user(
    email='<EMAIL>',
    username='newuser',
    password='newpassword123',
    first_name='New',
    last_name='User',
    role='customer',  # or 'service_provider'
    is_active=True
)
```

## Troubleshooting

### "Invalid credentials" Error
- ✅ Ensure you're using the **verified working** email and password combinations listed above
- ✅ Check that the backend server is running on `http://************:8000` (not localhost)
- ✅ Verify that the SQLite database is being used (check for `.env` file with `USE_SQLITE=true`)
- ⚠️ Avoid using credentials from TEST-ACCOUNTS-SPECIFICATION.md as they may not exist in the database

### Account Locked Error
If you see an account locked error, wait a few minutes or reset the failed login attempts:

```python
# In Django shell
from authentication.models import User
user = User.objects.get(email='<EMAIL>')
user.reset_failed_login()
```

## Security Note

These are test credentials for development only. In production:
- Use strong, unique passwords
- Implement proper user registration flows
- Enable email verification
- Use environment variables for sensitive data
