/**
 * useErrorHandler Hook - Centralized Error Handling for Vierla Application
 * 
 * This hook provides a comprehensive error handling system with automatic
 * error classification, user notification, and recovery mechanisms.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useRef } from 'react';
import { Alert } from 'react-native';
import {
  AppError,
  ErrorType,
  ErrorSeverity,
  ErrorContext,
  UseErrorHandlerReturn,
  DEFAULT_ERROR_CONFIG,
} from '../utils/errorTypes';
import {
  createAppError,
  createNetworkError,
  createValidationError,
  createAuthError,
  formatErrorMessage,
  getErrorTitle,
  isRetryableError,
  logError,
  provideErrorHaptics,
  showCriticalErrorAlert,
  shouldReportError,
} from '../utils/errorUtils';
import { useToast } from './useToast';

interface ErrorHandlerOptions {
  enableLogging?: boolean;
  enableHaptics?: boolean;
  enableToasts?: boolean;
  enableModals?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Error Handler Hook
 */
export const useErrorHandler = (
  options: ErrorHandlerOptions = {}
): UseErrorHandlerReturn => {
  const config = { ...DEFAULT_ERROR_CONFIG, ...options };
  const { showError, showWarning } = useToast();
  const retryCountRef = useRef<Map<string, number>>(new Map());

  /**
   * Main error handler
   */
  const handleError = useCallback(
    (error: Error, context?: ErrorContext): AppError => {
      const appError = createAppError(
        error,
        ErrorType.UNKNOWN,
        ErrorSeverity.MEDIUM,
        context
      );

      // Log error if enabled
      if (config.enableLogging) {
        logError(appError);
      }

      // Provide haptic feedback if enabled
      if (config.enableHaptics) {
        provideErrorHaptics(appError.severity);
      }

      // Show user notification based on severity
      if (config.enableToasts || config.enableModals) {
        showErrorNotification(appError);
      }

      return appError;
    },
    [config, showError, showWarning]
  );

  /**
   * Network error handler
   */
  const handleNetworkError = useCallback(
    (error: Error, context?: ErrorContext): AppError => {
      const appError = createNetworkError(null, error, context);

      // Log error
      if (config.enableLogging) {
        logError(appError);
      }

      // Provide haptic feedback
      if (config.enableHaptics) {
        provideErrorHaptics(appError.severity);
      }

      // Show toast notification with retry option
      if (config.enableToasts) {
        showError(
          'Connection Issue',
          formatErrorMessage(appError),
          {
            persistent: true,
            action: {
              label: 'Retry',
              onPress: () => {
                // Retry logic would be handled by the calling component
                console.log('Retry requested for network error');
              },
            },
          }
        );
      }

      return appError;
    },
    [config, showError]
  );

  /**
   * Validation error handler
   */
  const handleValidationError = useCallback(
    (field: string, message: string, context?: ErrorContext): AppError => {
      const appError = createValidationError(field, message, context);

      // Log error
      if (config.enableLogging) {
        logError(appError);
      }

      // Show warning toast for validation errors
      if (config.enableToasts) {
        showWarning(
          'Validation Error',
          message,
          {
            duration: 3000,
          }
        );
      }

      return appError;
    },
    [config, showWarning]
  );

  /**
   * Authentication error handler
   */
  const handleAuthError = useCallback(
    (error: Error, context?: ErrorContext): AppError => {
      const appError = createAuthError(error.message, context);

      // Log error
      if (config.enableLogging) {
        logError(appError);
      }

      // Provide haptic feedback
      if (config.enableHaptics) {
        provideErrorHaptics(appError.severity);
      }

      // Show modal for auth errors
      if (config.enableModals) {
        showCriticalErrorAlert(
          appError,
          undefined, // No retry for auth errors
          () => {
            // Report issue callback
            console.log('Report auth issue');
          }
        );
      }

      return appError;
    },
    [config]
  );

  /**
   * Show error alert
   */
  const showErrorAlert = useCallback(
    (
      title: string,
      message: string,
      actions?: Array<{ text: string; onPress?: () => void }>
    ): void => {
      const alertActions = actions || [{ text: 'OK' }];
      
      Alert.alert(
        title,
        message,
        alertActions.map(action => ({
          text: action.text,
          onPress: action.onPress,
          style: action.text === 'Cancel' ? 'cancel' : 'default',
        }))
      );
    },
    []
  );

  /**
   * Clear all errors
   */
  const clearErrors = useCallback((): void => {
    retryCountRef.current.clear();
    // Additional cleanup logic can be added here
  }, []);

  /**
   * Show error notification based on severity and type
   */
  const showErrorNotification = useCallback(
    (error: AppError): void => {
      const message = formatErrorMessage(error);
      const title = getErrorTitle(error.type, error.severity);

      switch (error.severity) {
        case ErrorSeverity.CRITICAL:
          if (config.enableModals) {
            showCriticalErrorAlert(error);
          }
          break;

        case ErrorSeverity.HIGH:
          if (config.enableToasts) {
            showError(title, message, {
              persistent: true,
              enableHaptics: config.enableHaptics,
            });
          }
          break;

        case ErrorSeverity.MEDIUM:
          if (config.enableToasts) {
            showError(title, message, {
              duration: 5000,
              enableHaptics: config.enableHaptics,
            });
          }
          break;

        case ErrorSeverity.LOW:
          if (config.enableToasts) {
            showWarning(title, message, {
              duration: 3000,
              enableHaptics: config.enableHaptics,
            });
          }
          break;
      }
    },
    [config, showError, showWarning]
  );

  /**
   * Handle retry logic
   */
  const handleRetry = useCallback(
    (error: AppError, retryFunction: () => Promise<void>): void => {
      const errorKey = `${error.type}_${error.message}`;
      const currentRetries = retryCountRef.current.get(errorKey) || 0;

      if (currentRetries >= config.maxRetries) {
        showError(
          'Max Retries Exceeded',
          'Unable to complete the operation after multiple attempts.',
          { persistent: true }
        );
        return;
      }

      retryCountRef.current.set(errorKey, currentRetries + 1);

      // Execute retry with delay
      setTimeout(async () => {
        try {
          await retryFunction();
          // Reset retry count on success
          retryCountRef.current.delete(errorKey);
        } catch (retryError) {
          // Handle retry failure
          const retryAppError = createAppError(
            retryError instanceof Error ? retryError : new Error(String(retryError)),
            error.type,
            error.severity,
            error.context
          );
          handleError(retryAppError, error.context);
        }
      }, config.retryDelay * Math.pow(2, currentRetries)); // Exponential backoff
    },
    [config, showError, handleError]
  );

  return {
    handleError,
    handleNetworkError,
    handleValidationError,
    handleAuthError,
    showErrorAlert,
    clearErrors,
  };
};
