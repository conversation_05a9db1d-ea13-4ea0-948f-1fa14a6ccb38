#!/usr/bin/env python3
"""
Script to align service categories with vierla.com website
This script will clean up duplicate categories and align with the official website categories
"""

import os
import sys
import django
from django.utils.text import slugify

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.catalog.models import ServiceCategory

# Official categories from vierla.com website
VIERLA_CATEGORIES = [
    {
        'name': 'Barbers',
        'slug': 'barbers',
        'description': 'Classic cuts, beard trims, hot towel shaves, and hair styling services',
        'icon': 'cut-outline',
        'mobile_icon': '✂️',
        'color': '#364035',  # Primary Forest Green from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 1
    },
    {
        'name': 'Makeup',
        'slug': 'makeup',
        'description': 'Event makeup, bridal looks, fashion makeup, and everyday glam',
        'icon': 'brush-outline',
        'mobile_icon': '💄',
        'color': '#8B9A8C',  # Sage Green Accent from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 2
    },
    {
        'name': 'Salons',
        'slug': 'salons',
        'description': 'Hair cuts & color, blowouts, treatments, and full styling',
        'icon': 'cut-outline',
        'mobile_icon': '💇‍♀️',
        'color': '#B8956A',  # Rich Gold Accent from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 3
    },
    {
        'name': 'Locs',
        'slug': 'locs',
        'description': 'Loc maintenance, retwisting, loc styling, and loc repair',
        'icon': 'flower-outline',
        'mobile_icon': '🌀',
        'color': '#C9BEB0',  # Soft Taupe from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 4
    },
    {
        'name': 'Braids',
        'slug': 'braids',
        'description': 'Box braids, cornrows, French braids, and protective styles',
        'icon': 'flower-outline',
        'mobile_icon': '🤎',
        'color': '#364035',  # Primary Forest Green from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 5
    },
    {
        'name': 'Nails',
        'slug': 'nails',
        'description': 'Manicures, pedicures, nail art, and gel polish',
        'icon': 'hand-left-outline',
        'mobile_icon': '💅',
        'color': '#8B9A8C',  # Sage Green Accent from COLOR_PALETTE.md
        'is_popular': True,
        'sort_order': 6
    },
    {
        'name': 'Brows',
        'slug': 'brows',
        'description': 'Eyebrow shaping, threading, tinting, and microblading',
        'icon': 'eye-outline',
        'mobile_icon': '🤨',
        'color': '#B8956A',  # Rich Gold Accent from COLOR_PALETTE.md
        'is_popular': False,
        'sort_order': 7
    },
    {
        'name': 'Eyelashes',
        'slug': 'eyelashes',
        'description': 'Lash extensions, lash lifts, lash tinting, and volume lashes',
        'icon': 'eye-outline',
        'mobile_icon': '👁️',
        'color': '#C9BEB0',  # Soft Taupe from COLOR_PALETTE.md
        'is_popular': False,
        'sort_order': 8
    }
]

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def clean_existing_categories():
    """Remove all existing categories to start fresh"""
    print_header("CLEANING EXISTING CATEGORIES")
    
    existing_count = ServiceCategory.objects.count()
    print(f"📊 Found {existing_count} existing categories")
    
    if existing_count > 0:
        ServiceCategory.objects.all().delete()
        print(f"🗑️  Deleted all {existing_count} existing categories")
    else:
        print("✅ No existing categories to clean")

def create_vierla_categories():
    """Create the official Vierla categories"""
    print_header("CREATING VIERLA CATEGORIES")
    
    created_categories = []
    
    for category_data in VIERLA_CATEGORIES:
        try:
            category = ServiceCategory.objects.create(**category_data)
            created_categories.append(category)
            print(f"✅ Created: {category.name} (Sort: {category.sort_order})")
            print(f"   📝 Description: {category.description}")
            print(f"   🎨 Color: {category.color} | Icon: {category.icon}")
            print(f"   📱 Mobile Icon: {category.mobile_icon} | Popular: {category.is_popular}")
            
        except Exception as e:
            print(f"❌ Failed to create {category_data['name']}: {str(e)}")
    
    print(f"\n🎉 Successfully created {len(created_categories)} categories")
    return created_categories

def verify_categories():
    """Verify the created categories"""
    print_header("VERIFYING CATEGORIES")
    
    categories = ServiceCategory.objects.filter(is_active=True).order_by('sort_order')
    
    print(f"📊 Found {categories.count()} active categories:")
    
    for category in categories:
        print(f"   {category.sort_order}. {category.name}")
        print(f"      🎨 Color: {category.color} | Icon: {category.icon}")
        print(f"      📱 Mobile: {category.mobile_icon} | Popular: {category.is_popular}")
        print(f"      📝 {category.description}")
        print()
    
    # Verify against expected categories
    expected_names = [cat['name'] for cat in VIERLA_CATEGORIES]
    actual_names = [cat.name for cat in categories]
    
    missing = set(expected_names) - set(actual_names)
    extra = set(actual_names) - set(expected_names)
    
    if missing:
        print(f"⚠️  Missing categories: {missing}")
    if extra:
        print(f"⚠️  Extra categories: {extra}")
    
    if not missing and not extra:
        print("✅ All categories match the expected Vierla categories!")

def main():
    """Main execution function"""
    print_header("VIERLA SERVICE CATEGORIES ALIGNMENT")
    print("🎯 Aligning service categories with vierla.com website")
    print("📋 This will clean existing categories and create new ones")
    
    try:
        # Step 1: Clean existing categories
        clean_existing_categories()
        
        # Step 2: Create Vierla categories
        create_vierla_categories()
        
        # Step 3: Verify the results
        verify_categories()
        
        print_header("ALIGNMENT COMPLETE")
        print("✅ Service categories have been successfully aligned with vierla.com")
        print("🔄 Frontend applications will now display the correct categories")
        
    except Exception as e:
        print(f"❌ Error during category alignment: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
