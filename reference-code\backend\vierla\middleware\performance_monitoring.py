"""
Performance Monitoring Middleware for Vierla Backend
Based on Backend Agent Consultation for Production Readiness
"""

import time
import logging
import psutil
from django.conf import settings
from django.db import connection
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.http import JsonResponse
import json
from datetime import datetime, timedelta

# Configure performance logger
performance_logger = logging.getLogger('vierla.performance')

class PerformanceMonitoringMiddleware(MiddlewareMixin):
    """
    Comprehensive performance monitoring middleware for Vierla backend.
    Tracks request/response times, database queries, memory usage, and API metrics.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Start performance tracking for incoming request."""
        request._performance_start_time = time.time()
        request._performance_start_queries = len(connection.queries)
        request._performance_start_memory = psutil.Process().memory_info().rss
        
        # Track API endpoint usage
        self._track_endpoint_usage(request)
        
        return None
    
    def process_response(self, request, response):
        """Complete performance tracking and log metrics."""
        if not hasattr(request, '_performance_start_time'):
            return response
        
        # Calculate performance metrics
        end_time = time.time()
        response_time = end_time - request._performance_start_time
        
        # Database query metrics
        end_queries = len(connection.queries)
        query_count = end_queries - request._performance_start_queries
        query_time = sum(float(query['time']) for query in connection.queries[request._performance_start_queries:])
        
        # Memory usage metrics
        end_memory = psutil.Process().memory_info().rss
        memory_delta = end_memory - request._performance_start_memory
        
        # Create performance metrics
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'response_time_ms': round(response_time * 1000, 2),
            'query_count': query_count,
            'query_time_ms': round(query_time * 1000, 2),
            'memory_delta_mb': round(memory_delta / 1024 / 1024, 2),
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'remote_addr': self._get_client_ip(request),
        }
        
        # Log performance metrics
        self._log_performance_metrics(metrics)
        
        # Store metrics for monitoring dashboard
        self._store_metrics(metrics)
        
        # Add performance headers for debugging
        if settings.DEBUG:
            response['X-Response-Time'] = f"{metrics['response_time_ms']}ms"
            response['X-Query-Count'] = str(metrics['query_count'])
            response['X-Query-Time'] = f"{metrics['query_time_ms']}ms"
        
        # Check for performance alerts
        self._check_performance_alerts(metrics)
        
        return response
    
    def _track_endpoint_usage(self, request):
        """Track API endpoint usage statistics."""
        endpoint_key = f"endpoint_usage:{request.method}:{request.path}"
        cache.set(endpoint_key, cache.get(endpoint_key, 0) + 1, timeout=3600)
    
    def _get_client_ip(self, request):
        """Get client IP address from request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _log_performance_metrics(self, metrics):
        """Log performance metrics to structured logger."""
        if metrics['response_time_ms'] > 1000:  # Log slow requests
            performance_logger.warning(
                f"Slow request detected: {metrics['method']} {metrics['path']} "
                f"took {metrics['response_time_ms']}ms with {metrics['query_count']} queries"
            )
        
        # Log all requests in debug mode
        if settings.DEBUG:
            performance_logger.info(f"Performance metrics: {json.dumps(metrics)}")
    
    def _store_metrics(self, metrics):
        """Store metrics in cache for monitoring dashboard."""
        # Store recent metrics (last hour)
        metrics_key = f"performance_metrics:{int(time.time())}"
        cache.set(metrics_key, metrics, timeout=3600)
        
        # Update aggregated statistics
        self._update_aggregated_stats(metrics)
    
    def _update_aggregated_stats(self, metrics):
        """Update aggregated performance statistics."""
        stats_key = "performance_stats"
        stats = cache.get(stats_key, {
            'total_requests': 0,
            'avg_response_time': 0,
            'slow_requests': 0,
            'error_requests': 0,
            'last_updated': datetime.now().isoformat()
        })
        
        # Update statistics
        stats['total_requests'] += 1
        stats['avg_response_time'] = (
            (stats['avg_response_time'] * (stats['total_requests'] - 1) + metrics['response_time_ms']) 
            / stats['total_requests']
        )
        
        if metrics['response_time_ms'] > 1000:
            stats['slow_requests'] += 1
        
        if metrics['status_code'] >= 400:
            stats['error_requests'] += 1
        
        stats['last_updated'] = datetime.now().isoformat()
        
        cache.set(stats_key, stats, timeout=86400)  # 24 hours
    
    def _check_performance_alerts(self, metrics):
        """Check for performance alerts and thresholds."""
        alerts = []
        
        # Response time alert
        if metrics['response_time_ms'] > 2000:
            alerts.append({
                'type': 'slow_response',
                'message': f"Very slow response: {metrics['response_time_ms']}ms",
                'severity': 'high'
            })
        
        # Query count alert
        if metrics['query_count'] > 20:
            alerts.append({
                'type': 'high_query_count',
                'message': f"High query count: {metrics['query_count']} queries",
                'severity': 'medium'
            })
        
        # Memory usage alert
        if metrics['memory_delta_mb'] > 50:
            alerts.append({
                'type': 'high_memory_usage',
                'message': f"High memory usage: {metrics['memory_delta_mb']}MB",
                'severity': 'medium'
            })
        
        # Store alerts for monitoring
        if alerts:
            alert_key = f"performance_alerts:{int(time.time())}"
            cache.set(alert_key, {
                'timestamp': datetime.now().isoformat(),
                'metrics': metrics,
                'alerts': alerts
            }, timeout=3600)
            
            # Log critical alerts
            for alert in alerts:
                if alert['severity'] == 'high':
                    performance_logger.error(f"Performance Alert: {alert['message']}")


class SystemHealthMiddleware(MiddlewareMixin):
    """
    System health monitoring middleware for production readiness.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Monitor system health on each request."""
        if request.path == '/health/':
            return self._health_check_response()
        return None
    
    def _health_check_response(self):
        """Generate comprehensive health check response."""
        try:
            # Database health
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                db_healthy = True
        except Exception:
            db_healthy = False
        
        # Cache health
        try:
            cache.set('health_check', 'ok', timeout=10)
            cache_healthy = cache.get('health_check') == 'ok'
        except Exception:
            cache_healthy = False
        
        # System resources
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_data = {
            'status': 'healthy' if db_healthy and cache_healthy else 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'services': {
                'database': 'healthy' if db_healthy else 'unhealthy',
                'cache': 'healthy' if cache_healthy else 'unhealthy',
            },
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': (disk.used / disk.total) * 100,
                'memory_available_mb': memory.available / 1024 / 1024,
                'disk_free_gb': disk.free / 1024 / 1024 / 1024,
            },
            'performance': cache.get('performance_stats', {}),
        }
        
        status_code = 200 if health_data['status'] == 'healthy' else 503
        return JsonResponse(health_data, status=status_code)
