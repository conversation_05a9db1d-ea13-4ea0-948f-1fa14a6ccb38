# Generated by Django 4.2.16 on 2025-07-06 22:21

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("bookings", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PaymentIntent",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "stripe_payment_intent_id",
                    models.CharField(max_length=255, unique=True),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                ("currency", models.Char<PERSON>ield(default="CAD", max_length=3)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("requires_payment_method", "Requires Payment Method"),
                            ("requires_confirmation", "Requires Confirmation"),
                            ("requires_action", "Requires Action"),
                            ("processing", "Processing"),
                            ("succeeded", "Succeeded"),
                            ("canceled", "Canceled"),
                            ("requires_capture", "Requires Capture"),
                        ],
                        max_length=30,
                    ),
                ),
                ("client_secret", models.CharField(max_length=255)),
                (
                    "payment_method_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "booking",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_intents",
                        to="bookings.booking",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_intents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "payment_intents",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PaymentMethod",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("card", "Credit/Debit Card"),
                            ("apple_pay", "Apple Pay"),
                            ("google_pay", "Google Pay"),
                            ("paypal", "PayPal"),
                            ("bank_transfer", "Bank Transfer"),
                        ],
                        max_length=20,
                    ),
                ),
                ("is_default", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("last_four", models.CharField(blank=True, max_length=4, null=True)),
                ("brand", models.CharField(blank=True, max_length=20, null=True)),
                ("expires_at", models.CharField(blank=True, max_length=7, null=True)),
                (
                    "stripe_payment_method_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "stripe_customer_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_methods",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "payment_methods",
                "ordering": ["-is_default", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Transaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("payment", "Payment"),
                            ("refund", "Refund"),
                            ("partial_refund", "Partial Refund"),
                            ("chargeback", "Chargeback"),
                            ("fee", "Fee"),
                        ],
                        default="payment",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("refunded", "Refunded"),
                            ("partially_refunded", "Partially Refunded"),
                            ("canceled", "Canceled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "service_fee",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                ("currency", models.CharField(default="CAD", max_length=3)),
                (
                    "stripe_charge_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "stripe_refund_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("failure_reason", models.TextField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "booking",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="bookings.booking",
                    ),
                ),
                (
                    "payment_intent",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="payments.paymentintent",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transactions",
                        to="payments.paymentmethod",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "transactions",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "status"], name="transaction_user_id_08c743_idx"
                    ),
                    models.Index(
                        fields=["booking", "type"],
                        name="transaction_booking_d30a3d_idx",
                    ),
                    models.Index(
                        fields=["stripe_charge_id"],
                        name="transaction_stripe__60c185_idx",
                    ),
                ],
            },
        ),
    ]
