# EPIC-02: Service Browsing & Display - Frontend UI/UX Architecture

## Overview
This document outlines the frontend architecture for EPIC-02, following the UX Interface Design System and ensuring seamless integration with the existing authentication system.

## Design System Compliance

### Color Palette (Following Brand Guidelines)
```typescript
export const Colors = {
  primary: {
    main: '#D81B60',      // Vierla Magenta
    light: '#FFD180',     // Soft Peach
    white: '#FFFFFF',     // Pure White
  },
  background: {
    light: '#F5F5F5',     // Cloud White
    surface: '#FFFFFF',   // Pure White
    dark: '#121212',      // Deep Space
    surfaceDark: '#1E1E1E', // Charcoal
  },
  text: {
    primary: '#212121',   // Onyx
    secondary: '#616161', // Graphite
    onDark: '#E0E0E0',   // Silver
  },
  status: {
    success: '#2E7D32',  // Forest Green
    error: '#C62828',    // Crimson Red
  },
  border: '#E0E0E0',     // Light Grey
};
```

### Typography Scale
```typescript
export const Typography = {
  h1: { fontSize: 32, fontWeight: 'bold', lineHeight: 40 },
  h2: { fontSize: 24, fontWeight: 'bold', lineHeight: 32 },
  h3: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
  bodyLarge: { fontSize: 18, fontWeight: 'normal', lineHeight: 24 },
  body: { fontSize: 16, fontWeight: 'normal', lineHeight: 22 },
  caption: { fontSize: 14, fontWeight: 'normal', lineHeight: 20 },
  small: { fontSize: 12, fontWeight: 'normal', lineHeight: 16 },
};
```

### Spacing System (8pt Grid)
```typescript
export const Spacing = {
  micro: 4,   // tight elements
  small: 8,   // related elements
  medium: 16, // component padding
  large: 24,  // section separation
  xl: 32,     // major layout divisions
};
```

## Component Architecture (Atomic Design)

### Atomic Components (Enhanced Existing)
Building on the existing Button, Input, and Text components:

#### ServiceCard Component
```typescript
interface ServiceCardProps {
  service: Service;
  onPress: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
  variant?: 'default' | 'compact' | 'featured';
}
```

#### CategoryCard Component
```typescript
interface CategoryCardProps {
  category: ServiceCategory;
  onPress: () => void;
  variant?: 'default' | 'compact' | 'icon-only';
}
```

#### ProviderCard Component
```typescript
interface ProviderCardProps {
  provider: ServiceProvider;
  onPress: () => void;
  showServices?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}
```

### Molecular Components

#### SearchBar Component
```typescript
interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  onSubmit: () => void;
  onFilterPress: () => void;
  placeholder?: string;
  showFilter?: boolean;
}
```

#### ServiceList Component
```typescript
interface ServiceListProps {
  services: Service[];
  onServicePress: (service: Service) => void;
  onFavoriteToggle?: (serviceId: string) => void;
  favoriteServices?: string[];
  loading?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  variant?: 'grid' | 'list';
}
```

#### FilterPanel Component
```typescript
interface FilterPanelProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onApply: () => void;
  onClear: () => void;
  categories: ServiceCategory[];
  visible: boolean;
  onClose: () => void;
}
```

### Organism Components (Screens)

#### ServicesScreen (Enhanced)
The main service browsing screen with:
- Search functionality
- Category browsing
- Service listings
- Filtering options

#### ServiceDetailsScreen
Detailed service information with:
- Service images and gallery
- Provider information
- Pricing and duration
- Booking CTA
- Reviews and ratings

#### CategoryScreen
Category-specific service browsing with:
- Category header and description
- Filtered service listings
- Subcategory navigation

## Screen Structure

### 1. ServicesScreen (Main Service Browsing)
```
┌─────────────────────────────────────┐
│ Header: "Services"                  │
├─────────────────────────────────────┤
│ SearchBar with Filter Button        │
├─────────────────────────────────────┤
│ Category Horizontal Scroll          │
│ [Hair] [Nails] [Spa] [Massage]...  │
├─────────────────────────────────────┤
│ Featured Services Section           │
│ ┌─────┐ ┌─────┐ ┌─────┐            │
│ │ Svc │ │ Svc │ │ Svc │            │
│ └─────┘ └─────┘ └─────┘            │
├─────────────────────────────────────┤
│ All Services List                   │
│ ┌─────────────────────────────────┐ │
│ │ Service Card with Image         │ │
│ │ Provider • Rating • Price       │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ Service Card with Image         │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2. ServiceDetailsScreen
```
┌─────────────────────────────────────┐
│ ← Back                    ♡ Favorite │
├─────────────────────────────────────┤
│                                     │
│        Service Image Gallery        │
│                                     │
├─────────────────────────────────────┤
│ Service Name                        │
│ Provider Name • ⭐ 4.8 (123)        │
├─────────────────────────────────────┤
│ $75 • 60 minutes                    │
├─────────────────────────────────────┤
│ Description                         │
│ Lorem ipsum dolor sit amet...       │
├─────────────────────────────────────┤
│ Provider Information                │
│ ┌─────────────────────────────────┐ │
│ │ Avatar • Name • Rating          │ │
│ │ Location • Experience           │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ [Book Now] [Message Provider]       │
└─────────────────────────────────────┘
```

### 3. SearchResultsScreen
```
┌─────────────────────────────────────┐
│ ← "haircut" 🔍 [Filter]             │
├─────────────────────────────────────┤
│ 24 results found                    │
├─────────────────────────────────────┤
│ Sort: [Relevance ▼]                 │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ Service Card                    │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ Service Card                    │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## Navigation Integration

### Tab Navigation Enhancement
Update the existing MainNavigator to include proper service browsing:

```typescript
const MainNavigator = () => (
  <Tab.Navigator>
    <Tab.Screen name="Home" component={HomeScreen} />
    <Tab.Screen name="Services" component={ServicesScreen} />
    <Tab.Screen name="Bookings" component={BookingsScreen} />
    <Tab.Screen name="Profile" component={ProfileScreen} />
  </Tab.Navigator>
);
```

### Stack Navigation for Service Flow
```typescript
const ServiceStackNavigator = () => (
  <Stack.Navigator>
    <Stack.Screen name="ServicesList" component={ServicesScreen} />
    <Stack.Screen name="ServiceDetails" component={ServiceDetailsScreen} />
    <Stack.Screen name="CategoryServices" component={CategoryScreen} />
    <Stack.Screen name="SearchResults" component={SearchResultsScreen} />
    <Stack.Screen name="ProviderProfile" component={ProviderProfileScreen} />
  </Stack.Navigator>
);
```

## API Integration Layer

### Service API Client
```typescript
export class ServiceApiClient {
  async getServices(filters?: SearchFilters): Promise<Service[]>
  async getService(id: string): Promise<Service>
  async getCategories(): Promise<ServiceCategory[]>
  async getProviders(filters?: ProviderFilters): Promise<ServiceProvider[]>
  async searchServices(query: string, filters?: SearchFilters): Promise<SearchResponse>
}
```

### State Management with TanStack Query
```typescript
// Custom hooks for service data
export const useServices = (filters?: SearchFilters) => {
  return useQuery({
    queryKey: ['services', filters],
    queryFn: () => serviceApi.getServices(filters),
  });
};

export const useService = (id: string) => {
  return useQuery({
    queryKey: ['service', id],
    queryFn: () => serviceApi.getService(id),
  });
};

export const useCategories = () => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: () => serviceApi.getCategories(),
  });
};
```

## Performance Optimizations

### Image Optimization
- Lazy loading for service images
- Multiple image sizes for different screen densities
- Placeholder images during loading

### List Performance
- FlatList with getItemLayout for known item heights
- Pagination for large service lists
- Memoization of service cards

### Search Optimization
- Debounced search input (300ms)
- Search result caching
- Progressive search suggestions

## Accessibility Features

### Screen Reader Support
- Proper accessibility labels for all interactive elements
- Semantic heading structure
- Focus management for navigation

### Visual Accessibility
- High contrast mode support
- Scalable text support
- Color-blind friendly design

## Testing Strategy

### Component Testing
- Unit tests for all atomic components
- Integration tests for molecular components
- Snapshot tests for consistent UI

### Screen Testing
- Navigation flow testing
- API integration testing
- User interaction testing

## Implementation Phases

### Phase 1: Core Components
1. ServiceCard component
2. CategoryCard component
3. SearchBar component
4. Basic ServicesScreen

### Phase 2: Enhanced Features
1. ServiceDetailsScreen
2. FilterPanel component
3. Search functionality
4. Category browsing

### Phase 3: Advanced Features
1. Provider profiles
2. Favorites system
3. Advanced filtering
4. Performance optimizations

## Next Steps
1. Implement atomic components (ServiceCard, CategoryCard)
2. Create API integration layer
3. Build ServicesScreen with basic functionality
4. Add search and filtering capabilities
5. Implement ServiceDetailsScreen
6. Add comprehensive testing
