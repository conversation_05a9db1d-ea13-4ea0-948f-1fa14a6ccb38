#!/bin/bash
# ============================================================================
# Vierla Database Startup Script (Unix/Linux/macOS)
# ============================================================================
# This script starts the database service for the Vierla application.
# Supports PostgreSQL, MySQL, and SQLite configurations.
#
# Usage: ./scripts/development/start-database.sh [options]
# Options:
#   --type <type>     Database type (postgresql, mysql, sqlite)
#   --port <port>     Database port (default: 5432 for PostgreSQL, 3306 for MySQL)
#   --help            Show this help message
# ============================================================================

set -e  # Exit on any error

# Script configuration
SCRIPT_NAME="Vierla Database Service"
SCRIPT_VERSION="1.0.0"
LOG_FILE="logs/database-service.log"
BACKEND_DIR="code/backend"

# Default configuration
DB_TYPE="sqlite"
DB_PORT="5432"
DB_HOST="localhost"

# Colors for output
COLOR_GREEN='\033[0;32m'
COLOR_YELLOW='\033[1;33m'
COLOR_RED='\033[0;31m'
COLOR_BLUE='\033[0;34m'
COLOR_CYAN='\033[0;36m'
COLOR_RESET='\033[0m'

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${COLOR_RESET}"
}

# Error handling
error_exit() {
    local message=$1
    log "ERROR" "$message"
    print_color "$COLOR_RED" "Error: $message"
    exit 1
}

# Show help message
show_help() {
    print_color "$COLOR_BLUE" "============================================================================"
    print_color "$COLOR_BLUE" "$SCRIPT_NAME v$SCRIPT_VERSION"
    print_color "$COLOR_BLUE" "============================================================================"
    echo
    print_color "$COLOR_CYAN" "Usage: ./scripts/development/start-database.sh [options]"
    echo
    echo "Options:"
    echo "  ${COLOR_YELLOW}--type <type>${COLOR_RESET}     Database type (postgresql, mysql, sqlite)"
    echo "  ${COLOR_YELLOW}--port <port>${COLOR_RESET}     Database port (default: 5432 for PostgreSQL, 3306 for MySQL)"
    echo "  ${COLOR_YELLOW}--help${COLOR_RESET}            Show this help message"
    echo
    echo "Examples:"
    echo "  ${COLOR_CYAN}./scripts/development/start-database.sh${COLOR_RESET}"
    echo "  ${COLOR_CYAN}./scripts/development/start-database.sh --type postgresql${COLOR_RESET}"
    echo "  ${COLOR_CYAN}./scripts/development/start-database.sh --type mysql --port 3306${COLOR_RESET}"
    echo
    echo "Supported Database Types:"
    echo "  ${COLOR_YELLOW}sqlite${COLOR_RESET}       - SQLite (file-based, no service required)"
    echo "  ${COLOR_YELLOW}postgresql${COLOR_RESET}   - PostgreSQL (requires PostgreSQL installation)"
    echo "  ${COLOR_YELLOW}mysql${COLOR_RESET}        - MySQL (requires MySQL installation)"
    echo
    exit 0
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --type)
                DB_TYPE="$2"
                shift 2
                ;;
            --port)
                DB_PORT="$2"
                shift 2
                ;;
            --help)
                show_help
                ;;
            *)
                print_color "$COLOR_RED" "Unknown option: $1"
                show_help
                ;;
        esac
    done
}

# Load database configuration from .env
load_db_config() {
    if [ -f "$BACKEND_DIR/.env" ]; then
        log "INFO" "Loading database configuration from .env"
        print_color "$COLOR_YELLOW" "Loading database configuration..."
        
        # Check for SQLite setting
        if grep -q "USE_SQLITE=True" "$BACKEND_DIR/.env"; then
            DB_TYPE="sqlite"
        fi
        
        # Check DATABASE_URL for database type
        if grep -q "postgresql" "$BACKEND_DIR/.env"; then
            DB_TYPE="postgresql"
        elif grep -q "mysql" "$BACKEND_DIR/.env"; then
            DB_TYPE="mysql"
        fi
    fi
}

# Start SQLite (no service required)
start_sqlite() {
    log "INFO" "Using SQLite database (no service required)"
    print_color "$COLOR_GREEN" "============================================================================"
    print_color "$COLOR_GREEN" "SQLite Database Configuration"
    print_color "$COLOR_GREEN" "============================================================================"
    echo
    print_color "$COLOR_YELLOW" "SQLite is a file-based database and doesn't require a separate service."
    print_color "$COLOR_YELLOW" "The database file will be created automatically when you start the backend."
    echo
    print_color "$COLOR_CYAN" "Database Information:"
    echo "  Database File: ${COLOR_BLUE}code/backend/db.sqlite3${COLOR_RESET}"
    echo "  No service startup required"
    echo
    print_color "$COLOR_GREEN" "SQLite is ready! You can now start the backend server."
    echo "Run: ${COLOR_CYAN}./scripts/development/start-backend.sh${COLOR_RESET}"
    echo
}

# Start PostgreSQL service
start_postgresql() {
    log "INFO" "Starting PostgreSQL database service"
    print_color "$COLOR_GREEN" "============================================================================"
    print_color "$COLOR_GREEN" "Starting PostgreSQL Database Service"
    print_color "$COLOR_GREEN" "============================================================================"
    echo

    # Check if PostgreSQL is installed
    if ! command -v pg_ctl &> /dev/null; then
        error_exit "PostgreSQL is not installed or not in PATH.\n\nTo install PostgreSQL:\n- Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib\n- CentOS/RHEL: sudo yum install postgresql-server postgresql-contrib\n- macOS: brew install postgresql"
    fi

    # Detect the operating system and start PostgreSQL accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting PostgreSQL via Homebrew..."
            brew services start postgresql || error_exit "Failed to start PostgreSQL service"
        else
            error_exit "Homebrew not found. Please install PostgreSQL manually."
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v systemctl &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting PostgreSQL via systemctl..."
            sudo systemctl start postgresql || error_exit "Failed to start PostgreSQL service"
            sudo systemctl enable postgresql
        elif command -v service &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting PostgreSQL via service..."
            sudo service postgresql start || error_exit "Failed to start PostgreSQL service"
        else
            error_exit "Unable to start PostgreSQL. Please start it manually."
        fi
    else
        error_exit "Unsupported operating system for automatic PostgreSQL startup"
    fi

    print_color "$COLOR_GREEN" "PostgreSQL service is running!"
    echo
    print_color "$COLOR_CYAN" "Connection Information:"
    echo "  Host: ${COLOR_BLUE}$DB_HOST:$DB_PORT${COLOR_RESET}"
    echo "  Default Database: ${COLOR_BLUE}postgres${COLOR_RESET}"
    echo "  Default User: ${COLOR_BLUE}postgres${COLOR_RESET}"
    echo
    print_color "$COLOR_YELLOW" "Make sure to create the Vierla database and configure DATABASE_URL in .env"
    echo
}

# Start MySQL service
start_mysql() {
    log "INFO" "Starting MySQL database service"
    print_color "$COLOR_GREEN" "============================================================================"
    print_color "$COLOR_GREEN" "Starting MySQL Database Service"
    print_color "$COLOR_GREEN" "============================================================================"
    echo

    # Check if MySQL is installed
    if ! command -v mysql &> /dev/null; then
        error_exit "MySQL is not installed or not in PATH.\n\nTo install MySQL:\n- Ubuntu/Debian: sudo apt-get install mysql-server\n- CentOS/RHEL: sudo yum install mysql-server\n- macOS: brew install mysql"
    fi

    # Detect the operating system and start MySQL accordingly
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting MySQL via Homebrew..."
            brew services start mysql || error_exit "Failed to start MySQL service"
        else
            error_exit "Homebrew not found. Please install MySQL manually."
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v systemctl &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting MySQL via systemctl..."
            sudo systemctl start mysql || sudo systemctl start mysqld || error_exit "Failed to start MySQL service"
            sudo systemctl enable mysql || sudo systemctl enable mysqld
        elif command -v service &> /dev/null; then
            print_color "$COLOR_YELLOW" "Starting MySQL via service..."
            sudo service mysql start || sudo service mysqld start || error_exit "Failed to start MySQL service"
        else
            error_exit "Unable to start MySQL. Please start it manually."
        fi
    else
        error_exit "Unsupported operating system for automatic MySQL startup"
    fi

    print_color "$COLOR_GREEN" "MySQL service is running!"
    echo
    print_color "$COLOR_CYAN" "Connection Information:"
    echo "  Host: ${COLOR_BLUE}$DB_HOST:$DB_PORT${COLOR_RESET}"
    echo "  Default Database: ${COLOR_BLUE}mysql${COLOR_RESET}"
    echo "  Default User: ${COLOR_BLUE}root${COLOR_RESET}"
    echo
    print_color "$COLOR_YELLOW" "Make sure to create the Vierla database and configure DATABASE_URL in .env"
    echo
}

# Main database startup function
start_database() {
    print_color "$COLOR_BLUE" "============================================================================"
    print_color "$COLOR_BLUE" "$SCRIPT_NAME v$SCRIPT_VERSION"
    print_color "$COLOR_BLUE" "============================================================================"
    echo

    # Create logs directory if it doesn't exist
    mkdir -p logs

    log "INFO" "Starting database service startup sequence"

    # Load database configuration
    load_db_config

    # Set default port based on database type
    if [ "$DB_TYPE" = "mysql" ]; then
        DB_PORT="3306"
    fi

    print_color "$COLOR_CYAN" "Database Configuration:"
    echo "  Type: ${COLOR_YELLOW}$DB_TYPE${COLOR_RESET}"
    echo "  Host: ${COLOR_YELLOW}$DB_HOST${COLOR_RESET}"
    echo "  Port: ${COLOR_YELLOW}$DB_PORT${COLOR_RESET}"
    echo

    # Handle different database types
    case "$DB_TYPE" in
        sqlite)
            start_sqlite
            ;;
        postgresql)
            start_postgresql
            ;;
        mysql)
            start_mysql
            ;;
        *)
            error_exit "Unsupported database type: $DB_TYPE"
            ;;
    esac

    log "INFO" "Database startup sequence completed"
    print_color "$COLOR_GREEN" "Database service is ready! You can now start the backend server."
    echo "Run: ${COLOR_CYAN}./scripts/development/start-backend.sh${COLOR_RESET}"
    echo
}

# Make script executable and run
chmod +x "$0"
parse_args "$@"
start_database
