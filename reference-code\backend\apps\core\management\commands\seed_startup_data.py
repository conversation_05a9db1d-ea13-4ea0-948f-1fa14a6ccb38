"""
Django management command to seed startup data for <PERSON>ierla backend
Usage: python manage.py seed_startup_data [--force] [--quick]
"""

import os
import sys
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings

from apps.authentication.models import User
from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.bookings.models import Booking


class Command(BaseCommand):
    help = 'Seed comprehensive startup data for development environment'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force seeding even if data already exists',
        )
        parser.add_argument(
            '--quick',
            action='store_true',
            help='Quick seeding with minimal data',
        )
        parser.add_argument(
            '--skip-verification',
            action='store_true',
            help='Skip final verification step',
        )

    def handle(self, *args, **options):
        self.force = options['force']
        self.quick = options['quick']
        self.skip_verification = options['skip_verification']
        
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting Vierla Backend Data Seeding...')
        )
        
        try:
            # Check existing data
            if not self.check_existing_data():
                return
            
            # Run seeding process
            self.run_seeding_process()
            
            # Final summary
            self.print_final_summary()
            
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.ERROR('❌ Seeding interrupted by user')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'💥 Seeding failed: {e}')
            )
            raise CommandError(f'Seeding failed: {e}')

    def check_existing_data(self):
        """Check if data already exists"""
        self.stdout.write('\n📊 Checking existing data...')
        
        categories_count = ServiceCategory.objects.count()
        providers_count = ServiceProvider.objects.count()
        services_count = Service.objects.count()
        users_count = User.objects.count()
        bookings_count = Booking.objects.count()
        
        self.stdout.write(f'   - Service Categories: {categories_count}')
        self.stdout.write(f'   - Service Providers: {providers_count}')
        self.stdout.write(f'   - Services: {services_count}')
        self.stdout.write(f'   - Users: {users_count}')
        self.stdout.write(f'   - Bookings: {bookings_count}')
        
        has_data = any([categories_count > 0, providers_count > 0, services_count > 0])
        
        if has_data and not self.force:
            self.stdout.write(
                self.style.WARNING('\n⚠️  Existing data detected!')
            )
            self.stdout.write(
                'Use --force to seed anyway or clear database first'
            )
            return False
        elif has_data and self.force:
            self.stdout.write(
                self.style.WARNING('\n⚠️  Existing data detected but --force specified')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\n✅ Database ready for seeding')
            )
        
        return True

    def run_script(self, script_name, description):
        """Run a seeding script"""
        self.stdout.write(f'\n🔄 {description}...')
        
        script_path = Path(settings.BASE_DIR) / 'scripts' / script_name
        
        if not script_path.exists():
            self.stdout.write(
                self.style.ERROR(f'❌ Script not found: {script_path}')
            )
            return False
        
        try:
            result = subprocess.run([
                sys.executable, str(script_path)
            ], cwd=settings.BASE_DIR, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ {description} completed')
                )
                return True
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ {description} failed')
                )
                if result.stderr:
                    self.stdout.write(f'Error: {result.stderr[:500]}...')
                return False
                
        except subprocess.TimeoutExpired:
            self.stdout.write(
                self.style.ERROR(f'⏰ {description} timed out')
            )
            return False
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'💥 {description} error: {e}')
            )
            return False

    def run_seeding_process(self):
        """Run the complete seeding process"""
        if self.quick:
            self.stdout.write(
                self.style.WARNING('\n🏃 Quick seeding mode - minimal data only')
            )
            scripts = [
                ('verify_service_categories.py', 'Service Categories'),
                ('create_test_accounts.py', 'Basic Test Accounts'),
            ]
        else:
            self.stdout.write(
                self.style.SUCCESS('\n🎯 Full seeding mode - comprehensive data')
            )
            scripts = [
                ('verify_service_categories.py', 'Service Categories'),
                ('create_mock_service_providers.py', 'Service Providers'),
                ('create_service_listings.py', 'Services and Pricing'),
                ('create_mock_customers.py', 'Customer Accounts'),
                ('create_booking_history.py', 'Booking History'),
            ]
        
        success_count = 0
        for script_name, description in scripts:
            if self.run_script(script_name, description):
                success_count += 1
            else:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Continuing despite {description} failure...')
                )
        
        # Run verification unless skipped
        if not self.skip_verification:
            if self.run_script('verify_database_seeding.py', 'Data Verification'):
                success_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(f'\n🎉 Seeding completed: {success_count}/{len(scripts)} successful')
        )

    def print_final_summary(self):
        """Print final summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🎉 VIERLA BACKEND SEEDING COMPLETE')
        )
        self.stdout.write('='*60)
        
        # Get final counts
        categories_count = ServiceCategory.objects.count()
        providers_count = ServiceProvider.objects.count()
        services_count = Service.objects.count()
        users_count = User.objects.count()
        bookings_count = Booking.objects.count()
        
        self.stdout.write('\n📊 Final Database State:')
        self.stdout.write(f'   ✅ Service Categories: {categories_count}')
        self.stdout.write(f'   ✅ Service Providers: {providers_count}')
        self.stdout.write(f'   ✅ Services: {services_count}')
        self.stdout.write(f'   ✅ Users: {users_count}')
        self.stdout.write(f'   ✅ Bookings: {bookings_count}')
        
        self.stdout.write('\n🚀 Backend Ready for Development!')
        self.stdout.write('📱 Frontend can now connect to populated backend')
        
        self.stdout.write('\n📋 Quick Access:')
        self.stdout.write('   🌐 API: http://************:8000/api/')
        self.stdout.write('   📚 Docs: http://************:8000/api/docs/')
        self.stdout.write('   🔧 Admin: http://************:8000/admin/')
        
        self.stdout.write('\n🔑 Test Credentials:')
        self.stdout.write('   👤 Customer: <EMAIL> / VierlaTest123!')
        self.stdout.write('   🏢 Provider: <EMAIL> / VierlaTest123!')
        
        if not self.quick:
            self.stdout.write('\n💡 Usage Tips:')
            self.stdout.write('   - Use test accounts for frontend development')
            self.stdout.write('   - All providers have realistic services and pricing')
            self.stdout.write('   - Booking history available for testing flows')
            self.stdout.write('   - Run with --quick for faster minimal seeding')
        
        self.stdout.write(
            self.style.SUCCESS('\n✨ Happy coding with Vierla! ✨')
        )
