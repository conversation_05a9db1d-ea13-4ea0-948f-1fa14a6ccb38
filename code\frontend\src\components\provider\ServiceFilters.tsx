/**
 * Service Filters Component
 * Advanced filtering and sorting options for services
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { ServiceCategory } from '../../services/api';

export interface ServiceFilters {
  search: string;
  category: string | null;
  status: 'all' | 'active' | 'inactive';
  priceRange: {
    min: number | null;
    max: number | null;
  };
  sortBy: 'name' | 'created_at' | 'base_price' | 'booking_count';
  sortOrder: 'asc' | 'desc';
}

interface ServiceFiltersProps {
  filters: ServiceFilters;
  categories: ServiceCategory[];
  onFiltersChange: (filters: ServiceFilters) => void;
  onClearFilters: () => void;
  activeFilterCount: number;
}

export const ServiceFiltersComponent: React.FC<ServiceFiltersProps> = ({
  filters,
  categories,
  onFiltersChange,
  onClearFilters,
  activeFilterCount,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempFilters, setTempFilters] = useState<ServiceFilters>(filters);

  const statusOptions = [
    { value: 'all', label: 'All Services' },
    { value: 'active', label: 'Active Only' },
    { value: 'inactive', label: 'Inactive Only' },
  ];

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'created_at', label: 'Date Created' },
    { value: 'base_price', label: 'Price' },
    { value: 'booking_count', label: 'Popularity' },
  ];

  const applyFilters = () => {
    onFiltersChange(tempFilters);
    setModalVisible(false);
  };

  const resetFilters = () => {
    const defaultFilters: ServiceFilters = {
      search: '',
      category: null,
      status: 'all',
      priceRange: { min: null, max: null },
      sortBy: 'created_at',
      sortOrder: 'desc',
    };
    setTempFilters(defaultFilters);
    onFiltersChange(defaultFilters);
    setModalVisible(false);
  };

  const updateTempFilter = (key: keyof ServiceFilters, value: any) => {
    setTempFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const updatePriceRange = (type: 'min' | 'max', value: string) => {
    const numValue = value === '' ? null : parseFloat(value);
    setTempFilters(prev => ({
      ...prev,
      priceRange: {
        ...prev.priceRange,
        [type]: numValue,
      },
    }));
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color={colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search services..."
          value={filters.search}
          onChangeText={(text) => onFiltersChange({ ...filters, search: text })}
          placeholderTextColor={colors.textSecondary}
        />
      </View>

      {/* Filter Button */}
      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => {
          setTempFilters(filters);
          setModalVisible(true);
        }}
      >
        <Icon name="filter-list" size={20} color={colors.primary} />
        <Text style={styles.filterButtonText}>Filters</Text>
        {activeFilterCount > 0 && (
          <View style={styles.filterBadge}>
            <Text style={styles.filterBadgeText}>{activeFilterCount}</Text>
          </View>
        )}
      </TouchableOpacity>

      {/* Filter Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Icon name="close" size={24} color={colors.textPrimary} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Filter & Sort</Text>
            <TouchableOpacity onPress={resetFilters}>
              <Text style={styles.resetText}>Reset</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Category Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>Category</Text>
              <View style={styles.optionsList}>
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    tempFilters.category === null && styles.selectedOption,
                  ]}
                  onPress={() => updateTempFilter('category', null)}
                >
                  <Text style={[
                    styles.optionText,
                    tempFilters.category === null && styles.selectedOptionText,
                  ]}>
                    All Categories
                  </Text>
                  {tempFilters.category === null && (
                    <Icon name="check" size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={[
                      styles.optionItem,
                      tempFilters.category === category.id && styles.selectedOption,
                    ]}
                    onPress={() => updateTempFilter('category', category.id)}
                  >
                    <Text style={[
                      styles.optionText,
                      tempFilters.category === category.id && styles.selectedOptionText,
                    ]}>
                      {category.name}
                    </Text>
                    {tempFilters.category === category.id && (
                      <Icon name="check" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Status Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>Status</Text>
              <View style={styles.optionsList}>
                {statusOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      tempFilters.status === option.value && styles.selectedOption,
                    ]}
                    onPress={() => updateTempFilter('status', option.value)}
                  >
                    <Text style={[
                      styles.optionText,
                      tempFilters.status === option.value && styles.selectedOptionText,
                    ]}>
                      {option.label}
                    </Text>
                    {tempFilters.status === option.value && (
                      <Icon name="check" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Price Range Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>Price Range</Text>
              <View style={styles.priceRangeContainer}>
                <View style={styles.priceInputContainer}>
                  <Text style={styles.priceLabel}>Min ($)</Text>
                  <TextInput
                    style={styles.priceInput}
                    placeholder="0"
                    value={tempFilters.priceRange.min?.toString() || ''}
                    onChangeText={(text) => updatePriceRange('min', text)}
                    keyboardType="numeric"
                    placeholderTextColor={colors.textSecondary}
                  />
                </View>
                <Text style={styles.priceSeparator}>to</Text>
                <View style={styles.priceInputContainer}>
                  <Text style={styles.priceLabel}>Max ($)</Text>
                  <TextInput
                    style={styles.priceInput}
                    placeholder="1000"
                    value={tempFilters.priceRange.max?.toString() || ''}
                    onChangeText={(text) => updatePriceRange('max', text)}
                    keyboardType="numeric"
                    placeholderTextColor={colors.textSecondary}
                  />
                </View>
              </View>
            </View>

            {/* Sort Options */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>Sort By</Text>
              <View style={styles.optionsList}>
                {sortOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.optionItem,
                      tempFilters.sortBy === option.value && styles.selectedOption,
                    ]}
                    onPress={() => updateTempFilter('sortBy', option.value)}
                  >
                    <Text style={[
                      styles.optionText,
                      tempFilters.sortBy === option.value && styles.selectedOptionText,
                    ]}>
                      {option.label}
                    </Text>
                    {tempFilters.sortBy === option.value && (
                      <Icon name="check" size={20} color={colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Sort Order */}
            <View style={styles.filterSection}>
              <Text style={styles.sectionTitle}>Sort Order</Text>
              <View style={styles.sortOrderContainer}>
                <TouchableOpacity
                  style={[
                    styles.sortOrderButton,
                    tempFilters.sortOrder === 'asc' && styles.selectedSortOrder,
                  ]}
                  onPress={() => updateTempFilter('sortOrder', 'asc')}
                >
                  <Icon name="arrow-upward" size={20} color={
                    tempFilters.sortOrder === 'asc' ? colors.white : colors.primary
                  } />
                  <Text style={[
                    styles.sortOrderText,
                    tempFilters.sortOrder === 'asc' && styles.selectedSortOrderText,
                  ]}>
                    Ascending
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.sortOrderButton,
                    tempFilters.sortOrder === 'desc' && styles.selectedSortOrder,
                  ]}
                  onPress={() => updateTempFilter('sortOrder', 'desc')}
                >
                  <Icon name="arrow-downward" size={20} color={
                    tempFilters.sortOrder === 'desc' ? colors.white : colors.primary
                  } />
                  <Text style={[
                    styles.sortOrderText,
                    tempFilters.sortOrder === 'desc' && styles.selectedSortOrderText,
                  ]}>
                    Descending
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
              <Text style={styles.applyButtonText}>Apply Filters</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.md,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  filterButtonText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  filterBadge: {
    backgroundColor: colors.primary,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.xs,
  },
  filterBadgeText: {
    ...typography.caption,
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    ...typography.h3,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  resetText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  filterSection: {
    marginVertical: spacing.lg,
  },
  sectionTitle: {
    ...typography.h4,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  optionsList: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  selectedOption: {
    backgroundColor: colors.primaryLight,
  },
  optionText: {
    ...typography.body,
    color: colors.textPrimary,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: '600',
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceInputContainer: {
    flex: 1,
  },
  priceLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.xs,
  },
  priceInput: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
  },
  priceSeparator: {
    ...typography.body,
    color: colors.textSecondary,
    marginHorizontal: spacing.md,
  },
  sortOrderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sortOrderButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.backgroundSecondary,
    paddingVertical: spacing.md,
    borderRadius: 8,
    marginHorizontal: spacing.xs,
  },
  selectedSortOrder: {
    backgroundColor: colors.primary,
  },
  sortOrderText: {
    ...typography.body,
    color: colors.primary,
    marginLeft: spacing.xs,
  },
  selectedSortOrderText: {
    color: colors.white,
  },
  modalFooter: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  applyButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
