"""
Test account security settings for <PERSON><PERSON><PERSON> backend
Configures security measures for test account management
"""
import os

# Test account domain for email validation
TEST_ACCOUNT_DOMAIN = 'test.com'

# Environment-specific test account settings
ENVIRONMENT = os.environ.get('ENVIRONMENT', 'development')

# Test account security settings
TEST_ACCOUNT_SECURITY = {
    # Automatically cleanup test accounts in production (DANGEROUS - use with caution)
    'AUTO_CLEANUP_IN_PRODUCTION': False,
    
    # Maximum number of test accounts allowed
    'MAX_TEST_ACCOUNTS': 1000,
    
    # Test account password requirements
    'PASSWORD_REQUIREMENTS': {
        'min_length': 8,
        'require_uppercase': True,
        'require_lowercase': True,
        'require_numbers': True,
        'require_special_chars': True,
    },
    
    # Test account email validation
    'EMAIL_VALIDATION': {
        'allowed_domains': ['test.com', 'example.com', 'localhost'],
        'require_test_domain': True,
    },
    
    # Test account cleanup settings
    'CLEANUP_SETTINGS': {
        'auto_cleanup_on_startup': False,
        'cleanup_older_than_days': 30,
        'preserve_recent_accounts': True,
    }
}

# Production safety settings
if ENVIRONMENT.lower() in ['production', 'prod', 'live']:
    # In production, disable all test account creation
    TEST_ACCOUNT_SECURITY['AUTO_CLEANUP_IN_PRODUCTION'] = True
    TEST_ACCOUNT_SECURITY['MAX_TEST_ACCOUNTS'] = 0
    
    # Enable automatic cleanup on startup in production
    TEST_ACCOUNT_SECURITY['CLEANUP_SETTINGS']['auto_cleanup_on_startup'] = True

# Development settings
elif ENVIRONMENT.lower() in ['development', 'dev', 'local']:
    # In development, allow more test accounts
    TEST_ACCOUNT_SECURITY['MAX_TEST_ACCOUNTS'] = 1000
    
    # Relaxed password requirements for development
    TEST_ACCOUNT_SECURITY['PASSWORD_REQUIREMENTS']['require_special_chars'] = False

# Testing settings
elif ENVIRONMENT.lower() in ['testing', 'test']:
    # In testing, allow unlimited test accounts
    TEST_ACCOUNT_SECURITY['MAX_TEST_ACCOUNTS'] = None
    
    # Enable automatic cleanup after tests
    TEST_ACCOUNT_SECURITY['CLEANUP_SETTINGS']['auto_cleanup_on_startup'] = True

# Logging configuration for test account security
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'security': {
            'format': '[{levelname}] {asctime} - TEST_ACCOUNT_SECURITY: {message}',
            'style': '{',
        },
    },
    'handlers': {
        'security_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/test_account_security.log',
            'formatter': 'security',
        },
        'security_console': {
            'level': 'WARNING',
            'class': 'logging.StreamHandler',
            'formatter': 'security',
        },
    },
    'loggers': {
        'test_account_security': {
            'handlers': ['security_file', 'security_console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Security middleware configuration
TEST_ACCOUNT_MIDDLEWARE = [
    'catalog.security.TestAccountMiddleware',
]

# Authentication backends for test accounts
TEST_ACCOUNT_AUTHENTICATION_BACKENDS = [
    'catalog.security.TestAccountAuthenticationBackend',
]

# API rate limiting for test accounts
TEST_ACCOUNT_RATE_LIMITS = {
    'login_attempts': '10/hour',
    'account_creation': '5/hour',
    'password_reset': '3/hour',
}

# Test account monitoring settings
TEST_ACCOUNT_MONITORING = {
    'enable_audit_logging': True,
    'alert_on_production_test_accounts': True,
    'daily_security_check': True,
    'email_alerts': {
        'enabled': False,
        'recipients': ['<EMAIL>'],
        'alert_threshold': 1,  # Alert if any test accounts found in production
    }
}

# Export settings for use in main settings
def get_test_account_settings():
    """Get test account settings for current environment"""
    return {
        'TEST_ACCOUNT_DOMAIN': TEST_ACCOUNT_DOMAIN,
        'TEST_ACCOUNT_SECURITY': TEST_ACCOUNT_SECURITY,
        'TEST_ACCOUNT_MIDDLEWARE': TEST_ACCOUNT_MIDDLEWARE,
        'TEST_ACCOUNT_AUTHENTICATION_BACKENDS': TEST_ACCOUNT_AUTHENTICATION_BACKENDS,
        'TEST_ACCOUNT_RATE_LIMITS': TEST_ACCOUNT_RATE_LIMITS,
        'TEST_ACCOUNT_MONITORING': TEST_ACCOUNT_MONITORING,
    }
