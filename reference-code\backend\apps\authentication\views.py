"""
Authentication views for Vierla Beauty Services Marketplace
Enhanced Django REST Framework views with JWT authentication
"""
from rest_framework import status, permissions, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.openapi import OpenApiTypes
import logging

from .models import User, UserProfile
from .serializers import (
    UserSerializer, UserRegistrationSerializer, UserLoginSerializer,
    PasswordChangeSerializer, PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer, EmailVerificationSerializer,
    UserProfileUpdateSerializer, UserProfileSerializer
)

logger = logging.getLogger(__name__)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token obtain view
    Enhanced with user data and device token handling
    """
    serializer_class = UserLoginSerializer

    @extend_schema(
        summary="User Login",
        description="Authenticate user and obtain JWT tokens",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "access": {"type": "string"},
                    "refresh": {"type": "string"},
                    "user": UserSerializer
                }
            }
        }
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(
            data=request.data, context={'request': request})

        try:
            serializer.is_valid(raise_exception=True)
        except ValidationError as e:
            logger.warning(f"Login attempt failed: {e}")

            # Check if this is an account lockout error
            error_detail = str(e.detail)
            if 'temporarily locked' in error_detail.lower():
                return Response(
                    {
                        'detail': _('Account is temporarily locked due to multiple failed login attempts. Please try again in 30 minutes or contact support.'),
                        'error_code': 'ACCOUNT_LOCKED',
                        'retry_after': 1800  # 30 minutes in seconds
                    },
                    status=status.HTTP_423_LOCKED  # Use 423 instead of 429 for account lockout
                )

            return Response(
                {'detail': _('Invalid credentials')},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.warning(f"Login attempt failed: {e}")
            return Response(
                {'detail': _('Invalid credentials')},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = serializer.validated_data['user']

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token

        # Customize token payload if needed
        access_token['role'] = user.role
        access_token['is_verified'] = user.is_verified

        # Serialize user data
        user_serializer = UserSerializer(user, context={'request': request})

        response_data = {
            'access': str(access_token),
            'refresh': str(refresh),
            'user': user_serializer.data
        }

        logger.info(f"User {user.email} logged in successfully")

        return Response(response_data, status=status.HTTP_200_OK)


class UserRegistrationView(generics.CreateAPIView):
    """
    User registration view
    Creates new user account with email verification
    """
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="User Registration",
        description="Register a new user account",
        responses={
            201: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"},
                    "user": UserSerializer
                }
            }
        }
    )
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()

        # Serialize user data
        user_serializer = UserSerializer(user, context={'request': request})

        logger.info(f"New user registered: {user.email}")

        return Response({
            'message': _('Registration successful. Please check your email to verify your account.'),
            'user': user_serializer.data
        }, status=status.HTTP_201_CREATED)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    User profile view
    Retrieve and update authenticated user's profile
    """
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    @extend_schema(
        summary="Get User Profile",
        description="Retrieve authenticated user's profile information"
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    @extend_schema(
        summary="Update User Profile",
        description="Update authenticated user's profile information"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class UserProfileDetailView(generics.RetrieveUpdateAPIView):
    """
    User profile detail view
    Manage extended profile information
    """
    serializer_class = UserProfileUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        profile, created = UserProfile.objects.get_or_create(
            user=self.request.user)
        return profile

    @extend_schema(
        summary="Get User Profile Details",
        description="Retrieve authenticated user's extended profile information"
    )
    def get(self, request, *args, **kwargs):
        profile = self.get_object()
        serializer = UserProfileSerializer(
            profile, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        summary="Update User Profile Details",
        description="Update authenticated user's extended profile information"
    )
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)


class PasswordChangeView(APIView):
    """
    Password change view
    Allows authenticated users to change their password
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Change Password",
        description="Change authenticated user's password",
        request=PasswordChangeSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        logger.info(f"Password changed for user: {request.user.email}")

        return Response({
            'message': _('Password changed successfully.')
        }, status=status.HTTP_200_OK)


class PasswordResetRequestView(APIView):
    """
    Password reset request view
    Handles forgot password functionality
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Request Password Reset",
        description="Request password reset email",
        request=PasswordResetRequestSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        logger.info(
            f"Password reset requested for email: {serializer.validated_data['email']}")

        return Response({
            'message': _('If an account with this email exists, you will receive password reset instructions.')
        }, status=status.HTTP_200_OK)


class PasswordResetConfirmView(APIView):
    """
    Password reset confirmation view
    Handles password reset with token
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Confirm Password Reset",
        description="Reset password using token",
        request=PasswordResetConfirmSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        serializer = PasswordResetConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        logger.info(f"Password reset completed for user: {user.email}")

        return Response({
            'message': _('Password reset successfully.')
        }, status=status.HTTP_200_OK)


class EmailVerificationView(APIView):
    """
    Email verification view
    Handles email verification with token
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Verify Email",
        description="Verify email address using token",
        request=EmailVerificationSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"},
                    "user": UserSerializer
                }
            }
        }
    )
    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Serialize user data
        user_serializer = UserSerializer(user, context={'request': request})

        logger.info(f"Email verified for user: {user.email}")

        return Response({
            'message': _('Email verified successfully.'),
            'user': user_serializer.data
        }, status=status.HTTP_200_OK)


class LogoutView(APIView):
    """
    Logout view
    Blacklists the refresh token
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="User Logout",
        description="Logout user and blacklist refresh token",
        request={
            "type": "object",
            "properties": {
                "refresh": {"type": "string"}
            }
        },
        responses={
            200: {
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        try:
            refresh_token = request.data.get("refresh")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            # Clear device token
            user = request.user
            user.device_token = ""
            user.save(update_fields=['device_token'])

            logger.info(f"User {user.email} logged out successfully")

            return Response({
                'message': _('Logged out successfully.')
            }, status=status.HTTP_200_OK)

        except TokenError:
            return Response({
                'message': _('Invalid token.')
            }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
@extend_schema(
    summary="Resend Email Verification",
    description="Resend email verification token",
    request={
        "type": "object",
        "properties": {
            "email": {"type": "string", "format": "email"}
        }
    },
    responses={
        200: {
            "type": "object",
            "properties": {
                "message": {"type": "string"}
            }
        }
    }
)
def resend_verification_email(request):
    """
    Resend email verification token
    """
    email = request.data.get('email', '').lower()

    if not email:
        return Response({
            'error': _('Email is required.')
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(email=email, is_active=True)

        if user.is_verified:
            return Response({
                'message': _('Email is already verified.')
            }, status=status.HTTP_200_OK)

        # Invalidate existing tokens
        from .models import EmailVerificationToken
        EmailVerificationToken.objects.filter(
            user=user, is_used=False).update(is_used=True)

        # Create new token
        import uuid
        from datetime import timedelta
        from django.utils import timezone as django_timezone

        token = str(uuid.uuid4())
        expires_at = django_timezone.now() + timedelta(hours=24)

        EmailVerificationToken.objects.create(
            user=user,
            token=token,
            expires_at=expires_at
        )

        # TODO: Send verification email
        # This would be implemented with Celery task

        logger.info(f"Verification email resent for user: {user.email}")

        return Response({
            'message': _('Verification email sent.')
        }, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        # Don't reveal if email exists for security
        return Response({
            'message': _('If an account with this email exists, you will receive verification instructions.')
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@extend_schema(
    summary="Check Authentication Status",
    description="Check if user is authenticated and get user info",
    responses={
        200: UserSerializer
    }
)
def auth_status(request):
    """
    Check authentication status and return user info
    """
    user_serializer = UserSerializer(
        request.user, context={'request': request})
    return Response(user_serializer.data, status=status.HTTP_200_OK)


class SocialAuthView(APIView):
    """
    Social authentication view
    Handles Google and Apple Sign-In authentication
    """
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="Social Authentication",
        description="Authenticate with Google or Apple Sign-In",
        request={
            "type": "object",
            "properties": {
                "provider": {"type": "string", "enum": ["google", "apple"]},
                "identity_token": {"type": "string"},
                "authorization_code": {"type": "string"},
                "user_id": {"type": "string"},
                "email": {"type": "string", "format": "email"},
                "first_name": {"type": "string"},
                "last_name": {"type": "string"},
            },
            "required": ["provider", "identity_token"]
        },
        responses={
            200: {
                "type": "object",
                "properties": {
                    "access": {"type": "string"},
                    "refresh": {"type": "string"},
                    "user": UserSerializer
                }
            }
        }
    )
    def post(self, request):
        """
        Handle social authentication
        """
        provider = request.data.get('provider', '').lower()
        identity_token = request.data.get('identity_token', '')
        email = request.data.get('email', '').lower()
        first_name = request.data.get('first_name', '')
        last_name = request.data.get('last_name', '')
        user_id = request.data.get('user_id', '')

        if not provider or provider not in ['google', 'apple']:
            return Response({
                'error': _('Invalid provider. Must be "google" or "apple".')
            }, status=status.HTTP_400_BAD_REQUEST)

        if not identity_token:
            return Response({
                'error': _('Identity token is required.')
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # For now, we'll create/get user based on email
            # In production, you would verify the identity_token with the provider

            if not email:
                # Generate a placeholder email if not provided
                email = f"{provider}.user.{user_id}@{provider}.com"

            # Try to get existing user
            user = None
            try:
                user = User.objects.get(email=email)
                logger.info(f"Existing {provider} user found: {email}")
            except User.DoesNotExist:
                # Create new user
                user = User.objects.create_user(
                    email=email,
                    first_name=first_name or f"{provider.title()} User",
                    last_name=last_name or "",
                    is_verified=True,  # Social auth users are pre-verified
                    role='customer'  # Default role
                )
                logger.info(f"New {provider} user created: {email}")

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token

            # Customize token payload
            access_token['role'] = user.role
            access_token['is_verified'] = user.is_verified

            # Serialize user data
            user_serializer = UserSerializer(
                user, context={'request': request})

            response_data = {
                'access': str(access_token),
                'refresh': str(refresh),
                'user': user_serializer.data
            }

            logger.info(
                f"{provider.title()} Sign-In successful for user: {user.email}")

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"{provider.title()} Sign-In error: {str(e)}")
            return Response({
                'error': _('Social authentication failed.')
            }, status=status.HTTP_400_BAD_REQUEST)


class RoleSwitchView(generics.GenericAPIView):
    """
    Role switching view for dual-role users
    Allows switching between customer and service provider roles
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Switch User Role",
        description="Switch between customer and service provider roles",
        request={
            "type": "object",
            "properties": {
                "target_role": {
                    "type": "string",
                    "enum": ["customer", "service_provider"],
                    "description": "Target role to switch to"
                }
            },
            "required": ["target_role"]
        },
        responses={
            200: {
                "type": "object",
                "properties": {
                    "access": {"type": "string"},
                    "refresh": {"type": "string"},
                    "user": UserSerializer,
                    "message": {"type": "string"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "error": {"type": "string"}
                }
            }
        }
    )
    def post(self, request):
        """
        Handle role switching for authenticated users
        """
        target_role = request.data.get('target_role', '').lower()
        current_user = request.user

        # Validate target role
        if target_role not in ['customer', 'service_provider']:
            return Response({
                'error': _('Invalid role. Must be "customer" or "service_provider".')
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if user is trying to switch to the same role
        if current_user.role == target_role:
            return Response({
                'error': _('You are already in the requested role.')
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Update user role
            current_user.role = target_role
            current_user.save(update_fields=['role'])

            # Generate new JWT tokens with updated role
            refresh = RefreshToken.for_user(current_user)
            access_token = refresh.access_token

            # Customize token payload with new role
            access_token['role'] = current_user.role
            access_token['is_verified'] = current_user.is_verified

            # Serialize updated user data
            user_serializer = UserSerializer(current_user, context={'request': request})

            response_data = {
                'access': str(access_token),
                'refresh': str(refresh),
                'user': user_serializer.data,
                'message': f'Successfully switched to {target_role} role.'
            }

            logger.info(f"User {current_user.email} switched to {target_role} role")

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Role switch error for user {current_user.email}: {str(e)}")
            return Response({
                'error': _('Role switching failed. Please try again.')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AvailableRolesView(generics.GenericAPIView):
    """
    Get available roles for the current user
    """
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="Get Available Roles",
        description="Get list of roles available for the current user",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "current_role": {"type": "string"},
                    "available_roles": {
                        "type": "array",
                        "items": {"type": "string"}
                    },
                    "can_switch": {"type": "boolean"}
                }
            }
        }
    )
    def get(self, request):
        """
        Return available roles for the current user
        """
        current_user = request.user
        current_role = current_user.role

        # Define available roles based on current role
        role_transitions = {
            'customer': ['service_provider'],
            'service_provider': ['customer'],
            'admin': ['customer', 'service_provider']
        }

        available_roles = role_transitions.get(current_role, [])

        response_data = {
            'current_role': current_role,
            'available_roles': available_roles,
            'can_switch': len(available_roles) > 0
        }

        return Response(response_data, status=status.HTTP_200_OK)
