#!/usr/bin/env python3
"""
Frontend-Backend Integration Testing Script
Tests all major API endpoints and authentication flows
"""

import requests
import json
import sys

BASE_URL = 'http://127.0.0.1:8000/api'

def test_authentication():
    """Test authentication endpoints"""
    print("🔐 Testing Authentication...")
    
    # Test login
    login_data = {
        'email': '<EMAIL>',
        'password': 'testpass123',
        'remember_me': True
    }
    
    try:
        response = requests.post(f'{BASE_URL}/auth/login/', json=login_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Login successful - User: {result['user']['first_name']} {result['user']['last_name']}")
            return result['access']
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_catalog_apis():
    """Test catalog endpoints"""
    print("\n📋 Testing Catalog APIs...")
    
    # Test categories
    try:
        response = requests.get(f'{BASE_URL}/catalog/categories/')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Categories: {data['count']} categories loaded")
        else:
            print(f"❌ Categories failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Categories error: {e}")
    
    # Test providers
    try:
        response = requests.get(f'{BASE_URL}/catalog/providers/')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Providers: {data['count']} providers loaded")
        else:
            print(f"❌ Providers failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Providers error: {e}")

def test_booking_apis(token):
    """Test booking endpoints with authentication"""
    print("\n📅 Testing Booking APIs...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test bookings list (should require auth)
    try:
        response = requests.get(f'{BASE_URL}/bookings/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Bookings: {data['count']} bookings loaded")
        else:
            print(f"❌ Bookings failed: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Bookings error: {e}")

def test_error_handling():
    """Test error handling scenarios"""
    print("\n⚠️ Testing Error Handling...")
    
    # Test invalid login
    try:
        response = requests.post(f'{BASE_URL}/auth/login/', json={
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        if response.status_code == 401 or response.status_code == 400:
            print("✅ Invalid login properly rejected")
        else:
            print(f"❌ Invalid login handling failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
    
    # Test unauthorized access
    try:
        response = requests.get(f'{BASE_URL}/bookings/')
        if response.status_code == 401:
            print("✅ Unauthorized access properly blocked")
        else:
            print(f"❌ Unauthorized access handling failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Unauthorized test failed: {e}")

def main():
    """Run all integration tests"""
    print("🚀 Starting Frontend-Backend Integration Tests\n")
    
    # Test authentication
    token = test_authentication()
    
    # Test catalog APIs
    test_catalog_apis()
    
    # Test booking APIs (if we have a token)
    if token:
        test_booking_apis(token)
    
    # Test error handling
    test_error_handling()
    
    print("\n✅ Integration testing completed!")

if __name__ == '__main__':
    main()
