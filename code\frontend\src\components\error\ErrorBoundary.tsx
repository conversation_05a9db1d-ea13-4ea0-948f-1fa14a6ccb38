/**
 * ErrorBoundary Component - React Error Boundary for Vierla Application
 * 
 * This component catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of crashing the app.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { Component, ReactNode } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Text } from '../Text';
import { Button } from '../Button';
import { ErrorDisplay } from './ErrorDisplay';
import {
  AppError,
  ErrorType,
  ErrorSeverity,
  createAppError,
} from '../../utils/errorTypes';
import {
  logError,
  provideErrorHaptics,
} from '../../utils/errorUtils';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: AppError, retry: () => void) => ReactNode;
  onError?: (error: AppError, errorInfo: React.ErrorInfo) => void;
  enableRetry?: boolean;
  enableReporting?: boolean;
  testID?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorInfo: React.ErrorInfo | null;
}

/**
 * Error Boundary Component
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Create AppError from the caught error
    const appError = createAppError(
      error,
      ErrorType.CLIENT,
      ErrorSeverity.HIGH,
      {
        component: 'ErrorBoundary',
        action: 'render',
        timestamp: Date.now(),
      }
    );

    return {
      hasError: true,
      error: appError,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const appError = createAppError(
      error,
      ErrorType.CLIENT,
      ErrorSeverity.HIGH,
      {
        component: 'ErrorBoundary',
        action: 'componentDidCatch',
        timestamp: Date.now(),
        metadata: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true,
        },
      }
    );

    // Log the error
    logError(appError);

    // Provide haptic feedback
    provideErrorHaptics(appError.severity);

    // Update state with error info
    this.setState({
      error: appError,
      errorInfo,
    });

    // Call custom error handler if provided
    this.props.onError?.(appError, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  handleReportIssue = () => {
    // TODO: Implement error reporting to external service
    console.log('Report issue requested', {
      error: this.state.error,
      errorInfo: this.state.errorInfo,
    });
  };

  render() {
    const { children, fallback, enableRetry = true, enableReporting = true, testID } = this.props;
    const { hasError, error } = this.state;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.handleRetry);
      }

      // Default error UI
      return (
        <View style={styles.container} testID={testID}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            <ErrorDisplay
              error={error}
              title="Something went wrong"
              description="An unexpected error occurred. You can try refreshing the screen or report this issue to our team."
              severity={ErrorSeverity.HIGH}
              variant="modal"
              showIcon={true}
              testID="error-boundary-display"
            />

            <View style={styles.actions}>
              {enableRetry && (
                <Button
                  title="Try Again"
                  onPress={this.handleRetry}
                  style={styles.retryButton}
                  testID="error-boundary-retry"
                />
              )}

              {enableReporting && (
                <Button
                  title="Report Issue"
                  onPress={this.handleReportIssue}
                  variant="outline"
                  style={styles.reportButton}
                  testID="error-boundary-report"
                />
              )}
            </View>

            {__DEV__ && (
              <View style={styles.debugInfo}>
                <Text style={styles.debugTitle}>Debug Information</Text>
                <Text style={styles.debugText}>
                  Error: {error.message}
                </Text>
                {error.originalError?.stack && (
                  <Text style={styles.debugText}>
                    Stack: {error.originalError.stack}
                  </Text>
                )}
                {this.state.errorInfo?.componentStack && (
                  <Text style={styles.debugText}>
                    Component Stack: {this.state.errorInfo.componentStack}
                  </Text>
                )}
              </View>
            )}
          </ScrollView>
        </View>
      );
    }

    return children;
  }
}

/**
 * Higher-order component for wrapping components with error boundary
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
};

/**
 * Hook for manually triggering error boundary
 */
export const useErrorBoundary = () => {
  const [, setState] = React.useState();

  return React.useCallback((error: Error) => {
    setState(() => {
      throw error;
    });
  }, []);
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  actions: {
    marginTop: 24,
    gap: 12,
  },
  retryButton: {
    backgroundColor: '#3B82F6',
  },
  reportButton: {
    borderColor: '#6B7280',
  },
  debugInfo: {
    marginTop: 32,
    padding: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    color: '#6B7280',
    fontFamily: 'monospace',
    marginBottom: 4,
    lineHeight: 16,
  },
});
