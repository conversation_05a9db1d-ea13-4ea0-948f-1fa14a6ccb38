"""
Comprehensive test suite for Django ALLOWED_HOSTS configuration
Tests for the ad-hoc request: Fix HTTP_HOST Header Error

This test suite ensures that the ALLOWED_HOSTS setting properly handles
requests from mobile devices and various network configurations, specifically
addressing the 'Invalid HTTP_HOST header: ************:8000' error.
"""
import pytest
import os
import django
from django.conf import settings
from django.test import TestCase, override_settings
from django.core.exceptions import DisallowedHost
from django.http import HttpRequest
from django.test.client import RequestFactory
from django.test import Client

# Configure Django settings for testing
if not settings.configured:
    settings.configure(
        DEBUG=True,
        SECRET_KEY='test-secret-key-for-allowed-hosts-testing',
        ALLOWED_HOSTS=['localhost', '127.0.0.1', '0.0.0.0', 'testserver', '************', '********'],
        USE_TZ=True,
        DATABASES={
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': ':memory:',
            }
        },
        INSTALLED_APPS=[
            'django.contrib.contenttypes',
            'django.contrib.auth',
        ],
        ROOT_URLCONF=[],
    )
    django.setup()


class ComprehensiveAllowedHostsTest(TestCase):
    """Comprehensive test suite for ALLOWED_HOSTS configuration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.client = Client()
    
    def test_required_hosts_in_allowed_hosts(self):
        """Test that all required hosts are present in ALLOWED_HOSTS"""
        required_hosts = [
            'localhost',
            '127.0.0.1', 
            '0.0.0.0',
            'testserver',
            '************',  # The specific IP from the ad-hoc request
            '********'       # Android emulator IP
        ]
        
        for host in required_hosts:
            with self.subTest(host=host):
                self.assertIn(host, settings.ALLOWED_HOSTS, 
                            f"Host '{host}' should be in ALLOWED_HOSTS")
    
    def test_mobile_device_ip_requests(self):
        """Test HTTP requests from mobile device IP (************)"""
        test_cases = [
            '************:8000',
            '************:3000',
            '************:8080',
            '************',  # Without port
        ]
        
        for host in test_cases:
            with self.subTest(host=host):
                request = self.factory.get('/api/', HTTP_HOST=host)
                try:
                    actual_host = request.get_host()
                    self.assertEqual(actual_host, host)
                except DisallowedHost:
                    self.fail(f"Request from {host} should be allowed but was rejected")
    
    def test_localhost_variations(self):
        """Test various localhost configurations"""
        localhost_variations = [
            'localhost:8000',
            'localhost:3000',
            'localhost',
            '127.0.0.1:8000',
            '127.0.0.1:3000',
            '127.0.0.1',
        ]
        
        for host in localhost_variations:
            with self.subTest(host=host):
                request = self.factory.get('/api/', HTTP_HOST=host)
                try:
                    actual_host = request.get_host()
                    self.assertEqual(actual_host, host)
                except DisallowedHost:
                    self.fail(f"Request from {host} should be allowed but was rejected")
    
    def test_android_emulator_requests(self):
        """Test requests from Android emulator (********)"""
        emulator_hosts = [
            '********:8000',
            '********:3000',
            '********',
        ]
        
        for host in emulator_hosts:
            with self.subTest(host=host):
                request = self.factory.get('/api/', HTTP_HOST=host)
                try:
                    actual_host = request.get_host()
                    self.assertEqual(actual_host, host)
                except DisallowedHost:
                    self.fail(f"Request from {host} should be allowed but was rejected")
    
    def test_disallowed_hosts_rejected(self):
        """Test that requests from non-allowed hosts are properly rejected"""
        malicious_hosts = [
            'malicious-host.com',
            'evil.example.com',
            '*************',  # Different network IP
            '********',       # Different internal IP
            'attacker.net:8000',
        ]
        
        for host in malicious_hosts:
            with self.subTest(host=host):
                request = self.factory.get('/api/', HTTP_HOST=host)
                with self.assertRaises(DisallowedHost, 
                                     msg=f"Request from {host} should be rejected"):
                    request.get_host()
    
    def test_case_sensitivity(self):
        """Test that host matching is case-insensitive where appropriate"""
        # Note: IP addresses should be exact, but hostnames might have case variations
        request = self.factory.get('/api/', HTTP_HOST='LOCALHOST:8000')
        # This might raise DisallowedHost depending on Django's implementation
        # The test documents the expected behavior
        try:
            host = request.get_host()
            # If it passes, document that case variations are allowed
            self.assertEqual(host, 'LOCALHOST:8000')
        except DisallowedHost:
            # If it fails, document that exact case matching is required
            pass  # This is acceptable behavior
    
    def test_port_variations(self):
        """Test that different ports work with allowed hosts"""
        base_hosts = ['localhost', '127.0.0.1', '************', '********']
        test_ports = ['8000', '3000', '8080', '9000']
        
        for base_host in base_hosts:
            for port in test_ports:
                host_with_port = f"{base_host}:{port}"
                with self.subTest(host=host_with_port):
                    request = self.factory.get('/api/', HTTP_HOST=host_with_port)
                    try:
                        actual_host = request.get_host()
                        self.assertEqual(actual_host, host_with_port)
                    except DisallowedHost:
                        self.fail(f"Request from {host_with_port} should be allowed")
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions"""
        edge_cases = [
            ('0.0.0.0:8000', True),   # Should be allowed
            ('testserver', True),      # Should be allowed
            ('', False),              # Empty host should be rejected
        ]
        
        for host, should_be_allowed in edge_cases:
            with self.subTest(host=host, should_be_allowed=should_be_allowed):
                if host:  # Skip empty host test as it might cause other issues
                    request = self.factory.get('/api/', HTTP_HOST=host)
                    if should_be_allowed:
                        try:
                            actual_host = request.get_host()
                            self.assertEqual(actual_host, host)
                        except DisallowedHost:
                            self.fail(f"Request from {host} should be allowed")
                    else:
                        with self.assertRaises(DisallowedHost):
                            request.get_host()


class AllowedHostsConfigurationTest(TestCase):
    """Test the actual Django settings configuration"""
    
    def test_settings_configuration(self):
        """Test that ALLOWED_HOSTS is properly configured in settings"""
        self.assertIsInstance(settings.ALLOWED_HOSTS, list)
        self.assertGreater(len(settings.ALLOWED_HOSTS), 0, 
                          "ALLOWED_HOSTS should not be empty")
    
    def test_production_safety(self):
        """Test that ALLOWED_HOSTS doesn't contain wildcard in production-like settings"""
        # This test ensures we don't accidentally allow all hosts
        self.assertNotIn('*', settings.ALLOWED_HOSTS, 
                        "Wildcard '*' should not be in ALLOWED_HOSTS for security")
    
    def test_specific_ip_present(self):
        """Test that the specific IP from the ad-hoc request is present"""
        self.assertIn('************', settings.ALLOWED_HOSTS,
                     "The IP ************ from the ad-hoc request must be in ALLOWED_HOSTS")


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
