/**
 * Enhanced Add Service Screen
 * Multi-step service creation with validation, auto-save, and progress indicators
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  BackHandler,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';

import { colors } from '../../theme';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { MultiStepServiceForm } from '../../components/provider/MultiStepServiceForm';
import { providerServiceAPI, ServiceCreateData, ServiceCategory } from '../../services/api';

export const EnhancedAddServiceScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  // Handle hardware back button
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Let the MultiStepServiceForm handle the back press
        return false;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [])
  );

  const fetchCategories = async () => {
    try {
      const response = await providerServiceAPI.getCategories();
      setCategories(response.data || response.results || response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      Alert.alert(
        'Error',
        'Failed to load service categories. Please check your connection and try again.',
        [
          { text: 'Retry', onPress: fetchCategories },
          { text: 'Cancel', onPress: () => navigation.goBack() },
        ]
      );
    } finally {
      setCategoriesLoading(false);
    }
  };

  const handleSubmit = async (data: ServiceCreateData) => {
    setLoading(true);
    try {
      const response = await providerServiceAPI.createService(data);
      
      Alert.alert(
        'Success!',
        'Your service has been created successfully and is now available to customers.',
        [
          {
            text: 'View Service',
            onPress: () => {
              navigation.navigate('EditService' as never, { 
                serviceId: response.data?.id || response.id 
              } as never);
            },
          },
          {
            text: 'Create Another',
            onPress: () => {
              // Reset the form by navigating back and forth
              navigation.goBack();
              setTimeout(() => {
                navigation.navigate('EnhancedAddService' as never);
              }, 100);
            },
          },
          {
            text: 'Done',
            style: 'default',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Failed to create service:', error);
      
      // Handle different types of errors
      if (error.response?.status === 400) {
        const errorData = error.response.data;
        let errorMessage = 'Please check the following errors:\n\n';
        
        if (typeof errorData === 'object') {
          Object.keys(errorData).forEach(field => {
            const fieldErrors = Array.isArray(errorData[field]) 
              ? errorData[field] 
              : [errorData[field]];
            errorMessage += `• ${field}: ${fieldErrors.join(', ')}\n`;
          });
        } else {
          errorMessage = errorData.message || 'Please check your input and try again.';
        }
        
        Alert.alert('Validation Error', errorMessage);
      } else if (error.response?.status === 403) {
        Alert.alert(
          'Service Limit Reached',
          'You have reached your service limit. Please verify your account or upgrade your plan to add more services.',
          [
            { text: 'OK' },
            { text: 'Upgrade Plan', onPress: () => {
              // Navigate to upgrade screen if available
              console.log('Navigate to upgrade plan');
            }},
          ]
        );
      } else if (error.response?.status === 401) {
        Alert.alert(
          'Authentication Error',
          'Your session has expired. Please log in again.',
          [
            { text: 'OK', onPress: () => {
              // Navigate to login screen
              navigation.reset({
                index: 0,
                routes: [{ name: 'Auth' as never }],
              });
            }},
          ]
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to create service. Please check your connection and try again.',
          [
            { text: 'Retry', onPress: () => handleSubmit(data) },
            { text: 'Cancel' },
          ]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // The MultiStepServiceForm will handle the cancel confirmation
    navigation.goBack();
  };

  if (categoriesLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  if (categories.length === 0) {
    // Show alert and navigate back
    React.useEffect(() => {
      Alert.alert(
        'No Categories Available',
        'No service categories are available. Please contact support.',
        [
          { text: 'Retry', onPress: fetchCategories },
          { text: 'Go Back', onPress: () => navigation.goBack() },
        ]
      );
    }, []);

    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          {/* Empty container while alert is shown */}
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <MultiStepServiceForm
          categories={categories}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
          submitButtonText="Create Service"
          isEditing={false}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
