"""
Custom permissions for Service Catalog
Role-based access control for mobile-first architecture
"""

from rest_framework import permissions
from rest_framework.permissions import BasePermission
from django.contrib.auth import get_user_model

User = get_user_model()


class IsOwnerOrReadOnly(BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.user == request.user


class IsServiceProviderOrReadOnly(BasePermission):
    """
    Permission that allows service providers to create/edit their content,
    and read-only access for others.
    """

    def has_permission(self, request, view):
        # Allow read access to authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated

        # Allow write access only to service providers
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        # Read permissions for authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated

        # Write permissions only for the owner
        if hasattr(obj, 'user'):
            return obj.user == request.user
        elif hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        
        return False


class IsProviderOwnerOrReadOnly(BasePermission):
    """
    Permission for ServiceProvider objects - only the owner can modify
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        # For POST (create), allow service providers to attempt creation
        # The view will handle validation for duplicate profiles
        if request.method == 'POST':
            return (
                request.user.is_authenticated and
                request.user.is_service_provider
            )

        # For other methods (PUT, PATCH, DELETE), require authentication
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Only the provider owner can modify
        return obj.user == request.user


class IsServiceOwnerOrReadOnly(BasePermission):
    """
    Permission for Service objects - only the provider owner can modify
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Only the service provider can modify their services
        return obj.provider.user == request.user


class IsAdminOrReadOnly(BasePermission):
    """
    Permission that allows admins to modify categories,
    and read-only access for others.
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return (
            request.user.is_authenticated and
            (request.user.is_staff or request.user.role == User.UserRole.ADMIN)
        )


class CanManageOwnServices(BasePermission):
    """
    Permission for service providers to manage their own services
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        # Check if the user owns the service through provider relationship
        if hasattr(obj, 'provider'):
            return obj.provider.user == request.user
        elif hasattr(obj, 'service'):
            return obj.service.provider.user == request.user
        
        return False


class CanViewProviderDetails(BasePermission):
    """
    Permission to view detailed provider information
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Providers can always view their own details
        if obj.user == request.user:
            return True
        
        # Others can view if provider is active and verified
        return obj.is_active and obj.is_verified


class CanBookService(BasePermission):
    """
    Permission to book services - customers only
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_customer and
            request.user.account_status == User.AccountStatus.ACTIVE
        )


class IsVerifiedProvider(BasePermission):
    """
    Permission that requires the user to be a verified service provider
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_service_provider and
            request.user.is_verified and
            request.user.account_status == User.AccountStatus.ACTIVE
        )


class CanManageGallery(BasePermission):
    """
    Permission to manage service gallery images
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        # Check if user owns the provider or service
        if obj.provider:
            return obj.provider.user == request.user
        elif obj.service:
            return obj.service.provider.user == request.user
        
        return False


class CanManageOperatingHours(BasePermission):
    """
    Permission to manage operating hours
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.is_service_provider
        )

    def has_object_permission(self, request, view, obj):
        return obj.provider.user == request.user


class IsActiveUser(BasePermission):
    """
    Permission that requires user to have active account status
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and
            request.user.account_status == User.AccountStatus.ACTIVE and
            not request.user.is_account_locked
        )


class CanSearchServices(BasePermission):
    """
    Permission for service search functionality
    """

    def has_permission(self, request, view):
        # Allow unauthenticated users to search (with rate limiting)
        return True


class CanAccessLocationData(BasePermission):
    """
    Permission to access location-based data
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Providers can access their own location data
        if hasattr(obj, 'user') and obj.user == request.user:
            return True
        
        # Others can access if provider allows public location
        if hasattr(obj, 'provider'):
            return obj.provider.is_active and obj.provider.is_verified
        
        return True


# Permission classes for different user roles
class CustomerPermissions:
    """Permission sets for customer users"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsActiveUser,
        ]


class ProviderPermissions:
    """Permission sets for service provider users"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            IsActiveUser,
            IsVerifiedProvider,
        ]


class AdminPermissions:
    """Permission sets for admin users"""
    
    @staticmethod
    def get_permissions():
        return [
            permissions.IsAuthenticated,
            permissions.IsAdminUser,
        ]


# Utility functions for permission checking
def user_can_modify_provider(user, provider):
    """Check if user can modify a provider profile"""
    return (
        user.is_authenticated and
        user.is_service_provider and
        provider.user == user
    )


def user_can_modify_service(user, service):
    """Check if user can modify a service"""
    return (
        user.is_authenticated and
        user.is_service_provider and
        service.provider.user == user
    )


def user_can_view_provider_details(user, provider):
    """Check if user can view detailed provider information"""
    if not user.is_authenticated:
        return False
    
    # Owner can always view
    if provider.user == user:
        return True
    
    # Others can view if provider is active and verified
    return provider.is_active and provider.is_verified


def user_can_book_service(user, service):
    """Check if user can book a service"""
    return (
        user.is_authenticated and
        user.is_customer and
        user.account_status == User.AccountStatus.ACTIVE and
        service.is_active and
        service.is_available and
        service.provider.is_active and
        service.provider.is_verified
    )


def get_user_permissions(user):
    """Get appropriate permission classes based on user role"""
    if not user.is_authenticated:
        return []
    
    if user.is_staff or user.role == User.UserRole.ADMIN:
        return AdminPermissions.get_permissions()
    elif user.is_service_provider:
        return ProviderPermissions.get_permissions()
    else:
        return CustomerPermissions.get_permissions()


def check_rate_limit_permission(request):
    """Check if request is within rate limits"""
    # This would integrate with Django rate limiting
    # For now, return True - implement with django-ratelimit
    return True


def validate_service_access(user, service):
    """Validate if user can access service details"""
    if not service.is_active:
        return user.is_authenticated and service.provider.user == user
    
    return True


def validate_provider_access(user, provider):
    """Validate if user can access provider details"""
    if not provider.is_active:
        return user.is_authenticated and provider.user == user
    
    return True
