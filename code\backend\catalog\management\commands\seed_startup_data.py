"""
Django management command to seed comprehensive startup data for Vierla backend
Creates a complete test environment with realistic data for development and testing
"""
import random
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from django.core.management import call_command

from catalog.models import ServiceProvider, ServiceCategory, Service

User = get_user_model()


class Command(BaseCommand):
    help = 'Seed comprehensive startup data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing data',
        )
        parser.add_argument(
            '--quick',
            action='store_true',
            help='Create minimal data for quick setup',
        )
        parser.add_argument(
            '--skip-verification',
            action='store_true',
            help='Skip data verification step',
        )

    def handle(self, *args, **options):
        # Security check - only allow in development/testing
        if not self.is_development_environment():
            raise CommandError(
                'Data seeding is only allowed in development/testing environments. '
                'Current environment: {}'.format(getattr(settings, 'ENVIRONMENT', 'unknown'))
            )

        self.force = options['force']
        self.quick = options['quick']
        self.skip_verification = options['skip_verification']

        self.stdout.write(
            self.style.SUCCESS('🚀 Starting Vierla Backend Data Seeding...')
        )

        if self.quick:
            self.stdout.write(
                self.style.WARNING('⚡ Quick mode: Creating minimal data only')
            )

        try:
            # Check existing data
            if not self.check_existing_data():
                return

            # Run seeding process
            self.run_seeding_process()

            # Final summary
            self.print_final_summary()

        except KeyboardInterrupt:
            self.stdout.write(
                self.style.ERROR('❌ Seeding interrupted by user')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'💥 Seeding failed: {e}')
            )
            raise CommandError(f'Seeding failed: {e}')

    def is_development_environment(self):
        """Check if we're in a development environment"""
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        
        # Allow in development, testing, or when DEBUG is True
        return environment in ['development', 'testing'] or debug_mode

    def check_existing_data(self):
        """Check if data already exists"""
        users_count = User.objects.filter(is_test_account=True).count()
        providers_count = ServiceProvider.objects.count()
        services_count = Service.objects.count()

        if users_count > 0 or providers_count > 0 or services_count > 0:
            if not self.force:
                self.stdout.write(
                    self.style.WARNING(
                        f'⚠️  Existing data found:\n'
                        f'   👥 Test Users: {users_count}\n'
                        f'   🏢 Providers: {providers_count}\n'
                        f'   🛍️  Services: {services_count}\n\n'
                        f'Use --force to recreate data or cleanup first:\n'
                        f'   python manage.py cleanup_test_accounts'
                    )
                )
                return False
            else:
                self.stdout.write(
                    self.style.WARNING('🔄 Force mode: Will recreate existing data')
                )
                # Cleanup existing test data
                call_command('cleanup_test_accounts', '--force')

        return True

    def run_seeding_process(self):
        """Run the complete seeding process"""
        if self.quick:
            self.stdout.write(
                self.style.WARNING('\n🏃 Quick seeding mode - minimal data only')
            )
            steps = [
                ('create_service_categories', 'Service Categories'),
                ('create_basic_test_accounts', 'Basic Test Accounts'),
            ]
        else:
            self.stdout.write(
                self.style.SUCCESS('\n🎯 Full seeding mode - comprehensive data')
            )
            steps = [
                ('create_service_categories', 'Service Categories'),
                ('create_comprehensive_test_accounts', 'Comprehensive Test Accounts'),
                ('create_additional_services', 'Additional Services'),
                ('create_sample_bookings', 'Sample Booking History'),
            ]

        success_count = 0
        for step_method, description in steps:
            if self.run_step(step_method, description):
                success_count += 1
            else:
                self.stdout.write(
                    self.style.WARNING(f'⚠️  Continuing despite {description} failure...')
                )

        # Run verification unless skipped
        if not self.skip_verification:
            if self.verify_seeded_data():
                success_count += 1

        self.stdout.write(
            self.style.SUCCESS(f'\n🎉 Seeding completed: {success_count}/{len(steps)} successful')
        )

    def run_step(self, method_name, description):
        """Run a single seeding step"""
        self.stdout.write(f'\n📋 Step: {description}')
        self.stdout.write('-' * 40)
        
        try:
            method = getattr(self, method_name)
            result = method()
            self.stdout.write(
                self.style.SUCCESS(f'✅ {description} completed successfully')
            )
            return True
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ {description} failed: {e}')
            )
            return False

    def create_service_categories(self):
        """Create comprehensive service categories"""
        categories = [
            {
                'name': 'Hair & Beauty',
                'slug': 'hair-beauty',
                'description': 'Hair styling, cuts, coloring, and beauty treatments',
                'is_active': True,
                'is_popular': True,
                'sort_order': 1
            },
            {
                'name': 'Wellness & Spa',
                'slug': 'wellness-spa',
                'description': 'Massage therapy, spa treatments, and wellness services',
                'is_active': True,
                'is_popular': True,
                'sort_order': 2
            },
            {
                'name': 'Fitness & Training',
                'slug': 'fitness-training',
                'description': 'Personal training, group fitness, and specialized workouts',
                'is_active': True,
                'is_popular': True,
                'sort_order': 3
            },
            {
                'name': 'Skincare & Aesthetics',
                'slug': 'skincare-aesthetics',
                'description': 'Facial treatments, skincare, and aesthetic services',
                'is_active': True,
                'is_popular': True,
                'sort_order': 4
            },
            {
                'name': 'Nail Care',
                'slug': 'nail-care',
                'description': 'Manicures, pedicures, and nail art services',
                'is_active': True,
                'is_popular': False,
                'sort_order': 5
            }
        ]

        if not self.quick:
            # Add more categories for comprehensive testing
            categories.extend([
                {
                    'name': 'Massage Therapy',
                    'slug': 'massage-therapy',
                    'description': 'Therapeutic and relaxation massage services',
                    'is_active': True,
                    'is_popular': False,
                    'sort_order': 6
                },
                {
                    'name': 'Mental Health & Counseling',
                    'slug': 'mental-health',
                    'description': 'Counseling, therapy, and mental health services',
                    'is_active': True,
                    'is_popular': False,
                    'sort_order': 7
                },
                {
                    'name': 'Nutrition & Dietetics',
                    'slug': 'nutrition',
                    'description': 'Nutritional counseling and dietary planning',
                    'is_active': True,
                    'is_popular': False,
                    'sort_order': 8
                }
            ])

        created_count = 0
        for category_data in categories:
            category, created = ServiceCategory.objects.get_or_create(
                slug=category_data['slug'],
                defaults=category_data
            )
            if created:
                created_count += 1
                self.stdout.write(f'✅ Created category: {category.name}')
            else:
                self.stdout.write(f'📋 Category exists: {category.name}')

        self.stdout.write(f'📊 Categories created: {created_count}')
        return True

    def create_basic_test_accounts(self):
        """Create basic test accounts using the existing command"""
        call_command('create_test_accounts', '--force')
        return True

    def create_comprehensive_test_accounts(self):
        """Create comprehensive test accounts with more variety"""
        # First create basic accounts
        call_command('create_test_accounts', '--force')
        
        # Then add more diverse accounts
        self.create_additional_customers()
        self.create_additional_providers()
        return True

    def create_additional_customers(self):
        """Create additional diverse customer accounts"""
        additional_customers = [
            {
                'email': '<EMAIL>',
                'first_name': 'Alex',
                'last_name': 'Chen',
                'phone': '+***********'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Maria',
                'last_name': 'Garcia',
                'phone': '+***********'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'James',
                'last_name': 'Wilson',
                'phone': '+***********'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Priya',
                'last_name': 'Patel',
                'phone': '+***********'
            }
        ]

        for data in additional_customers:
            if not User.objects.filter(email=data['email']).exists():
                User.objects.create_user(
                    email=data['email'],
                    username=data['email'],  # Use email as username
                    password='TestPass123!',
                    first_name=data['first_name'],
                    last_name=data['last_name'],
                    role='customer',
                    is_active=True,
                    is_verified=True,
                    email_verified_at=timezone.now(),
                    phone=data.get('phone'),
                    is_test_account=True
                )
                self.stdout.write(f'✅ Created additional customer: {data["first_name"]} {data["last_name"]}')

    def create_additional_providers(self):
        """Create additional diverse provider accounts"""
        additional_providers = [
            {
                'email': '<EMAIL>',
                'first_name': 'Lisa',
                'last_name': 'Zhang',
                'business_name': 'Zen Wellness Center',
                'business_description': 'Holistic wellness and mindfulness services',
                'category_slug': 'wellness-spa',
                'phone': '+***********'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Dr. Sarah',
                'last_name': 'Kim',
                'business_name': 'Glow Aesthetics Clinic',
                'business_description': 'Advanced skincare and aesthetic treatments',
                'category_slug': 'skincare-aesthetics',
                'phone': '+***********'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Nina',
                'last_name': 'Rodriguez',
                'business_name': 'Nail Artistry Studio',
                'business_description': 'Creative nail art and premium nail care',
                'category_slug': 'nail-care',
                'phone': '+***********'
            }
        ]

        for data in additional_providers:
            if not User.objects.filter(email=data['email']).exists():
                # Create provider user
                provider_user = User.objects.create_user(
                    email=data['email'],
                    username=data['email'],  # Use email as username
                    password='TestPass123!',
                    first_name=data['first_name'],
                    last_name=data['last_name'],
                    role='service_provider',
                    is_active=True,
                    is_verified=True,
                    email_verified_at=timezone.now(),
                    phone=data.get('phone'),
                    is_test_account=True
                )

                # Get category
                try:
                    category = ServiceCategory.objects.get(slug=data['category_slug'])
                except ServiceCategory.DoesNotExist:
                    category = ServiceCategory.objects.first()

                # Create service provider profile
                provider_profile = ServiceProvider.objects.create(
                    user=provider_user,
                    business_name=data['business_name'],
                    business_description=data['business_description'],
                    business_phone=data.get('phone', '+***********'),
                    business_email=data['email'],
                    address=f'{random.randint(100, 999)} {random.choice(["Queen", "King", "Bay", "Yonge"])} Street',
                    city='Toronto',
                    state='Ontario',
                    zip_code=f'M{random.randint(1,9)}{random.choice(["A","B","C","E","G","H","J","K","L","M","N","P","R","S","T","V","W","X","Y"])}{random.randint(1,9)}',
                    country='Canada',
                    latitude=Decimal(str(43.6532 + random.uniform(-0.1, 0.1))),
                    longitude=Decimal(str(-79.3832 + random.uniform(-0.1, 0.1))),
                    is_verified=True,
                    is_active=True,
                    rating=Decimal(str(round(random.uniform(4.0, 5.0), 1))),
                    review_count=random.randint(5, 50),
                    years_of_experience=random.randint(2, 15)
                )

                # Add category to provider
                provider_profile.categories.add(category)
                self.stdout.write(f'✅ Created additional provider: {data["business_name"]}')

    def create_additional_services(self):
        """Create additional services for existing providers"""
        providers = ServiceProvider.objects.all()

        service_templates = {
            'hair-beauty': [
                {'name': 'Balayage', 'description': 'Hand-painted hair highlighting technique', 'base_price': 150.00, 'duration': 180},
                {'name': 'Hair Treatment', 'description': 'Deep conditioning hair treatment', 'base_price': 45.00, 'duration': 30},
                {'name': 'Blowout', 'description': 'Professional hair styling and blowout', 'base_price': 35.00, 'duration': 45},
            ],
            'wellness-spa': [
                {'name': 'Hot Stone Massage', 'description': 'Therapeutic hot stone massage', 'base_price': 110.00, 'duration': 90},
                {'name': 'Aromatherapy Session', 'description': 'Relaxing aromatherapy treatment', 'base_price': 65.00, 'duration': 60},
                {'name': 'Body Wrap', 'description': 'Detoxifying body wrap treatment', 'base_price': 95.00, 'duration': 75},
            ],
            'skincare-aesthetics': [
                {'name': 'Chemical Peel', 'description': 'Professional chemical peel treatment', 'base_price': 125.00, 'duration': 60},
                {'name': 'Microdermabrasion', 'description': 'Skin resurfacing treatment', 'base_price': 85.00, 'duration': 45},
                {'name': 'Anti-Aging Facial', 'description': 'Advanced anti-aging facial treatment', 'base_price': 140.00, 'duration': 90},
            ],
            'nail-care': [
                {'name': 'Gel Manicure', 'description': 'Long-lasting gel nail polish', 'base_price': 35.00, 'duration': 60},
                {'name': 'Pedicure Deluxe', 'description': 'Luxury pedicure with massage', 'base_price': 55.00, 'duration': 75},
                {'name': 'Nail Art Design', 'description': 'Custom nail art and design', 'base_price': 25.00, 'duration': 30},
            ]
        }

        created_count = 0
        for provider in providers:
            category = provider.categories.first()
            if not category:
                continue

            category_slug = category.slug
            templates = service_templates.get(category_slug, [])

            # Add 1-2 additional services per provider
            for template in templates[:random.randint(1, 2)]:
                if not Service.objects.filter(provider=provider, name=template['name']).exists():
                    Service.objects.create(
                        provider=provider,
                        category=category,
                        name=template['name'],
                        description=template['description'],
                        base_price=Decimal(str(template['base_price'])),
                        duration=template['duration'],
                        buffer_time=random.randint(10, 20),
                        is_available=True,
                        is_active=True,
                        booking_count=random.randint(1, 15)
                    )
                    created_count += 1

        self.stdout.write(f'📊 Additional services created: {created_count}')
        return True

    def create_sample_bookings(self):
        """Create sample booking history (placeholder for future implementation)"""
        # This would create sample bookings when booking system is implemented
        self.stdout.write('📅 Sample booking creation skipped (booking system not yet implemented)')
        return True

    def verify_seeded_data(self):
        """Verify that all data was seeded correctly"""
        self.stdout.write('\n🔍 Verifying seeded data...')

        # Count created data
        test_users = User.objects.filter(is_test_account=True).count()
        customers = User.objects.filter(is_test_account=True, role='customer').count()
        providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        categories = ServiceCategory.objects.count()
        services = Service.objects.count()

        # Verify minimum requirements
        issues = []

        if categories < 3:
            issues.append(f'Insufficient categories: {categories} (expected at least 3)')

        if customers < 3:
            issues.append(f'Insufficient customers: {customers} (expected at least 3)')

        if providers < 2:
            issues.append(f'Insufficient providers: {providers} (expected at least 2)')

        if services < 5:
            issues.append(f'Insufficient services: {services} (expected at least 5)')

        if issues:
            self.stdout.write(
                self.style.ERROR('❌ Data verification failed:')
            )
            for issue in issues:
                self.stdout.write(f'   • {issue}')
            return False
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ Data verification passed')
            )
            self.stdout.write(f'   📊 Test Users: {test_users}')
            self.stdout.write(f'   👤 Customers: {customers}')
            self.stdout.write(f'   🏢 Providers: {providers}')
            self.stdout.write(f'   📂 Categories: {categories}')
            self.stdout.write(f'   🛍️  Services: {services}')
            return True

    def print_final_summary(self):
        """Print final summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🎉 VIERLA BACKEND SEEDING COMPLETE')
        )
        self.stdout.write('='*60)

        # Get final counts
        test_users = User.objects.filter(is_test_account=True).count()
        customers = User.objects.filter(is_test_account=True, role='customer').count()
        providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        categories = ServiceCategory.objects.count()
        services = Service.objects.count()

        self.stdout.write(f'\n📊 Final Data Summary:')
        self.stdout.write(f'   👥 Total Test Users: {test_users}')
        self.stdout.write(f'   👤 Customers: {customers}')
        self.stdout.write(f'   🏢 Providers: {providers}')
        self.stdout.write(f'   📂 Categories: {categories}')
        self.stdout.write(f'   🛍️  Services: {services}')

        self.stdout.write(f'\n🔑 Test Credentials:')
        self.stdout.write(f'   Password for all accounts: TestPass123!')

        self.stdout.write(f'\n📱 Quick Access:')
        self.stdout.write(f'   🌐 API Base: http://localhost:8000/api/')
        self.stdout.write(f'   📚 API Docs: http://localhost:8000/api/docs/')
        self.stdout.write(f'   🔧 Admin Panel: http://localhost:8000/admin/')

        self.stdout.write(f'\n🔑 Sample Test Accounts:')
        self.stdout.write(f'   👤 Customer: <EMAIL>')
        self.stdout.write(f'   🏢 Provider: <EMAIL>')

        self.stdout.write(f'\n💡 Management Commands:')
        self.stdout.write(f'   🧹 Cleanup: python manage.py cleanup_test_accounts')
        self.stdout.write(f'   🔄 Recreate: python manage.py seed_startup_data --force')
        self.stdout.write(f'   ⚡ Quick setup: python manage.py seed_startup_data --quick')

        self.stdout.write(f'\n🚀 Backend ready for development!')
        self.stdout.write(f'📱 Frontend can now connect to fully populated backend')
        self.stdout.write(f'🧪 All test data available for comprehensive testing')
