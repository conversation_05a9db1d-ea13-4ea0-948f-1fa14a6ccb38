# Generated by Django 4.2.16 on 2025-06-19 13:21

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("catalog", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="serviceprovider",
            name="display_show_availability",
            field=models.BooleanField(
                default=True,
                help_text="Whether to show real-time availability",
                verbose_name="display show availability",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="display_show_duration",
            field=models.BooleanField(
                default=True,
                help_text="Whether to show service duration",
                verbose_name="display show duration",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="display_show_prices",
            field=models.BooleanField(
                default=True,
                help_text="Whether to show service prices",
                verbose_name="display show prices",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="display_show_ratings",
            field=models.<PERSON>oleanField(
                default=True,
                help_text="Whether to show ratings and reviews",
                verbose_name="display show ratings",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="portfolio_instagram_max_posts",
            field=models.PositiveIntegerField(
                default=9,
                help_text="Maximum number of Instagram posts to display",
                validators=[
                    django.core.validators.MinValueValidator(3),
                    django.core.validators.MaxValueValidator(20),
                ],
                verbose_name="instagram max posts",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="portfolio_layout",
            field=models.CharField(
                choices=[
                    ("grid", "Grid Layout"),
                    ("masonry", "Masonry Layout"),
                    ("carousel", "Carousel Layout"),
                ],
                default="grid",
                help_text="Layout style for portfolio display",
                max_length=10,
                verbose_name="portfolio layout",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="portfolio_show_instagram_captions",
            field=models.BooleanField(
                default=True,
                help_text="Whether to show Instagram post captions",
                verbose_name="show instagram captions",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="portfolio_type",
            field=models.CharField(
                choices=[("upload", "Upload Images"), ("instagram", "Instagram Feed")],
                default="upload",
                help_text="Type of portfolio display",
                max_length=10,
                verbose_name="portfolio type",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="store_theme_accent_color",
            field=models.CharField(
                default="#E8F5E8",
                help_text="Accent color for store theme (hex format)",
                max_length=7,
                verbose_name="store theme accent color",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="store_theme_layout",
            field=models.CharField(
                choices=[
                    ("grid", "Grid Layout"),
                    ("list", "List Layout"),
                    ("card", "Card Layout"),
                ],
                default="card",
                help_text="Layout style for store display",
                max_length=10,
                verbose_name="store theme layout",
            ),
        ),
        migrations.AddField(
            model_name="serviceprovider",
            name="store_theme_primary_color",
            field=models.CharField(
                default="#7C9A85",
                help_text="Primary color for store theme (hex format)",
                max_length=7,
                verbose_name="store theme primary color",
            ),
        ),
    ]
