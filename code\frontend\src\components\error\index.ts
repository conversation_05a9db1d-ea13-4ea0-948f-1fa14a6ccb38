/**
 * Error Components Index - Standardized Error Handling Exports
 * 
 * This file exports all error handling components, hooks, and utilities
 * for easy importing throughout the application.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Error Display Components
export { ErrorDisplay, ValidationError, SuccessDisplay } from './ErrorDisplay';
export type { ErrorDisplayProps, ValidationErrorProps, SuccessDisplayProps } from './ErrorDisplay';

// Error Boundary
export { ErrorBoundary, withErrorBoundary, useErrorBoundary } from './ErrorBoundary';

// Toast System
export { ToastSystem, ToastProvider } from '../feedback/ToastSystem';

// Hooks
export { useErrorHandler } from '../../hooks/useErrorHandler';
export { useToast, toast, setGlobalToastManager } from '../../hooks/useToast';

// Types and Utilities
export * from '../../utils/errorTypes';
export * from '../../utils/errorUtils';

// Re-export commonly used types for convenience
export type {
  AppError,
  ErrorContext,
  ErrorDisplayProps,
  ToastConfig,
  UseErrorHandlerReturn,
  UseToastReturn,
} from '../../utils/errorTypes';

export {
  ErrorType,
  ErrorSeverity,
  ErrorVariant,
  ERROR_MESSAGES,
  ERROR_ICONS,
  SEVERITY_COLORS,
  TOAST_COLORS,
} from '../../utils/errorTypes';
