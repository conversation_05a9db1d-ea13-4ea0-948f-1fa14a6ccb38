/**
 * ProviderOnboardingCarousel Component
 * 
 * Onboarding carousel specifically for service providers showing business
 * features and benefits relevant to service providers.
 */

import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button } from '../../components/ui';
import { colors, spacing } from '../../theme';

const { width } = Dimensions.get('window');

type ProviderOnboardingNavigationProp = StackNavigationProp<any, 'ProviderOnboarding'>;

interface OnboardingSlide {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
}

const onboardingSlides: OnboardingSlide[] = [
  {
    id: 'reach',
    title: 'Reach More Customers',
    description: 'Connect with customers actively looking for your services',
    icon: '📈',
    features: [
      'Expanded customer base',
      'Location-based discovery',
      'Service category listings',
      'Professional profile showcase',
    ],
  },
  {
    id: 'manage',
    title: 'Manage Your Business',
    description: 'Streamline bookings, scheduling, and customer communication',
    icon: '📋',
    features: [
      'Centralized booking system',
      'Calendar integration',
      'Customer management',
      'Service portfolio',
    ],
  },
  {
    id: 'payments',
    title: 'Secure Payments',
    description: 'Get paid quickly and securely for every completed service',
    icon: '💳',
    features: [
      'Instant payment processing',
      'Multiple payment methods',
      'Automatic invoicing',
      'Financial tracking',
    ],
  },
  {
    id: 'reputation',
    title: 'Build Your Reputation',
    description: 'Grow your business with authentic reviews and ratings',
    icon: '🏆',
    features: [
      'Customer review system',
      'Rating improvements',
      'Portfolio showcase',
      'Trust building tools',
    ],
  },
  {
    id: 'growth',
    title: 'Business Growth Tools',
    description: 'Access analytics and insights to grow your service business',
    icon: '📊',
    features: [
      'Performance analytics',
      'Customer insights',
      'Revenue tracking',
      'Growth recommendations',
    ],
  },
];

export const ProviderOnboardingCarousel: React.FC = () => {
  const navigation = useNavigation<ProviderOnboardingNavigationProp>();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleNext = () => {
    if (currentSlide < onboardingSlides.length - 1) {
      const nextSlide = currentSlide + 1;
      setCurrentSlide(nextSlide);
      scrollViewRef.current?.scrollTo({
        x: nextSlide * width,
        animated: true,
      });
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      setCurrentSlide(prevSlide);
      scrollViewRef.current?.scrollTo({
        x: prevSlide * width,
        animated: true,
      });
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = async () => {
    setIsLoading(true);

    try {
      // Mark onboarding as completed
      await AsyncStorage.setItem('onboarding_completed', 'true');
      await AsyncStorage.setItem('onboarding_step', 'completed');
      await AsyncStorage.setItem('user_type', 'service_provider');

      // Navigate to provider registration/setup
      navigation.reset({
        index: 0,
        routes: [{ name: 'Auth' }],
      });
    } catch (error) {
      console.error('Error completing provider onboarding:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentSlide(slideIndex);
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View key={slide.id} style={styles.slide}>
      <View style={styles.slideContent}>
        {/* Icon */}
        <View style={styles.iconContainer}>
          <Text variant="display" style={styles.slideIcon}>
            {slide.icon}
          </Text>
        </View>

        {/* Title & Description */}
        <Text variant="h2" style={styles.slideTitle}>
          {slide.title}
        </Text>
        
        <Text variant="body" color="secondary" style={styles.slideDescription}>
          {slide.description}
        </Text>

        {/* Features List */}
        <View style={styles.featuresList}>
          {slide.features.map((feature, featureIndex) => (
            <View key={featureIndex} style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text variant="body" style={styles.featureText}>
                {feature}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  const renderProgressIndicators = () => (
    <View style={styles.progressContainer}>
      {onboardingSlides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.progressDot,
            index === currentSlide && styles.progressDotActive,
          ]}
        />
      ))}
    </View>
  );

  const isLastSlide = currentSlide === onboardingSlides.length - 1;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text variant="caption" color="primary" style={styles.businessLabel}>
          FOR BUSINESS
        </Text>
        
        <Button
          variant="outline"
          onPress={handleSkip}
          style={styles.skipButton}
          testID="skip-button"
        >
          Skip
        </Button>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        style={styles.scrollView}
        testID="provider-onboarding-carousel"
      >
        {onboardingSlides.map(renderSlide)}
      </ScrollView>

      {/* Progress Indicators */}
      {renderProgressIndicators()}

      {/* Navigation */}
      <View style={styles.navigation}>
        <Button
          variant="outline"
          onPress={handlePrevious}
          disabled={currentSlide === 0}
          style={[styles.navButton, styles.previousButton]}
          testID="previous-button"
        >
          Previous
        </Button>

        <Button
          onPress={handleNext}
          loading={isLoading && isLastSlide}
          style={[styles.navButton, styles.nextButton]}
          testID={isLastSlide ? "start-building-button" : "next-button"}
        >
          {isLastSlide ? "Start Building" : "Next"}
        </Button>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
  },
  businessLabel: {
    fontWeight: '600',
    letterSpacing: 1,
  },
  skipButton: {
    paddingHorizontal: spacing.lg,
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width,
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  iconContainer: {
    marginBottom: spacing.xl,
  },
  slideIcon: {
    fontSize: 80,
    textAlign: 'center',
  },
  slideTitle: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.text.primary,
  },
  slideDescription: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  featuresList: {
    alignSelf: 'stretch',
    maxWidth: 300,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  featureBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginRight: spacing.md,
  },
  featureText: {
    flex: 1,
    color: colors.text.primary,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.border,
    marginHorizontal: spacing.xs,
  },
  progressDotActive: {
    backgroundColor: colors.primary,
    width: 24,
  },
  navigation: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    gap: spacing.md,
  },
  navButton: {
    flex: 1,
  },
  previousButton: {
    // Additional styles for previous button if needed
  },
  nextButton: {
    // Additional styles for next button if needed
  },
});

export default ProviderOnboardingCarousel;
