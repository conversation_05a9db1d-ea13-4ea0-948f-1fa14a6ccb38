# Vierla Development Scripts

This directory contains development and deployment scripts for the Vierla Beauty Services Marketplace application.

## Directory Structure

```
scripts/
├── README.md                 # This file - script documentation
├── development/             # Development environment scripts
│   ├── start-backend.bat   # Windows backend startup script
│   ├── start-backend.sh    # Unix/Linux backend startup script
│   ├── start-database.bat  # Windows database startup script
│   ├── start-database.sh   # Unix/Linux database startup script
│   └── setup-dev.bat       # Development environment setup
├── deployment/             # Production deployment scripts
│   └── (future deployment scripts)
└── utilities/              # Utility and maintenance scripts
    └── (future utility scripts)
```

## Quick Start Guide

### Prerequisites
- Python 3.11+ installed
- Node.js 18+ installed (for frontend)
- Git installed

### Backend Development

1. **Setup Development Environment** (first time only):
   ```bash
   # Windows
   .\scripts\development\setup-dev.bat
   
   # Unix/Linux/macOS
   ./scripts/development/setup-dev.sh
   ```

2. **Start Database** (if using external database):
   ```bash
   # Windows
   .\scripts\development\start-database.bat
   
   # Unix/Linux/macOS
   ./scripts/development/start-database.sh
   ```

3. **Start Backend Server**:
   ```bash
   # Windows
   .\scripts\development\start-backend.bat
   
   # Unix/Linux/macOS
   ./scripts/development/start-backend.sh
   ```

### Default Configuration

- **Backend Server**: `http://localhost:8000`
- **Database**: SQLite (development) / PostgreSQL (production)
- **API Documentation**: `http://localhost:8000/api/docs/`
- **Admin Interface**: `http://localhost:8000/admin/`

## Script Design Principles

All scripts in this directory follow these principles:

1. **Cross-Platform Compatibility**: Separate scripts for Windows (.bat) and Unix (.sh)
2. **Environment Isolation**: Proper virtual environment management
3. **Error Handling**: Comprehensive error checking and user feedback
4. **Logging**: Structured logging for debugging and monitoring
5. **Configuration**: Environment-based configuration management
6. **Documentation**: Clear usage instructions and examples

## Environment Variables

Scripts use the following environment variables:

- `VIERLA_ENV`: Environment (development/staging/production)
- `VIERLA_DEBUG`: Enable debug mode (true/false)
- `VIERLA_HOST`: Server host (default: localhost)
- `VIERLA_PORT`: Server port (default: 8000)
- `DATABASE_URL`: Database connection string

## Troubleshooting

### Common Issues

1. **Port Already in Use**:
   - Check if another process is using port 8000
   - Use `netstat -ano | findstr :8000` (Windows) or `lsof -i :8000` (Unix)

2. **Virtual Environment Issues**:
   - Delete `venv` directory and run setup script again
   - Ensure Python 3.11+ is installed and in PATH

3. **Database Connection Issues**:
   - Check database service is running
   - Verify connection string in environment variables

### Getting Help

- Check script logs in `logs/` directory
- Review error messages for specific guidance
- Consult project documentation in `docs/`

## Contributing

When adding new scripts:

1. Follow the established naming convention
2. Include comprehensive error handling
3. Add documentation to this README
4. Test on both Windows and Unix systems
5. Follow the project's clean code principles
