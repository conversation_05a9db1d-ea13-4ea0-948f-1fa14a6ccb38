# API Documentation

**Version:** 1.0.0  
**Last Updated:** December 2024  
**Framework:** React Native with Expo 53

---

## Overview

This document provides comprehensive API documentation for utility functions, hooks, and services implemented in the enhanced frontend system.

---

## Performance Utilities

### Performance Monitor

**Location:** `src/utils/performance.ts`

#### `performanceMonitor.recordMetric(metric: PerformanceMetric): void`

Records a performance metric for analysis.

**Parameters:**
- `metric`: Performance metric object

**Example:**
```typescript
performanceMonitor.recordMetric({
  name: 'component-render',
  value: 45.2,
  timestamp: Date.now(),
  type: 'timing',
  tags: { component: 'UserProfile' }
});
```

#### `performanceMonitor.getMetrics(): PerformanceMetric[]`

Retrieves all recorded performance metrics.

**Returns:** Array of performance metrics

#### `performanceMonitor.analyzePerformance(): PerformanceAnalysis`

Analyzes performance data and provides insights.

**Returns:** Performance analysis object with recommendations

---

### Bundle Optimization

**Location:** `src/utils/bundleOptimization.ts`

#### `createOptimizedLazyComponent<T>(loader, options): LazyExoticComponent<T>`

Creates an optimized lazy-loaded component with performance tracking.

**Parameters:**
- `loader`: Function returning component promise
- `options`: Configuration options

**Example:**
```typescript
const LazyDashboard = createOptimizedLazyComponent(
  () => import('./Dashboard'),
  {
    name: 'Dashboard',
    type: 'critical',
    preload: true
  }
);
```

#### `getBundleMetrics(): BundleMetrics`

Retrieves current bundle optimization metrics.

**Returns:** Bundle metrics and recommendations

---

## Accessibility Utilities

### Screen Reader Utils

**Location:** `src/utils/screenReaderUtils.ts`

#### `generateFormFieldLabel(label, required, error, value): string`

Generates comprehensive accessibility label for form fields.

**Parameters:**
- `label`: Field label
- `required`: Whether field is required
- `error`: Error message (optional)
- `value`: Current value (optional)

**Example:**
```typescript
const accessibilityLabel = generateFormFieldLabel(
  'Email Address',
  true,
  'Invalid email format',
  '<EMAIL>'
);
// Returns: "Email Address, required, error: Invalid email format, current value: <EMAIL>"
```

#### `generateWidgetDescription(type, title, data, position): AccessibilityProps`

Generates accessibility description for dashboard widgets.

**Parameters:**
- `type`: Widget type
- `title`: Widget title
- `data`: Widget data
- `position`: Grid position (optional)

**Returns:** Object with accessibility label, hint, and value

---

### Focus Management

**Location:** `src/utils/accessibilityUtils.ts`

#### `FocusUtils.getFocusIndicatorStyle(isFocused, baseStyle, options): ViewStyle`

Generates WCAG-compliant focus indicator styles.

**Parameters:**
- `isFocused`: Focus state
- `baseStyle`: Base component styles
- `options`: Focus indicator options

**Example:**
```typescript
const focusStyles = FocusUtils.getFocusIndicatorStyle(
  isFocused,
  {},
  {
    color: '#5A7A63',
    width: 3,
    offset: 2
  }
);
```

---

## Testing Utilities

### Test Utils

**Location:** `src/utils/testUtils.ts`

#### `renderWithProviders(ui, options): RenderResult`

Enhanced render function with theme and provider support.

**Parameters:**
- `ui`: React element to render
- `options`: Render options

**Example:**
```typescript
const { getByText } = renderWithProviders(
  <MyComponent />,
  { 
    theme: 'dark',
    withTheme: true,
    withNavigation: true
  }
);
```

#### `mockFactories.user(overrides): MockUser`

Creates mock user data for testing.

**Parameters:**
- `overrides`: Property overrides

**Example:**
```typescript
const mockUser = mockFactories.user({
  role: 'provider',
  isVerified: false
});
```

---

### Performance Testing

#### `performanceTestUtils.measureRenderTime(renderFn): Promise<number>`

Measures component render time.

**Parameters:**
- `renderFn`: Function that renders component

**Returns:** Render time in milliseconds

**Example:**
```typescript
const renderTime = await performanceTestUtils.measureRenderTime(() =>
  renderWithProviders(<HeavyComponent />)
);

performanceTestUtils.expectRenderTimeBelow(renderTime, 100);
```

---

### Accessibility Testing

#### `accessibilityTestUtils.expectAccessibilityLabel(element, expectedLabel): void`

Asserts element has correct accessibility label.

**Parameters:**
- `element`: React element
- `expectedLabel`: Expected label (optional)

**Example:**
```typescript
accessibilityTestUtils.expectAccessibilityLabel(
  button,
  'Submit form'
);
```

---

## Lazy Loading Utilities

### Lazy Loading Metrics

**Location:** `src/utils/lazyLoadingMetrics.ts`

#### `trackImageLazyLoad(imageUrl, options): string`

Tracks image lazy loading performance.

**Parameters:**
- `imageUrl`: Image URL
- `options`: Tracking options

**Returns:** Tracking ID

**Example:**
```typescript
const trackingId = trackImageLazyLoad('hero-image.jpg', {
  wasLazy: true,
  wasInViewport: false,
  size: 1024000
});
```

#### `completeLazyLoadTracking(id, success, options): void`

Completes lazy loading tracking.

**Parameters:**
- `id`: Tracking ID
- `success`: Whether loading succeeded
- `options`: Additional options

#### `getLazyLoadingSummary(): LazyLoadSummary`

Gets comprehensive lazy loading metrics.

**Returns:** Summary of lazy loading performance

---

## Hooks

### Performance Monitoring Hook

**Location:** `src/hooks/usePerformanceMonitoring.ts`

#### `usePerformanceMonitoring(componentName, options): PerformanceHookResult`

React hook for component-level performance monitoring.

**Parameters:**
- `componentName`: Name of component
- `options`: Monitoring options

**Returns:** Performance monitoring utilities

**Example:**
```typescript
const {
  metrics,
  startRender,
  endRender,
  measureAsync,
  isSlowComponent
} = usePerformanceMonitoring('UserProfile', {
  trackRender: true,
  slowRenderThreshold: 16
});

// Measure async operation
const data = await measureAsync('fetchUserData', async () => {
  return await api.getUserData();
});
```

---

## Theme System

### Theme Context

**Location:** `src/contexts/ThemeContext.tsx`

#### `useTheme(): ThemeContextValue`

Hook for accessing theme context.

**Returns:** Theme context value

**Example:**
```typescript
const { isDark, theme, toggleTheme } = useTheme();

const styles = StyleSheet.create({
  container: {
    backgroundColor: isDark ? theme.darkColors.background : theme.colors.background
  }
});
```

---

## Error Handling

### Error Boundary

**Location:** `src/components/error/ErrorBoundary.tsx`

#### Error Boundary Props

```typescript
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  context?: string;
}
```

**Example:**
```typescript
<ErrorBoundary
  onError={(error, errorInfo) => {
    console.error('Component error:', error);
    // Send to error reporting service
  }}
  context="UserDashboard"
  showDetails={__DEV__}
>
  <UserDashboard />
</ErrorBoundary>
```

---

## Constants and Configuration

### WCAG Standards

**Location:** `src/utils/accessibilityUtils.ts`

```typescript
export const WCAG_STANDARDS = {
  FOCUS_INDICATORS: {
    MIN_WIDTH: 2,
    RECOMMENDED_WIDTH: 3,
    OFFSET: 2
  },
  TOUCH_TARGETS: {
    MINIMUM_SIZE: 44
  },
  COLOR_CONTRAST: {
    AA_NORMAL: 4.5,
    AA_LARGE: 3.0,
    AAA_NORMAL: 7.0,
    AAA_LARGE: 4.5
  }
};
```

### Performance Thresholds

```typescript
export const PERFORMANCE_THRESHOLDS = {
  RENDER_TIME: 16, // 60fps
  BUNDLE_SIZE: 2 * 1024 * 1024, // 2MB
  MEMORY_USAGE: 80, // 80%
  CACHE_HIT_RATE: 70 // 70%
};
```

---

## Type Definitions

### Core Types

```typescript
// Performance metric type
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

// Accessibility props type
interface AccessibilityProps {
  accessibilityLabel: string;
  accessibilityHint: string;
  accessibilityValue?: { text: string };
}

// Theme colors type
interface ThemeColors {
  primary: string;
  secondary: string;
  accent: Record<number, string>;
  semantic: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}
```

---

## Error Codes

### Performance Errors

- `PERF_001`: Render time exceeds threshold
- `PERF_002`: Bundle size exceeds limit
- `PERF_003`: Memory usage too high
- `PERF_004`: Cache hit rate too low

### Accessibility Errors

- `A11Y_001`: Missing accessibility label
- `A11Y_002`: Insufficient color contrast
- `A11Y_003`: Touch target too small
- `A11Y_004`: Missing focus indicator

---

## Best Practices

### Performance

1. Use lazy loading for components >100KB
2. Implement performance monitoring for critical paths
3. Monitor bundle size regularly
4. Optimize images and assets

### Accessibility

1. Always provide accessibility labels
2. Ensure 4.5:1 color contrast minimum
3. Test with screen readers
4. Implement keyboard navigation

### Testing

1. Use provided test utilities
2. Test accessibility features
3. Measure performance in tests
4. Mock external dependencies

---

**This API documentation provides comprehensive coverage of all utility functions and hooks in the enhanced frontend system.**
