
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for features/payout-management</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> features/payout-management</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.17% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>64/116</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.71% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>18/70</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">41.93% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">55.76% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>58/104</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="PayoutDashboardScreen.tsx"><a href="PayoutDashboardScreen.tsx.html">PayoutDashboardScreen.tsx</a></td>
	<td data-value="62.79" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 62%"></div><div class="cover-empty" style="width: 38%"></div></div>
	</td>
	<td data-value="62.79" class="pct medium">62.79%</td>
	<td data-value="43" class="abs medium">27/43</td>
	<td data-value="19.04" class="pct low">19.04%</td>
	<td data-value="21" class="abs low">4/21</td>
	<td data-value="41.66" class="pct low">41.66%</td>
	<td data-value="12" class="abs low">5/12</td>
	<td data-value="64.1" class="pct medium">64.1%</td>
	<td data-value="39" class="abs medium">25/39</td>
	</tr>

<tr>
	<td class="file medium" data-value="PayoutOnboardingScreen.tsx"><a href="PayoutOnboardingScreen.tsx.html">PayoutOnboardingScreen.tsx</a></td>
	<td data-value="50.68" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 50%"></div><div class="cover-empty" style="width: 50%"></div></div>
	</td>
	<td data-value="50.68" class="pct medium">50.68%</td>
	<td data-value="73" class="abs medium">37/73</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="49" class="abs low">14/49</td>
	<td data-value="42.1" class="pct low">42.1%</td>
	<td data-value="19" class="abs low">8/19</td>
	<td data-value="50.76" class="pct medium">50.76%</td>
	<td data-value="65" class="abs medium">33/65</td>
	</tr>

<tr>
	<td class="file empty" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="0" class="pic empty">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T23:26:10.033Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    