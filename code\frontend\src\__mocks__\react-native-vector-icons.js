/**
 * Mock for react-native-vector-icons
 */

import React from 'react';

const MockIcon = (props) => {
  const { name, size, color, style, testID, ...otherProps } = props;
  
  return React.createElement('Text', {
    testID: testID || `icon-${name}`,
    style: [
      {
        fontSize: size || 24,
        color: color || '#000',
      },
      style,
    ],
    ...otherProps,
  }, name || 'icon');
};

export default MockIcon;
