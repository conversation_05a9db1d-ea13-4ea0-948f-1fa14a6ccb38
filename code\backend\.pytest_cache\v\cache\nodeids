["authentication/test_acceptance.py::EmailVerificationAcceptanceTests::test_user_can_verify_email_with_valid_token", "authentication/test_acceptance.py::SocialAuthenticationAcceptanceTests::test_user_can_authenticate_with_google", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_account_lockout_after_failed_attempts", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_can_login_with_valid_credentials", "authentication/test_acceptance.py::UserLoginAcceptanceTests::test_user_cannot_login_with_invalid_credentials", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_can_register_with_email_and_password", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_cannot_register_with_duplicate_email", "authentication/test_acceptance.py::UserRegistrationAcceptanceTests::test_user_cannot_register_with_weak_password", "authentication/test_urls.py::AuthenticationURLAccessTests::test_login_url_accessible", "authentication/test_urls.py::AuthenticationURLAccessTests::test_profile_url_requires_authentication", "authentication/test_urls.py::AuthenticationURLAccessTests::test_register_url_accessible", "authentication/test_urls.py::AuthenticationURLTests::test_login_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_logout_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_password_reset_confirm_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_password_reset_request_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_profile_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_register_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_social_auth_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_token_refresh_url_resolves", "authentication/test_urls.py::AuthenticationURLTests::test_verify_email_url_resolves", "authentication/tests.py::AuthenticationAPITests::test_account_lockout_after_failed_attempts", "authentication/tests.py::AuthenticationAPITests::test_protected_endpoint_requires_authentication", "authentication/tests.py::AuthenticationAPITests::test_protected_endpoint_with_valid_token", "authentication/tests.py::AuthenticationAPITests::test_token_refresh_invalid_token", "authentication/tests.py::AuthenticationAPITests::test_token_refresh_success", "authentication/tests.py::AuthenticationAPITests::test_user_login_invalid_credentials", "authentication/tests.py::AuthenticationAPITests::test_user_login_success", "authentication/tests.py::AuthenticationAPITests::test_user_login_unverified_account", "authentication/tests.py::AuthenticationAPITests::test_user_registration_duplicate_email", "authentication/tests.py::AuthenticationAPITests::test_user_registration_invalid_password", "authentication/tests.py::AuthenticationAPITests::test_user_registration_success", "authentication/tests.py::UserModelTests::test_account_lockout_mechanism", "authentication/tests.py::UserModelTests::test_create_superuser", "authentication/tests.py::UserModelTests::test_create_user_with_email", "authentication/tests.py::UserModelTests::test_create_user_without_email_raises_error", "authentication/tests.py::UserModelTests::test_phone_number_validation", "authentication/tests.py::UserModelTests::test_reset_failed_login_attempts", "authentication/tests.py::UserModelTests::test_user_full_name_property", "authentication/tests.py::UserModelTests::test_user_role_choices", "authentication/tests.py::UserModelTests::test_verify_email_method", "catalog/tests/test_account_management.py::DataVerificationTest::test_verify_sample_data_command", "catalog/tests/test_account_management.py::DataVerificationTest::test_verify_sample_data_detailed", "catalog/tests/test_account_management.py::SampleDataGenerationTest::test_customer_factory", "catalog/tests/test_account_management.py::SampleDataGenerationTest::test_generate_sample_data_command", "catalog/tests/test_account_management.py::SampleDataGenerationTest::test_provider_factory", "catalog/tests/test_account_management.py::SampleDataGenerationTest::test_service_factory", "catalog/tests/test_account_management.py::SecurityAuditTest::test_audit_production_check", "catalog/tests/test_account_management.py::SecurityAuditTest::test_audit_test_accounts_command", "catalog/tests/test_account_management.py::SecurityAuditTest::test_audit_test_accounts_detailed", "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_blocked_in_production", "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_customers_only", "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_test_accounts_dry_run", "catalog/tests/test_account_management.py::TestAccountCleanupTest::test_cleanup_test_accounts_force", "catalog/tests/test_account_management.py::TestAccountCreationTest::test_create_customers_only", "catalog/tests/test_account_management.py::TestAccountCreationTest::test_create_providers_only", "catalog/tests/test_account_management.py::TestAccountCreationTest::test_create_test_accounts_blocked_in_production", "catalog/tests/test_account_management.py::TestAccountCreationTest::test_create_test_accounts_command_basic", "catalog/tests/test_account_management.py::TestAccountCreationTest::test_create_test_accounts_with_force", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_environment_detection_development", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_environment_detection_production", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_test_account_email_validation", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_test_account_manager_blocked_in_production", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_test_account_manager_create_user", "catalog/tests/test_account_management.py::TestAccountSecurityTest::test_test_account_manager_invalid_email", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_bulk_service_operations", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_concurrent_service_updates", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_create_service_success", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_create_service_validation_errors", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_delete_service_ownership_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_delete_service_success", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_error_handling_and_edge_cases", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_list_services_authenticated_provider", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_list_services_customer_user", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_list_services_unauthenticated", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_pagination", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_retrieve_service_ownership_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_retrieve_service_success", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_analytics_data", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_availability_management", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_buffer_time_handling", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_category_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_duration_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_filtering", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_image_handling", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_metrics_in_response", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_ordering", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_performance_metrics", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_price_type_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_price_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_requirements_and_instructions", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_service_search_functionality", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_update_service_ownership_validation", "catalog/tests/test_provider_service_api_integration.py::TestProviderServiceAPIIntegration::test_update_service_success", "debug_test.py::test_registration", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_api_error_handling", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_api_performance_requirements", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_category_based_service_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_comprehensive_search_integration", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_featured_services_endpoint", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_multi_category_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_popular_services_endpoint", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_price_range_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_provider_verification_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_search_suggestions", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_availability_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_category_listing", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_detail_view", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_duration_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_image_handling", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_listing_with_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_metrics_tracking", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_pagination", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_rating_filtering", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_search_functionality", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_sorting_options", "services/test_acceptance.py::ServiceBrowsingAcceptanceTests::test_service_status_management", "simple_allowed_hosts_test.py::test_allowed_hosts", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_127_0_0_1_allowed", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_android_emulator_ip_allowed", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_disallowed_host_rejected", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_localhost_allowed", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_network_ip_192_168_2_65_allowed", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_request_from_localhost_accepted", "test_allowed_hosts.py::AllowedHostsConfigurationTest::test_request_from_network_ip_accepted", "test_allowed_hosts_comprehensive.py::AllowedHostsConfigurationTest::test_production_safety", "test_allowed_hosts_comprehensive.py::AllowedHostsConfigurationTest::test_settings_configuration", "test_allowed_hosts_comprehensive.py::AllowedHostsConfigurationTest::test_specific_ip_present", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_android_emulator_requests", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_case_sensitivity", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_disallowed_hosts_rejected", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_edge_cases", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_localhost_variations", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_mobile_device_ip_requests", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_port_variations", "test_allowed_hosts_comprehensive.py::ComprehensiveAllowedHostsTest::test_required_hosts_in_allowed_hosts"]