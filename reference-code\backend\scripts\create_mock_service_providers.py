#!/usr/bin/env python
"""
Create Mock Service Provider Database Seeder
Creates 40 comprehensive service provider accounts (5 per category) with complete profile information
"""

import os
import sys
import django
import random
from decimal import Decimal
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from apps.catalog.models import ServiceProvider, ServiceCategory
from apps.authentication.models import UserProfile

User = get_user_model()

# Mock data based on research
MOCK_PROVIDERS_DATA = {
    'hair-styling': [
        {
            'name': '<PERSON>',
            'business_name': 'Trendy Cuts & Color Studio',
            'description': 'Specializing in modern cuts, balayage, and color correction. 8 years of experience with Aveda Institute training. Expert in contemporary hair trends and personalized styling.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '123 Queen Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5H 2M9',
            'latitude': Decimal('43.6532'),
            'longitude': Decimal('-79.3832'),
            'website': 'https://trendycuts.com',
            'instagram': 'trendycuts_sophia',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 127,
            'profile_image': 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Marcus Johnson',
            'business_name': 'Classic & Contemporary Hair',
            'description': 'Expert in both men\'s and women\'s styling with 12 years experience. Celebrity clientele and modern techniques combined with classic barbering skills.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '456 Bloor Street East',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4W 1A8',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3948'),
            'website': 'https://classichair.com',
            'instagram': 'marcus_hairstylist',
            'years_experience': 12,
            'rating': Decimal('4.9'),
            'review_count': 203,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Isabella Chen',
            'business_name': 'Curly Hair Sanctuary',
            'description': 'DevaCurl certified specialist in curly and textured hair. 6 years of experience helping clients embrace their natural texture with expert cuts and treatments.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '789 College Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6G 1C5',
            'latitude': Decimal('43.6577'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://curlysanctuary.com',
            'instagram': 'curly_sanctuary',
            'years_experience': 6,
            'rating': Decimal('4.7'),
            'review_count': 89,
            'profile_image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'David Thompson',
            'business_name': 'Precision Cut Masters',
            'description': 'Vidal Sassoon trained precision cut specialist with 15 years experience. Known for geometric cuts and architectural styling that enhances natural features.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '321 Spadina Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5T 2E2',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.3957'),
            'website': 'https://precisioncuts.com',
            'instagram': 'precision_david',
            'years_experience': 15,
            'rating': Decimal('4.9'),
            'review_count': 156,
            'profile_image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Aria Patel',
            'business_name': 'Color Transformation Studio',
            'description': 'L\'Oréal certified color expert specializing in dramatic transformations and color corrections. 10 years experience with fantasy colors and advanced techniques.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '654 Danforth Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4K 1R1',
            'latitude': Decimal('43.6779'),
            'longitude': Decimal('-79.3499'),
            'website': 'https://colortransform.com',
            'instagram': 'aria_colorist',
            'years_experience': 10,
            'rating': Decimal('4.8'),
            'review_count': 142,
            'profile_image': 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'nail-care': [
        {
            'name': 'Luna Rodriguez',
            'business_name': 'Nail Art Virtuoso',
            'description': 'Instagram sensation with 50k followers specializing in intricate nail art and custom designs. 7 years experience creating unique artistic expressions on nails.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '987 King Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5V 1M2',
            'latitude': Decimal('43.6426'),
            'longitude': Decimal('-79.4000'),
            'website': 'https://nailvirtuoso.com',
            'instagram': 'luna_nailart',
            'years_experience': 7,
            'rating': Decimal('4.9'),
            'review_count': 234,
            'profile_image': 'https://images.unsplash.com/photo-**********-94ddf0286df2?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'James Kim',
            'business_name': 'Precision Nail Studio',
            'description': 'OPI certified nail technician expert in gel extensions and nail health. 9 years experience with focus on precision application and long-lasting results.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '147 Yonge Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5C 1W7',
            'latitude': Decimal('43.6532'),
            'longitude': Decimal('-79.3832'),
            'website': 'https://precisionnails.com',
            'instagram': 'james_nails',
            'years_experience': 9,
            'rating': Decimal('4.8'),
            'review_count': 178,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Zara Ahmed',
            'business_name': 'Luxury Nail Lounge',
            'description': 'High-end nail services with premium products and spa-trained techniques. 11 years experience providing luxury nail care in an elegant environment.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '258 Avenue Road',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5R 2J1',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3948'),
            'website': 'https://luxurynails.com',
            'instagram': 'luxury_nails_zara',
            'years_experience': 11,
            'rating': Decimal('4.9'),
            'review_count': 167,
            'profile_image': 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Tyler Brooks',
            'business_name': 'Gentleman\'s Nail Care',
            'description': 'Specialized men\'s grooming and nail care services. 5 years experience with barbershop background, focusing on professional men\'s nail maintenance.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '369 Richmond Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5V 1X1',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.3957'),
            'website': 'https://gentlemannails.com',
            'instagram': 'gentleman_nails',
            'years_experience': 5,
            'rating': Decimal('4.7'),
            'review_count': 92,
            'profile_image': 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Priya Singh',
            'business_name': 'Natural Nail Wellness',
            'description': 'Holistic approach to nail health with natural treatments and organic products. 8 years experience focusing on nail strengthening and natural beauty.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '741 Bathurst Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5S 2R6',
            'latitude': Decimal('43.6577'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://naturalnails.com',
            'instagram': 'natural_nails_priya',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 134,
            'profile_image': 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'lash-extensions': [
        {
            'name': 'Anastasia Volkov',
            'business_name': 'Volume Lash Studio',
            'description': 'Russian volume and mega volume specialist with 6 years experience. Certified trainer in advanced lash techniques and dramatic transformations.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '852 Queen Street East',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4M 1J2',
            'latitude': Decimal('43.6579'),
            'longitude': Decimal('-79.3499'),
            'website': 'https://volumelash.com',
            'instagram': 'anastasia_lashes',
            'years_experience': 6,
            'rating': Decimal('4.9'),
            'review_count': 189,
            'profile_image': 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Chloe Williams',
            'business_name': 'Natural Lash Enhancement',
            'description': 'Specializing in natural-looking classic lashes with gentle techniques. 4 years experience creating subtle, beautiful enhancements.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '963 Ossington Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6G 3V2',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.4200'),
            'website': 'https://naturallash.com',
            'instagram': 'chloe_naturallash',
            'years_experience': 4,
            'rating': Decimal('4.7'),
            'review_count': 98,
            'profile_image': 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Sakura Tanaka',
            'business_name': 'Precision Lash Artistry',
            'description': 'Japanese precision techniques with detailed mapping and perfect symmetry. 8 years experience in meticulous lash application.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '174 Baldwin Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5T 1L8',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.3957'),
            'website': 'https://precisionlash.com',
            'instagram': 'sakura_lashes',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 156,
            'profile_image': 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Maya Jackson',
            'business_name': 'Dramatic Lash Creations',
            'description': 'Bold, dramatic lash looks for editorial and special events. 5 years experience with colored lashes and avant-garde styles.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '285 Roncesvalles Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6R 2M3',
            'latitude': Decimal('43.6426'),
            'longitude': Decimal('-79.4472'),
            'website': 'https://dramaticlash.com',
            'instagram': 'maya_dramaticlash',
            'years_experience': 5,
            'rating': Decimal('4.8'),
            'review_count': 123,
            'profile_image': 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Elena Rossi',
            'business_name': 'Lash Health Specialists',
            'description': 'Focus on lash health and gentle application for sensitive eyes. 7 years experience with natural lash care and treatments.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '396 Harbord Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6G 1H7',
            'latitude': Decimal('43.6577'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://lashhealthspa.com',
            'instagram': 'elena_lashhealth',
            'years_experience': 7,
            'rating': Decimal('4.9'),
            'review_count': 167,
            'profile_image': 'https://images.unsplash.com/photo-**********-7c40e5a71c5e?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'braiding': [
        {
            'name': 'Keisha Washington',
            'business_name': 'Protective Style Studio',
            'description': 'Expert in healthy protective braiding styles with 12 years experience. Natural hair advocate specializing in scalp health and hair growth.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '507 Jane Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6S 3Z3',
            'latitude': Decimal('43.6629'),
            'longitude': Decimal('-79.4900'),
            'website': 'https://protectivestyles.com',
            'instagram': 'keisha_braids',
            'years_experience': 12,
            'rating': Decimal('4.9'),
            'review_count': 245,
            'profile_image': 'https://images.unsplash.com/photo-**********-deb4988cc6c0?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Amara Okafor',
            'business_name': 'Traditional & Modern Braiding',
            'description': 'African traditional and contemporary braiding styles. 15 years experience as cultural specialist in authentic and modern techniques.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '618 St. Clair Avenue West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6C 1A9',
            'latitude': Decimal('43.6777'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://traditionalbraids.com',
            'instagram': 'amara_braids',
            'years_experience': 15,
            'rating': Decimal('4.8'),
            'review_count': 198,
            'profile_image': 'https://images.unsplash.com/photo-1485875437342-9b39470b3d95?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Destiny Brown',
            'business_name': 'Quick Braid Express',
            'description': 'Efficient, high-quality braiding services. 8 years experience as speed braiding champion with focus on quality and time management.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '729 Eglinton Avenue West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5N 1A3',
            'latitude': Decimal('43.7001'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://quickbraids.com',
            'instagram': 'destiny_quickbraids',
            'years_experience': 8,
            'rating': Decimal('4.7'),
            'review_count': 156,
            'profile_image': 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Nia Thompson',
            'business_name': 'Creative Braid Artistry',
            'description': 'Unique patterns and creative braiding designs. 10 years experience as social media influencer with artistic and innovative styles.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '840 Dundas Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6J 1V5',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://creativebraids.com',
            'instagram': 'nia_creativebraids',
            'years_experience': 10,
            'rating': Decimal('4.8'),
            'review_count': 187,
            'profile_image': 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Zuri Davis',
            'business_name': 'Gentle Braiding Techniques',
            'description': 'Gentle braiding focused on scalp health and comfort. 9 years experience specializing in sensitive scalp care and healthy techniques.',
            'email': '<EMAIL>',
            'phone': '+**********0',
            'address': '951 Bloor Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6H 1L7',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.4200'),
            'website': 'https://gentlebraids.com',
            'instagram': 'zuri_gentlebraids',
            'years_experience': 9,
            'rating': Decimal('4.9'),
            'review_count': 134,
            'profile_image': 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'makeup': [
        {
            'name': 'Valentina Cruz',
            'business_name': 'Bridal Beauty Studio',
            'description': 'Expert in bridal and special occasion makeup with 9 years experience. Destination wedding specialist with airbrush expertise.',
            'email': '<EMAIL>',
            'phone': '+**********1',
            'address': '162 Cumberland Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5R 1A8',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3948'),
            'website': 'https://bridalbeauty.com',
            'instagram': 'valentina_bridal',
            'years_experience': 9,
            'rating': Decimal('4.9'),
            'review_count': 167,
            'profile_image': 'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Jordan Mitchell',
            'business_name': 'Editorial Makeup Artistry',
            'description': 'High-fashion and editorial makeup specialist with 7 years experience. Magazine work and creative looks for photoshoots.',
            'email': '<EMAIL>',
            'phone': '+**********2',
            'address': '273 King Street East',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5A 1K2',
            'latitude': Decimal('43.6532'),
            'longitude': Decimal('-79.3600'),
            'website': 'https://editorialmakeup.com',
            'instagram': 'jordan_editorial',
            'years_experience': 7,
            'rating': Decimal('4.8'),
            'review_count': 134,
            'profile_image': 'https://images.unsplash.com/photo-1463453091185-61582044d556?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Layla Hassan',
            'business_name': 'Natural Glam Studio',
            'description': 'Natural beauty enhancement specialist with 6 years experience. Clean beauty advocate focusing on enhancing natural features.',
            'email': '<EMAIL>',
            'phone': '+**********3',
            'address': '384 Parliament Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5A 3A2',
            'latitude': Decimal('43.6579'),
            'longitude': Decimal('-79.3600'),
            'website': 'https://naturalglam.com',
            'instagram': 'layla_naturalglam',
            'years_experience': 6,
            'rating': Decimal('4.7'),
            'review_count': 98,
            'profile_image': 'https://images.unsplash.com/photo-1502823403499-6ccfcf4fb453?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Cameron Lee',
            'business_name': 'Special Effects Makeup',
            'description': 'Theatrical and special effects makeup artist with 11 years experience. Film industry background with character and SFX work.',
            'email': '<EMAIL>',
            'phone': '+**********4',
            'address': '495 Adelaide Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5V 1T2',
            'latitude': Decimal('43.6426'),
            'longitude': Decimal('-79.4000'),
            'website': 'https://sfxmakeup.com',
            'instagram': 'cameron_sfx',
            'years_experience': 11,
            'rating': Decimal('4.8'),
            'review_count': 156,
            'profile_image': 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Gabriella Santos',
            'business_name': 'Multicultural Beauty',
            'description': 'Expert in makeup for all skin tones with 8 years experience. Diversity advocate specializing in inclusive beauty techniques.',
            'email': '<EMAIL>',
            'phone': '+**********5',
            'address': '506 College Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6G 1A8',
            'latitude': Decimal('43.6577'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://multiculturalbeauty.com',
            'instagram': 'gabriella_beauty',
            'years_experience': 8,
            'rating': Decimal('4.9'),
            'review_count': 189,
            'profile_image': 'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'skincare': [
        {
            'name': 'Dr. Sarah Kim',
            'business_name': 'Medical Aesthetics Clinic',
            'description': 'Licensed medical aesthetician with 12 years experience. Clinical treatments and advanced skincare procedures.',
            'email': '<EMAIL>',
            'phone': '+**********6',
            'address': '617 Mount Pleasant Road',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4S 2M5',
            'latitude': Decimal('43.6900'),
            'longitude': Decimal('-79.3900'),
            'website': 'https://medicalaesthetics.com',
            'instagram': 'dr_sarah_skincare',
            'years_experience': 12,
            'rating': Decimal('4.9'),
            'review_count': 234,
            'profile_image': 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Olivia Green',
            'business_name': 'Organic Skincare Spa',
            'description': 'Natural and organic skincare specialist with 8 years experience. Holistic approach to skin health and wellness.',
            'email': '<EMAIL>',
            'phone': '+**********7',
            'address': '728 Danforth Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4J 1L1',
            'latitude': Decimal('43.6779'),
            'longitude': Decimal('-79.3499'),
            'website': 'https://organicskincare.com',
            'instagram': 'olivia_organicskin',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 167,
            'profile_image': 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Marcus Rivera',
            'business_name': 'Anti-Aging Specialists',
            'description': 'Advanced anti-aging treatments with 10 years experience. Mature skin specialist with cutting-edge techniques.',
            'email': '<EMAIL>',
            'phone': '+**********8',
            'address': '839 Bay Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5S 3A7',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3832'),
            'website': 'https://antiagingspa.com',
            'instagram': 'marcus_antiaging',
            'years_experience': 10,
            'rating': Decimal('4.8'),
            'review_count': 145,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Jasmine Taylor',
            'business_name': 'Acne Treatment Center',
            'description': 'Acne and problematic skin specialist with 7 years experience. Teen and adult acne treatment expert.',
            'email': '<EMAIL>',
            'phone': '+**********9',
            'address': '940 Yonge Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4W 2J2',
            'latitude': Decimal('43.6777'),
            'longitude': Decimal('-79.3832'),
            'website': 'https://acnetreatment.com',
            'instagram': 'jasmine_acnecare',
            'years_experience': 7,
            'rating': Decimal('4.7'),
            'review_count': 123,
            'profile_image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Alessandro Rossi',
            'business_name': 'Luxury Spa Experience',
            'description': 'High-end European spa treatments with 15 years experience. Luxury facial and relaxation specialist.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '151 Bloor Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5S 1S4',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3948'),
            'website': 'https://luxuryspa.com',
            'instagram': 'alessandro_spa',
            'years_experience': 15,
            'rating': Decimal('4.9'),
            'review_count': 198,
            'profile_image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'massage': [
        {
            'name': 'Michael Chen',
            'business_name': 'Therapeutic Massage Clinic',
            'description': 'Licensed massage therapist specializing in injury rehabilitation with 11 years experience. Sports medicine background.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '262 Carlaw Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4M 2S1',
            'latitude': Decimal('43.6579'),
            'longitude': Decimal('-79.3400'),
            'website': 'https://therapeuticmassage.com',
            'instagram': 'michael_massage',
            'years_experience': 11,
            'rating': Decimal('4.9'),
            'review_count': 234,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Serenity Johnson',
            'business_name': 'Relaxation & Wellness Spa',
            'description': 'Stress relief and relaxation specialist with 9 years experience. Swedish massage and aromatherapy expert.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '373 Dupont Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5R 1V9',
            'latitude': Decimal('43.6748'),
            'longitude': Decimal('-79.4103'),
            'website': 'https://relaxationspa.com',
            'instagram': 'serenity_wellness',
            'years_experience': 9,
            'rating': Decimal('4.8'),
            'review_count': 189,
            'profile_image': 'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Carlos Mendoza',
            'business_name': 'Sports Recovery Center',
            'description': 'Athletic performance and recovery specialist with 8 years experience. Professional athlete clientele.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '484 Eastern Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4M 1B7',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.3400'),
            'website': 'https://sportsrecovery.com',
            'instagram': 'carlos_sportsrecovery',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 167,
            'profile_image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Harmony Williams',
            'business_name': 'Prenatal & Family Wellness',
            'description': 'Prenatal and family massage specialist with 7 years experience. Certified prenatal massage therapist.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '595 Gerrard Street East',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4M 1Y2',
            'latitude': Decimal('43.6579'),
            'longitude': Decimal('-79.3499'),
            'website': 'https://prenatalwellness.com',
            'instagram': 'harmony_prenatal',
            'years_experience': 7,
            'rating': Decimal('4.9'),
            'review_count': 145,
            'profile_image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Zen Nakamura',
            'business_name': 'Traditional & Modern Massage',
            'description': 'Eastern and Western massage techniques with 13 years experience. Multiple certifications in diverse modalities.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '706 St. Clair Avenue West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6C 1B2',
            'latitude': Decimal('43.6777'),
            'longitude': Decimal('-79.4200'),
            'website': 'https://traditionalmassage.com',
            'instagram': 'zen_massage',
            'years_experience': 13,
            'rating': Decimal('4.9'),
            'review_count': 212,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        }
    ],
    'barbering': [
        {
            'name': 'Antonio Barbieri',
            'business_name': 'Traditional Barber Masters',
            'description': 'Old-school barbering with modern techniques. 20 years experience with family tradition in classic barbering.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '817 College Street',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6G 1C8',
            'latitude': Decimal('43.6577'),
            'longitude': Decimal('-79.4200'),
            'website': 'https://traditionalbarber.com',
            'instagram': 'antonio_barber',
            'years_experience': 20,
            'rating': Decimal('4.9'),
            'review_count': 298,
            'profile_image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Jamal Washington',
            'business_name': 'Modern Fade Specialists',
            'description': 'Contemporary cuts and fades expert with 8 years experience. Urban styles and modern barbering techniques.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '928 Dundas Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M6J 1W3',
            'latitude': Decimal('43.6548'),
            'longitude': Decimal('-79.4200'),
            'website': 'https://modernfades.com',
            'instagram': 'jamal_fades',
            'years_experience': 8,
            'rating': Decimal('4.8'),
            'review_count': 189,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Viktor Petrov',
            'business_name': 'Luxury Grooming Lounge',
            'description': 'High-end men\'s grooming and styling with 12 years experience. Upscale clientele and premium services.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '139 Yorkville Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5R 1C4',
            'latitude': Decimal('43.6677'),
            'longitude': Decimal('-79.3948'),
            'website': 'https://luxurygrooming.com',
            'instagram': 'viktor_luxury',
            'years_experience': 12,
            'rating': Decimal('4.9'),
            'review_count': 234,
            'profile_image': 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Diego Martinez',
            'business_name': 'Quick & Quality Cuts',
            'description': 'Efficient service for busy professionals with 6 years experience. Quality cuts without compromising speed.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '250 Front Street West',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M5V 3A2',
            'latitude': Decimal('43.6426'),
            'longitude': Decimal('-79.3900'),
            'website': 'https://quickcuts.com',
            'instagram': 'diego_quickcuts',
            'years_experience': 6,
            'rating': Decimal('4.7'),
            'review_count': 156,
            'profile_image': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
        },
        {
            'name': 'Rashid Ali',
            'business_name': 'Multicultural Hair Experts',
            'description': 'Diverse hair types and textures specialist with 10 years experience. Cultural sensitivity and expertise.',
            'email': '<EMAIL>',
            'phone': '+***********',
            'address': '361 Broadview Avenue',
            'city': 'Toronto',
            'state': 'ON',
            'zip_code': 'M4M 2H5',
            'latitude': Decimal('43.6579'),
            'longitude': Decimal('-79.3499'),
            'website': 'https://multiculturalhair.com',
            'instagram': 'rashid_multicultural',
            'years_experience': 10,
            'rating': Decimal('4.8'),
            'review_count': 178,
            'profile_image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'
        }
    ]
}

# Toronto locations for realistic addresses
TORONTO_LOCATIONS = [
    {'lat': 43.6532, 'lng': -79.3832, 'area': 'Downtown'},
    {'lat': 43.6677, 'lng': -79.3948, 'area': 'Yorkville'},
    {'lat': 43.6548, 'lng': -79.3957, 'area': 'Entertainment District'},
    {'lat': 43.6577, 'lng': -79.4103, 'area': 'Little Italy'},
    {'lat': 43.6779, 'lng': -79.3499, 'area': 'The Beaches'},
    {'lat': 43.6426, 'lng': -79.4000, 'area': 'King West'},
    {'lat': 43.6629, 'lng': -79.3957, 'area': 'Midtown'},
    {'lat': 43.6532, 'lng': -79.3832, 'area': 'Financial District'},
]

def generate_phone_number():
    """Generate a realistic Toronto phone number"""
    return f"+1416555{random.randint(1000, 9999)}"

def get_random_location():
    """Get a random Toronto location"""
    return random.choice(TORONTO_LOCATIONS)

def create_service_provider_account(provider_data, category_slug):
    """Create a complete service provider account with user and profile"""
    try:
        # Create user account
        user = User.objects.create_user(
            email=provider_data['email'],
            password='VierlaTest123!',  # Standard test password
            first_name=provider_data['name'].split()[0],
            last_name=provider_data['name'].split()[-1],
            role='service_provider',
            phone=provider_data['phone'],
            is_verified=True,
            is_active=True
        )
        
        # Update user profile (created automatically by signal)
        profile = user.profile
        profile.address = provider_data['address']
        profile.city = provider_data['city']
        profile.state = provider_data['state']
        profile.zip_code = provider_data['zip_code']
        profile.country = 'Canada'
        profile.latitude = provider_data['latitude']
        profile.longitude = provider_data['longitude']
        profile.business_name = provider_data['business_name']
        profile.business_description = provider_data['description']
        profile.years_of_experience = provider_data['years_experience']
        profile.website = provider_data['website']
        profile.instagram = provider_data['instagram']
        profile.save()
        
        # Get the service category
        try:
            category = ServiceCategory.objects.get(slug=category_slug)
        except ServiceCategory.DoesNotExist:
            print(f"❌ Category '{category_slug}' not found. Skipping {provider_data['name']}")
            user.delete()  # Clean up
            return None
        
        # Create service provider profile
        service_provider = ServiceProvider.objects.create(
            user=user,
            business_name=provider_data['business_name'],
            business_description=provider_data['description'],
            business_phone=provider_data['phone'],
            business_email=provider_data['email'],
            address=provider_data['address'],
            city=provider_data['city'],
            state=provider_data['state'],
            zip_code=provider_data['zip_code'],
            country='Canada',
            latitude=provider_data['latitude'],
            longitude=provider_data['longitude'],
            website=provider_data['website'],
            instagram_handle=provider_data['instagram'],
            years_of_experience=provider_data['years_experience'],
            rating=provider_data['rating'],
            review_count=provider_data['review_count'],
            is_verified=True,
            is_active=True,
            is_featured=random.choice([True, False]),  # Random featured status
            total_bookings=random.randint(50, 500),
            mobile_optimized=True
        )
        
        # Add category to provider
        service_provider.categories.add(category)
        
        print(f"✅ Created service provider: {provider_data['business_name']} ({provider_data['name']})")
        return service_provider
        
    except Exception as e:
        print(f"❌ Error creating service provider {provider_data['name']}: {e}")
        return None

def main():
    """Main function to create all mock service providers"""
    print("🚀 Creating Mock Service Provider Accounts")
    print("=" * 60)
    
    created_count = 0
    total_count = 0
    
    # Process each category
    for category_slug, providers in MOCK_PROVIDERS_DATA.items():
        print(f"\n📂 Creating providers for category: {category_slug}")
        print("-" * 40)
        
        for provider_data in providers:
            total_count += 1
            provider = create_service_provider_account(provider_data, category_slug)
            if provider:
                created_count += 1
    
    print(f"\n📊 Summary")
    print("=" * 60)
    print(f"Total providers processed: {total_count}")
    print(f"Successfully created: {created_count}")
    print(f"Failed: {total_count - created_count}")
    
    if created_count > 0:
        print(f"\n🎉 Successfully created {created_count} service provider accounts!")
        print("📝 Test login credentials:")
        print("   Password: VierlaTest123!")
        print("   Example: <EMAIL> / VierlaTest123!")
    else:
        print("\n⚠️ No service providers were created. Please check the errors above.")

if __name__ == "__main__":
    main()
