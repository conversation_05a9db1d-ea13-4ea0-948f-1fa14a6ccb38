# Generated migration for populating service categories

from django.db import migrations
from django.utils.text import slugify
import uuid


def create_service_categories(apps, schema_editor):
    """Create the 8 main service categories for Vierla beauty services"""
    ServiceCategory = apps.get_model('catalog', 'ServiceCategory')
    
    categories = [
        {
            'name': 'Hair Styling',
            'icon': '💇‍♀️',
            'mobile_icon': '💇‍♀️',
            'description': 'Professional hair styling and cutting services including cuts, blowouts, and styling',
            'color': '#E91E63',  # Pink
            'is_popular': True,
            'sort_order': 1
        },
        {
            'name': 'Nail Care',
            'icon': '💅',
            'mobile_icon': '💅',
            'description': 'Manicure, pedicure, and nail art services for beautiful and healthy nails',
            'color': '#9C27B0',  # Purple
            'is_popular': True,
            'sort_order': 2
        },
        {
            'name': 'Lash Extensions',
            'icon': '👁️',
            'mobile_icon': '👁️',
            'description': 'Eyelash extensions and enhancement services for stunning eyes',
            'color': '#673AB7',  # Deep Purple
            'is_popular': True,
            'sort_order': 3
        },
        {
            'name': 'Braiding',
            'icon': '🎀',
            'mobile_icon': '🎀',
            'description': 'Hair braiding and protective styling services for all hair types',
            'color': '#3F51B5',  # Indigo
            'is_popular': True,
            'sort_order': 4
        },
        {
            'name': 'Makeup',
            'icon': '💄',
            'mobile_icon': '💄',
            'description': 'Professional makeup application services for all occasions',
            'color': '#2196F3',  # Blue
            'is_popular': True,
            'sort_order': 5
        },
        {
            'name': 'Skincare',
            'icon': '✨',
            'mobile_icon': '✨',
            'description': 'Facial treatments and skincare services for healthy, glowing skin',
            'color': '#00BCD4',  # Cyan
            'is_popular': True,
            'sort_order': 6
        },
        {
            'name': 'Massage',
            'icon': '💆‍♀️',
            'mobile_icon': '💆‍♀️',
            'description': 'Therapeutic and relaxation massage services for wellness and stress relief',
            'color': '#009688',  # Teal
            'is_popular': False,
            'sort_order': 7
        },
        {
            'name': 'Barbering',
            'icon': '✂️',
            'mobile_icon': '✂️',
            'description': 'Professional barbering and grooming services for men',
            'color': '#4CAF50',  # Green
            'is_popular': False,
            'sort_order': 8
        }
    ]
    
    created_count = 0
    for cat_data in categories:
        # Generate slug from name
        cat_data['slug'] = slugify(cat_data['name'])
        
        # Create category if it doesn't exist
        category, created = ServiceCategory.objects.get_or_create(
            name=cat_data['name'],
            defaults=cat_data
        )
        
        if created:
            created_count += 1
            print(f"Created category: {category.name}")
        else:
            print(f"Category already exists: {category.name}")
    
    print(f"Migration completed. Created {created_count} new categories.")


def reverse_service_categories(apps, schema_editor):
    """Remove the service categories"""
    ServiceCategory = apps.get_model('catalog', 'ServiceCategory')
    
    category_names = [
        'Hair Styling', 'Nail Care', 'Lash Extensions', 'Braiding',
        'Makeup', 'Skincare', 'Massage', 'Barbering'
    ]
    
    deleted_count = 0
    for name in category_names:
        deleted, _ = ServiceCategory.objects.filter(name=name).delete()
        if deleted:
            deleted_count += deleted
            print(f"Deleted category: {name}")
    
    print(f"Reverse migration completed. Deleted {deleted_count} categories.")


class Migration(migrations.Migration):

    dependencies = [
        ('catalog', '0003_searchhistory_searchhistory_unique_user_query_type'),
    ]

    operations = [
        migrations.RunPython(
            create_service_categories,
            reverse_service_categories,
            elidable=True,
        ),
    ]
