# Test Accounts Integration Guide - Frontend V1

## Overview

This guide explains how to use the comprehensive test accounts system that connects frontend_v1 with the backend test data. The system provides 34 pre-configured test accounts across all service categories for development and testing purposes.

## Test Accounts Summary

### Account Statistics
- **Total Accounts**: 34
- **Customer Accounts**: 4
- **Service Provider Accounts**: 30 (5 per category)

### Service Categories
- **Barber**: 5 providers
- **Salon**: 5 providers
- **Nail Services**: 5 providers
- **Lash Services**: 5 providers
- **Braiding**: 5 providers
- **Massage**: 5 providers
- **Skincare**: 5 providers

### Geographic Distribution
- **Ottawa**: 20 accounts
- **Toronto**: 14 accounts

## Quick Access Accounts

For rapid testing, use these pre-configured accounts:

### Customer Account
```
Email: <EMAIL>
Password: testpass123
Role: Customer
```

### Service Provider Accounts
```
Hair Services Provider:
Email: <EMAIL>
Password: TestPass123!
Business: Hair Services Studio 1

Nail Services Provider:
Email: <EMAIL>
Password: TestPass123!
Business: Nail Services Studio 1

Lash Services Provider:
Email: <EMAIL>
Password: TestPass123!
Business: Lash Services Studio 1
```

## Integration Methods

### 1. Using Test Accounts Service

```typescript
import { testAccountsService } from '../services/testAccountsService';

// Quick login with predefined accounts
const loginAsCustomer = async () => {
  const result = await testAccountsService.quickLogin('CUSTOMER');
  if (result.success) {
    console.log('Logged in as customer:', result.account);
  }
};

// Login with random account
const loginRandomProvider = async () => {
  const result = await testAccountsService.loginWithRandomAccount('service_provider');
  if (result.success) {
    console.log('Logged in as provider:', result.account);
  }
};

// Get accounts for specific testing scenarios
const getBookingTestAccounts = () => {
  return testAccountsService.getAccountsForScenario('booking');
};
```

### 2. Using Test Accounts Configuration

```typescript
import { 
  QUICK_LOGIN_ACCOUNTS,
  getTestAccountsByCategory,
  findTestAccountByEmail 
} from '../config/testAccounts';

// Access quick login accounts
const customerAccount = QUICK_LOGIN_ACCOUNTS.CUSTOMER;
const barberProvider = QUICK_LOGIN_ACCOUNTS.BARBER_PROVIDER;
const salonProvider = QUICK_LOGIN_ACCOUNTS.SALON_PROVIDER;

// Get all barber providers
const barberProviders = getTestAccountsByCategory('Barber');
// Get all salon providers
const salonProviders = getTestAccountsByCategory('Salon');

// Find specific account
const account = findTestAccountByEmail('<EMAIL>');
```

### 3. Using Development Panel (Development Only)

```typescript
import { TestAccountsPanel } from '../components/dev/TestAccountsPanel';

// Add to your development screen
const DevelopmentScreen = () => {
  const [showTestPanel, setShowTestPanel] = useState(false);

  return (
    <View>
      <Button 
        title="Open Test Accounts" 
        onPress={() => setShowTestPanel(true)} 
      />
      
      <TestAccountsPanel
        visible={showTestPanel}
        onClose={() => setShowTestPanel(false)}
        onLoginSuccess={(result) => {
          console.log('Login successful:', result);
        }}
      />
    </View>
  );
};
```

## Testing Scenarios

### Booking Flow Testing
```typescript
// Get appropriate accounts for booking testing
const { customer, provider } = testAccountsService.getAccountsForScenario('booking');

// Test booking flow
const testBookingFlow = async () => {
  // Login as customer
  await testAccountsService.loginWithTestAccount(customer);
  
  // Navigate to provider's services
  // Create booking
  // Test booking confirmation
  
  // Switch to provider account
  await testAccountsService.loginWithTestAccount(provider);
  
  // Test provider booking management
};
```

### Multi-Role Testing
```typescript
// Test messaging between customer and provider
const testMessaging = async () => {
  const accounts = testAccountsService.getAccountsForScenario('messaging');
  
  // Login as customer, send message
  await testAccountsService.loginWithTestAccount(accounts.customer);
  // Send message logic
  
  // Login as provider, respond to message
  await testAccountsService.loginWithTestAccount(accounts.provider);
  // Respond to message logic
};
```

### Category-Specific Testing
```typescript
// Test all providers in a specific category
const testBarberServices = async () => {
  const barberProviders = testAccountsService.getProvidersByCategory('Barber');

  for (const provider of barberProviders) {
    await testAccountsService.loginWithTestAccount(provider);
    // Test provider-specific functionality
  }
};

const testSalonServices = async () => {
  const salonProviders = testAccountsService.getProvidersByCategory('Salon');

  for (const provider of salonProviders) {
    await testAccountsService.loginWithTestAccount(provider);
    // Test provider-specific functionality
  }
};
```

## Backend Integration

### API Endpoints
The test accounts work with these backend endpoints:
- `POST /api/auth/login/` - Login with test credentials
- `POST /api/auth/register/` - Register new accounts (if needed)
- `GET /api/auth/profile/` - Get user profile data
- `GET /api/providers/` - Get service providers list
- `GET /api/services/` - Get services by provider

### Data Validation
All test accounts are pre-created in the backend with:
- ✅ Verified email addresses
- ✅ Complete profile information
- ✅ Associated services and pricing
- ✅ Geographic location data
- ✅ Business information (for providers)

## Development Workflow

### 1. Quick Development Setup
```typescript
// In your development component
useEffect(() => {
  if (__DEV__) {
    // Auto-login with test account for faster development
    testAccountsService.quickLogin('CUSTOMER');
  }
}, []);
```

### 2. Feature Testing
```typescript
// Test specific features with appropriate accounts
const testPaymentFlow = async () => {
  const accounts = testAccountsService.getAccountsForScenario('payments');
  
  // Test customer payment
  await testAccountsService.loginWithTestAccount(accounts.customer);
  // Payment flow testing
  
  // Test provider payout
  await testAccountsService.loginWithTestAccount(accounts.provider);
  // Payout flow testing
};
```

### 3. Automated Testing
```typescript
// Use in automated tests
describe('Booking Flow', () => {
  it('should complete booking flow', async () => {
    const { customer, provider } = testAccountsService.getAccountsForScenario('booking');
    
    // Test with real backend data
    const loginResult = await testAccountsService.loginWithTestAccount(customer);
    expect(loginResult.success).toBe(true);
    
    // Continue with booking flow tests
  });
});
```

## Security Considerations

### Development Only
- Test accounts are only available in development builds (`__DEV__ = true`)
- Production builds automatically disable test account functionality
- All test passwords use strong patterns for security awareness

### Data Isolation
- Test accounts use `.test` email domains
- Separate from production user data
- Can be safely reset or recreated

## Troubleshooting

### Common Issues

1. **Login Fails**
   - Verify backend is running on correct port (************:8000)
   - Check network connectivity
   - Ensure test accounts exist in backend database

2. **Test Mode Disabled**
   - Verify `__DEV__` is true
   - Check test mode setting: `testAccountsService.isTestModeActive()`

3. **Account Not Found**
   - Verify email spelling
   - Check if backend database has been reset
   - Run backend test account creation script

### Backend Setup
```bash
# Create test accounts in backend
cd backend
python scripts/create_test_accounts.py
python scripts/continue_test_accounts.py
```

### Verification
```typescript
// Verify test accounts are working
const verifyTestAccounts = async () => {
  const stats = testAccountsService.getAccountsStats();
  console.log('Test Accounts Available:', stats);
  
  // Test login
  const result = await testAccountsService.quickLogin('CUSTOMER');
  console.log('Test Login Result:', result.success);
};
```

## Best Practices

1. **Use Scenario-Based Accounts**: Use `getAccountsForScenario()` for feature-specific testing
2. **Reset Between Tests**: Clear test data between automated tests
3. **Log Account Usage**: Use the built-in logging for debugging
4. **Validate Responses**: Always check login results before proceeding
5. **Handle Errors**: Implement proper error handling for network issues

## Support

For issues with test accounts integration:
1. Check the test accounts service tests for usage examples
2. Verify backend test data exists
3. Review network configuration for API connectivity
4. Contact development team for backend-specific issues
