/**
 * Enhanced Provider Services Screen
 * Enhanced version with grid view, advanced filtering, and improved UX
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { LoadingSpinner } from '../../components/LoadingSpinner';
import { 
  ServiceList, 
  ServiceGrid, 
  ServiceFiltersComponent, 
  ViewToggle,
  ServiceFilters,
  ViewMode 
} from '../../components/provider';
import { providerServiceAPI, Service, ServiceCategory, ProviderSummary } from '../../services/api';

export const EnhancedProviderServicesScreen: React.FC = () => {
  const navigation = useNavigation();
  
  // State management
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [providerSummary, setProviderSummary] = useState<ProviderSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  
  // Filter state
  const [filters, setFilters] = useState<ServiceFilters>({
    search: '',
    category: null,
    status: 'all',
    priceRange: { min: null, max: null },
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // Computed values
  const filteredServices = useCallback(() => {
    let filtered = [...services];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(service =>
        service.name.toLowerCase().includes(searchLower) ||
        service.description.toLowerCase().includes(searchLower) ||
        service.category.name.toLowerCase().includes(searchLower)
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(service => service.category.id === filters.category);
    }

    // Status filter
    if (filters.status !== 'all') {
      const isActive = filters.status === 'active';
      filtered = filtered.filter(service => service.is_available === isActive);
    }

    // Price range filter
    if (filters.priceRange.min !== null) {
      filtered = filtered.filter(service => service.base_price >= filters.priceRange.min!);
    }
    if (filters.priceRange.max !== null) {
      filtered = filtered.filter(service => service.base_price <= filters.priceRange.max!);
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (filters.sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'base_price':
          aValue = a.base_price;
          bValue = b.base_price;
          break;
        case 'booking_count':
          aValue = a.booking_count;
          bValue = b.booking_count;
          break;
        case 'created_at':
        default:
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
      }

      if (filters.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [services, filters]);

  const activeFilterCount = useCallback(() => {
    let count = 0;
    if (filters.category) count++;
    if (filters.status !== 'all') count++;
    if (filters.priceRange.min !== null || filters.priceRange.max !== null) count++;
    if (filters.sortBy !== 'created_at' || filters.sortOrder !== 'desc') count++;
    return count;
  }, [filters]);

  // Data fetching
  const fetchData = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      
      const [servicesData, categoriesData, summaryData] = await Promise.all([
        providerServiceAPI.getServices(),
        providerServiceAPI.getCategories(),
        providerServiceAPI.getDashboardSummary(),
      ]);

      setServices(servicesData.results || servicesData);
      setCategories(categoriesData.results || categoriesData);
      setProviderSummary(summaryData);
    } catch (error) {
      console.error('Error fetching data:', error);
      Alert.alert('Error', 'Failed to load services. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchData(false);
  };

  // Service actions
  const handleServicePress = (service: Service) => {
    // Navigate to service details or edit
    navigation.navigate('EditService' as never, { serviceId: service.id } as never);
  };

  const handleEditService = (service: Service) => {
    navigation.navigate('EditService' as never, { serviceId: service.id } as never);
  };

  const handleToggleServiceStatus = async (service: Service) => {
    try {
      await providerServiceAPI.toggleServiceStatus(service.id);
      
      // Update local state optimistically
      setServices(prev => prev.map(s => 
        s.id === service.id 
          ? { ...s, is_available: !s.is_available }
          : s
      ));

      Alert.alert(
        'Success',
        `Service ${service.is_available ? 'deactivated' : 'activated'} successfully`
      );
    } catch (error) {
      console.error('Error toggling service status:', error);
      Alert.alert('Error', 'Failed to update service status. Please try again.');
    }
  };

  const handleDeleteService = (service: Service) => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await providerServiceAPI.deleteService(service.id);
              setServices(prev => prev.filter(s => s.id !== service.id));
              Alert.alert('Success', 'Service deleted successfully');
            } catch (error) {
              console.error('Error deleting service:', error);
              Alert.alert('Error', 'Failed to delete service. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleBulkAction = async (serviceIds: string[], action: 'activate' | 'deactivate' | 'delete') => {
    try {
      if (action === 'delete') {
        await Promise.all(serviceIds.map(id => providerServiceAPI.deleteService(id)));
        setServices(prev => prev.filter(s => !serviceIds.includes(s.id)));
      } else {
        const isActivating = action === 'activate';
        await Promise.all(serviceIds.map(id => 
          providerServiceAPI.updateService(id, { is_available: isActivating })
        ));
        setServices(prev => prev.map(s => 
          serviceIds.includes(s.id) 
            ? { ...s, is_available: isActivating }
            : s
        ));
      }

      Alert.alert('Success', `Bulk ${action} completed successfully`);
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      Alert.alert('Error', `Failed to perform bulk ${action}. Please try again.`);
    }
  };

  const handleAddService = () => {
    navigation.navigate('AddService' as never);
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: null,
      status: 'all',
      priceRange: { min: null, max: null },
      sortBy: 'created_at',
      sortOrder: 'desc',
    });
  };

  // Effects
  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  if (loading) {
    return <LoadingSpinner />;
  }

  const filtered = filteredServices();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>My Services</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddService}>
          <Icon name="add" size={20} color={colors.white} />
          <Text style={styles.addButtonText}>Add Service</Text>
        </TouchableOpacity>
      </View>

      {/* Provider Summary */}
      {providerSummary && (
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.total_services}</Text>
              <Text style={styles.summaryLabel}>Total</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.active_services}</Text>
              <Text style={styles.summaryLabel}>Active</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{providerSummary.inactive_services}</Text>
              <Text style={styles.summaryLabel}>Inactive</Text>
            </View>
          </View>
        </View>
      )}

      {/* Filters and View Toggle */}
      <View style={styles.controlsContainer}>
        <ServiceFiltersComponent
          filters={filters}
          categories={categories}
          onFiltersChange={setFilters}
          onClearFilters={clearFilters}
          activeFilterCount={activeFilterCount()}
        />
        <ViewToggle
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          style={styles.viewToggle}
        />
      </View>

      {/* Services List/Grid */}
      {viewMode === 'list' ? (
        <ServiceList
          services={filtered}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onServicePress={handleServicePress}
          onEditService={handleEditService}
          onToggleServiceStatus={handleToggleServiceStatus}
          onDeleteService={handleDeleteService}
          onBulkAction={handleBulkAction}
          selectable={true}
          emptyTitle="No Services Found"
          emptyDescription={filters.search || filters.category || filters.status !== 'all' 
            ? "Try adjusting your filters to see more services"
            : "Create your first service to get started"
          }
          emptyActionText="Add Service"
          onEmptyAction={handleAddService}
        />
      ) : (
        <ServiceGrid
          services={filtered}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onServicePress={handleServicePress}
          onEditService={handleEditService}
          onToggleServiceStatus={handleToggleServiceStatus}
          onDeleteService={handleDeleteService}
          onBulkAction={handleBulkAction}
          selectable={true}
          emptyTitle="No Services Found"
          emptyDescription={filters.search || filters.category || filters.status !== 'all' 
            ? "Try adjusting your filters to see more services"
            : "Create your first service to get started"
          }
          emptyActionText="Add Service"
          onEmptyAction={handleAddService}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...typography.h2,
    color: colors.textPrimary,
    fontWeight: '600',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  },
  addButtonText: {
    ...typography.caption,
    color: colors.white,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  summaryCard: {
    backgroundColor: colors.white,
    marginHorizontal: spacing.lg,
    marginVertical: spacing.md,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    ...typography.h2,
    color: colors.primary,
    fontWeight: '700',
    marginBottom: spacing.xs,
  },
  summaryLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  viewToggle: {
    marginLeft: spacing.md,
  },
});
