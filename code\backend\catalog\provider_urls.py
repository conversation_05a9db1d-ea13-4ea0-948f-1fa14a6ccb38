"""
URL configuration for Provider Service Management API
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .provider_views import ProviderServiceViewSet

# Create router and register provider viewsets
router = DefaultRouter()
router.register(r'services', ProviderServiceViewSet, basename='provider-service')

app_name = 'provider'

urlpatterns = [
    path('', include(router.urls)),
]
