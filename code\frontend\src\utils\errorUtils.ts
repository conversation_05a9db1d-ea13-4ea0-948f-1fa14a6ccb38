/**
 * Error Utilities for Vierla Application
 * 
 * This file contains utility functions for error handling, formatting,
 * and processing throughout the application.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import {
  AppError,
  ErrorType,
  ErrorSeverity,
  ErrorContext,
  ERROR_MESSAGES,
  createAppError,
  getErrorTypeFromStatus,
  getErrorSeverity,
} from './errorTypes';

/**
 * Format error message for user display
 */
export const formatErrorMessage = (error: AppError): string => {
  // Use user-friendly message if available
  if (error.userMessage) {
    return error.userMessage;
  }

  // Use predefined messages based on error type
  switch (error.type) {
    case ErrorType.NETWORK:
      if (error.message.includes('timeout')) {
        return ERROR_MESSAGES.NETWORK.TIMEOUT;
      }
      if (error.message.includes('offline') || error.message.includes('network')) {
        return ERROR_MESSAGES.NETWORK.OFFLINE;
      }
      return ERROR_MESSAGES.NETWORK.CONNECTION_FAILED;

    case ErrorType.AUTHENTICATION:
      if (error.message.includes('expired') || error.message.includes('token')) {
        return ERROR_MESSAGES.AUTHENTICATION.EXPIRED;
      }
      if (error.message.includes('credentials') || error.message.includes('password')) {
        return ERROR_MESSAGES.AUTHENTICATION.INVALID_CREDENTIALS;
      }
      if (error.message.includes('unauthorized')) {
        return ERROR_MESSAGES.AUTHENTICATION.UNAUTHORIZED;
      }
      return ERROR_MESSAGES.AUTHENTICATION.EXPIRED;

    case ErrorType.SERVER:
      if (error.message.includes('maintenance')) {
        return ERROR_MESSAGES.SERVER.MAINTENANCE;
      }
      if (error.message.includes('unavailable')) {
        return ERROR_MESSAGES.SERVER.SERVICE_UNAVAILABLE;
      }
      return ERROR_MESSAGES.SERVER.INTERNAL_ERROR;

    case ErrorType.CLIENT:
      if (error.message.includes('not found') || error.message.includes('404')) {
        return ERROR_MESSAGES.CLIENT.NOT_FOUND;
      }
      if (error.message.includes('forbidden') || error.message.includes('403')) {
        return ERROR_MESSAGES.CLIENT.FORBIDDEN;
      }
      return ERROR_MESSAGES.CLIENT.BAD_REQUEST;

    case ErrorType.VALIDATION:
      return error.message; // Validation messages are usually user-friendly

    default:
      return ERROR_MESSAGES.UNKNOWN.GENERIC;
  }
};

/**
 * Get error title based on type and severity
 */
export const getErrorTitle = (type: ErrorType, severity: ErrorSeverity): string => {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'Critical Error';
    case ErrorSeverity.HIGH:
      return 'Error';
    case ErrorSeverity.MEDIUM:
      return 'Something went wrong';
    case ErrorSeverity.LOW:
      return 'Notice';
    default:
      return 'Error';
  }
};

/**
 * Determine if error should be retryable
 */
export const isRetryableError = (error: AppError): boolean => {
  if (error.retryable === false) return false;
  
  switch (error.type) {
    case ErrorType.NETWORK:
    case ErrorType.SERVER:
      return true;
    case ErrorType.AUTHENTICATION:
    case ErrorType.CLIENT:
    case ErrorType.VALIDATION:
      return false;
    default:
      return false;
  }
};

/**
 * Get retry delay with exponential backoff
 */
export const getRetryDelay = (retryCount: number, baseDelay: number = 1000): number => {
  return Math.min(baseDelay * Math.pow(2, retryCount), 30000); // Max 30 seconds
};

/**
 * Provide haptic feedback based on error severity
 */
export const provideErrorHaptics = async (severity: ErrorSeverity): Promise<void> => {
  try {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        break;
      case ErrorSeverity.HIGH:
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        break;
      case ErrorSeverity.MEDIUM:
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case ErrorSeverity.LOW:
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
    }
  } catch (error) {
    // Haptics might not be available on all devices
    console.warn('Haptics not available:', error);
  }
};

/**
 * Log error for debugging and monitoring
 */
export const logError = (error: AppError): void => {
  const logData = {
    id: error.id,
    type: error.type,
    severity: error.severity,
    message: error.message,
    code: error.code,
    context: error.context,
    timestamp: new Date(error.timestamp).toISOString(),
    stack: error.originalError?.stack,
  };

  // In development, log to console
  if (__DEV__) {
    console.group(`🚨 Error [${error.severity.toUpperCase()}]`);
    console.error('Error Details:', logData);
    if (error.originalError) {
      console.error('Original Error:', error.originalError);
    }
    console.groupEnd();
  }

  // In production, send to error reporting service
  // TODO: Integrate with error reporting service (Sentry, Bugsnag, etc.)
  // errorReportingService.captureError(logData);
};

/**
 * Create error from network response
 */
export const createNetworkError = (
  response: Response | null,
  originalError?: Error,
  context?: ErrorContext
): AppError => {
  if (!response) {
    return createAppError(
      originalError || new Error('Network request failed'),
      ErrorType.NETWORK,
      ErrorSeverity.MEDIUM,
      context
    );
  }

  const type = getErrorTypeFromStatus(response.status);
  const severity = getErrorSeverity(type, response.status);
  
  let message = `HTTP ${response.status}: ${response.statusText}`;
  if (originalError) {
    message = originalError.message;
  }

  return createAppError(
    message,
    type,
    severity,
    {
      ...context,
      metadata: {
        ...context?.metadata,
        status: response.status,
        statusText: response.statusText,
        url: response.url,
      }
    }
  );
};

/**
 * Create validation error
 */
export const createValidationError = (
  field: string,
  message: string,
  context?: ErrorContext
): AppError => {
  return createAppError(
    message,
    ErrorType.VALIDATION,
    ErrorSeverity.LOW,
    {
      ...context,
      metadata: {
        ...context?.metadata,
        field,
      }
    }
  );
};

/**
 * Create authentication error
 */
export const createAuthError = (
  message: string = 'Authentication failed',
  context?: ErrorContext
): AppError => {
  return createAppError(
    message,
    ErrorType.AUTHENTICATION,
    ErrorSeverity.HIGH,
    context
  );
};

/**
 * Show native alert for critical errors
 */
export const showCriticalErrorAlert = (
  error: AppError,
  onRetry?: () => void,
  onReport?: () => void
): void => {
  const title = getErrorTitle(error.type, error.severity);
  const message = formatErrorMessage(error);
  
  const buttons = [];
  
  if (onRetry && isRetryableError(error)) {
    buttons.push({
      text: 'Retry',
      onPress: onRetry,
    });
  }
  
  if (onReport && error.severity === ErrorSeverity.CRITICAL) {
    buttons.push({
      text: 'Report Issue',
      onPress: onReport,
    });
  }
  
  buttons.push({
    text: 'OK',
    style: 'cancel' as const,
  });

  Alert.alert(title, message, buttons);
};

/**
 * Handle async operation with error handling
 */
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context?: ErrorContext
): Promise<{ data: T | null; error: AppError | null }> => {
  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    const appError = createAppError(
      error instanceof Error ? error : new Error(String(error)),
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      context
    );
    logError(appError);
    return { data: null, error: appError };
  }
};

/**
 * Debounce error reporting to prevent spam
 */
const errorReportingCache = new Map<string, number>();
const ERROR_REPORTING_DEBOUNCE = 5000; // 5 seconds

export const shouldReportError = (error: AppError): boolean => {
  const key = `${error.type}_${error.message}`;
  const lastReported = errorReportingCache.get(key);
  const now = Date.now();
  
  if (!lastReported || now - lastReported > ERROR_REPORTING_DEBOUNCE) {
    errorReportingCache.set(key, now);
    return true;
  }
  
  return false;
};
