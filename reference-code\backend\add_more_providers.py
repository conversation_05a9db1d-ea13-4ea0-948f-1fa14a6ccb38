#!/usr/bin/env python3
"""
Add more service providers to ensure each category has at least 3 providers
with distinct configurations covering Toronto and Ottawa locations.
"""

from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from django.contrib.auth import get_user_model
import os
import sys
import django
from decimal import Decimal
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()


User = get_user_model()


# Toronto and Ottawa locations
TORONTO_LOCATIONS = [
    {
        'address': '123 Queen Street West',
        'city': 'Toronto',
        'state': 'Ontario',
        'zip_code': 'M5H 2M9',
        'latitude': Decimal('43.6532'),
        'longitude': Decimal('-79.3832')
    },
    {
        'address': '456 King Street East',
        'city': 'Toronto',
        'state': 'Ontario',
        'zip_code': 'M5A 1L1',
        'latitude': Decimal('43.6426'),
        'longitude': Decimal('-79.3656')
    },
    {
        'address': '789 Bloor Street West',
        'city': 'Toronto',
        'state': 'Ontario',
        'zip_code': 'M6G 1K7',
        'latitude': Decimal('43.6629'),
        'longitude': Decimal('-79.4103')
    },
    {
        'address': '321 Yonge Street',
        'city': 'Toronto',
        'state': 'Ontario',
        'zip_code': 'M5B 1R7',
        'latitude': Decimal('43.6565'),
        'longitude': Decimal('-79.3799')
    }
]

OTTAWA_LOCATIONS = [
    {
        'address': '234 Sparks Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1P 5B5',
        'latitude': Decimal('45.4215'),
        'longitude': Decimal('-75.6972')
    },
    {
        'address': '567 Bank Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1S 3T4',
        'latitude': Decimal('45.4042'),
        'longitude': Decimal('-75.6903')
    },
    {
        'address': '890 Rideau Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1N 5Y4',
        'latitude': Decimal('45.4274'),
        'longitude': Decimal('-75.6812')
    },
    {
        'address': '135 Somerset Street West',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K2P 0J4',
        'latitude': Decimal('45.4112'),
        'longitude': Decimal('-75.6934')
    }
]

# Provider templates for each category
PROVIDER_TEMPLATES = {
    'Hair Services': [
        {'name': 'Elite Hair Studio',
            'description': 'Premium hair styling and coloring services with expert stylists'},
        {'name': 'Modern Cuts Salon',
            'description': 'Contemporary hair salon specializing in trendy cuts and styles'},
        {'name': 'Hair Artistry',
            'description': 'Creative hair design studio offering unique styling solutions'},
        {'name': 'Luxe Hair Lounge',
            'description': 'Upscale salon providing luxury hair care and styling services'},
    ],
    'Nail Services': [
        {'name': 'Perfect Nails Spa',
            'description': 'Professional nail care and artistic nail designs'},
        {'name': 'Glamour Nails',
            'description': 'Full-service nail salon with gel, acrylic, and natural nail care'},
        {'name': 'Nail Boutique',
            'description': 'Boutique nail salon specializing in custom nail art and manicures'},
        {'name': 'Polished Perfection',
            'description': 'High-end nail salon offering premium nail treatments'},
    ],
    'Lash Services': [
        {'name': 'Lash Extensions Pro',
            'description': 'Expert lash extension services with premium materials'},
        {'name': 'Beautiful Lashes',
            'description': 'Professional lash lifting, tinting, and extension services'},
        {'name': 'Lash Studio',
            'description': 'Specialized lash studio offering volume and classic extensions'},
        {'name': 'Eye Candy Lashes',
            'description': 'Luxury lash services with personalized styling'},
    ],
    'Braiding': [
        {'name': 'Braids & Beyond',
            'description': 'Expert braiding services for all hair types and styles'},
        {'name': 'African Hair Artistry',
            'description': 'Traditional and modern braiding techniques'},
        {'name': 'Braid Masters',
            'description': 'Professional braiding salon with creative styling options'},
        {'name': 'Crown Braiding Studio',
            'description': 'Specialized braiding services with protective styling focus'},
    ],
    'Locs & Twists': [
        {'name': 'Loc Love Studio',
            'description': 'Professional loc maintenance and styling services'},
        {'name': 'Twist & Loc Salon',
            'description': 'Expert loc installation, maintenance, and creative styling'},
        {'name': 'Natural Hair Haven',
            'description': 'Specialized in natural hair care, locs, and twist styles'},
        {'name': 'Loc Artistry',
            'description': 'Creative loc styling and maintenance with artistic flair'},
    ],
    'Makeup': [
        {'name': 'Glam Makeup Studio',
            'description': 'Professional makeup services for all occasions'},
        {'name': 'Beauty Bar',
            'description': 'Full-service makeup studio with bridal and event specialization'},
        {'name': 'Makeup Artistry',
            'description': 'Creative makeup services with artistic and editorial focus'},
        {'name': 'Perfect Face Studio',
            'description': 'Professional makeup application and beauty consultations'},
    ],
    'Makeup Services': [
        {'name': 'Glam Makeup Studio',
            'description': 'Professional makeup services for all occasions'},
        {'name': 'Beauty Bar',
            'description': 'Full-service makeup studio with bridal and event specialization'},
        {'name': 'Makeup Artistry',
            'description': 'Creative makeup services with artistic and editorial focus'},
        {'name': 'Perfect Face Studio',
            'description': 'Professional makeup application and beauty consultations'},
    ],
    'Hair & Beauty': [
        {'name': 'Elite Hair Studio',
            'description': 'Premium hair styling and coloring services with expert stylists'},
        {'name': 'Modern Cuts Salon',
            'description': 'Contemporary hair salon specializing in trendy cuts and styles'},
        {'name': 'Hair Artistry',
            'description': 'Creative hair design studio offering unique styling solutions'},
        {'name': 'Luxe Hair Lounge',
            'description': 'Upscale salon providing luxury hair care and styling services'},
    ],
    'Skincare & Facials': [
        {'name': 'Glow Skincare Studio',
            'description': 'Professional facial treatments and skincare consultations'},
        {'name': 'Radiant Skin Spa',
            'description': 'Luxury spa offering advanced facial treatments and skincare'},
        {'name': 'Clear Skin Clinic',
            'description': 'Medical-grade skincare treatments and acne solutions'},
        {'name': 'Facial Bliss',
            'description': 'Relaxing facial treatments with organic and natural products'},
    ],
    'Massage Therapy': [
        {'name': 'Zen Massage Studio',
            'description': 'Therapeutic massage services for relaxation and wellness'},
        {'name': 'Healing Touch Spa',
            'description': 'Professional massage therapy for pain relief and stress reduction'},
        {'name': 'Muscle Relief Clinic',
            'description': 'Sports massage and deep tissue therapy specialists'},
        {'name': 'Tranquil Massage',
            'description': 'Swedish and hot stone massage in a peaceful environment'},
    ]
}


def create_provider_user(provider_data, location, category_name):
    """Create a user account for the provider"""
    username = provider_data['name'].lower().replace(
        ' ', '_').replace('&', 'and')
    email = f"{username}@example.com"

    # Check if user already exists
    if User.objects.filter(email=email).exists():
        return None

    user = User.objects.create_user(
        username=username,
        email=email,
        password='password123',
        first_name=provider_data['name'].split()[0],
        last_name=provider_data['name'].split(
        )[-1] if len(provider_data['name'].split()) > 1 else 'Studio',
        role='service_provider',
        is_active=True
    )
    return user


def create_service_provider(provider_data, location, category):
    """Create a service provider"""
    user = create_provider_user(provider_data, location, category.name)
    if not user:
        return None

    provider = ServiceProvider.objects.create(
        user=user,
        business_name=provider_data['name'],
        business_description=provider_data['description'],
        business_phone=f"+1{random.randint(**********, **********)}",
        business_email=user.email,
        address=location['address'],
        city=location['city'],
        state=location['state'],
        zip_code=location['zip_code'],
        country='Canada',
        latitude=location['latitude'],
        longitude=location['longitude'],
        rating=Decimal(str(round(random.uniform(4.0, 5.0), 1))),
        review_count=random.randint(15, 150),
        total_bookings=random.randint(50, 500),
        years_of_experience=random.randint(2, 15),
        is_verified=True,
        is_featured=random.choice([True, False]),
        is_active=True,
        mobile_optimized=True
    )

    # Add to category
    provider.categories.add(category)

    return provider


def main():
    print("🚀 ADDING MORE SERVICE PROVIDERS")
    print("=" * 60)

    categories = ServiceCategory.objects.all()
    if not categories.exists():
        print("❌ No categories found. Please create categories first.")
        return

    print(f"📋 Found {categories.count()} categories")

    total_created = 0
    all_locations = TORONTO_LOCATIONS + OTTAWA_LOCATIONS

    for category in categories:
        current_count = ServiceProvider.objects.filter(
            categories=category).count()
        needed = max(0, 3 - current_count)

        print(f"\n🏪 Category: {category.name}")
        print(f"   Current providers: {current_count}")
        print(f"   Need to add: {needed}")

        if needed == 0:
            print("   ✅ Already has enough providers")
            continue

        # Get provider templates for this category
        templates = PROVIDER_TEMPLATES.get(category.name, [])
        if not templates:
            print(f"   ⚠️ No templates for {category.name}")
            continue

        # Create providers
        for i in range(needed):
            if i >= len(templates):
                break

            template = templates[i]
            location = all_locations[i % len(all_locations)]

            provider = create_service_provider(template, location, category)
            if provider:
                total_created += 1
                print(
                    f"   ✅ Created: {provider.business_name} in {provider.city}")
            else:
                print(f"   ❌ Failed to create: {template['name']}")

    print(f"\n" + "=" * 60)
    print(f"📊 SUMMARY")
    print(f"=" * 60)
    print(f"✅ Created {total_created} new service providers")

    # Print final statistics
    print(f"\n📈 FINAL TOTALS:")
    for category in categories:
        count = ServiceProvider.objects.filter(categories=category).count()
        print(f"   {category.name}: {count} providers")

    print(f"\n   👥 Total Users: {User.objects.count()}")
    print(f"   🏪 Total Providers: {ServiceProvider.objects.count()}")


if __name__ == '__main__':
    main()
