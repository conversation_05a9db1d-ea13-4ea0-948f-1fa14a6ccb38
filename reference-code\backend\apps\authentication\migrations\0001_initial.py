# Generated by Django 4.2.16 on 2025-06-17 22:12

from django.conf import settings
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.Char<PERSON>ield(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                        verbose_name="active",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=254, unique=True, verbose_name="email address"
                    ),
                ),
                (
                    "phone",
                    models.CharField(
                        blank=True,
                        help_text="Phone number in international format",
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="phone number",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("customer", "Customer"),
                            ("service_provider", "Service Provider"),
                            ("admin", "Admin"),
                        ],
                        default="customer",
                        max_length=20,
                        verbose_name="user role",
                    ),
                ),
                (
                    "avatar",
                    models.ImageField(
                        blank=True,
                        help_text="Profile picture",
                        null=True,
                        upload_to="avatars/%Y/%m/",
                        verbose_name="avatar",
                    ),
                ),
                (
                    "date_of_birth",
                    models.DateField(
                        blank=True, null=True, verbose_name="date of birth"
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        blank=True,
                        help_text="Brief description about yourself",
                        max_length=500,
                        verbose_name="biography",
                    ),
                ),
                (
                    "account_status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("suspended", "Suspended"),
                            ("pending_verification", "Pending Verification"),
                        ],
                        default="pending_verification",
                        max_length=20,
                        verbose_name="account status",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether this user has verified their email address.",
                        verbose_name="verified",
                    ),
                ),
                (
                    "email_verified_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="email verified at"
                    ),
                ),
                (
                    "phone_verified_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="phone verified at"
                    ),
                ),
                (
                    "device_token",
                    models.TextField(
                        blank=True,
                        help_text="Push notification device token",
                        verbose_name="device token",
                    ),
                ),
                (
                    "preferred_language",
                    models.CharField(
                        default="en",
                        help_text="User preferred language code",
                        max_length=10,
                        verbose_name="preferred language",
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        default="UTC",
                        help_text="User timezone",
                        max_length=50,
                        verbose_name="timezone",
                    ),
                ),
                (
                    "email_notifications",
                    models.BooleanField(
                        default=True, verbose_name="email notifications"
                    ),
                ),
                (
                    "sms_notifications",
                    models.BooleanField(
                        default=False, verbose_name="SMS notifications"
                    ),
                ),
                (
                    "push_notifications",
                    models.BooleanField(
                        default=True, verbose_name="push notifications"
                    ),
                ),
                (
                    "two_factor_enabled",
                    models.BooleanField(
                        default=False, verbose_name="two factor authentication"
                    ),
                ),
                (
                    "last_password_change",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last password change"
                    ),
                ),
                (
                    "failed_login_attempts",
                    models.PositiveIntegerField(
                        default=0, verbose_name="failed login attempts"
                    ),
                ),
                (
                    "account_locked_until",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="account locked until"
                    ),
                ),
                (
                    "is_test_account",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as test/mock account for development",
                        verbose_name="test account",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="created at"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "last_activity",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last activity"
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "db_table": "users",
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True, max_length=255, verbose_name="address"
                    ),
                ),
                (
                    "city",
                    models.CharField(blank=True, max_length=100, verbose_name="city"),
                ),
                (
                    "state",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="state/province"
                    ),
                ),
                (
                    "zip_code",
                    models.CharField(
                        blank=True, max_length=20, verbose_name="zip/postal code"
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True,
                        default="Canada",
                        max_length=100,
                        verbose_name="country",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        help_text="Latitude coordinate for location-based services",
                        max_digits=9,
                        null=True,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        help_text="Longitude coordinate for location-based services",
                        max_digits=9,
                        null=True,
                        verbose_name="longitude",
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True,
                        help_text="Business name for service providers",
                        max_length=200,
                        verbose_name="business name",
                    ),
                ),
                (
                    "business_description",
                    models.TextField(
                        blank=True,
                        help_text="Description of services offered",
                        verbose_name="business description",
                    ),
                ),
                (
                    "years_of_experience",
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name="years of experience"
                    ),
                ),
                ("website", models.URLField(blank=True, verbose_name="website")),
                (
                    "instagram",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Instagram handle"
                    ),
                ),
                ("facebook", models.URLField(blank=True, verbose_name="Facebook page")),
                (
                    "search_radius",
                    models.PositiveIntegerField(
                        default=25,
                        help_text="Preferred search radius for finding services",
                        verbose_name="search radius (km)",
                    ),
                ),
                (
                    "auto_accept_bookings",
                    models.BooleanField(
                        default=False,
                        help_text="Automatically accept booking requests (providers only)",
                        verbose_name="auto accept bookings",
                    ),
                ),
                (
                    "show_phone_publicly",
                    models.BooleanField(
                        default=False, verbose_name="show phone publicly"
                    ),
                ),
                (
                    "show_email_publicly",
                    models.BooleanField(
                        default=False, verbose_name="show email publicly"
                    ),
                ),
                (
                    "allow_reviews",
                    models.BooleanField(default=True, verbose_name="allow reviews"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="updated at"),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "user_profiles",
                "indexes": [
                    models.Index(
                        fields=["city", "state"], name="user_profil_city_c8a47f_idx"
                    ),
                    models.Index(
                        fields=["latitude", "longitude"],
                        name="user_profil_latitud_505ce2_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        max_length=255, unique=True, verbose_name="reset token"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Password Reset Token",
                "verbose_name_plural": "Password Reset Tokens",
                "db_table": "password_reset_tokens",
                "indexes": [
                    models.Index(fields=["token"], name="password_re_token_060a1f_idx"),
                    models.Index(
                        fields=["expires_at"], name="password_re_expires_8e96b7_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.CharField(
                        max_length=255, unique=True, verbose_name="verification token"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="created at"),
                ),
                ("expires_at", models.DateTimeField(verbose_name="expires at")),
                ("is_used", models.BooleanField(default=False, verbose_name="is used")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Token",
                "verbose_name_plural": "Email Verification Tokens",
                "db_table": "email_verification_tokens",
                "indexes": [
                    models.Index(fields=["token"], name="email_verif_token_df7c5e_idx"),
                    models.Index(
                        fields=["expires_at"], name="email_verif_expires_770728_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["email"], name="users_email_4b85f2_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["role"], name="users_role_0ace22_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(
                fields=["account_status"], name="users_account_33da0c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["created_at"], name="users_created_6541e9_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["is_verified"], name="users_is_veri_63cd6e_idx"),
        ),
    ]
