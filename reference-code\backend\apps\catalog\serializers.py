"""
Service Catalog serializers for Vierla Beauty Services Marketplace
Enhanced Django REST Framework serializers with mobile-first design
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    ServiceCategory, ServiceProvider, Service, OperatingHours,
    ServiceAvailability, ServiceGallery, ServiceLocation
)

User = get_user_model()


class ServiceCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for service categories with hierarchical support
    Mobile-optimized with essential fields
    """

    subcategories = serializers.SerializerMethodField()
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    service_count = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'slug', 'description', 'icon', 'color', 'image',
            'parent', 'parent_name', 'is_popular', 'is_active', 'sort_order',
            'mobile_icon', 'subcategories', 'service_count', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def get_subcategories(self, obj):
        """Get subcategories for hierarchical display"""
        if hasattr(obj, 'subcategories'):
            subcategories = obj.subcategories.filter(is_active=True)
            return ServiceCategorySerializer(subcategories, many=True, context=self.context).data
        return []

    def get_service_count(self, obj):
        """Get count of active services in this category"""
        return obj.services.filter(is_active=True, is_available=True).count()


class ServiceCategoryListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for category lists (mobile optimization)
    """

    service_count = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'slug', 'icon', 'color', 'mobile_icon',
            'is_popular', 'service_count'
        ]

    def get_service_count(self, obj):
        """Get count of active services in this category"""
        return obj.services.filter(is_active=True, is_available=True).count()


class OperatingHoursSerializer(serializers.ModelSerializer):
    """
    Serializer for provider operating hours
    """

    day_display = serializers.CharField(
        source='get_day_display', read_only=True)
    is_24_hours = serializers.BooleanField(read_only=True)
    has_break = serializers.BooleanField(read_only=True)

    class Meta:
        model = OperatingHours
        fields = [
            'id', 'day', 'day_display', 'is_open', 'open_time', 'close_time',
            'break_start', 'break_end', 'notes', 'is_24_hours', 'has_break'
        ]
        read_only_fields = ['id']

    def validate(self, data):
        """Validate operating hours logic"""
        if data.get('is_open', True):
            if not data.get('open_time') or not data.get('close_time'):
                raise serializers.ValidationError(
                    "Open and close times are required when provider is open"
                )

            # Validate break times if provided
            break_start = data.get('break_start')
            break_end = data.get('break_end')

            if break_start and break_end:
                if break_start >= break_end:
                    raise serializers.ValidationError(
                        "Break start time must be before break end time"
                    )

                open_time = data.get('open_time')
                close_time = data.get('close_time')

                if break_start < open_time or break_end > close_time:
                    raise serializers.ValidationError(
                        "Break times must be within operating hours"
                    )

        return data


class ServiceGallerySerializer(serializers.ModelSerializer):
    """
    Serializer for service gallery images
    """

    image_url = serializers.SerializerMethodField()
    display_name = serializers.CharField(read_only=True)

    class Meta:
        model = ServiceGallery
        fields = [
            'id', 'image', 'image_url', 'image_type', 'caption', 'alt_text',
            'is_featured', 'is_cover', 'sort_order', 'display_name', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

    def get_image_url(self, obj):
        """Get full URL for image"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None


class ServiceLocationSerializer(serializers.ModelSerializer):
    """
    Serializer for service location information
    """

    location_type_display = serializers.CharField(
        source='get_location_type_display', read_only=True)
    is_mobile_service = serializers.BooleanField(read_only=True)
    is_virtual_service = serializers.BooleanField(read_only=True)
    has_travel_fee = serializers.BooleanField(read_only=True)

    class Meta:
        model = ServiceLocation
        fields = [
            'id', 'location_type', 'location_type_display', 'travel_radius',
            'travel_fee', 'service_areas', 'virtual_platform', 'location_notes',
            'is_mobile_service', 'is_virtual_service', 'has_travel_fee'
        ]
        read_only_fields = ['id']

    def validate(self, data):
        """Validate location configuration"""
        location_type = data.get('location_type')

        if location_type in ['mobile', 'both']:
            if not data.get('travel_radius'):
                raise serializers.ValidationError(
                    "Travel radius is required for mobile services"
                )

        if location_type == 'virtual':
            if not data.get('virtual_platform'):
                raise serializers.ValidationError(
                    "Virtual platform is required for virtual services"
                )

        return data


class ServiceAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for service availability settings
    """

    availability_type_display = serializers.CharField(
        source='get_availability_type_display', read_only=True)
    earliest_booking_time = serializers.DateTimeField(
        source='get_earliest_booking_time', read_only=True)
    latest_booking_time = serializers.DateTimeField(
        source='get_latest_booking_time', read_only=True)

    class Meta:
        model = ServiceAvailability
        fields = [
            'id', 'availability_type', 'availability_type_display',
            'min_advance_booking', 'max_advance_booking', 'max_bookings_per_day',
            'max_bookings_per_slot', 'cancellation_hours', 'weekend_available',
            'holiday_available', 'instant_booking', 'earliest_booking_time',
            'latest_booking_time'
        ]
        read_only_fields = ['id']

    def validate(self, data):
        """Validate availability settings"""
        min_advance = data.get('min_advance_booking', 0)
        max_advance = data.get('max_advance_booking', 90)

        if min_advance >= max_advance * 24:  # Convert days to hours
            raise serializers.ValidationError(
                "Minimum advance booking must be less than maximum advance booking"
            )

        return data


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for individual services
    Mobile-optimized with comprehensive information
    """

    provider_name = serializers.CharField(
        source='provider.business_name', read_only=True)
    category_name = serializers.CharField(
        source='category.name', read_only=True)
    category_icon = serializers.CharField(
        source='category.icon', read_only=True)
    display_price = serializers.CharField(read_only=True)
    display_duration = serializers.CharField(read_only=True)
    total_duration_with_buffer = serializers.IntegerField(read_only=True)
    image_url = serializers.SerializerMethodField()

    # Related objects
    availability = ServiceAvailabilitySerializer(read_only=True)
    location_info = ServiceLocationSerializer(read_only=True)
    gallery = ServiceGallerySerializer(many=True, read_only=True)

    class Meta:
        model = Service
        fields = [
            'id', 'name', 'description', 'short_description', 'base_price',
            'price_type', 'max_price', 'display_price', 'duration', 'buffer_time',
            'display_duration', 'total_duration_with_buffer', 'image', 'image_url',
            'is_popular', 'is_available', 'is_active', 'requirements',
            'preparation_instructions', 'mobile_description', 'booking_count',
            'category', 'provider_name', 'category_name', 'category_icon', 'availability',
            'location_info', 'gallery', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'booking_count', 'created_at', 'updated_at']

    def get_image_url(self, obj):
        """Get full URL for service image"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

    def validate(self, data):
        """Validate service data"""
        price_type = data.get('price_type', 'fixed')
        base_price = data.get('base_price')
        max_price = data.get('max_price')

        if price_type == 'range' and max_price:
            if max_price <= base_price:
                raise serializers.ValidationError(
                    "Maximum price must be greater than base price for range pricing"
                )

        # Only validate duration if it's being updated
        if 'duration' in data:
            duration = data.get('duration')
            if duration <= 0:
                raise serializers.ValidationError(
                    "Service duration must be greater than 0 minutes"
                )

        return data


class ServiceListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for service lists (mobile optimization)
    """

    provider_name = serializers.CharField(
        source='provider.business_name', read_only=True)
    provider_rating = serializers.DecimalField(
        source='provider.rating', max_digits=3, decimal_places=2, read_only=True)
    category_name = serializers.CharField(
        source='category.name', read_only=True)
    category_icon = serializers.CharField(
        source='category.icon', read_only=True)
    display_price = serializers.CharField(read_only=True)
    display_duration = serializers.CharField(read_only=True)
    image_url = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = [
            'id', 'name', 'short_description', 'display_price', 'display_duration',
            'image_url', 'is_popular', 'is_available', 'provider_name',
            'provider_rating', 'category_name', 'category_icon', 'distance'
        ]

    def get_image_url(self, obj):
        """Get full URL for service image"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

    def get_distance(self, obj):
        """Get distance from user location (if provided in context)"""
        distances = self.context.get('distances', {})
        return distances.get(str(obj.id))


class ServiceProviderSerializer(serializers.ModelSerializer):
    """
    Comprehensive serializer for service providers
    Mobile-optimized with all necessary information
    """

    user_name = serializers.CharField(
        source='user.get_full_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    full_address = serializers.CharField(read_only=True)
    has_location = serializers.BooleanField(read_only=True)
    display_rating = serializers.CharField(read_only=True)
    profile_image_url = serializers.SerializerMethodField()
    cover_image_url = serializers.SerializerMethodField()

    # Related objects
    categories = ServiceCategoryListSerializer(many=True, read_only=True)
    services = ServiceListSerializer(many=True, read_only=True)
    operating_hours = OperatingHoursSerializer(many=True, read_only=True)
    gallery = ServiceGallerySerializer(many=True, read_only=True)

    class Meta:
        model = ServiceProvider
        fields = [
            'id', 'business_name', 'business_description', 'business_phone',
            'business_email', 'address', 'city', 'state', 'zip_code', 'country',
            'latitude', 'longitude', 'full_address', 'has_location', 'website',
            'instagram_handle', 'facebook_url', 'profile_image', 'profile_image_url',
            'cover_image', 'cover_image_url', 'is_verified', 'is_featured',
            'is_active', 'rating', 'review_count', 'display_rating',
            'total_bookings', 'years_of_experience', 'mobile_optimized',
            'user_name', 'user_email', 'categories', 'services', 'operating_hours',
            'gallery', 'created_at', 'updated_at',
            # Store customization fields
            'store_theme_primary_color', 'store_theme_accent_color', 'store_theme_layout',
            'display_show_prices', 'display_show_duration', 'display_show_ratings', 'display_show_availability',
            'portfolio_type', 'portfolio_instagram_max_posts', 'portfolio_show_instagram_captions', 'portfolio_layout'
        ]
        read_only_fields = [
            'id', 'rating', 'review_count', 'total_bookings', 'created_at', 'updated_at'
        ]

    def get_profile_image_url(self, obj):
        """Get full URL for profile image"""
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

    def get_cover_image_url(self, obj):
        """Get full URL for cover image"""
        if obj.cover_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.cover_image.url)
            return obj.cover_image.url
        return None

    def validate(self, data):
        """Validate provider data"""
        latitude = data.get('latitude')
        longitude = data.get('longitude')

        if latitude is not None and longitude is not None:
            if not (-90 <= latitude <= 90):
                raise serializers.ValidationError(
                    "Latitude must be between -90 and 90 degrees"
                )
            if not (-180 <= longitude <= 180):
                raise serializers.ValidationError(
                    "Longitude must be between -180 and 180 degrees"
                )

        return data


class ServiceProviderListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for provider lists (mobile optimization)
    """

    display_rating = serializers.CharField(read_only=True)
    profile_image_url = serializers.SerializerMethodField()
    category_names = serializers.SerializerMethodField()
    service_count = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()

    class Meta:
        model = ServiceProvider
        fields = [
            'id', 'business_name', 'business_description', 'city', 'state',
            'latitude', 'longitude', 'profile_image_url', 'is_verified',
            'is_featured', 'rating', 'review_count', 'display_rating',
            'category_names', 'service_count', 'distance'
        ]

    def get_profile_image_url(self, obj):
        """Get full URL for profile image"""
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

    def get_category_names(self, obj):
        """Get list of category names"""
        return [category.name for category in obj.categories.filter(is_active=True)]

    def get_service_count(self, obj):
        """Get count of active services"""
        return obj.services.filter(is_active=True, is_available=True).count()

    def get_distance(self, obj):
        """Get distance from user location (if provided in context)"""
        user_location = self.context.get('user_location')
        if user_location and obj.has_location:
            # This would be calculated in the view using PostGIS or similar
            # For now, return None as placeholder
            return self.context.get('distances', {}).get(str(obj.id))
        return None


class ServiceProviderCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating service provider profiles
    """

    class Meta:
        model = ServiceProvider
        fields = [
            'business_name', 'business_description', 'business_phone',
            'business_email', 'address', 'city', 'state', 'zip_code', 'country',
            'latitude', 'longitude', 'website', 'instagram_handle', 'facebook_url',
            'profile_image', 'cover_image', 'years_of_experience', 'mobile_optimized'
        ]

    def validate(self, data):
        """Validate provider creation data"""
        # Ensure user doesn't already have a provider profile
        user = self.context['request'].user
        if hasattr(user, 'provider_profile'):
            raise serializers.ValidationError(
                "User already has a service provider profile"
            )

        # Validate coordinates if provided
        latitude = data.get('latitude')
        longitude = data.get('longitude')

        if latitude is not None and longitude is not None:
            if not (-90 <= latitude <= 90):
                raise serializers.ValidationError(
                    "Latitude must be between -90 and 90 degrees"
                )
            if not (-180 <= longitude <= 180):
                raise serializers.ValidationError(
                    "Longitude must be between -180 and 180 degrees"
                )

        return data

    def create(self, validated_data):
        """Create provider profile and associate with user"""
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)
