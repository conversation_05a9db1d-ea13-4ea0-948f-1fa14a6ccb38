/**
 * Error Handling System Tests
 * Tests for the standardized error handling utilities and components
 */

import {
  createAppError,
  formatErrorMessage,
  getErrorTitle,
  isRetryableError,
  getRetryDelay,
  createNetworkError,
  createValidationError,
  createAuthError,
  ErrorType,
  ErrorSeverity,
  ERROR_MESSAGES,
} from '../../utils/errorTypes';

import {
  logError,
  provideErrorHaptics,
  withErrorHandling,
  shouldReportError,
} from '../../utils/errorUtils';

// Mock Expo Haptics
import * as Haptics from 'expo-haptics';
jest.mock('expo-haptics');
const mockedHaptics = Haptics as jest.Mocked<typeof Haptics>;

describe('Error Handling System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear console mocks
    (console.error as jest.Mock).mockClear();
    (console.warn as jest.Mock).mockClear();
  });

  describe('Error Creation', () => {
    it('should create AppError with correct properties', () => {
      const error = new Error('Test error');
      const context = {
        screen: 'TestScreen',
        component: 'TestComponent',
        action: 'testAction',
      };

      const appError = createAppError(error, ErrorType.NETWORK, ErrorSeverity.HIGH, context);

      expect(appError.type).toBe(ErrorType.NETWORK);
      expect(appError.severity).toBe(ErrorSeverity.HIGH);
      expect(appError.message).toBe('Test error');
      expect(appError.originalError).toBe(error);
      expect(appError.context).toBe(context);
      expect(appError.retryable).toBe(true);
      expect(appError.retryCount).toBe(0);
      expect(appError.id).toBeDefined();
      expect(appError.timestamp).toBeDefined();
    });

    it('should create AppError from string message', () => {
      const message = 'String error message';
      const appError = createAppError(message, ErrorType.VALIDATION, ErrorSeverity.LOW);

      expect(appError.message).toBe(message);
      expect(appError.originalError).toBeUndefined();
      expect(appError.type).toBe(ErrorType.VALIDATION);
      expect(appError.severity).toBe(ErrorSeverity.LOW);
    });

    it('should create network error with response', () => {
      const mockResponse = {
        status: 500,
        statusText: 'Internal Server Error',
        url: 'http://example.com/api/test',
      } as Response;

      const context = { screen: 'TestScreen' };
      const networkError = createNetworkError(mockResponse, undefined, context);

      expect(networkError.type).toBe(ErrorType.SERVER);
      expect(networkError.severity).toBe(ErrorSeverity.HIGH);
      expect(networkError.context?.metadata?.status).toBe(500);
      expect(networkError.context?.metadata?.statusText).toBe('Internal Server Error');
      expect(networkError.context?.metadata?.url).toBe('http://example.com/api/test');
    });

    it('should create validation error', () => {
      const field = 'email';
      const message = 'Invalid email format';
      const validationError = createValidationError(field, message);

      expect(validationError.type).toBe(ErrorType.VALIDATION);
      expect(validationError.severity).toBe(ErrorSeverity.LOW);
      expect(validationError.message).toBe(message);
      expect(validationError.context?.metadata?.field).toBe(field);
    });

    it('should create authentication error', () => {
      const message = 'Session expired';
      const authError = createAuthError(message);

      expect(authError.type).toBe(ErrorType.AUTHENTICATION);
      expect(authError.severity).toBe(ErrorSeverity.HIGH);
      expect(authError.message).toBe(message);
    });
  });

  describe('Error Formatting', () => {
    it('should format network error messages', () => {
      const networkError = createAppError(
        'Connection timeout',
        ErrorType.NETWORK,
        ErrorSeverity.MEDIUM
      );

      const formatted = formatErrorMessage(networkError);
      expect(formatted).toBe(ERROR_MESSAGES.NETWORK.TIMEOUT);
    });

    it('should format authentication error messages', () => {
      const authError = createAppError(
        'Token expired',
        ErrorType.AUTHENTICATION,
        ErrorSeverity.HIGH
      );

      const formatted = formatErrorMessage(authError);
      expect(formatted).toBe(ERROR_MESSAGES.AUTHENTICATION.EXPIRED);
    });

    it('should use user message when available', () => {
      const error = createAppError('Technical error', ErrorType.UNKNOWN, ErrorSeverity.MEDIUM);
      error.userMessage = 'User-friendly message';

      const formatted = formatErrorMessage(error);
      expect(formatted).toBe('User-friendly message');
    });

    it('should get appropriate error titles', () => {
      expect(getErrorTitle(ErrorType.NETWORK, ErrorSeverity.CRITICAL)).toBe('Critical Error');
      expect(getErrorTitle(ErrorType.AUTHENTICATION, ErrorSeverity.HIGH)).toBe('Error');
      expect(getErrorTitle(ErrorType.VALIDATION, ErrorSeverity.MEDIUM)).toBe('Something went wrong');
      expect(getErrorTitle(ErrorType.UNKNOWN, ErrorSeverity.LOW)).toBe('Notice');
    });
  });

  describe('Error Retry Logic', () => {
    it('should identify retryable errors', () => {
      const networkError = createAppError('Network failed', ErrorType.NETWORK, ErrorSeverity.MEDIUM);
      const serverError = createAppError('Server error', ErrorType.SERVER, ErrorSeverity.HIGH);
      const authError = createAppError('Auth failed', ErrorType.AUTHENTICATION, ErrorSeverity.HIGH);
      const validationError = createAppError('Invalid input', ErrorType.VALIDATION, ErrorSeverity.LOW);

      expect(isRetryableError(networkError)).toBe(true);
      expect(isRetryableError(serverError)).toBe(true);
      expect(isRetryableError(authError)).toBe(false);
      expect(isRetryableError(validationError)).toBe(false);
    });

    it('should calculate retry delay with exponential backoff', () => {
      expect(getRetryDelay(0, 1000)).toBe(1000);
      expect(getRetryDelay(1, 1000)).toBe(2000);
      expect(getRetryDelay(2, 1000)).toBe(4000);
      expect(getRetryDelay(3, 1000)).toBe(8000);
      expect(getRetryDelay(10, 1000)).toBe(30000); // Max 30 seconds
    });

    it('should respect non-retryable flag', () => {
      const error = createAppError('Network failed', ErrorType.NETWORK, ErrorSeverity.MEDIUM);
      error.retryable = false;

      expect(isRetryableError(error)).toBe(false);
    });
  });

  describe('Error Logging', () => {
    it('should log error in development mode', () => {
      const error = createAppError('Test error', ErrorType.NETWORK, ErrorSeverity.MEDIUM);
      
      logError(error);

      expect(console.error).toHaveBeenCalled();
    });

    it('should include error context in logs', () => {
      const error = createAppError(
        'Test error',
        ErrorType.NETWORK,
        ErrorSeverity.MEDIUM,
        {
          screen: 'TestScreen',
          component: 'TestComponent',
          metadata: { userId: '123' },
        }
      );

      logError(error);

      expect(console.error).toHaveBeenCalledWith(
        'Error Details:',
        expect.objectContaining({
          type: ErrorType.NETWORK,
          severity: ErrorSeverity.MEDIUM,
          context: expect.objectContaining({
            screen: 'TestScreen',
            component: 'TestComponent',
            metadata: { userId: '123' },
          }),
        })
      );
    });
  });

  describe('Haptic Feedback', () => {
    it('should provide appropriate haptic feedback for different severities', async () => {
      await provideErrorHaptics(ErrorSeverity.CRITICAL);
      expect(mockedHaptics.notificationAsync).toHaveBeenCalledWith(
        Haptics.NotificationFeedbackType.Error
      );

      await provideErrorHaptics(ErrorSeverity.HIGH);
      expect(mockedHaptics.notificationAsync).toHaveBeenCalledWith(
        Haptics.NotificationFeedbackType.Warning
      );

      await provideErrorHaptics(ErrorSeverity.MEDIUM);
      expect(mockedHaptics.impactAsync).toHaveBeenCalledWith(
        Haptics.ImpactFeedbackStyle.Medium
      );

      await provideErrorHaptics(ErrorSeverity.LOW);
      expect(mockedHaptics.impactAsync).toHaveBeenCalledWith(
        Haptics.ImpactFeedbackStyle.Light
      );
    });

    it('should handle haptic errors gracefully', async () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      mockedHaptics.notificationAsync.mockRejectedValue(new Error('Haptics not available'));

      await provideErrorHaptics(ErrorSeverity.HIGH);

      expect(consoleWarnSpy).toHaveBeenCalledWith('Haptics not available:', expect.any(Error));
      consoleWarnSpy.mockRestore();
    });
  });

  describe('Error Handling Wrapper', () => {
    it('should handle successful operations', async () => {
      const successfulOperation = async () => 'success result';
      const context = { screen: 'TestScreen' };

      const { data, error } = await withErrorHandling(successfulOperation, context);

      expect(data).toBe('success result');
      expect(error).toBeNull();
    });

    it('should handle failed operations', async () => {
      const failedOperation = async () => {
        throw new Error('Operation failed');
      };
      const context = { screen: 'TestScreen' };

      const { data, error } = await withErrorHandling(failedOperation, context);

      expect(data).toBeNull();
      expect(error).toBeDefined();
      expect(error?.message).toBe('Operation failed');
      expect(error?.context).toBe(context);
    });

    it('should handle non-Error exceptions', async () => {
      const failedOperation = async () => {
        throw 'String error';
      };

      const { data, error } = await withErrorHandling(failedOperation);

      expect(data).toBeNull();
      expect(error).toBeDefined();
      expect(error?.message).toBe('String error');
    });
  });

  describe('Error Reporting', () => {
    it('should allow error reporting', () => {
      const error1 = createAppError('Test error', ErrorType.NETWORK, ErrorSeverity.MEDIUM);
      const error2 = createAppError('Test error', ErrorType.NETWORK, ErrorSeverity.MEDIUM);

      expect(shouldReportError(error1)).toBe(true);
      expect(shouldReportError(error2)).toBe(false); // Same error type and message
    });

    it('should allow reporting after debounce period', () => {
      jest.useFakeTimers();
      
      const error1 = createAppError('Test error', ErrorType.NETWORK, ErrorSeverity.MEDIUM);
      const error2 = createAppError('Test error', ErrorType.NETWORK, ErrorSeverity.MEDIUM);

      expect(shouldReportError(error1)).toBe(true);
      expect(shouldReportError(error2)).toBe(false);

      // Fast forward time
      jest.advanceTimersByTime(6000); // 6 seconds

      expect(shouldReportError(error2)).toBe(true);

      jest.useRealTimers();
    });
  });
});
