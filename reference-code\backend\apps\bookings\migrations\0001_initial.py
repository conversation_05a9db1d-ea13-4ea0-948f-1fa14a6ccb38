# Generated by Django 4.2.16 on 2025-06-19 03:20

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("catalog", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Booking",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When this record was created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When this record was last updated"
                    ),
                ),
                (
                    "booking_number",
                    models.CharField(
                        editable=False,
                        help_text="Human-readable booking number",
                        max_length=20,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Confirmation"),
                            ("confirmed", "Confirmed"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        default="pending",
                        help_text="Current booking status",
                        max_length=20,
                    ),
                ),
                (
                    "payment_status",
                    models.CharField(
                        choices=[
                            ("pending", "Payment Pending"),
                            ("paid", "Paid"),
                            ("partially_paid", "Partially Paid"),
                            ("refunded", "Refunded"),
                            ("failed", "Payment Failed"),
                        ],
                        default="pending",
                        help_text="Payment status",
                        max_length=20,
                    ),
                ),
                (
                    "scheduled_datetime",
                    models.DateTimeField(
                        help_text="Scheduled date and time for the service"
                    ),
                ),
                (
                    "duration_minutes",
                    models.PositiveIntegerField(
                        help_text="Duration of the service in minutes"
                    ),
                ),
                (
                    "end_datetime",
                    models.DateTimeField(
                        blank=True,
                        help_text="Calculated end time (scheduled_datetime + duration)",
                        null=True,
                    ),
                ),
                (
                    "location_type",
                    models.CharField(
                        choices=[
                            ("salon", "At Salon"),
                            ("mobile", "Mobile Service"),
                            ("online", "Online Service"),
                        ],
                        default="salon",
                        help_text="Type of service location",
                        max_length=10,
                    ),
                ),
                (
                    "service_address",
                    models.TextField(
                        blank=True, help_text="Address where service will be performed"
                    ),
                ),
                (
                    "service_latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        help_text="Latitude for mobile services",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "service_longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=7,
                        help_text="Longitude for mobile services",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base service price",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "additional_charges",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Additional charges (travel, materials, etc.)",
                        max_digits=10,
                    ),
                ),
                (
                    "discount_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Discount applied",
                        max_digits=10,
                    ),
                ),
                (
                    "tax_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Tax amount",
                        max_digits=10,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total amount to be paid",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.01"))
                        ],
                    ),
                ),
                (
                    "customer_notes",
                    models.TextField(
                        blank=True, help_text="Special instructions from customer"
                    ),
                ),
                (
                    "provider_notes",
                    models.TextField(
                        blank=True, help_text="Notes from service provider"
                    ),
                ),
                (
                    "internal_notes",
                    models.TextField(
                        blank=True, help_text="Internal notes for admin use"
                    ),
                ),
                ("confirmed_at", models.DateTimeField(blank=True, null=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("cancelled_at", models.DateTimeField(blank=True, null=True)),
                (
                    "cancellation_reason",
                    models.TextField(blank=True, help_text="Reason for cancellation"),
                ),
                (
                    "is_mobile_optimized",
                    models.BooleanField(
                        default=True, help_text="Optimized for mobile display"
                    ),
                ),
                (
                    "cancelled_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who cancelled the booking",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="cancelled_bookings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        help_text="Customer who made the booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="customer_bookings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider for this booking",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="provider_bookings",
                        to="catalog.serviceprovider",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        help_text="Service being booked",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bookings",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking",
                "verbose_name_plural": "Bookings",
                "db_table": "bookings_booking",
                "ordering": ["-scheduled_datetime", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BookingStateChange",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When this record was created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When this record was last updated"
                    ),
                ),
                (
                    "from_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Confirmation"),
                            ("confirmed", "Confirmed"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        help_text="Previous status",
                        max_length=20,
                    ),
                ),
                (
                    "to_status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Confirmation"),
                            ("confirmed", "Confirmed"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                            ("rescheduled", "Rescheduled"),
                        ],
                        help_text="New status",
                        max_length=20,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes about the state change"
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        help_text="Booking that changed state",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="state_changes",
                        to="bookings.booking",
                    ),
                ),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who made the change",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="booking_state_changes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking State Change",
                "verbose_name_plural": "Booking State Changes",
                "db_table": "bookings_state_change",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BookingNotification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When this record was created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When this record was last updated"
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("booking_created", "Booking Created"),
                            ("booking_confirmed", "Booking Confirmed"),
                            ("booking_cancelled", "Booking Cancelled"),
                            ("booking_reminder", "Booking Reminder"),
                            ("booking_started", "Booking Started"),
                            ("booking_completed", "Booking Completed"),
                            ("payment_required", "Payment Required"),
                            ("payment_received", "Payment Received"),
                        ],
                        help_text="Type of notification",
                        max_length=20,
                    ),
                ),
                (
                    "channel",
                    models.CharField(
                        choices=[
                            ("push", "Push Notification"),
                            ("email", "Email"),
                            ("sms", "SMS"),
                            ("in_app", "In-App Notification"),
                        ],
                        help_text="Channel to send notification through",
                        max_length=10,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("read", "Read"),
                        ],
                        default="pending",
                        help_text="Status of the notification",
                        max_length=10,
                    ),
                ),
                (
                    "title",
                    models.CharField(help_text="Notification title", max_length=200),
                ),
                ("message", models.TextField(help_text="Notification message")),
                (
                    "scheduled_for",
                    models.DateTimeField(
                        blank=True,
                        help_text="When to send this notification (for reminders)",
                        null=True,
                    ),
                ),
                (
                    "sent_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the notification was sent",
                        null=True,
                    ),
                ),
                (
                    "read_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the notification was read",
                        null=True,
                    ),
                ),
                (
                    "external_id",
                    models.CharField(
                        blank=True,
                        help_text="External service notification ID",
                        max_length=100,
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error message if sending failed"
                    ),
                ),
                (
                    "booking",
                    models.ForeignKey(
                        help_text="Booking this notification is about",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notifications",
                        to="bookings.booking",
                    ),
                ),
                (
                    "recipient",
                    models.ForeignKey(
                        help_text="User who should receive this notification",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="booking_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Booking Notification",
                "verbose_name_plural": "Booking Notifications",
                "db_table": "bookings_notification",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TimeSlot",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text="Unique identifier",
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, help_text="When this record was created"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When this record was last updated"
                    ),
                ),
                ("date", models.DateField(help_text="Date of the time slot")),
                ("start_time", models.TimeField(help_text="Start time of the slot")),
                ("end_time", models.TimeField(help_text="End time of the slot")),
                (
                    "duration_minutes",
                    models.PositiveIntegerField(
                        help_text="Duration of the slot in minutes"
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this slot is available for booking",
                    ),
                ),
                (
                    "max_bookings",
                    models.PositiveIntegerField(
                        default=1, help_text="Maximum number of bookings for this slot"
                    ),
                ),
                (
                    "current_bookings",
                    models.PositiveIntegerField(
                        default=0, help_text="Current number of bookings for this slot"
                    ),
                ),
                (
                    "override_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Override price for this specific time slot",
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "is_break",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is a break/unavailable slot",
                    ),
                ),
                (
                    "is_recurring",
                    models.BooleanField(
                        default=False, help_text="Whether this slot repeats weekly"
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Internal notes about this time slot"
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider offering this time slot",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_slots",
                        to="catalog.serviceprovider",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        blank=True,
                        help_text="Specific service (optional, can be general availability)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_slots",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Time Slot",
                "verbose_name_plural": "Time Slots",
                "db_table": "bookings_time_slot",
                "ordering": ["date", "start_time"],
                "indexes": [
                    models.Index(
                        fields=["provider", "date", "is_available"],
                        name="bookings_ti_provide_3f3b82_idx",
                    ),
                    models.Index(
                        fields=["date", "start_time"],
                        name="bookings_ti_date_e208a2_idx",
                    ),
                    models.Index(
                        fields=["service", "date"],
                        name="bookings_ti_service_c25be6_idx",
                    ),
                ],
            },
        ),
        migrations.AddConstraint(
            model_name="timeslot",
            constraint=models.CheckConstraint(
                check=models.Q(("end_time__gt", models.F("start_time"))),
                name="end_time_after_start_time",
            ),
        ),
        migrations.AddConstraint(
            model_name="timeslot",
            constraint=models.CheckConstraint(
                check=models.Q(("current_bookings__lte", models.F("max_bookings"))),
                name="bookings_within_limit",
            ),
        ),
        migrations.AddIndex(
            model_name="bookingstatechange",
            index=models.Index(
                fields=["booking", "created_at"], name="bookings_st_booking_184ac6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookingstatechange",
            index=models.Index(
                fields=["to_status", "created_at"],
                name="bookings_st_to_stat_354cef_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bookingnotification",
            index=models.Index(
                fields=["booking", "notification_type"],
                name="bookings_no_booking_e08869_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bookingnotification",
            index=models.Index(
                fields=["recipient", "status"], name="bookings_no_recipie_f787b4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bookingnotification",
            index=models.Index(
                fields=["scheduled_for", "status"],
                name="bookings_no_schedul_82a655_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="bookingnotification",
            index=models.Index(
                fields=["channel", "status"], name="bookings_no_channel_5ed462_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["customer", "status"], name="bookings_bo_custome_0cbf77_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["provider", "scheduled_datetime"],
                name="bookings_bo_provide_60b1fc_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["status", "scheduled_datetime"],
                name="bookings_bo_status_ebb0d4_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["scheduled_datetime", "status"],
                name="bookings_bo_schedul_5a0fcb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["booking_number"], name="bookings_bo_booking_03d631_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="booking",
            index=models.Index(
                fields=["payment_status"], name="bookings_bo_payment_27706e_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="booking",
            constraint=models.CheckConstraint(
                check=models.Q(("total_amount__gt", 0)), name="positive_total_amount"
            ),
        ),
        migrations.AddConstraint(
            model_name="booking",
            constraint=models.CheckConstraint(
                check=models.Q(("duration_minutes__gt", 0)), name="positive_duration"
            ),
        ),
        migrations.AddConstraint(
            model_name="booking",
            constraint=models.CheckConstraint(
                check=models.Q(("base_price__gte", 0)), name="non_negative_base_price"
            ),
        ),
    ]
