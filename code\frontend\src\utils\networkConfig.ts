/**
 * Network Configuration Utility
 * Helps with network configuration and connectivity testing
 */

import { apiClient, API_BASE_URL } from '../services/api/client';

/**
 * Test API connectivity
 */
export const testApiConnectivity = async (): Promise<{
  isConnected: boolean;
  latency?: number;
  error?: string;
}> => {
  const startTime = Date.now();
  
  try {
    // Test with a simple health check endpoint
    const response = await apiClient.get('/health/', { timeout: 5000 });
    const latency = Date.now() - startTime;
    
    return {
      isConnected: response.status === 200,
      latency,
    };
  } catch (error: any) {
    return {
      isConnected: false,
      error: error.message || 'Connection failed',
    };
  }
};

/**
 * Get current API configuration
 */
export const getApiConfig = () => {
  return {
    baseURL: API_BASE_URL,
    isDevelopment: __DEV__,
    timeout: 10000,
  };
};

/**
 * Suggest alternative API URLs for development
 */
export const getAlternativeApiUrls = (): string[] => {
  if (!__DEV__) {
    return [];
  }
  
  return [
    'http://localhost:8000/api',
    'http://127.0.0.1:8000/api',
    'http://********:8000/api', // Android emulator host
    'http://*************:8000/api', // Common local network
    'http://*************:8000/api', // Common local network
  ];
};

/**
 * Test multiple API URLs to find working one
 */
export const findWorkingApiUrl = async (): Promise<{
  workingUrl?: string;
  results: Array<{ url: string; success: boolean; latency?: number; error?: string }>;
}> => {
  const urls = [API_BASE_URL, ...getAlternativeApiUrls()];
  const results: Array<{ url: string; success: boolean; latency?: number; error?: string }> = [];
  let workingUrl: string | undefined;
  
  for (const url of urls) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${url}/health/`, {
        method: 'GET',
        timeout: 3000,
      });
      
      const latency = Date.now() - startTime;
      const success = response.ok;
      
      results.push({
        url,
        success,
        latency,
      });
      
      if (success && !workingUrl) {
        workingUrl = url;
      }
    } catch (error: any) {
      results.push({
        url,
        success: false,
        error: error.message,
      });
    }
  }
  
  return { workingUrl, results };
};

/**
 * Get network troubleshooting suggestions
 */
export const getNetworkTroubleshootingSuggestions = (): string[] => {
  const suggestions = [
    'Check your internet connection',
    'Ensure the backend server is running',
  ];
  
  if (__DEV__) {
    suggestions.push(
      'Verify the backend is running on the correct port (8000)',
      'Check if your device/emulator can reach the backend IP address',
      'Try using localhost (127.0.0.1) if testing on the same machine',
      'For Android emulator, try using ******** instead of localhost',
      'Check firewall settings that might block the connection',
      'Verify CORS settings in the backend allow your frontend origin'
    );
  }
  
  return suggestions;
};

/**
 * Log network configuration for debugging
 */
export const logNetworkConfig = () => {
  const config = getApiConfig();
  
  console.log('=== Network Configuration ===');
  console.log('Base URL:', config.baseURL);
  console.log('Development Mode:', config.isDevelopment);
  console.log('Timeout:', config.timeout);
  
  if (config.isDevelopment) {
    console.log('Alternative URLs:', getAlternativeApiUrls());
  }
  
  console.log('==============================');
};

/**
 * Create a network diagnostic report
 */
export const createNetworkDiagnosticReport = async (): Promise<{
  timestamp: string;
  config: ReturnType<typeof getApiConfig>;
  connectivity: Awaited<ReturnType<typeof testApiConnectivity>>;
  alternativeUrls?: Awaited<ReturnType<typeof findWorkingApiUrl>>;
  suggestions: string[];
}> => {
  const timestamp = new Date().toISOString();
  const config = getApiConfig();
  const connectivity = await testApiConnectivity();
  
  let alternativeUrls;
  if (!connectivity.isConnected && config.isDevelopment) {
    alternativeUrls = await findWorkingApiUrl();
  }
  
  const suggestions = getNetworkTroubleshootingSuggestions();
  
  return {
    timestamp,
    config,
    connectivity,
    alternativeUrls,
    suggestions,
  };
};
