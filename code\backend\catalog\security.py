"""
Security utilities for test account management and isolation
Provides security measures to ensure test accounts are properly isolated
"""
import os
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.contrib.auth import get_user_model
from functools import wraps

User = get_user_model()


class TestAccountSecurity:
    """Security utilities for test account management"""
    
    @staticmethod
    def is_development_environment():
        """Check if we're in a development environment"""
        # Check multiple environment indicators
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        testing_mode = 'test' in os.environ.get('DJANGO_SETTINGS_MODULE', '').lower()
        
        # Allow in development, testing, or when DEBUG is True
        allowed_environments = ['development', 'testing', 'local', 'dev']
        return (
            environment.lower() in allowed_environments or
            debug_mode or
            testing_mode
        )
    
    @staticmethod
    def is_production_environment():
        """Check if we're in a production environment"""
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        
        # Production indicators
        production_environments = ['production', 'prod', 'live']
        return (
            environment.lower() in production_environments and
            not debug_mode
        )
    
    @staticmethod
    def validate_test_account_operation():
        """Validate that test account operations are allowed"""
        if TestAccountSecurity.is_production_environment():
            raise PermissionDenied(
                "Test account operations are not allowed in production environment"
            )
    
    @staticmethod
    def get_test_account_domain():
        """Get the domain used for test accounts"""
        return getattr(settings, 'TEST_ACCOUNT_DOMAIN', 'test.com')
    
    @staticmethod
    def is_test_account_email(email):
        """Check if an email belongs to a test account"""
        test_domain = TestAccountSecurity.get_test_account_domain()
        return email.endswith(f'@{test_domain}')
    
    @staticmethod
    def validate_test_account_email(email):
        """Validate that an email is appropriate for test accounts"""
        if not TestAccountSecurity.is_test_account_email(email):
            raise ValueError(f"Test account emails must use domain: {TestAccountSecurity.get_test_account_domain()}")
    
    @staticmethod
    def get_test_account_queryset():
        """Get queryset for test accounts only"""
        return User.objects.filter(is_test_account=True)
    
    @staticmethod
    def cleanup_test_accounts_on_production():
        """Emergency cleanup of test accounts if found in production"""
        if TestAccountSecurity.is_production_environment():
            test_accounts = TestAccountSecurity.get_test_account_queryset()
            if test_accounts.exists():
                count = test_accounts.count()
                test_accounts.delete()
                return count
        return 0


def require_development_environment(func):
    """Decorator to require development environment for function execution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        TestAccountSecurity.validate_test_account_operation()
        return func(*args, **kwargs)
    return wrapper


def test_account_only(func):
    """Decorator to ensure function only operates on test accounts"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        TestAccountSecurity.validate_test_account_operation()
        return func(*args, **kwargs)
    return wrapper


class TestAccountMiddleware:
    """Middleware to handle test account security"""
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Check for test accounts in production
        if TestAccountSecurity.is_production_environment():
            # Log and cleanup any test accounts found in production
            cleaned_count = TestAccountSecurity.cleanup_test_accounts_on_production()
            if cleaned_count > 0:
                # Log this security event
                import logging
                logger = logging.getLogger('security')
                logger.warning(f"Cleaned up {cleaned_count} test accounts found in production")
        
        response = self.get_response(request)
        return response


class TestAccountAuthenticationBackend:
    """Custom authentication backend for test accounts with additional security"""
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        """Authenticate test accounts with additional security checks"""
        if not TestAccountSecurity.is_development_environment():
            # Don't authenticate test accounts in production
            return None
        
        try:
            user = User.objects.get(email=username, is_test_account=True)
            if user.check_password(password):
                return user
        except User.DoesNotExist:
            pass
        
        return None
    
    def get_user(self, user_id):
        """Get test user with security checks"""
        if not TestAccountSecurity.is_development_environment():
            return None
        
        try:
            return User.objects.get(pk=user_id, is_test_account=True)
        except User.DoesNotExist:
            return None


class TestAccountManager:
    """Manager for test account operations with security"""
    
    @staticmethod
    @require_development_environment
    def create_test_user(**kwargs):
        """Create a test user with security validation"""
        email = kwargs.get('email')
        if email:
            TestAccountSecurity.validate_test_account_email(email)
        
        kwargs['is_test_account'] = True
        return User.objects.create_user(**kwargs)
    
    @staticmethod
    @require_development_environment
    def get_test_accounts():
        """Get all test accounts"""
        return TestAccountSecurity.get_test_account_queryset()
    
    @staticmethod
    @require_development_environment
    def cleanup_test_accounts():
        """Clean up all test accounts"""
        test_accounts = TestAccountSecurity.get_test_account_queryset()
        count = test_accounts.count()
        test_accounts.delete()
        return count
    
    @staticmethod
    def validate_environment():
        """Validate current environment for test account operations"""
        return TestAccountSecurity.is_development_environment()


# Environment-specific settings validation
def validate_test_account_settings():
    """Validate test account related settings"""
    issues = []
    
    # Check if test accounts are properly configured
    if TestAccountSecurity.is_production_environment():
        # In production, ensure no test account settings are enabled
        if hasattr(settings, 'TEST_ACCOUNT_DOMAIN'):
            issues.append("TEST_ACCOUNT_DOMAIN should not be set in production")
        
        # Check for any test accounts in production
        if User.objects.filter(is_test_account=True).exists():
            issues.append("Test accounts found in production database")
    
    else:
        # In development, ensure test account settings are properly configured
        if not hasattr(settings, 'TEST_ACCOUNT_DOMAIN'):
            issues.append("TEST_ACCOUNT_DOMAIN should be set in development")
    
    return issues


# Security audit functions
def audit_test_accounts():
    """Audit test accounts for security issues"""
    audit_results = {
        'environment': 'production' if TestAccountSecurity.is_production_environment() else 'development',
        'test_accounts_count': User.objects.filter(is_test_account=True).count(),
        'issues': [],
        'recommendations': []
    }
    
    # Check for test accounts in production
    if TestAccountSecurity.is_production_environment():
        if audit_results['test_accounts_count'] > 0:
            audit_results['issues'].append(
                f"Found {audit_results['test_accounts_count']} test accounts in production"
            )
            audit_results['recommendations'].append(
                "Remove all test accounts from production database immediately"
            )
    
    # Check for test accounts with production-like emails
    test_accounts_bad_emails = User.objects.filter(
        is_test_account=True
    ).exclude(
        email__endswith=f'@{TestAccountSecurity.get_test_account_domain()}'
    )
    
    if test_accounts_bad_emails.exists():
        audit_results['issues'].append(
            f"Found {test_accounts_bad_emails.count()} test accounts with non-test email domains"
        )
        audit_results['recommendations'].append(
            f"Ensure all test accounts use @{TestAccountSecurity.get_test_account_domain()} domain"
        )
    
    # Check for unverified test accounts
    unverified_test_accounts = User.objects.filter(
        is_test_account=True,
        is_verified=False
    )
    
    if unverified_test_accounts.exists():
        audit_results['issues'].append(
            f"Found {unverified_test_accounts.count()} unverified test accounts"
        )
        audit_results['recommendations'].append(
            "Verify all test accounts for proper functionality"
        )
    
    return audit_results


# Production safety check
def production_safety_check():
    """Perform production safety check for test accounts"""
    if not TestAccountSecurity.is_production_environment():
        return {"status": "skipped", "reason": "Not in production environment"}
    
    issues = []
    actions_taken = []
    
    # Check for test accounts
    test_accounts = User.objects.filter(is_test_account=True)
    if test_accounts.exists():
        count = test_accounts.count()
        issues.append(f"Found {count} test accounts in production")
        
        # Auto-cleanup if enabled
        if getattr(settings, 'AUTO_CLEANUP_TEST_ACCOUNTS_IN_PRODUCTION', False):
            test_accounts.delete()
            actions_taken.append(f"Automatically deleted {count} test accounts")
        else:
            actions_taken.append("Test accounts found but auto-cleanup is disabled")
    
    return {
        "status": "completed",
        "issues": issues,
        "actions_taken": actions_taken,
        "test_accounts_found": len(issues) > 0
    }
