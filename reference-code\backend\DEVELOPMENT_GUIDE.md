# Vierla Backend Development Guide

## 👨‍💻 **Development Workflow & Standards**

This guide provides comprehensive development standards, workflows, and best practices for the Vierla Beauty Services Marketplace backend development team using Django 4.2.16 + DRF architecture.

**Target Audience**: Backend developers, code reviewers, DevOps engineers  
**Technology Stack**: Django 4.2.16 + DRF + PostgreSQL + Redis + Celery
**Code Standards**: PEP 8 + Black + isort + mypy + Django best practices

---

## 🚀 **Development Environment Setup**

### **Prerequisites**
```bash
# Required Software
- Python 3.12+
- PostgreSQL 16+
- Redis 7+
- Docker & Docker Compose
- Git 2.x+
- Node.js 18+ (for frontend integration)

# Optional but Recommended
- PyCharm Professional or VS Code with Python extensions
- pgAdmin 4 for database management
- Redis Commander for Redis management
- Postman or Insomnia for API testing
```

### **Project Setup**
```bash
# 1. Clone the repository
git clone <repository-url>
cd vierla-codebase/backend

# 2. Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/macOS
# or venv\Scripts\activate  # Windows

# 3. Install dependencies
pip install -r requirements/development.txt

# 4. Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# 5. Set up database
python scripts/setup.py --environment=development
python manage.py migrate

# 6. Create superuser
python manage.py createsuperuser

# 7. Load sample data
python scripts/seed.py --sample-data

# 8. Start development server
python manage.py runserver 0.0.0.0:8000
```

### **Docker Development Setup**
```bash
# Start all services
docker-compose -f docker/docker-compose.yml up -d

# View logs
docker-compose logs -f backend

# Execute commands in container
docker-compose exec backend python manage.py migrate
docker-compose exec backend python manage.py shell

# Stop services
docker-compose down
```

---

## 📝 **Coding Standards**

### **Python Standards**

#### **Code Formatting**
```python
# ✅ Good: Follow PEP 8 and Black formatting
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from decimal import Decimal

from django.db import models
from django.contrib.auth.models import AbstractUser
from rest_framework import serializers, viewsets, permissions


class ServiceProvider(models.Model):
    """Service provider model with comprehensive business information."""
    
    user = models.OneToOneField(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='provider_profile'
    )
    business_name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    address = models.TextField()
    latitude = models.DecimalField(max_digits=10, decimal_places=8)
    longitude = models.DecimalField(max_digits=11, decimal_places=8)
    
    class Meta:
        db_table = 'catalog_service_providers'
        indexes = [
            models.Index(fields=['business_name']),
            models.Index(fields=['latitude', 'longitude']),
        ]
    
    def __str__(self) -> str:
        return self.business_name
    
    @property
    def average_rating(self) -> Decimal:
        """Calculate average rating from reviews."""
        from apps.reviews.models import Review
        
        reviews = Review.objects.filter(provider=self)
        if not reviews.exists():
            return Decimal('0.00')
        
        total_rating = sum(review.rating for review in reviews)
        return Decimal(total_rating / reviews.count()).quantize(Decimal('0.01'))
```

#### **Type Hints**
```python
# ✅ Good: Comprehensive type hints
from typing import List, Optional, Dict, Any, Union
from django.http import HttpRequest, HttpResponse
from rest_framework.request import Request
from rest_framework.response import Response

class ProviderService:
    """Service layer for provider operations."""
    
    def __init__(self, repository: 'ProviderRepository') -> None:
        self.repository = repository
    
    async def get_providers_by_location(
        self,
        latitude: float,
        longitude: float,
        radius_km: int = 10,
        category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get providers within specified radius of location."""
        providers = await self.repository.find_by_location(
            latitude=latitude,
            longitude=longitude,
            radius_km=radius_km,
            category=category
        )
        
        return [
            {
                'id': str(provider.id),
                'business_name': provider.business_name,
                'rating': float(provider.average_rating),
                'distance_km': provider.distance_km,
            }
            for provider in providers
        ]
    
    async def create_provider(
        self,
        user_id: str,
        provider_data: Dict[str, Any]
    ) -> Optional['ServiceProvider']:
        """Create new service provider."""
        try:
            return await self.repository.create(
                user_id=user_id,
                **provider_data
            )
        except Exception as e:
            logger.error(f"Failed to create provider: {e}")
            return None
```

#### **Error Handling**
```python
# ✅ Good: Comprehensive error handling
from apps.core.exceptions import (
    ValidationError,
    NotFoundError,
    PermissionDeniedError,
    BusinessLogicError
)

class BookingService:
    """Service layer for booking operations."""
    
    async def create_booking(
        self,
        customer_id: str,
        provider_id: str,
        service_id: str,
        booking_data: Dict[str, Any]
    ) -> 'Booking':
        """Create new booking with comprehensive validation."""
        
        # Validate customer exists
        customer = await self.user_repository.get_by_id(customer_id)
        if not customer:
            raise NotFoundError(f"Customer {customer_id} not found")
        
        # Validate provider exists and is active
        provider = await self.provider_repository.get_by_id(provider_id)
        if not provider or not provider.is_active:
            raise NotFoundError(f"Provider {provider_id} not found or inactive")
        
        # Validate service belongs to provider
        service = await self.service_repository.get_by_id(service_id)
        if not service or service.provider_id != provider_id:
            raise ValidationError("Service does not belong to specified provider")
        
        # Check time slot availability
        requested_datetime = booking_data.get('scheduled_datetime')
        if not await self.is_time_slot_available(provider_id, requested_datetime):
            raise BusinessLogicError("Requested time slot is not available")
        
        try:
            booking = await self.booking_repository.create({
                'customer_id': customer_id,
                'provider_id': provider_id,
                'service_id': service_id,
                **booking_data
            })
            
            # Trigger booking created event
            await self.event_bus.publish(BookingCreatedEvent(
                booking_id=str(booking.id),
                customer_id=customer_id,
                provider_id=provider_id
            ))
            
            return booking
            
        except Exception as e:
            logger.error(f"Failed to create booking: {e}")
            raise BusinessLogicError("Failed to create booking")
```

### **Django Standards**

#### **Model Design**
```python
# ✅ Good: Well-designed Django models
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from apps.core.models import BaseModel, TimestampedModel

class Booking(BaseModel, TimestampedModel):
    """Booking model with state machine and audit trail."""
    
    class Status(models.TextChoices):
        PENDING = 'pending', 'Pending'
        CONFIRMED = 'confirmed', 'Confirmed'
        IN_PROGRESS = 'in_progress', 'In Progress'
        COMPLETED = 'completed', 'Completed'
        CANCELLED = 'cancelled', 'Cancelled'
        NO_SHOW = 'no_show', 'No Show'
    
    customer = models.ForeignKey(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='customer_bookings'
    )
    provider = models.ForeignKey(
        'catalog.ServiceProvider',
        on_delete=models.CASCADE,
        related_name='provider_bookings'
    )
    service = models.ForeignKey(
        'catalog.Service',
        on_delete=models.CASCADE,
        related_name='bookings'
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    scheduled_datetime = models.DateTimeField()
    duration_minutes = models.PositiveIntegerField()
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'bookings_booking'
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['provider', 'scheduled_datetime']),
            models.Index(fields=['status', 'scheduled_datetime']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(total_amount__gt=0),
                name='positive_total_amount'
            ),
            models.CheckConstraint(
                check=models.Q(duration_minutes__gt=0),
                name='positive_duration'
            ),
        ]
    
    def __str__(self) -> str:
        return f"Booking {self.id} - {self.customer.email} with {self.provider.business_name}"
    
    def can_be_cancelled(self) -> bool:
        """Check if booking can be cancelled."""
        return self.status in [self.Status.PENDING, self.Status.CONFIRMED]
    
    def get_cancellation_deadline(self) -> datetime:
        """Get the deadline for cancellation (24 hours before)."""
        return self.scheduled_datetime - timedelta(hours=24)
```

#### **Serializer Design**
```python
# ✅ Good: Comprehensive DRF serializers
from rest_framework import serializers
from apps.core.serializers import BaseSerializer

class BookingSerializer(BaseSerializer):
    """Comprehensive booking serializer with validation."""
    
    customer = serializers.StringRelatedField(read_only=True)
    provider = serializers.StringRelatedField(read_only=True)
    service = serializers.StringRelatedField(read_only=True)
    can_be_cancelled = serializers.BooleanField(read_only=True)
    cancellation_deadline = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'id', 'customer', 'provider', 'service', 'status',
            'scheduled_datetime', 'duration_minutes', 'total_amount',
            'notes', 'can_be_cancelled', 'cancellation_deadline',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_scheduled_datetime(self, value: datetime) -> datetime:
        """Validate booking datetime is in the future."""
        if value <= timezone.now():
            raise serializers.ValidationError(
                "Booking must be scheduled for a future date and time"
            )
        
        # Check if it's within business hours
        if not self._is_within_business_hours(value):
            raise serializers.ValidationError(
                "Booking must be within business hours (9 AM - 6 PM)"
            )
        
        return value
    
    def validate(self, attrs: Dict[str, Any]) -> Dict[str, Any]:
        """Cross-field validation."""
        scheduled_datetime = attrs.get('scheduled_datetime')
        duration_minutes = attrs.get('duration_minutes')
        
        if scheduled_datetime and duration_minutes:
            end_time = scheduled_datetime + timedelta(minutes=duration_minutes)
            
            # Check for overlapping bookings
            overlapping_bookings = Booking.objects.filter(
                provider=attrs.get('provider'),
                scheduled_datetime__lt=end_time,
                scheduled_datetime__gte=scheduled_datetime - timedelta(
                    minutes=duration_minutes
                ),
                status__in=[Booking.Status.CONFIRMED, Booking.Status.IN_PROGRESS]
            )
            
            if overlapping_bookings.exists():
                raise serializers.ValidationError(
                    "This time slot conflicts with an existing booking"
                )
        
        return attrs
    
    def _is_within_business_hours(self, datetime_value: datetime) -> bool:
        """Check if datetime is within business hours."""
        hour = datetime_value.hour
        return 9 <= hour < 18  # 9 AM to 6 PM

class BookingCreateSerializer(BookingSerializer):
    """Serializer for creating bookings."""
    
    provider_id = serializers.UUIDField(write_only=True)
    service_id = serializers.UUIDField(write_only=True)
    
    class Meta(BookingSerializer.Meta):
        fields = BookingSerializer.Meta.fields + ['provider_id', 'service_id']
    
    def create(self, validated_data: Dict[str, Any]) -> Booking:
        """Create booking with proper relationships."""
        provider_id = validated_data.pop('provider_id')
        service_id = validated_data.pop('service_id')
        
        # Get related objects
        provider = ServiceProvider.objects.get(id=provider_id)
        service = Service.objects.get(id=service_id)
        
        return Booking.objects.create(
            provider=provider,
            service=service,
            **validated_data
        )
```

#### **ViewSet Design**
```python
# ✅ Good: Well-structured DRF viewsets
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from apps.core.permissions import IsOwnerOrReadOnly
from apps.core.pagination import StandardResultsSetPagination

class BookingViewSet(viewsets.ModelViewSet):
    """Comprehensive booking management viewset."""
    
    serializer_class = BookingSerializer
    permission_classes = [permissions.IsAuthenticated, IsOwnerOrReadOnly]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status', 'provider', 'service']
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Get bookings based on user role."""
        user = self.request.user
        
        if user.role == 'customer':
            return Booking.objects.filter(customer=user)
        elif user.role == 'provider':
            return Booking.objects.filter(provider__user=user)
        elif user.role == 'admin':
            return Booking.objects.all()
        
        return Booking.objects.none()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return BookingCreateSerializer
        return BookingSerializer
    
    def perform_create(self, serializer):
        """Create booking with current user as customer."""
        serializer.save(customer=self.request.user)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a booking."""
        booking = self.get_object()
        
        if not booking.can_be_cancelled():
            return Response(
                {'error': 'Booking cannot be cancelled in current status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if timezone.now() > booking.get_cancellation_deadline():
            return Response(
                {'error': 'Cancellation deadline has passed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking.status = Booking.Status.CANCELLED
        booking.save()
        
        # Trigger cancellation event
        from apps.core.events import event_bus
        event_bus.publish(BookingCancelledEvent(
            booking_id=str(booking.id),
            cancelled_by=str(request.user.id)
        ))
        
        return Response(
            {'message': 'Booking cancelled successfully'},
            status=status.HTTP_200_OK
        )
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm a booking (provider only)."""
        booking = self.get_object()
        
        # Check permissions
        if request.user.role != 'provider' or booking.provider.user != request.user:
            return Response(
                {'error': 'Only the service provider can confirm bookings'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if booking.status != Booking.Status.PENDING:
            return Response(
                {'error': 'Only pending bookings can be confirmed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking.status = Booking.Status.CONFIRMED
        booking.save()
        
        return Response(
            {'message': 'Booking confirmed successfully'},
            status=status.HTTP_200_OK
        )
```

---

## 🧪 **Testing Standards**

### **Test Structure**
```python
# ✅ Good: Comprehensive test structure
import pytest
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from apps.authentication.models import User
from apps.catalog.models import ServiceProvider, Service
from apps.bookings.models import Booking
from tests.factories import UserFactory, ProviderFactory, ServiceFactory

class BookingModelTest(TestCase):
    """Test booking model functionality."""
    
    def setUp(self):
        self.customer = UserFactory(role='customer')
        self.provider = ProviderFactory()
        self.service = ServiceFactory(provider=self.provider)
    
    def test_booking_creation(self):
        """Test booking can be created with valid data."""
        booking = Booking.objects.create(
            customer=self.customer,
            provider=self.provider,
            service=self.service,
            scheduled_datetime=timezone.now() + timedelta(days=1),
            duration_minutes=60,
            total_amount=Decimal('100.00')
        )
        
        self.assertEqual(booking.status, Booking.Status.PENDING)
        self.assertEqual(booking.customer, self.customer)
        self.assertEqual(booking.provider, self.provider)
    
    def test_booking_can_be_cancelled(self):
        """Test booking cancellation logic."""
        booking = Booking.objects.create(
            customer=self.customer,
            provider=self.provider,
            service=self.service,
            scheduled_datetime=timezone.now() + timedelta(days=1),
            duration_minutes=60,
            total_amount=Decimal('100.00'),
            status=Booking.Status.CONFIRMED
        )
        
        self.assertTrue(booking.can_be_cancelled())
        
        booking.status = Booking.Status.COMPLETED
        self.assertFalse(booking.can_be_cancelled())

class BookingAPITest(APITestCase):
    """Test booking API endpoints."""
    
    def setUp(self):
        self.customer = UserFactory(role='customer')
        self.provider_user = UserFactory(role='provider')
        self.provider = ProviderFactory(user=self.provider_user)
        self.service = ServiceFactory(provider=self.provider)
    
    def test_create_booking_authenticated(self):
        """Test authenticated user can create booking."""
        self.client.force_authenticate(user=self.customer)
        
        data = {
            'provider_id': str(self.provider.id),
            'service_id': str(self.service.id),
            'scheduled_datetime': (timezone.now() + timedelta(days=1)).isoformat(),
            'duration_minutes': 60,
            'total_amount': '100.00'
        }
        
        response = self.client.post('/api/v1/bookings/', data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Booking.objects.count(), 1)
        
        booking = Booking.objects.first()
        self.assertEqual(booking.customer, self.customer)
        self.assertEqual(booking.provider, self.provider)
    
    def test_create_booking_unauthenticated(self):
        """Test unauthenticated user cannot create booking."""
        data = {
            'provider_id': str(self.provider.id),
            'service_id': str(self.service.id),
            'scheduled_datetime': (timezone.now() + timedelta(days=1)).isoformat(),
            'duration_minutes': 60,
            'total_amount': '100.00'
        }
        
        response = self.client.post('/api/v1/bookings/', data)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(Booking.objects.count(), 0)
    
    def test_cancel_booking(self):
        """Test booking cancellation endpoint."""
        booking = Booking.objects.create(
            customer=self.customer,
            provider=self.provider,
            service=self.service,
            scheduled_datetime=timezone.now() + timedelta(days=1),
            duration_minutes=60,
            total_amount=Decimal('100.00'),
            status=Booking.Status.CONFIRMED
        )
        
        self.client.force_authenticate(user=self.customer)
        
        response = self.client.post(f'/api/v1/bookings/{booking.id}/cancel/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        booking.refresh_from_db()
        self.assertEqual(booking.status, Booking.Status.CANCELLED)

@pytest.mark.django_db
class TestBookingService:
    """Test booking service layer."""
    
    def test_create_booking_success(self, booking_service, customer, provider, service):
        """Test successful booking creation."""
        booking_data = {
            'scheduled_datetime': timezone.now() + timedelta(days=1),
            'duration_minutes': 60,
            'total_amount': Decimal('100.00')
        }
        
        booking = await booking_service.create_booking(
            customer_id=str(customer.id),
            provider_id=str(provider.id),
            service_id=str(service.id),
            booking_data=booking_data
        )
        
        assert booking is not None
        assert booking.customer == customer
        assert booking.provider == provider
        assert booking.service == service
    
    def test_create_booking_invalid_time_slot(self, booking_service, customer, provider, service):
        """Test booking creation with invalid time slot."""
        # Create existing booking
        existing_booking_time = timezone.now() + timedelta(days=1)
        Booking.objects.create(
            customer=customer,
            provider=provider,
            service=service,
            scheduled_datetime=existing_booking_time,
            duration_minutes=60,
            total_amount=Decimal('100.00'),
            status=Booking.Status.CONFIRMED
        )
        
        # Try to create overlapping booking
        booking_data = {
            'scheduled_datetime': existing_booking_time + timedelta(minutes=30),
            'duration_minutes': 60,
            'total_amount': Decimal('100.00')
        }
        
        with pytest.raises(BusinessLogicError):
            await booking_service.create_booking(
                customer_id=str(customer.id),
                provider_id=str(provider.id),
                service_id=str(service.id),
                booking_data=booking_data
            )
```

---

## 🔧 **Development Tools**

### **VS Code Configuration**
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    },
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/migrations": false
    }
}
```

### **Pre-commit Hooks**
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.12

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [django-stubs]
```

---

## 📊 **Performance Best Practices**

### **Database Optimization**
```python
# ✅ Good: Optimized database queries
class OptimizedProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """Optimized provider viewset with efficient queries."""
    
    def get_queryset(self):
        return ServiceProvider.objects.select_related(
            'user'
        ).prefetch_related(
            'services',
            'categories',
            'reviews'
        ).annotate(
            avg_rating=Avg('reviews__rating'),
            review_count=Count('reviews'),
            service_count=Count('services')
        ).filter(
            is_active=True
        )
    
    @action(detail=False)
    def nearby(self, request):
        """Get nearby providers with optimized query."""
        lat = float(request.query_params.get('lat', 0))
        lng = float(request.query_params.get('lng', 0))
        radius = int(request.query_params.get('radius', 10))
        
        # Use database-level distance calculation
        providers = self.get_queryset().extra(
            select={
                'distance': '''
                    6371 * acos(
                        cos(radians(%s)) * cos(radians(latitude)) *
                        cos(radians(longitude) - radians(%s)) +
                        sin(radians(%s)) * sin(radians(latitude))
                    )
                '''
            },
            select_params=[lat, lng, lat],
            where=['6371 * acos(cos(radians(%s)) * cos(radians(latitude)) * cos(radians(longitude) - radians(%s)) + sin(radians(%s)) * sin(radians(latitude))) <= %s'],
            params=[lat, lng, lat, radius]
        ).order_by('distance')
        
        serializer = self.get_serializer(providers, many=True)
        return Response(serializer.data)
```

### **Caching Strategy**
```python
# ✅ Good: Multi-layer caching
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator

@method_decorator(cache_page(60 * 15), name='list')  # 15 minutes
class CachedProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """Provider viewset with caching."""
    
    def list(self, request, *args, **kwargs):
        """List providers with caching."""
        cache_key = f"providers_list_{request.GET.urlencode()}"
        cached_response = cache.get(cache_key)
        
        if cached_response:
            return Response(cached_response)
        
        response = super().list(request, *args, **kwargs)
        cache.set(cache_key, response.data, 60 * 15)  # 15 minutes
        
        return response
```

---

## 🚀 **Deployment Best Practices**

### **Environment Configuration**
```python
# config/settings/base.py
import os
from pathlib import Path
from decouple import config

BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Security
SECRET_KEY = config('SECRET_KEY')
DEBUG = config('DEBUG', default=False, cast=bool)
ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='').split(',')

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
        'OPTIONS': {
            'sslmode': config('DB_SSLMODE', default='prefer'),
        },
    }
}

# Redis
REDIS_URL = config('REDIS_URL', default='redis://localhost:6379/0')

# Celery
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL
```

---

**Development Status**: ✅ **STANDARDS DEFINED**  
**Next Phase**: Implementation of core services following these standards  
**Team Onboarding**: Use this guide for new developer orientation
