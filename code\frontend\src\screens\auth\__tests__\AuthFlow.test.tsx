/**
 * Authentication Flow Integration Tests
 * Tests the complete authentication flow from screens to API
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { LoginScreen } from '../LoginScreen';
import { RegisterScreen } from '../RegisterScreen';
import { authAPI } from '../../../services/api/auth';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiSet: jest.fn(),
  multiGet: jest.fn(),
  clear: jest.fn(),
}));

jest.mock('../../../services/api/auth', () => ({
  authAPI: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    refreshToken: jest.fn(),
    getProfile: jest.fn(),
    socialAuth: jest.fn(),
  },
}));

const mockedAuthAPI = authAPI as jest.Mocked<typeof authAPI>;
const mockedAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Test navigation setup
const Stack = createStackNavigator();

const TestNavigator: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="Test" component={() => <>{children}</>} />
        </Stack.Navigator>
      </NavigationContainer>
    </QueryClientProvider>
  );
};

describe('Authentication Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('LoginScreen Flow', () => {
    const mockNavigation = {
      navigate: jest.fn(),
      replace: jest.fn(),
      goBack: jest.fn(),
    };

    it('should complete successful login flow with test account', async () => {
      const mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: 27,
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'Customer',
          role: 'customer',
          is_verified: true,
        },
      };

      mockedAuthAPI.login.mockResolvedValue(mockAuthResponse);
      mockedAsyncStorage.multiSet.mockResolvedValue();

      const { getByPlaceholderText, getByText } = render(
        <TestNavigator>
          <LoginScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      // Fill in login form
      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const loginButton = getByText('Sign In');

      await act(async () => {
        fireEvent.changeText(emailInput, '<EMAIL>');
        fireEvent.changeText(passwordInput, 'TestPass123!');
      });

      await act(async () => {
        fireEvent.press(loginButton);
      });

      // Verify API call
      await waitFor(() => {
        expect(mockedAuthAPI.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'TestPass123!',
        });
      });

      // Verify token storage
      await waitFor(() => {
        expect(mockedAsyncStorage.multiSet).toHaveBeenCalledWith([
          ['access_token', 'mock-access-token'],
          ['refresh_token', 'mock-refresh-token'],
          ['user', JSON.stringify(mockAuthResponse.user)],
        ]);
      });

      // Verify navigation
      await waitFor(() => {
        expect(mockNavigation.replace).toHaveBeenCalledWith('Main');
      });
    });

    it('should handle login validation errors', async () => {
      const { getByPlaceholderText, getByText, queryByText } = render(
        <TestNavigator>
          <LoginScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      const loginButton = getByText('Sign In');

      // Try to login without filling fields
      await act(async () => {
        fireEvent.press(loginButton);
      });

      // Should show validation errors
      await waitFor(() => {
        expect(queryByText('Email is required')).toBeTruthy();
        expect(queryByText('Password is required')).toBeTruthy();
      });

      // Should not call API
      expect(mockedAuthAPI.login).not.toHaveBeenCalled();
    });

    it('should handle invalid credentials error', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Invalid credentials' },
        },
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      const { getByPlaceholderText, getByText } = render(
        <TestNavigator>
          <LoginScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const loginButton = getByText('Sign In');

      await act(async () => {
        fireEvent.changeText(emailInput, '<EMAIL>');
        fireEvent.changeText(passwordInput, 'wrong-password');
        fireEvent.press(loginButton);
      });

      await waitFor(() => {
        expect(mockedAuthAPI.login).toHaveBeenCalled();
      });

      // Should not navigate or store tokens
      expect(mockNavigation.replace).not.toHaveBeenCalled();
      expect(mockedAsyncStorage.multiSet).not.toHaveBeenCalled();
    });

    it('should handle account locked error', async () => {
      const mockError = {
        response: {
          status: 423,
          data: {
            detail: 'Account is temporarily locked due to multiple failed login attempts.',
            error_code: 'ACCOUNT_LOCKED',
            retry_after: 1800,
          },
        },
        retryAfter: 1800,
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      const { getByPlaceholderText, getByText } = render(
        <TestNavigator>
          <LoginScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const loginButton = getByText('Sign In');

      await act(async () => {
        fireEvent.changeText(emailInput, '<EMAIL>');
        fireEvent.changeText(passwordInput, 'TestPass123!');
        fireEvent.press(loginButton);
      });

      await waitFor(() => {
        expect(mockedAuthAPI.login).toHaveBeenCalled();
      });

      // Should not navigate or store tokens
      expect(mockNavigation.replace).not.toHaveBeenCalled();
      expect(mockedAsyncStorage.multiSet).not.toHaveBeenCalled();
    });

    it('should navigate to register screen', async () => {
      const { getByText } = render(
        <TestNavigator>
          <LoginScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      const signUpButton = getByText("Don't have an account? Sign Up");

      await act(async () => {
        fireEvent.press(signUpButton);
      });

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Register');
    });
  });

  describe('RegisterScreen Flow', () => {
    const mockNavigation = {
      navigate: jest.fn(),
      replace: jest.fn(),
      goBack: jest.fn(),
    };

    it('should complete successful registration flow', async () => {
      const mockAuthResponse = {
        access: 'mock-access-token',
        refresh: 'mock-refresh-token',
        user: {
          id: 34,
          email: '<EMAIL>',
          first_name: 'New',
          last_name: 'User',
          role: 'customer',
          is_verified: false,
        },
      };

      mockedAuthAPI.register.mockResolvedValue(mockAuthResponse);
      mockedAsyncStorage.multiSet.mockResolvedValue();

      const { getByPlaceholderText, getByText } = render(
        <TestNavigator>
          <RegisterScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      // Fill in registration form
      const firstNameInput = getByPlaceholderText('Enter your first name');
      const lastNameInput = getByPlaceholderText('Enter your last name');
      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const confirmPasswordInput = getByPlaceholderText('Confirm your password');
      const registerButton = getByText('Create Account');

      await act(async () => {
        fireEvent.changeText(firstNameInput, 'New');
        fireEvent.changeText(lastNameInput, 'User');
        fireEvent.changeText(emailInput, '<EMAIL>');
        fireEvent.changeText(passwordInput, 'TestPass123!');
        fireEvent.changeText(confirmPasswordInput, 'TestPass123!');
      });

      await act(async () => {
        fireEvent.press(registerButton);
      });

      // Verify API call
      await waitFor(() => {
        expect(mockedAuthAPI.register).toHaveBeenCalledWith({
          first_name: 'New',
          last_name: 'User',
          email: '<EMAIL>',
          password: 'TestPass123!',
          password_confirm: 'TestPass123!',
          role: 'customer',
        });
      });

      // Verify token storage
      await waitFor(() => {
        expect(mockedAsyncStorage.multiSet).toHaveBeenCalledWith([
          ['access_token', 'mock-access-token'],
          ['refresh_token', 'mock-refresh-token'],
          ['user', JSON.stringify(mockAuthResponse.user)],
        ]);
      });

      // Verify navigation
      await waitFor(() => {
        expect(mockNavigation.replace).toHaveBeenCalledWith('Main');
      });
    });

    it('should handle registration validation errors', async () => {
      const mockError = {
        response: {
          status: 400,
          data: {
            email: ['User with this email already exists.'],
            password: ['Password is too weak.'],
          },
        },
      };

      mockedAuthAPI.register.mockRejectedValue(mockError);

      const { getByPlaceholderText, getByText } = render(
        <TestNavigator>
          <RegisterScreen navigation={mockNavigation} />
        </TestNavigator>
      );

      const firstNameInput = getByPlaceholderText('Enter your first name');
      const lastNameInput = getByPlaceholderText('Enter your last name');
      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const confirmPasswordInput = getByPlaceholderText('Confirm your password');
      const registerButton = getByText('Create Account');

      await act(async () => {
        fireEvent.changeText(firstNameInput, 'New');
        fireEvent.changeText(lastNameInput, 'User');
        fireEvent.changeText(emailInput, '<EMAIL>');
        fireEvent.changeText(passwordInput, '123');
        fireEvent.changeText(confirmPasswordInput, '123');
        fireEvent.press(registerButton);
      });

      await waitFor(() => {
        expect(mockedAuthAPI.register).toHaveBeenCalled();
      });

      // Should not navigate or store tokens
      expect(mockNavigation.replace).not.toHaveBeenCalled();
      expect(mockedAsyncStorage.multiSet).not.toHaveBeenCalled();
    });
  });
});
