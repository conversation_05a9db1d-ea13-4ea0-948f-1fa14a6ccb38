/**
 * Integration Test Script
 * Tests the authentication flow between frontend and backend
 */

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:8000/api';

async function testAuthenticationFlow() {
  console.log('🔐 Testing Vierla Authentication Integration');
  console.log('=' * 50);

  try {
    // Test 1: Register a new user
    console.log('\n📝 Test 1: User Registration');
    const registerData = {
      first_name: 'Test',
      last_name: 'User',
      email: `test${Date.now()}@example.com`,
      username: `testuser${Date.now()}`,
      password: 'TestPassword123!',
      password_confirm: 'TestPassword123!'
    };

    const registerResponse = await axios.post(`${API_BASE_URL}/auth/register/`, registerData);
    console.log('✅ Registration successful');
    console.log('User:', registerResponse.data.user.email);
    console.log('Access token received:', !!registerResponse.data.access);

    // Test 2: Login with the registered user
    console.log('\n🔑 Test 2: User Login');
    const loginData = {
      email: registerData.email,
      password: registerData.password
    };

    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login/`, loginData);
    console.log('✅ Login successful');
    console.log('User:', loginResponse.data.user.email);
    console.log('Access token received:', !!loginResponse.data.access);
    console.log('Refresh token received:', !!loginResponse.data.refresh);

    // Test 3: Access protected endpoint with token
    console.log('\n🔒 Test 3: Protected Endpoint Access');
    const token = loginResponse.data.access;
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile/`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Protected endpoint access successful');
    console.log('Profile data received:', !!profileResponse.data);

    // Test 4: Token refresh
    console.log('\n🔄 Test 4: Token Refresh');
    const refreshToken = loginResponse.data.refresh;
    const refreshResponse = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
      refresh: refreshToken
    });
    console.log('✅ Token refresh successful');
    console.log('New access token received:', !!refreshResponse.data.access);

    console.log('\n🎉 All authentication tests passed!');
    console.log('✅ Frontend-Backend integration is working correctly');

  } catch (error) {
    console.error('\n❌ Authentication test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    process.exit(1);
  }
}

// Run the test
testAuthenticationFlow();
