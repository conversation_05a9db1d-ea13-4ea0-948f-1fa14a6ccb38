"""
Django filters for Service Catalog
Enhanced filtering capabilities for services, providers, and categories
"""
import django_filters
from django.db.models import Q
from .models import Service, ServiceProvider, ServiceCategory


class ServiceFilter(django_filters.FilterSet):
    """
    Comprehensive filter for Service model with search and sorting capabilities
    """
    
    # Text search
    name = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')
    search = django_filters.CharFilter(method='filter_search')
    
    # Provider filters
    provider_name = django_filters.CharFilter(
        field_name='provider__business_name', 
        lookup_expr='icontains'
    )
    provider_city = django_filters.CharFilter(
        field_name='provider__city', 
        lookup_expr='icontains'
    )
    provider_verified = django_filters.BooleanFilter(
        field_name='provider__is_verified'
    )
    min_rating = django_filters.NumberFilter(
        field_name='provider__rating', 
        lookup_expr='gte'
    )
    
    # Category filters
    category = django_filters.ModelChoiceFilter(
        queryset=ServiceCategory.objects.filter(is_active=True)
    )
    categories = django_filters.ModelMultipleChoiceFilter(
        field_name='category',
        queryset=ServiceCategory.objects.filter(is_active=True),
        conjoined=False  # OR logic
    )
    
    # Price filters
    min_price = django_filters.NumberFilter(
        field_name='base_price', 
        lookup_expr='gte'
    )
    max_price = django_filters.NumberFilter(
        field_name='base_price', 
        lookup_expr='lte'
    )
    price_type = django_filters.ChoiceFilter(
        choices=Service.PRICE_TYPE_CHOICES
    )
    
    # Duration filters
    min_duration = django_filters.NumberFilter(
        field_name='duration', 
        lookup_expr='gte'
    )
    max_duration = django_filters.NumberFilter(
        field_name='duration', 
        lookup_expr='lte'
    )
    
    # Status filters
    is_popular = django_filters.BooleanFilter()
    is_available = django_filters.BooleanFilter()
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    
    # Sorting
    sort_by = django_filters.CharFilter(method='filter_sort')
    
    class Meta:
        model = Service
        fields = [
            'name', 'description', 'search', 'provider_name', 'provider_city',
            'provider_verified', 'min_rating', 'category', 'categories',
            'min_price', 'max_price', 'price_type', 'min_duration', 'max_duration',
            'is_popular', 'is_available', 'has_location', 'sort_by'
        ]
    
    def filter_search(self, queryset, name, value):
        """
        Multi-field search with relevance scoring
        """
        if value:
            return queryset.filter(
                Q(name__icontains=value) |
                Q(description__icontains=value) |
                Q(provider__business_name__icontains=value) |
                Q(category__name__icontains=value) |
                Q(provider__city__icontains=value)
            ).distinct()
        return queryset
    
    def filter_has_location(self, queryset, name, value):
        """
        Filter services by providers with location data
        """
        if value is True:
            return queryset.filter(
                provider__latitude__isnull=False,
                provider__longitude__isnull=False
            )
        elif value is False:
            return queryset.filter(
                Q(provider__latitude__isnull=True) |
                Q(provider__longitude__isnull=True)
            )
        return queryset
    
    def filter_sort(self, queryset, name, value):
        """
        Apply sorting based on sort_by parameter
        """
        sort_options = {
            'relevance': ['-is_popular', 'base_price', 'name'],
            'price_low': ['base_price'],
            'price_high': ['-base_price'],
            'rating': ['-provider__rating'],
            'popularity': ['-is_popular', '-booking_count'],
            'duration': ['duration'],
            'newest': ['-created_at'],
            'name': ['name'],
        }
        
        if value in sort_options:
            return queryset.order_by(*sort_options[value])
        
        return queryset


class ServiceProviderFilter(django_filters.FilterSet):
    """
    Filter for ServiceProvider model
    """
    
    # Text search
    business_name = django_filters.CharFilter(lookup_expr='icontains')
    search = django_filters.CharFilter(method='filter_search')
    
    # Location filters
    city = django_filters.CharFilter(lookup_expr='icontains')
    state = django_filters.CharFilter(lookup_expr='icontains')
    country = django_filters.CharFilter(lookup_expr='icontains')
    has_location = django_filters.BooleanFilter(method='filter_has_location')
    
    # Status filters
    is_verified = django_filters.BooleanFilter()
    is_featured = django_filters.BooleanFilter()
    is_active = django_filters.BooleanFilter()
    
    # Rating filters
    min_rating = django_filters.NumberFilter(
        field_name='rating', 
        lookup_expr='gte'
    )
    
    # Category filters
    categories = django_filters.ModelMultipleChoiceFilter(
        queryset=ServiceCategory.objects.filter(is_active=True),
        conjoined=False
    )
    
    # Sorting
    sort_by = django_filters.CharFilter(method='filter_sort')
    
    class Meta:
        model = ServiceProvider
        fields = [
            'business_name', 'search', 'city', 'state', 'country',
            'has_location', 'is_verified', 'is_featured', 'is_active',
            'min_rating', 'categories', 'sort_by'
        ]
    
    def filter_search(self, queryset, name, value):
        """
        Multi-field search for providers
        """
        if value:
            return queryset.filter(
                Q(business_name__icontains=value) |
                Q(business_description__icontains=value) |
                Q(city__icontains=value) |
                Q(categories__name__icontains=value)
            ).distinct()
        return queryset
    
    def filter_has_location(self, queryset, name, value):
        """
        Filter providers by location data availability
        """
        if value is True:
            return queryset.filter(
                latitude__isnull=False,
                longitude__isnull=False
            )
        elif value is False:
            return queryset.filter(
                Q(latitude__isnull=True) |
                Q(longitude__isnull=True)
            )
        return queryset
    
    def filter_sort(self, queryset, name, value):
        """
        Apply sorting for providers
        """
        sort_options = {
            'name': ['business_name'],
            'rating': ['-rating'],
            'featured': ['-is_featured', '-rating'],
            'newest': ['-created_at'],
            'bookings': ['-total_bookings'],
            'experience': ['-years_of_experience'],
        }
        
        if value in sort_options:
            return queryset.order_by(*sort_options[value])
        
        return queryset


class ServiceCategoryFilter(django_filters.FilterSet):
    """
    Filter for ServiceCategory model
    """
    
    # Text search
    name = django_filters.CharFilter(lookup_expr='icontains')
    search = django_filters.CharFilter(method='filter_search')
    
    # Hierarchy filters
    parent = django_filters.ModelChoiceFilter(
        queryset=ServiceCategory.objects.filter(is_active=True)
    )
    has_parent = django_filters.BooleanFilter(method='filter_has_parent')
    
    # Status filters
    is_popular = django_filters.BooleanFilter()
    is_active = django_filters.BooleanFilter()
    
    # Service count filters
    min_services = django_filters.NumberFilter(method='filter_min_services')
    
    # Sorting
    sort_by = django_filters.CharFilter(method='filter_sort')
    
    class Meta:
        model = ServiceCategory
        fields = [
            'name', 'search', 'parent', 'has_parent', 'is_popular',
            'is_active', 'min_services', 'sort_by'
        ]
    
    def filter_search(self, queryset, name, value):
        """
        Search categories by name and description
        """
        if value:
            return queryset.filter(
                Q(name__icontains=value) |
                Q(description__icontains=value)
            ).distinct()
        return queryset
    
    def filter_has_parent(self, queryset, name, value):
        """
        Filter categories by parent relationship
        """
        if value is True:
            return queryset.filter(parent__isnull=False)
        elif value is False:
            return queryset.filter(parent__isnull=True)
        return queryset
    
    def filter_min_services(self, queryset, name, value):
        """
        Filter categories by minimum number of active services
        """
        if value is not None:
            # This would require annotation in the view
            # For now, we'll implement basic filtering
            category_ids = []
            for category in queryset:
                if category.service_count >= value:
                    category_ids.append(category.id)
            return queryset.filter(id__in=category_ids)
        return queryset
    
    def filter_sort(self, queryset, name, value):
        """
        Apply sorting for categories
        """
        sort_options = {
            'name': ['name'],
            'popular': ['-is_popular', 'sort_order', 'name'],
            'order': ['sort_order', 'name'],
            'newest': ['-created_at'],
        }
        
        if value in sort_options:
            return queryset.order_by(*sort_options[value])
        
        return queryset
