import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

const { width: screenWidth } = Dimensions.get('window');

// Types
interface ServiceDetails {
  id: string;
  name: string;
  description: string;
  short_description?: string;
  base_price: number;
  price_type: 'fixed' | 'hourly' | 'range' | 'consultation';
  max_price?: number;
  display_price: string;
  duration: number;
  display_duration: string;
  image?: string;
  is_popular: boolean;
  is_available: boolean;
  requirements?: string;
  preparation_instructions?: string;
  provider: {
    id: string;
    business_name: string;
    business_description: string;
    rating: number;
    review_count: number;
    city: string;
    state: string;
    is_verified: boolean;
    profile_image?: string;
    years_of_experience?: number;
  };
  category: {
    id: string;
    name: string;
    icon: string;
    color: string;
  };
}

// Mock data
const mockServiceDetails: ServiceDetails = {
  id: '1',
  name: 'Premium Haircut & Styling',
  description: 'Experience our signature haircut service with personalized styling consultation. Our expert stylists will work with you to create the perfect look that complements your face shape and lifestyle. Includes wash, cut, style, and finishing products.',
  short_description: 'Premium haircut with styling consultation',
  base_price: 85.00,
  price_type: 'fixed',
  display_price: '$85.00',
  duration: 75,
  display_duration: '1h 15m',
  image: undefined,
  is_popular: true,
  is_available: true,
  requirements: 'Please arrive with clean, dry hair. Avoid using heavy styling products before your appointment.',
  preparation_instructions: 'Come with inspiration photos if you have a specific style in mind. We recommend booking a consultation for major style changes.',
  provider: {
    id: 'provider-1',
    business_name: 'Elite Hair Studio',
    business_description: 'Premium hair styling and treatments with over 8 years of experience',
    rating: 4.9,
    review_count: 156,
    city: 'Toronto',
    state: 'Ontario',
    is_verified: true,
    years_of_experience: 8,
  },
  category: {
    id: 'category-1',
    name: 'Hair Services',
    icon: '💇‍♀️',
    color: '#FF5722',
  },
};

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  status: {
    success: '#2E7D32',
    error: '#C62828',
  },
  border: '#E0E0E0',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
};

export const ServiceDetailsScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [service, setService] = useState<ServiceDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);

  // Get service ID from route params
  const serviceId = (route.params as any)?.serviceId || '1';

  useEffect(() => {
    loadServiceDetails();
  }, [serviceId]);

  const loadServiceDetails = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await serviceApi.getService(serviceId);
      // setService(response.data);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setService(mockServiceDetails);
    } catch (error) {
      Alert.alert('Error', 'Failed to load service details');
    } finally {
      setLoading(false);
    }
  }, [serviceId]);

  const handleBackPress = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  const handleFavoriteToggle = useCallback(() => {
    setIsFavorite(prev => !prev);
    // TODO: Update favorite status via API
  }, []);

  const handleBookNow = useCallback(() => {
    if (!service?.is_available) {
      Alert.alert('Unavailable', 'This service is currently unavailable');
      return;
    }
    
    // TODO: Navigate to booking screen
    Alert.alert('Book Service', `Booking ${service?.name}`);
  }, [service]);

  const handleMessageProvider = useCallback(() => {
    // TODO: Navigate to messaging screen
    Alert.alert('Message Provider', `Contact ${service?.provider.business_name}`);
  }, [service]);

  const handleProviderPress = useCallback(() => {
    // TODO: Navigate to provider profile screen
    Alert.alert('Provider Profile', `View ${service?.provider.business_name} profile`);
  }, [service]);

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={handleBackPress}
        testID="service-details-back-button"
      >
        <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={handleFavoriteToggle}
        testID="service-details-favorite-button"
      >
        <Ionicons
          name={isFavorite ? 'heart' : 'heart-outline'}
          size={24}
          color={isFavorite ? Colors.status.error : Colors.text.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderServiceImage = () => {
    if (service?.image) {
      return (
        <Image
          source={{ uri: service.image }}
          style={styles.serviceImage}
          testID="service-details-image"
        />
      );
    }
    
    return (
      <View style={styles.placeholderImage} testID="service-details-placeholder">
        <Ionicons
          name="image-outline"
          size={64}
          color={Colors.text.secondary}
        />
      </View>
    );
  };

  const renderServiceInfo = () => (
    <View style={styles.serviceInfo}>
      <Text style={styles.serviceName} testID="service-details-name">
        {service?.name}
      </Text>
      
      <View style={styles.providerRow}>
        <Text style={styles.providerName} testID="service-details-provider">
          {service?.provider.business_name}
        </Text>
        
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color={Colors.primary.light} />
          <Text style={styles.ratingText}>
            {service?.provider.rating.toFixed(1)} ({service?.provider.review_count})
          </Text>
        </View>
        
        {service?.provider.is_verified && (
          <View style={styles.verifiedBadge}>
            <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
            <Text style={styles.verifiedText}>Verified</Text>
          </View>
        )}
      </View>
      
      <View style={styles.priceRow}>
        <Text style={styles.price} testID="service-details-price">
          {service?.display_price}
        </Text>
        <Text style={styles.duration} testID="service-details-duration">
          {service?.display_duration}
        </Text>
      </View>
    </View>
  );

  const renderDescription = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Description</Text>
      <Text style={styles.description} testID="service-details-description">
        {service?.description}
      </Text>
    </View>
  );

  const renderRequirements = () => {
    if (!service?.requirements && !service?.preparation_instructions) return null;
    
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Requirements & Preparation</Text>
        
        {service.requirements && (
          <View style={styles.requirementItem}>
            <Text style={styles.requirementTitle}>Requirements:</Text>
            <Text style={styles.requirementText}>{service.requirements}</Text>
          </View>
        )}
        
        {service.preparation_instructions && (
          <View style={styles.requirementItem}>
            <Text style={styles.requirementTitle}>Preparation:</Text>
            <Text style={styles.requirementText}>{service.preparation_instructions}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderProviderInfo = () => (
    <TouchableOpacity
      style={styles.providerSection}
      onPress={handleProviderPress}
      testID="service-details-provider-section"
    >
      <Text style={styles.sectionTitle}>Provider Information</Text>
      
      <View style={styles.providerCard}>
        <View style={styles.providerAvatar}>
          {service?.provider.profile_image ? (
            <Image
              source={{ uri: service.provider.profile_image }}
              style={styles.avatarImage}
            />
          ) : (
            <Ionicons name="person" size={32} color={Colors.text.secondary} />
          )}
        </View>
        
        <View style={styles.providerDetails}>
          <Text style={styles.providerCardName}>{service?.provider.business_name}</Text>
          <Text style={styles.providerLocation}>
            {service?.provider.city}, {service?.provider.state}
          </Text>
          {service?.provider.years_of_experience && (
            <Text style={styles.providerExperience}>
              {service.provider.years_of_experience} years experience
            </Text>
          )}
        </View>
        
        <Ionicons name="chevron-forward" size={20} color={Colors.text.secondary} />
      </View>
    </TouchableOpacity>
  );

  const renderActionButtons = () => (
    <View style={styles.actionButtons}>
      <TouchableOpacity
        style={[
          styles.bookButton,
          !service?.is_available && styles.disabledButton
        ]}
        onPress={handleBookNow}
        disabled={!service?.is_available}
        testID="service-details-book-button"
      >
        <Text style={[
          styles.bookButtonText,
          !service?.is_available && styles.disabledButtonText
        ]}>
          {service?.is_available ? 'Book Now' : 'Currently Unavailable'}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.messageButton}
        onPress={handleMessageProvider}
        testID="service-details-message-button"
      >
        <Ionicons name="chatbubble-outline" size={20} color={Colors.primary.main} />
        <Text style={styles.messageButtonText}>Message Provider</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading service details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!service) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Service not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} testID="service-details-screen">
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        testID="service-details-scroll"
      >
        {renderServiceImage()}
        {renderServiceInfo()}
        {renderDescription()}
        {renderRequirements()}
        {renderProviderInfo()}
      </ScrollView>
      
      {renderActionButtons()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.background.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.small,
  },
  favoriteButton: {
    padding: Spacing.small,
  },
  content: {
    flex: 1,
  },
  serviceImage: {
    width: screenWidth,
    height: 250,
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: screenWidth,
    height: 250,
    backgroundColor: Colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceInfo: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  serviceName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Spacing.small,
  },
  providerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.medium,
    flexWrap: 'wrap',
  },
  providerName: {
    fontSize: 16,
    color: Colors.text.primary,
    fontWeight: '500',
    marginRight: Spacing.medium,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.medium,
  },
  ratingText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: Spacing.micro,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedText: {
    fontSize: 12,
    color: Colors.status.success,
    marginLeft: Spacing.micro,
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  duration: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
  section: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    marginTop: Spacing.small,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.medium,
  },
  description: {
    fontSize: 16,
    color: Colors.text.primary,
    lineHeight: 24,
  },
  requirementItem: {
    marginBottom: Spacing.medium,
  },
  requirementTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.micro,
  },
  requirementText: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
  },
  providerSection: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    marginTop: Spacing.small,
  },
  providerCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  providerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.medium,
  },
  avatarImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  providerDetails: {
    flex: 1,
  },
  providerCardName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  providerLocation: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: Spacing.micro,
  },
  providerExperience: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: Spacing.micro,
  },
  actionButtons: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  bookButton: {
    backgroundColor: Colors.primary.main,
    paddingVertical: Spacing.medium,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  disabledButton: {
    backgroundColor: Colors.text.secondary,
  },
  bookButtonText: {
    color: Colors.primary.white,
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButtonText: {
    color: Colors.background.light,
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.medium,
    borderWidth: 1,
    borderColor: Colors.primary.main,
    borderRadius: 8,
  },
  messageButtonText: {
    color: Colors.primary.main,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: Spacing.small,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ServiceDetailsScreen;
