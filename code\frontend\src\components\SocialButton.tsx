/**
 * Social Button Component
 * Reusable button for social authentication (Google, Apple)
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SocialButtonProps {
  provider: 'google' | 'apple';
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
}

export const SocialButton: React.FC<SocialButtonProps> = ({
  provider,
  onPress,
  loading = false,
  disabled = false,
  style,
}) => {
  const getProviderConfig = () => {
    switch (provider) {
      case 'google':
        return {
          title: 'Continue with Google',
          icon: 'logo-google' as keyof typeof Ionicons.glyphMap,
          backgroundColor: '#FFFFFF',
          textColor: '#1C1C1E',
          borderColor: '#E5E5EA',
        };
      case 'apple':
        return {
          title: 'Continue with <PERSON>',
          icon: 'logo-apple' as keyof typeof Ionicons.glyphMap,
          backgroundColor: '#000000',
          textColor: '#FFFFFF',
          borderColor: '#000000',
        };
      default:
        return {
          title: 'Continue',
          icon: 'help' as keyof typeof Ionicons.glyphMap,
          backgroundColor: '#FFFFFF',
          textColor: '#1C1C1E',
          borderColor: '#E5E5EA',
        };
    }
  };

  const config = getProviderConfig();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    },
    disabled && styles.disabled,
    style,
  ];

  const textStyle = [
    styles.text,
    { color: config.textColor },
    disabled && styles.disabledText,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      testID={`${provider}-signin-button`}
    >
      <View style={styles.content}>
        {loading ? (
          <ActivityIndicator
            color={config.textColor}
            size="small"
            style={styles.icon}
          />
        ) : (
          <Ionicons
            name={config.icon}
            size={20}
            color={config.textColor}
            style={styles.icon}
          />
        )}
        <Text style={textStyle}>{config.title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    borderWidth: 1,
    minHeight: 44,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginVertical: 4,
  },
  disabled: {
    opacity: 0.5,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 12,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  disabledText: {
    opacity: 0.5,
  },
});
