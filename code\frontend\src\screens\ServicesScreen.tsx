import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Import components
import { ServiceList, CategoryCard, ServiceCard } from '../components';
import type { Service, ServiceCategory } from '../components';

// Mock data - will be replaced with API calls
const mockCategories: ServiceCategory[] = [
  {
    id: '1',
    name: 'Hair Services',
    slug: 'hair-services',
    icon: '💇‍♀️',
    color: '#FF5722',
    mobile_icon: '✂️',
    is_popular: true,
    service_count: 15,
  },
  {
    id: '2',
    name: 'Nail Services',
    slug: 'nail-services',
    icon: '💅',
    color: '#9C27B0',
    mobile_icon: '💅',
    is_popular: true,
    service_count: 12,
  },
  {
    id: '3',
    name: 'Spa Services',
    slug: 'spa-services',
    icon: '🧖‍♀️',
    color: '#4CAF50',
    mobile_icon: '🧖‍♀️',
    is_popular: false,
    service_count: 8,
  },
];

const mockServices: Service[] = [
  {
    id: '1',
    name: 'Premium Haircut',
    short_description: 'Professional haircut with styling consultation',
    base_price: 75.00,
    price_type: 'fixed',
    display_price: '$75.00',
    duration: 60,
    display_duration: '1h',
    image: undefined,
    is_popular: true,
    is_available: true,
    booking_count: 25,
    provider_name: 'Hair Studio Elite',
    provider_rating: 4.8,
    provider_city: 'Toronto',
    category_name: 'Hair Services',
    category_icon: '💇‍♀️',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Luxury Manicure',
    short_description: 'Premium nail care with gel polish',
    base_price: 55.00,
    price_type: 'fixed',
    display_price: '$55.00',
    duration: 45,
    display_duration: '45m',
    image: undefined,
    is_popular: false,
    is_available: true,
    booking_count: 18,
    provider_name: 'Nail Boutique',
    provider_rating: 4.6,
    provider_city: 'Vancouver',
    category_name: 'Nail Services',
    category_icon: '💅',
    created_at: '2024-01-02T00:00:00Z',
  },
];

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  border: '#E0E0E0',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
};

export const ServicesScreen: React.FC = () => {
  const navigation = useNavigation();
  const [services, setServices] = useState<Service[]>(mockServices);
  const [categories, setCategories] = useState<ServiceCategory[]>(mockCategories);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [favoriteServices, setFavoriteServices] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Load initial data
  useEffect(() => {
    loadServices();
    loadCategories();
  }, []);

  const loadServices = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await serviceApi.getServices();
      // setServices(response.data);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setServices(mockServices);
    } catch (error) {
      Alert.alert('Error', 'Failed to load services');
    } finally {
      setLoading(false);
    }
  }, []);

  const loadCategories = useCallback(async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await serviceApi.getCategories();
      // setCategories(response.data);
      
      setCategories(mockCategories);
    } catch (error) {
      Alert.alert('Error', 'Failed to load categories');
    }
  }, []);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadServices();
    setRefreshing(false);
  }, [loadServices]);

  const handleServicePress = useCallback((service: Service) => {
    // TODO: Navigate to service details screen
    // navigation.navigate('ServiceDetails', { serviceId: service.id });
    Alert.alert('Service Selected', `You selected: ${service.name}`);
  }, []);

  const handleCategoryPress = useCallback((category: ServiceCategory) => {
    // TODO: Navigate to category services screen
    // navigation.navigate('CategoryServices', { categoryId: category.id });
    Alert.alert('Category Selected', `You selected: ${category.name}`);
  }, []);

  const handleFavoriteToggle = useCallback((serviceId: string) => {
    setFavoriteServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  }, []);

  const handleSearchPress = useCallback(() => {
    // TODO: Navigate to search screen
    // navigation.navigate('Search');
    Alert.alert('Search', 'Search functionality coming soon');
  }, []);

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Services</Text>
      <TouchableOpacity
        style={styles.searchButton}
        onPress={handleSearchPress}
        testID="services-search-button"
      >
        <Ionicons name="search" size={24} color={Colors.text.primary} />
      </TouchableOpacity>
    </View>
  );

  const renderCategories = () => (
    <View style={styles.categoriesSection}>
      <Text style={styles.sectionTitle}>Categories</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContainer}
        testID="services-categories-scroll"
      >
        {categories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            onPress={() => handleCategoryPress(category)}
            variant="default"
            testID={`services-category-${category.id}`}
          />
        ))}
      </ScrollView>
    </View>
  );

  const renderFeaturedServices = () => {
    const featuredServices = services.filter(service => service.is_popular);
    
    if (featuredServices.length === 0) return null;

    return (
      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>Featured Services</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.featuredContainer}
          testID="services-featured-scroll"
        >
          {featuredServices.map((service) => (
            <View key={service.id} style={styles.featuredServiceCard}>
              <ServiceCard
                service={service}
                onPress={() => handleServicePress(service)}
                onFavorite={() => handleFavoriteToggle(service.id)}
                isFavorite={favoriteServices.includes(service.id)}
                variant="featured"
                testID={`services-featured-${service.id}`}
              />
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} testID="services-screen">
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        testID="services-main-scroll"
      >
        {renderCategories()}
        {renderFeaturedServices()}
        
        <View style={styles.allServicesSection}>
          <Text style={styles.sectionTitle}>All Services</Text>
          <ServiceList
            services={services}
            onServicePress={handleServicePress}
            onFavoriteToggle={handleFavoriteToggle}
            favoriteServices={favoriteServices}
            loading={loading}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            variant="list"
            testID="services-all-list"
            emptyMessage="No services available"
            emptySubtitle="Check back later for new services"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.background.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  searchButton: {
    padding: Spacing.small,
  },
  content: {
    flex: 1,
  },
  categoriesSection: {
    backgroundColor: Colors.background.surface,
    paddingVertical: Spacing.large,
    marginBottom: Spacing.small,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.medium,
    paddingHorizontal: Spacing.medium,
  },
  categoriesContainer: {
    paddingHorizontal: Spacing.medium,
  },
  featuredSection: {
    backgroundColor: Colors.background.surface,
    paddingVertical: Spacing.large,
    marginBottom: Spacing.small,
  },
  featuredContainer: {
    paddingHorizontal: Spacing.medium,
  },
  featuredServiceCard: {
    width: 280,
    marginRight: Spacing.medium,
  },
  allServicesSection: {
    backgroundColor: Colors.background.surface,
    paddingTop: Spacing.large,
    flex: 1,
  },
});

export default ServicesScreen;
