/**
 * Enhanced Initialization Screens Tests
 * Tests to verify enhanced initialization and onboarding screens function correctly
 */

import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { EnhancedInitializationScreen } from '../EnhancedInitializationScreen';
import { EnhancedWelcomeScreen } from '../EnhancedWelcomeScreen';
import { EnhancedRoleSelectionScreen } from '../EnhancedRoleSelectionScreen';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock navigation
const mockNavigate = jest.fn();
const mockReset = jest.fn();
const mockGoBack = jest.fn();

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    reset: mockReset,
    goBack: mockGoBack,
  }),
}));

// Mock Animated
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Animated: {
      ...RN.Animated,
      timing: jest.fn(() => ({
        start: jest.fn((callback) => callback && callback()),
      })),
      parallel: jest.fn(() => ({
        start: jest.fn((callback) => callback && callback()),
      })),
      sequence: jest.fn(() => ({
        start: jest.fn((callback) => callback && callback()),
      })),
      Value: jest.fn(() => ({
        setValue: jest.fn(),
        interpolate: jest.fn(() => 0),
      })),
    },
  };
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const Stack = createStackNavigator();
  
  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Test" component={() => <>{children}</>} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

describe('EnhancedInitializationScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
  });

  it('renders correctly with loading state', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    expect(getByText('Vierla')).toBeTruthy();
    expect(getByText('Initializing app...')).toBeTruthy();
  });

  it('shows progress indicator during initialization', async () => {
    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    // Progress bar should be visible
    const progressBar = getByTestId('progress-bar');
    expect(progressBar).toBeTruthy();
  });

  it('displays initialization steps correctly', async () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    // Should show first initialization step
    expect(getByText('Initializing app...')).toBeTruthy();
  });

  it('navigates to RoleSelection for new users', async () => {
    (AsyncStorage.getItem as jest.Mock)
      .mockResolvedValueOnce(null) // No existing user data
      .mockResolvedValueOnce(null); // No onboarding completed

    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('RoleSelection');
    }, { timeout: 3000 });
  });

  it('navigates to Auth for existing users', async () => {
    (AsyncStorage.getItem as jest.Mock)
      .mockResolvedValueOnce('existing_user') // Existing user data
      .mockResolvedValueOnce('true'); // Onboarding completed

    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockReset).toHaveBeenCalledWith({
        index: 0,
        routes: [{ name: 'Auth' }],
      });
    }, { timeout: 3000 });
  });

  it('calls onComplete callback when provided', async () => {
    const mockOnComplete = jest.fn();

    render(
      <TestWrapper>
        <EnhancedInitializationScreen onComplete={mockOnComplete} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockOnComplete).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('handles initialization errors gracefully', async () => {
    (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

    const { getByText } = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(getByText(/Failed to initialize app/)).toBeTruthy();
    });
  });
});

describe('EnhancedWelcomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with all UI elements', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    expect(getByText('Vierla')).toBeTruthy();
    expect(getByText('Your trusted service marketplace')).toBeTruthy();
    expect(getByText('Welcome to Vierla')).toBeTruthy();
    expect(getByText('Get Started')).toBeTruthy();
  });

  it('displays feature cards correctly', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    expect(getByText('Why Choose Vierla?')).toBeTruthy();
    // Feature cards should be visible
    expect(getByText(/trusted service providers/i)).toBeTruthy();
  });

  it('navigates to initialization when Get Started is pressed', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    const getStartedButton = getByText('Get Started');
    fireEvent.press(getStartedButton);

    expect(mockNavigate).toHaveBeenCalledWith('Initialization');
  });

  it('shows animated logo and content', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    // Logo container should be present
    const logoContainer = getByTestId('logo-container');
    expect(logoContainer).toBeTruthy();
  });

  it('handles back navigation correctly', () => {
    const mockOnBack = jest.fn();

    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen onBack={mockOnBack} />
      </TestWrapper>
    );

    // If back button exists, test it
    try {
      const backButton = getByTestId('back-button');
      fireEvent.press(backButton);
      expect(mockOnBack).toHaveBeenCalled();
    } catch {
      // Back button might not exist on welcome screen
    }
  });
});

describe('EnhancedRoleSelectionScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
  });

  it('renders correctly with role options', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    expect(getByText('Choose Your Role')).toBeTruthy();
    expect(getByText('I need services')).toBeTruthy();
    expect(getByText('I provide services')).toBeTruthy();
    expect(getByText('Continue')).toBeTruthy();
  });

  it('allows selecting customer role', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    const customerOption = getByText('I need services');
    fireEvent.press(customerOption);

    // Role should be selected (visual feedback)
    expect(customerOption).toBeTruthy();
  });

  it('allows selecting provider role', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    const providerOption = getByText('I provide services');
    fireEvent.press(providerOption);

    // Role should be selected (visual feedback)
    expect(providerOption).toBeTruthy();
  });

  it('navigates to CustomerOnboarding when customer role selected', async () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // Select customer role
    const customerOption = getByText('I need services');
    fireEvent.press(customerOption);

    // Press continue
    const continueButton = getByText('Continue');
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('selected_role', 'customer');
      expect(mockNavigate).toHaveBeenCalledWith('CustomerOnboarding');
    });
  });

  it('navigates to ProviderOnboarding when provider role selected', async () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // Select provider role
    const providerOption = getByText('I provide services');
    fireEvent.press(providerOption);

    // Press continue
    const continueButton = getByText('Continue');
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('selected_role', 'service_provider');
      expect(mockNavigate).toHaveBeenCalledWith('ProviderOnboarding');
    });
  });

  it('shows error when no role selected and continue pressed', () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // Press continue without selecting role
    const continueButton = getByText('Continue');
    fireEvent.press(continueButton);

    // Should show alert (mocked)
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('handles back navigation correctly', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    try {
      const backButton = getByTestId('back-button');
      fireEvent.press(backButton);
      expect(mockGoBack).toHaveBeenCalled();
    } catch {
      // Back button might be implemented differently
    }
  });

  it('saves role selection to AsyncStorage', async () => {
    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // Select customer role
    const customerOption = getByText('I need services');
    fireEvent.press(customerOption);

    // Press continue
    const continueButton = getByText('Continue');
    fireEvent.press(continueButton);

    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('selected_role', 'customer');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('onboarding_step', 'role_selected');
    });
  });

  it('handles AsyncStorage errors gracefully', async () => {
    (AsyncStorage.setItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

    const { getByText } = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // Select customer role
    const customerOption = getByText('I need services');
    fireEvent.press(customerOption);

    // Press continue
    const continueButton = getByText('Continue');
    fireEvent.press(continueButton);

    await waitFor(() => {
      // Should handle error gracefully
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });
  });
});

describe('Enhanced Screens Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('maintains consistent styling across all enhanced screens', () => {
    // Test that all enhanced screens use consistent design patterns
    const welcomeScreen = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    const initScreen = render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    const roleScreen = render(
      <TestWrapper>
        <EnhancedRoleSelectionScreen />
      </TestWrapper>
    );

    // All should have Vierla branding
    expect(welcomeScreen.getByText('Vierla')).toBeTruthy();
    expect(initScreen.getByText('Vierla')).toBeTruthy();
    expect(roleScreen.getByText('Choose Your Role')).toBeTruthy();
  });

  it('follows proper navigation flow between enhanced screens', async () => {
    // Test complete flow: Welcome -> Initialization -> RoleSelection
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

    // Start with welcome screen
    const { getByText: getWelcomeText } = render(
      <TestWrapper>
        <EnhancedWelcomeScreen />
      </TestWrapper>
    );

    fireEvent.press(getWelcomeText('Get Started'));
    expect(mockNavigate).toHaveBeenCalledWith('Initialization');

    // Initialization should navigate to role selection for new users
    render(
      <TestWrapper>
        <EnhancedInitializationScreen />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('RoleSelection');
    });
  });
});
