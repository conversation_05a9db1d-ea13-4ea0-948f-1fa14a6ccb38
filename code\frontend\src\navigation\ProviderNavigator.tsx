/**
 * Provider Navigator
 * Navigation structure for service providers
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

import { ProviderDashboardScreen } from '../screens/provider/ProviderDashboardScreen';
import { EnhancedProviderServicesScreen } from '../screens/provider/EnhancedProviderServicesScreen';
import { AddServiceScreen } from '../screens/provider/AddServiceScreen';
import { EnhancedAddServiceScreen } from '../screens/provider/EnhancedAddServiceScreen';
import { EditServiceScreen } from '../screens/provider/EditServiceScreen';
import { ProfileScreen } from '../screens/main/ProfileScreen';
import { colors } from '../theme';

export type ProviderTabParamList = {
  Dashboard: undefined;
  Services: undefined;
  Bookings: undefined;
  Profile: undefined;
};

export type ProviderStackParamList = {
  ProviderTabs: undefined;
  AddService: undefined;
  EnhancedAddService: undefined;
  EditService: { serviceId: string };
  ServiceDetails: { serviceId: string };
  ProviderAnalytics: undefined;
  ProviderBookings: undefined;
};

const Tab = createBottomTabNavigator<ProviderTabParamList>();
const Stack = createStackNavigator<ProviderStackParamList>();

// Placeholder components for missing screens
const ProviderBookingsScreen: React.FC = () => {
  const React = require('react');
  const { View, Text, StyleSheet } = require('react-native');
  
  return (
    <View style={styles.placeholder}>
      <Text style={styles.placeholderText}>Provider Bookings</Text>
      <Text style={styles.placeholderSubtext}>Coming Soon</Text>
    </View>
  );
};

const ProviderTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Services') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Bookings') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.border,
          borderTopWidth: 1,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={ProviderDashboardScreen}
        options={{
          title: 'Dashboard',
        }}
      />
      <Tab.Screen
        name="Services"
        component={EnhancedProviderServicesScreen}
        options={{
          title: 'Services',
        }}
      />
      <Tab.Screen 
        name="Bookings" 
        component={ProviderBookingsScreen}
        options={{
          title: 'Bookings',
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
};

export const ProviderNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: colors.background },
      }}
    >
      <Stack.Screen 
        name="ProviderTabs" 
        component={ProviderTabs}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AddService"
        component={AddServiceScreen}
        options={{
          headerShown: true,
          title: 'Add Service',
          headerStyle: {
            backgroundColor: colors.white,
          },
          headerTintColor: colors.textPrimary,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      />
      <Stack.Screen
        name="EnhancedAddService"
        component={EnhancedAddServiceScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen 
        name="EditService" 
        component={EditServiceScreen}
        options={{
          headerShown: true,
          title: 'Edit Service',
          headerStyle: {
            backgroundColor: colors.white,
          },
          headerTintColor: colors.textPrimary,
          headerTitleStyle: {
            fontWeight: '600',
          },
        }}
      />
    </Stack.Navigator>
  );
};

const styles = require('react-native').StyleSheet.create({
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: 20,
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: colors.textSecondary,
  },
});
