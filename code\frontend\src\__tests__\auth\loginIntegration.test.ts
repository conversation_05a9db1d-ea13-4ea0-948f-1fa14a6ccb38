/**
 * Login Integration Tests
 * End-to-end tests for login authentication flow
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../../services/api/auth';
import { apiClient } from '../../services/api/client';

// Mock AsyncStorage
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Mock fetch for actual HTTP requests
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('Login Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.getItem.mockResolvedValue(null);
    mockAsyncStorage.setItem.mockResolvedValue();
    mockAsyncStorage.multiSet.mockResolvedValue();
    mockAsyncStorage.multiRemove.mockResolvedValue();
  });

  describe('Successful Login Flow', () => {
    it('should complete full login flow with valid credentials', async () => {
      // Mock successful login response
      const mockLoginResponse = {
        access: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
        refresh: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer' as const,
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockLoginResponse,
        headers: new Headers(),
        redirected: false,
        statusText: 'OK',
        type: 'basic',
        url: 'http://************:8000/api/auth/login/',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as Response);

      // Test the login flow
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const result = await authAPI.login(loginData);

      // Verify the response
      expect(result).toEqual(mockLoginResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://************:8000/api/auth/login/',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(loginData),
        })
      );
    });

    it('should handle token storage after successful login', async () => {
      const tokens = {
        access: 'access-token-123',
        refresh: 'refresh-token-456',
      };

      const userData = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        full_name: 'Test User',
        role: 'customer' as const,
        is_verified: true,
        account_status: 'active',
        created_at: '2025-08-06T00:00:00Z',
      };

      // Simulate storing tokens and user data
      await mockAsyncStorage.multiSet([
        ['access_token', tokens.access],
        ['refresh_token', tokens.refresh],
        ['user', JSON.stringify(userData)],
      ]);

      expect(mockAsyncStorage.multiSet).toHaveBeenCalledWith([
        ['access_token', tokens.access],
        ['refresh_token', tokens.refresh],
        ['user', JSON.stringify(userData)],
      ]);
    });
  });

  describe('Authentication Error Scenarios', () => {
    it('should handle invalid credentials (400 error)', async () => {
      const mockErrorResponse = {
        detail: 'Invalid email or password',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => mockErrorResponse,
        headers: new Headers(),
        redirected: false,
        statusText: 'Bad Request',
        type: 'basic',
        url: 'http://************:8000/api/auth/login/',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as Response);

      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      try {
        await authAPI.login(loginData);
        fail('Expected login to throw an error');
      } catch (error: any) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toEqual(mockErrorResponse);
      }
    });

    it('should handle account locked (423 error)', async () => {
      const mockErrorResponse = {
        detail: 'Account temporarily locked due to multiple failed attempts',
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 423,
        json: async () => mockErrorResponse,
        headers: new Headers(),
        redirected: false,
        statusText: 'Locked',
        type: 'basic',
        url: 'http://************:8000/api/auth/login/',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as Response);

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      try {
        await authAPI.login(loginData);
        fail('Expected login to throw an error');
      } catch (error: any) {
        expect(error.response.status).toBe(423);
        expect(error.response.data).toEqual(mockErrorResponse);
      }
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network request failed'));

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      try {
        await authAPI.login(loginData);
        fail('Expected login to throw an error');
      } catch (error: any) {
        expect(error.message).toBe('Network request failed');
      }
    });

    it('should handle server errors (500)', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ detail: 'Internal server error' }),
        headers: new Headers(),
        redirected: false,
        statusText: 'Internal Server Error',
        type: 'basic',
        url: 'http://************:8000/api/auth/login/',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as Response);

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      try {
        await authAPI.login(loginData);
        fail('Expected login to throw an error');
      } catch (error: any) {
        expect(error.response.status).toBe(500);
      }
    });
  });

  describe('Token Management', () => {
    it('should add authorization header to subsequent requests', async () => {
      const accessToken = 'test-access-token';
      mockAsyncStorage.getItem.mockResolvedValue(accessToken);

      // Mock a protected API call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({ message: 'success' }),
        headers: new Headers(),
        redirected: false,
        statusText: 'OK',
        type: 'basic',
        url: 'http://************:8000/api/auth/profile/',
        clone: jest.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        formData: jest.fn(),
        text: jest.fn(),
      } as Response);

      await authAPI.getProfile();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://************:8000/api/auth/profile/',
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: `Bearer ${accessToken}`,
          }),
        })
      );
    });

    it('should handle token refresh on 401 error', async () => {
      const refreshToken = 'test-refresh-token';
      const newTokens = {
        access: 'new-access-token',
        refresh: 'new-refresh-token',
      };

      mockAsyncStorage.getItem
        .mockResolvedValueOnce('expired-access-token') // First call for access token
        .mockResolvedValueOnce(refreshToken); // Second call for refresh token

      // Mock 401 response for initial request
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          json: async () => ({ detail: 'Token expired' }),
          headers: new Headers(),
          redirected: false,
          statusText: 'Unauthorized',
          type: 'basic',
          url: 'http://************:8000/api/auth/profile/',
          clone: jest.fn(),
          body: null,
          bodyUsed: false,
          arrayBuffer: jest.fn(),
          blob: jest.fn(),
          formData: jest.fn(),
          text: jest.fn(),
        } as Response)
        // Mock successful refresh response
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => newTokens,
          headers: new Headers(),
          redirected: false,
          statusText: 'OK',
          type: 'basic',
          url: 'http://************:8000/api/auth/refresh/',
          clone: jest.fn(),
          body: null,
          bodyUsed: false,
          arrayBuffer: jest.fn(),
          blob: jest.fn(),
          formData: jest.fn(),
          text: jest.fn(),
        } as Response)
        // Mock successful retry with new token
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: async () => ({ message: 'success' }),
          headers: new Headers(),
          redirected: false,
          statusText: 'OK',
          type: 'basic',
          url: 'http://************:8000/api/auth/profile/',
          clone: jest.fn(),
          body: null,
          bodyUsed: false,
          arrayBuffer: jest.fn(),
          blob: jest.fn(),
          formData: jest.fn(),
          text: jest.fn(),
        } as Response);

      const result = await authAPI.getProfile();

      expect(result).toEqual({ message: 'success' });
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('access_token', newTokens.access);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('refresh_token', newTokens.refresh);
    });
  });

  describe('Backend Connectivity', () => {
    it('should connect to correct backend URL', () => {
      expect(apiClient.defaults.baseURL).toBe('http://************:8000/api');
    });

    it('should have correct timeout configuration', () => {
      expect(apiClient.defaults.timeout).toBe(10000);
    });

    it('should have correct content type header', () => {
      expect(apiClient.defaults.headers['Content-Type']).toBe('application/json');
    });
  });
});
