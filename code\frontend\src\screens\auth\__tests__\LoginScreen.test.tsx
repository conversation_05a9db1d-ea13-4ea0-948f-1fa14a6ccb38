/**
 * LoginScreen Test Suite
 * Simplified tests for login functionality
 */

import React from 'react';
import { LoginScreen } from '../LoginScreen';

// Mock React Native components
jest.mock('react-native', () => ({
  View: 'View',
  Text: 'Text',
  ScrollView: 'ScrollView',
  KeyboardAvoidingView: 'KeyboardAvoidingView',
  Platform: { OS: 'ios' },
  Alert: { alert: jest.fn() },
  StyleSheet: { create: (styles: any) => styles },
}));

// Mock the navigation prop
const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

describe('LoginScreen', () => {
  it('renders without crashing', () => {
    expect(() => {
      React.createElement(LoginScreen, { navigation: mockNavigation });
    }).not.toThrow();
  });

  it('accepts navigation prop', () => {
    const element = React.createElement(LoginScreen, { navigation: mockNavigation });
    expect(element.props.navigation).toBe(mockNavigation);
  });
});
