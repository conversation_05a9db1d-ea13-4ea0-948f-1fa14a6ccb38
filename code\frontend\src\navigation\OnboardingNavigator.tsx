/**
 * OnboardingNavigator
 * 
 * Navigation stack for the onboarding flow including welcome, initialization,
 * role selection, and onboarding carousels.
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import {
  EnhancedWelcomeScreen as WelcomeScreen,
  EnhancedInitializationScreen as InitializationScreen,
  EnhancedRoleSelectionScreen as RoleSelectionScreen,
  CustomerOnboardingCarousel,
  ProviderOnboardingCarousel,
} from '../screens/onboarding';
import OnboardingFlow from '../screens/onboarding/OnboardingFlow';

export type OnboardingStackParamList = {
  Welcome: undefined;
  Initialization: undefined;
  RoleSelection: undefined;
  CustomerOnboarding: undefined;
  ProviderOnboarding: undefined;
  OnboardingFlow: undefined;
};

const Stack = createStackNavigator<OnboardingStackParamList>();

export const OnboardingNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="Welcome"
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFFFFF' },
        gestureEnabled: false, // Disable swipe back during onboarding
      }}
    >
      <Stack.Screen 
        name="Welcome" 
        component={WelcomeScreen}
        options={{
          gestureEnabled: false, // Prevent going back from welcome
        }}
      />
      
      <Stack.Screen 
        name="Initialization" 
        component={InitializationScreen}
        options={{
          gestureEnabled: false, // Prevent interrupting initialization
        }}
      />
      
      <Stack.Screen 
        name="RoleSelection" 
        component={RoleSelectionScreen}
      />
      
      <Stack.Screen 
        name="CustomerOnboarding" 
        component={CustomerOnboardingCarousel}
      />
      
      <Stack.Screen 
        name="ProviderOnboarding" 
        component={ProviderOnboardingCarousel}
      />
    </Stack.Navigator>
  );
};

export default OnboardingNavigator;
