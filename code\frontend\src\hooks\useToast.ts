/**
 * useToast Hook - Toast Notification Management for Vierla Application
 * 
 * This hook provides a simple interface for showing toast notifications
 * throughout the application with consistent styling and behavior.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import { useCallback, useRef } from 'react';
import * as Haptics from 'expo-haptics';
import {
  ToastConfig,
  UseToastReturn,
} from '../utils/errorTypes';

// Global toast manager reference
let globalToastManager: {
  showToast: (config: Omit<ToastConfig, 'id'>) => void;
  dismissAll: () => void;
} | null = null;

/**
 * Set the global toast manager (called by ToastProvider)
 */
export const setGlobalToastManager = (manager: typeof globalToastManager) => {
  globalToastManager = manager;
};

/**
 * Toast Hook
 */
export const useToast = (): UseToastReturn => {
  const toastQueue = useRef<ToastConfig[]>([]);

  /**
   * Show success toast
   */
  const showSuccess = useCallback(
    (title: string, message?: string, options?: Partial<ToastConfig>): void => {
      const config: Omit<ToastConfig, 'id'> = {
        type: 'success',
        title,
        message,
        duration: 3000,
        enableHaptics: true,
        ...options,
      };

      if (globalToastManager) {
        globalToastManager.showToast(config);
      } else {
        // Queue toast if manager not ready
        toastQueue.current.push(config as ToastConfig);
      }

      // Haptic feedback
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    },
    []
  );

  /**
   * Show error toast
   */
  const showError = useCallback(
    (title: string, message?: string, options?: Partial<ToastConfig>): void => {
      const config: Omit<ToastConfig, 'id'> = {
        type: 'error',
        title,
        message,
        duration: 5000,
        enableHaptics: true,
        ...options,
      };

      if (globalToastManager) {
        globalToastManager.showToast(config);
      } else {
        // Queue toast if manager not ready
        toastQueue.current.push(config as ToastConfig);
      }

      // Haptic feedback
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    },
    []
  );

  /**
   * Show warning toast
   */
  const showWarning = useCallback(
    (title: string, message?: string, options?: Partial<ToastConfig>): void => {
      const config: Omit<ToastConfig, 'id'> = {
        type: 'warning',
        title,
        message,
        duration: 4000,
        enableHaptics: true,
        ...options,
      };

      if (globalToastManager) {
        globalToastManager.showToast(config);
      } else {
        // Queue toast if manager not ready
        toastQueue.current.push(config as ToastConfig);
      }

      // Haptic feedback
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    },
    []
  );

  /**
   * Show info toast
   */
  const showInfo = useCallback(
    (title: string, message?: string, options?: Partial<ToastConfig>): void => {
      const config: Omit<ToastConfig, 'id'> = {
        type: 'info',
        title,
        message,
        duration: 3000,
        enableHaptics: false,
        ...options,
      };

      if (globalToastManager) {
        globalToastManager.showToast(config);
      } else {
        // Queue toast if manager not ready
        toastQueue.current.push(config as ToastConfig);
      }

      // Haptic feedback
      if (config.enableHaptics) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    },
    []
  );

  /**
   * Dismiss all toasts
   */
  const dismissAll = useCallback((): void => {
    if (globalToastManager) {
      globalToastManager.dismissAll();
    }
    toastQueue.current = [];
  }, []);

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    dismissAll,
  };
};

/**
 * Standalone toast functions for use outside of React components
 */
export const toast = {
  success: (title: string, message?: string, options?: Partial<ToastConfig>) => {
    const config: Omit<ToastConfig, 'id'> = {
      type: 'success',
      title,
      message,
      duration: 3000,
      enableHaptics: true,
      ...options,
    };

    if (globalToastManager) {
      globalToastManager.showToast(config);
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  },

  error: (title: string, message?: string, options?: Partial<ToastConfig>) => {
    const config: Omit<ToastConfig, 'id'> = {
      type: 'error',
      title,
      message,
      duration: 5000,
      enableHaptics: true,
      ...options,
    };

    if (globalToastManager) {
      globalToastManager.showToast(config);
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
    }
  },

  warning: (title: string, message?: string, options?: Partial<ToastConfig>) => {
    const config: Omit<ToastConfig, 'id'> = {
      type: 'warning',
      title,
      message,
      duration: 4000,
      enableHaptics: true,
      ...options,
    };

    if (globalToastManager) {
      globalToastManager.showToast(config);
      if (config.enableHaptics !== false) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    }
  },

  info: (title: string, message?: string, options?: Partial<ToastConfig>) => {
    const config: Omit<ToastConfig, 'id'> = {
      type: 'info',
      title,
      message,
      duration: 3000,
      enableHaptics: false,
      ...options,
    };

    if (globalToastManager) {
      globalToastManager.showToast(config);
      if (config.enableHaptics) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }
  },

  dismissAll: () => {
    if (globalToastManager) {
      globalToastManager.dismissAll();
    }
  },
};
