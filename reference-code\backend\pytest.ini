[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings.testing
python_files = tests.py test_*.py *_tests.py
ignore = */management/* */migrations/*
python_classes = Test* *Tests
python_functions = test_*
testpaths = tests apps
norecursedirs = */management/* */migrations/* */venv/* */node_modules/*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=apps
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=85
    --reuse-db
    --nomigrations
    --maxfail=5
    -p no:warnings
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    auth: Authentication tests
    catalog: Catalog tests
    bookings: Booking tests
    api: API tests
    models: Model tests
    views: View tests
    serializers: Serializer tests
    utils: Utility tests
    security: Security tests
    performance: Performance tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:django_oauth_toolkit.*
    ignore::UserWarning:rest_framework_simplejwt.*
