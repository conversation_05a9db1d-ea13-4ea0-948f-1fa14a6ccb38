import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';

// Types
export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  icon: string;
  color: string;
  mobile_icon?: string;
  is_popular: boolean;
  service_count: number;
  image?: string;
}

export interface CategoryCardProps {
  category: ServiceCategory;
  onPress: () => void;
  variant?: 'default' | 'compact' | 'icon-only';
  testID?: string;
}

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  border: '#E0E0E0',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
};

export const CategoryCard: React.FC<CategoryCardProps> = ({
  category,
  onPress,
  variant = 'default',
  testID,
}) => {
  const renderIcon = () => {
    // If category has an image, use it
    if (category.image) {
      return (
        <Image
          source={{ uri: category.image }}
          style={styles.categoryImage}
          testID={`${testID}-image`}
        />
      );
    }

    // Otherwise, render the icon as text (emoji or icon name)
    return (
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: category.color || Colors.primary.main },
        ]}
        testID={`${testID}-icon-container`}
      >
        <Text style={styles.iconText} testID={`${testID}-icon`}>
          {category.mobile_icon || category.icon}
        </Text>
      </View>
    );
  };

  const renderServiceCount = () => {
    if (variant === 'icon-only') return null;

    return (
      <Text style={styles.serviceCount} testID={`${testID}-service-count`}>
        {category.service_count} service{category.service_count !== 1 ? 's' : ''}
      </Text>
    );
  };

  const renderPopularBadge = () => {
    if (!category.is_popular || variant === 'icon-only') return null;

    return (
      <View style={styles.popularBadge} testID={`${testID}-popular-badge`}>
        <Text style={styles.popularText}>Popular</Text>
      </View>
    );
  };

  const getCardStyle = () => {
    switch (variant) {
      case 'compact':
        return [styles.card, styles.compactCard];
      case 'icon-only':
        return [styles.card, styles.iconOnlyCard];
      default:
        return styles.card;
    }
  };

  const getContentStyle = () => {
    switch (variant) {
      case 'icon-only':
        return styles.iconOnlyContent;
      default:
        return styles.content;
    }
  };

  return (
    <TouchableOpacity
      style={getCardStyle()}
      onPress={onPress}
      testID={testID}
      activeOpacity={0.7}
    >
      <View style={styles.iconWrapper}>
        {renderIcon()}
        {renderPopularBadge()}
      </View>

      <View style={getContentStyle()}>
        <Text
          style={[
            styles.categoryName,
            variant === 'icon-only' && styles.iconOnlyName,
          ]}
          numberOfLines={variant === 'icon-only' ? 2 : 1}
          testID={`${testID}-name`}
        >
          {category.name}
        </Text>

        {renderServiceCount()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.background.surface,
    borderRadius: 12,
    marginRight: Spacing.medium,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    minWidth: 120,
  },
  compactCard: {
    minWidth: 100,
    marginRight: Spacing.small,
  },
  iconOnlyCard: {
    minWidth: 80,
    marginRight: Spacing.small,
    alignItems: 'center',
  },
  iconWrapper: {
    position: 'relative',
    alignItems: 'center',
    paddingTop: Spacing.medium,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.small,
  },
  iconText: {
    fontSize: 24,
    color: Colors.primary.white,
  },
  categoryImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: Spacing.small,
    resizeMode: 'cover',
  },
  popularBadge: {
    position: 'absolute',
    top: Spacing.micro,
    right: Spacing.micro,
    backgroundColor: Colors.primary.main,
    paddingHorizontal: Spacing.micro,
    paddingVertical: 2,
    borderRadius: 8,
  },
  popularText: {
    color: Colors.primary.white,
    fontSize: 10,
    fontWeight: '600',
  },
  content: {
    padding: Spacing.medium,
    paddingTop: 0,
  },
  iconOnlyContent: {
    paddingHorizontal: Spacing.small,
    paddingBottom: Spacing.medium,
    paddingTop: 0,
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.micro,
    textAlign: 'left',
  },
  iconOnlyName: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  serviceCount: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
});

export default CategoryCard;
