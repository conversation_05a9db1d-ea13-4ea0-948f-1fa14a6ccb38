/**
 * Theme Context
 * Provides theme configuration throughout the application
 * Implements safety measures to prevent hermes runtime errors
 */

import React, { createContext, useContext, ReactNode, useMemo } from 'react';
import { theme as defaultTheme } from '../theme';

export interface ThemeContextType {
  colors: typeof defaultTheme.colors;
  spacing: typeof defaultTheme.spacing;
  typography: typeof defaultTheme.typography;
  borderRadius: typeof defaultTheme.borderRadius;
  shadows: typeof defaultTheme.shadows;
}

// Create theme context with default value to prevent undefined errors
const ThemeContext = createContext<ThemeContextType>(defaultTheme);

export interface ThemeProviderProps {
  children: ReactNode;
  theme?: ThemeContextType;
}

/**
 * ThemeProvider component
 * Provides theme configuration to all child components
 * Implements safety measures to prevent runtime errors
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  theme: customTheme 
}) => {
  // Use custom theme if provided, otherwise use default theme
  const themeValue = useMemo(() => {
    const finalTheme = customTheme || defaultTheme;
    
    // Validate theme structure to prevent runtime errors
    if (!finalTheme || typeof finalTheme !== 'object') {
      console.warn('[ThemeProvider] Invalid theme provided, using default theme');
      return defaultTheme;
    }

    // Ensure required properties exist
    const requiredProperties = ['colors', 'spacing', 'typography', 'borderRadius', 'shadows'];
    const missingProperties = requiredProperties.filter(prop => !finalTheme[prop as keyof ThemeContextType]);
    
    if (missingProperties.length > 0) {
      console.warn(`[ThemeProvider] Missing theme properties: ${missingProperties.join(', ')}, using default theme`);
      return defaultTheme;
    }

    // Validate colors structure
    if (!finalTheme.colors || typeof finalTheme.colors !== 'object') {
      console.warn('[ThemeProvider] Invalid colors object, using default theme');
      return defaultTheme;
    }

    // Ensure critical color properties exist
    const criticalColorProperties = ['primary', 'background', 'text'];
    const missingColorProperties = criticalColorProperties.filter(prop => !finalTheme.colors[prop as keyof typeof finalTheme.colors]);
    
    if (missingColorProperties.length > 0) {
      console.warn(`[ThemeProvider] Missing critical color properties: ${missingColorProperties.join(', ')}, using default theme`);
      return defaultTheme;
    }

    return finalTheme;
  }, [customTheme]);

  return (
    <ThemeContext.Provider value={themeValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * useTheme hook
 * Provides safe access to theme configuration
 * Implements fallback mechanisms to prevent runtime errors
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  
  // Context should always be available due to default value, but add safety check
  if (!context) {
    console.warn('[useTheme] Theme context not found, using default theme');
    return defaultTheme;
  }

  // Additional safety check for context integrity
  if (typeof context !== 'object' || !context.colors) {
    console.warn('[useTheme] Invalid theme context, using default theme');
    return defaultTheme;
  }

  return context;
};

/**
 * Safe color access utility
 * Provides safe access to nested color properties
 */
export const useSafeColor = (colorPath: string, fallback?: string): string => {
  const { colors } = useTheme();
  
  try {
    const pathParts = colorPath.split('.');
    let current: any = colors;
    
    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        console.warn(`[useSafeColor] Color path "${colorPath}" not found, using fallback`);
        return fallback || colors.primary;
      }
    }
    
    if (typeof current === 'string') {
      return current;
    } else {
      console.warn(`[useSafeColor] Color path "${colorPath}" is not a string, using fallback`);
      return fallback || colors.primary;
    }
  } catch (error) {
    console.error(`[useSafeColor] Error accessing color path "${colorPath}":`, error);
    return fallback || colors.primary;
  }
};

/**
 * Safe spacing access utility
 * Provides safe access to spacing values
 */
export const useSafeSpacing = (spacingKey: keyof ThemeContextType['spacing']): number => {
  const { spacing } = useTheme();
  
  try {
    const value = spacing[spacingKey];
    if (typeof value === 'number') {
      return value;
    } else {
      console.warn(`[useSafeSpacing] Spacing "${spacingKey}" is not a number, using fallback`);
      return 16; // Default spacing
    }
  } catch (error) {
    console.error(`[useSafeSpacing] Error accessing spacing "${spacingKey}":`, error);
    return 16; // Default spacing
  }
};

/**
 * Theme validation utility
 * Validates theme structure to prevent runtime errors
 */
export const validateTheme = (theme: any): boolean => {
  try {
    if (!theme || typeof theme !== 'object') {
      return false;
    }

    const requiredProperties = ['colors', 'spacing', 'typography', 'borderRadius', 'shadows'];
    const hasAllProperties = requiredProperties.every(prop => theme[prop]);
    
    if (!hasAllProperties) {
      return false;
    }

    // Validate colors structure
    if (!theme.colors || typeof theme.colors !== 'object') {
      return false;
    }

    const requiredColors = ['primary', 'background', 'text'];
    const hasAllColors = requiredColors.every(color => theme.colors[color]);
    
    return hasAllColors;
  } catch (error) {
    console.error('[validateTheme] Error validating theme:', error);
    return false;
  }
};

export default ThemeProvider;
