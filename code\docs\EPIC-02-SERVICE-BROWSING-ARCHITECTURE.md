# EPIC-02: Service Browsing & Display - System Architecture

## Overview

EPIC-02 implements a comprehensive service catalog system that allows users to browse, search, and discover services offered by providers. This builds upon the authentication foundation from EPIC-01.

## Legacy Feature Analysis

Based on the reference-codebase analysis, the legacy system includes:

### Core Service Features
- **Service Categories**: Hierarchical category system with icons, colors, and images
- **Service Listings**: Comprehensive service information with pricing, duration, and descriptions
- **Service Search**: Advanced search with filters, sorting, and suggestions
- **Service Discovery**: Popular services, featured services, and recommendations
- **Service Details**: Full service information with provider details and media

### Advanced Features
- **Location-based Search**: Geographic filtering and distance calculations
- **Price Filtering**: Range-based price filtering with multiple pricing types
- **Availability Filtering**: Real-time availability and booking status
- **Rating & Reviews**: Service ratings and review integration
- **Service Media**: Image galleries and service photos

## Database Schema Design

### ServiceCategory Model
```python
class ServiceCategory(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=120, unique=True)
    description = models.TextField()
    icon = models.CharField(max_length=50)  # Icon identifier
    color = models.CharField(max_length=7, default='#8FBC8F')  # Hex color
    image = models.ImageField(upload_to='categories/%Y/%m/', blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True)
    is_popular = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    mobile_icon = models.CharField(max_length=50, blank=True)
    service_count = models.PositiveIntegerField(default=0)  # Calculated field
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### Service Model
```python
class Service(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    provider = models.ForeignKey(User, on_delete=models.CASCADE, related_name='services')
    category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE)
    name = models.CharField(max_length=200)
    description = models.TextField()
    short_description = models.CharField(max_length=255, blank=True)
    mobile_description = models.CharField(max_length=160, blank=True)
    
    # Pricing
    base_price = models.DecimalField(max_digits=10, decimal_places=2)
    price_type = models.CharField(max_length=20, choices=[
        ('fixed', 'Fixed Price'),
        ('hourly', 'Hourly Rate'),
        ('range', 'Price Range'),
        ('consultation', 'Consultation Required'),
    ], default='fixed')
    max_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    
    # Duration
    duration = models.PositiveIntegerField()  # minutes
    buffer_time = models.PositiveIntegerField(default=15)
    
    # Media
    image = models.ImageField(upload_to='services/%Y/%m/', blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_available = models.BooleanField(default=True)
    is_popular = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    
    # Metrics
    booking_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    review_count = models.PositiveIntegerField(default=0)
    
    # Requirements
    requirements = models.JSONField(default=list, blank=True)
    preparation_instructions = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## API Endpoints Design

### Category Endpoints
- `GET /api/services/categories/` - List all categories
- `GET /api/services/categories/{id}/` - Category details
- `GET /api/services/categories/popular/` - Popular categories
- `GET /api/services/categories/{id}/services/` - Services in category

### Service Endpoints
- `GET /api/services/` - List services with filtering
- `GET /api/services/{id}/` - Service details
- `GET /api/services/popular/` - Popular services
- `GET /api/services/featured/` - Featured services
- `GET /api/services/search/` - Advanced search

### Search & Filter Parameters
```
?search=query          # Text search
?category=uuid         # Filter by category
?price_min=decimal     # Minimum price
?price_max=decimal     # Maximum price
?duration_min=int      # Minimum duration
?duration_max=int      # Maximum duration
?is_popular=bool       # Popular services only
?is_featured=bool      # Featured services only
?is_available=bool     # Available services only
?ordering=field        # Sort by: name, price, duration, rating, created_at
```

## Frontend Component Architecture

### Screen Components
1. **ServicesScreen** - Main service browsing screen
2. **ServiceCategoriesScreen** - Category browsing
3. **ServiceDetailScreen** - Individual service details
4. **SearchScreen** - Advanced search interface

### Reusable Components
1. **ServiceCard** - Service listing card
2. **CategoryCard** - Category display card
3. **SearchBar** - Search input with suggestions
4. **FilterPanel** - Advanced filtering options
5. **ServiceGrid** - Grid layout for services
6. **ServiceList** - List layout for services

### Navigation Structure
```
MainNavigator
├── ServicesTab
│   ├── ServicesScreen (Service listings)
│   ├── ServiceCategoriesScreen (Categories)
│   ├── ServiceDetailScreen (Service details)
│   └── SearchScreen (Search & filters)
```

## State Management

### API Service Structure
```typescript
// services/api/servicesApi.ts
export const servicesApi = {
  getServices: (filters?: ServiceFilters) => Promise<PaginatedResponse<Service>>,
  getService: (id: string) => Promise<Service>,
  getCategories: () => Promise<ServiceCategory[]>,
  getPopularServices: () => Promise<Service[]>,
  getFeaturedServices: () => Promise<Service[]>,
  searchServices: (query: string, filters?: SearchFilters) => Promise<SearchResponse>,
  getServicesByCategory: (categoryId: string) => Promise<Service[]>,
};
```

### React Query Integration
```typescript
// hooks/useServices.ts
export const useServices = (filters?: ServiceFilters) => {
  return useQuery({
    queryKey: ['services', filters],
    queryFn: () => servicesApi.getServices(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useServiceCategories = () => {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: servicesApi.getCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};
```

## Implementation Plan

### Phase 1: Backend Foundation
1. Create service catalog Django app
2. Implement ServiceCategory and Service models
3. Create database migrations
4. Set up admin interface

### Phase 2: API Development
1. Create serializers for models
2. Implement ViewSets and API endpoints
3. Add filtering and search capabilities
4. Create pagination and ordering

### Phase 3: Frontend Foundation
1. Create service-related screens
2. Implement basic service listing
3. Add category browsing
4. Create service detail view

### Phase 4: Advanced Features
1. Implement search functionality
2. Add filtering and sorting
3. Create service cards and layouts
4. Add loading states and error handling

### Phase 5: Integration & Testing
1. Connect frontend to backend APIs
2. Test all service browsing flows
3. Optimize performance and caching
4. Add comprehensive test coverage

## Success Criteria

✅ **Service Categories**: Hierarchical category system with visual elements
✅ **Service Listings**: Comprehensive service information display
✅ **Search Functionality**: Text search with filtering and sorting
✅ **Service Details**: Complete service information with provider details
✅ **Mobile Optimization**: Responsive design for mobile devices
✅ **Performance**: Fast loading with proper caching and pagination
✅ **Test Coverage**: 100% test coverage for all components and APIs

## Dependencies

- **EPIC-01**: Authentication system (completed)
- **User Management**: Provider profiles for service ownership
- **Media Handling**: Image upload and storage for service photos
- **Location Services**: Geographic data for location-based features (future)

## Next Steps

After EPIC-02 completion, this foundation will enable:
- **EPIC-03**: Service Creation & Management for Providers
- **EPIC-05**: Advanced Search & Filtering
- **EPIC-06**: Appointment Booking System
- **EPIC-07**: Reviews and Rating System
