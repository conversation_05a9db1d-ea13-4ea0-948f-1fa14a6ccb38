@echo off
REM Setup PostgreSQL database for Vierla application
REM This script creates the database and user for the Vierla application

echo Setting up PostgreSQL database for Vierla...

REM Set PostgreSQL environment variables
set PGPASSWORD=postgres

REM Create database user
echo Creating database user 'vierla_user'...
psql -U postgres -h localhost -c "CREATE USER vierla_user WITH PASSWORD 'vierla_password';"

REM Create database
echo Creating database 'vierla_db'...
psql -U postgres -h localhost -c "CREATE DATABASE vierla_db OWNER vierla_user;"

REM Grant privileges
echo Granting privileges...
psql -U postgres -h localhost -c "GRANT ALL PRIVILEGES ON DATABASE vierla_db TO vierla_user;"
psql -U postgres -h localhost -c "ALTER USER vierla_user CREATEDB;"

echo PostgreSQL setup complete!
echo Database: vierla_db
echo User: vierla_user
echo Password: vierla_password

pause
