"""
Management command to reset/cleanup test data for Vierla platform.

This command removes all test accounts and associated data according to the
TEST-ACCOUNTS-SPECIFICATION.md document.

Usage:
    python manage.py reset_test_data --force
    python manage.py reset_test_data --force --customers-only
    python manage.py reset_test_data --force --providers-only
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
from django.db import transaction
from catalog.models import User, ServiceProvider, Service, Booking
import os


class Command(BaseCommand):
    help = 'Reset/cleanup test data for the Vierla platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Required flag to confirm data deletion',
            required=True
        )
        parser.add_argument(
            '--customers-only',
            action='store_true',
            help='Reset only customer test accounts'
        )
        parser.add_argument(
            '--providers-only',
            action='store_true',
            help='Reset only provider test accounts'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.WARNING('🗑️  RESETTING TEST DATA FOR VIERLA PLATFORM')
        )
        self.stdout.write('='*60)

        # Security check
        if not self.is_development_environment():
            self.stdout.write(
                self.style.ERROR(
                    '❌ This command can only be run in development environment!'
                )
            )
            return

        if not options['force']:
            self.stdout.write(
                self.style.ERROR(
                    '❌ --force flag is required to confirm data deletion!'
                )
            )
            return

        dry_run = options['dry_run']
        customers_only = options['customers_only']
        providers_only = options['providers_only']

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN MODE - No data will be deleted')
            )

        try:
            with transaction.atomic():
                # Get counts before deletion
                counts = self.get_test_data_counts(customers_only, providers_only)
                
                if dry_run:
                    self.print_deletion_summary(counts, dry_run=True)
                    return

                # Perform deletion
                deleted_counts = self.delete_test_data(customers_only, providers_only)
                
                # Print summary
                self.print_deletion_summary(deleted_counts)

                self.stdout.write('\n' + '='*60)
                self.stdout.write(
                    self.style.SUCCESS('✅ TEST DATA RESET COMPLETED SUCCESSFULLY!')
                )
                self.stdout.write('='*60)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during test data reset: {str(e)}')
            )
            raise

    def is_development_environment(self):
        """Check if running in development environment"""
        return (
            settings.DEBUG and 
            'test' in settings.DATABASES['default']['NAME'].lower() or
            'dev' in settings.DATABASES['default']['NAME'].lower() or
            os.environ.get('DJANGO_ENV') == 'development'
        )

    def get_test_data_counts(self, customers_only=False, providers_only=False):
        """Get counts of test data that would be deleted"""
        counts = {}

        if not providers_only:
            # Customer accounts
            customer_users = User.objects.filter(
                is_test_account=True,
                role='customer'
            )
            counts['customers'] = customer_users.count()

        if not customers_only:
            # Provider accounts and related data
            provider_users = User.objects.filter(
                is_test_account=True,
                role='service_provider'
            )
            counts['providers'] = provider_users.count()

            # Services from test providers
            test_services = Service.objects.filter(
                provider__user__is_test_account=True
            )
            counts['services'] = test_services.count()

            # Bookings involving test accounts
            test_bookings = Booking.objects.filter(
                customer__is_test_account=True
            ) | Booking.objects.filter(
                service__provider__user__is_test_account=True
            )
            counts['bookings'] = test_bookings.count()

        # Admin accounts
        if not customers_only and not providers_only:
            admin_users = User.objects.filter(
                is_test_account=True,
                role='admin'
            )
            counts['admins'] = admin_users.count()

        return counts

    def delete_test_data(self, customers_only=False, providers_only=False):
        """Delete test data and return counts"""
        deleted_counts = {}

        # Delete bookings first (foreign key constraints)
        if not customers_only:
            test_bookings = Booking.objects.filter(
                customer__is_test_account=True
            ) | Booking.objects.filter(
                service__provider__user__is_test_account=True
            )
            deleted_counts['bookings'] = test_bookings.count()
            test_bookings.delete()
            self.stdout.write(f'🗑️  Deleted {deleted_counts["bookings"]} test bookings')

        # Delete services
        if not customers_only:
            test_services = Service.objects.filter(
                provider__user__is_test_account=True
            )
            deleted_counts['services'] = test_services.count()
            test_services.delete()
            self.stdout.write(f'🗑️  Deleted {deleted_counts["services"]} test services')

        # Delete provider profiles
        if not customers_only:
            test_providers = ServiceProvider.objects.filter(
                user__is_test_account=True
            )
            test_providers.delete()
            self.stdout.write(f'🗑️  Deleted provider profiles')

        # Delete user accounts
        if not providers_only:
            customer_users = User.objects.filter(
                is_test_account=True,
                role='customer'
            )
            deleted_counts['customers'] = customer_users.count()
            customer_users.delete()
            self.stdout.write(f'🗑️  Deleted {deleted_counts["customers"]} customer accounts')

        if not customers_only:
            provider_users = User.objects.filter(
                is_test_account=True,
                role='service_provider'
            )
            deleted_counts['providers'] = provider_users.count()
            provider_users.delete()
            self.stdout.write(f'🗑️  Deleted {deleted_counts["providers"]} provider accounts')

        # Delete admin accounts
        if not customers_only and not providers_only:
            admin_users = User.objects.filter(
                is_test_account=True,
                role='admin'
            )
            deleted_counts['admins'] = admin_users.count()
            admin_users.delete()
            self.stdout.write(f'🗑️  Deleted {deleted_counts["admins"]} admin accounts')

        return deleted_counts

    def print_deletion_summary(self, counts, dry_run=False):
        """Print summary of what was/would be deleted"""
        action = "Would delete" if dry_run else "Deleted"
        
        self.stdout.write(f'\n📊 {action}:')
        for data_type, count in counts.items():
            emoji = {
                'admins': '👑',
                'customers': '👤',
                'providers': '🏢',
                'services': '🛍️',
                'bookings': '📅'
            }.get(data_type, '📋')
            
            self.stdout.write(f'   {emoji} {data_type.title()}: {count}')
