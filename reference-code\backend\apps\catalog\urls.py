"""
Service Catalog URL configuration for Vierla Beauty Services Marketplace
Enhanced routing with mobile-first API design
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ServiceCategoryViewSet,
    ServiceProviderViewSet,
    ServiceViewSet,
    OperatingHoursViewSet,
    ServiceAvailabilityViewSet,
    ServiceGalleryViewSet,
    ServiceLocationViewSet,
    SearchView,
    SearchProvidersView,
    SearchServicesView,
    EnhancedSearchView
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'categories', ServiceCategoryViewSet,
                basename='categories')
router.register(r'providers', ServiceProviderViewSet,
                basename='providers')
router.register(r'services', ServiceViewSet, basename='services')
router.register(r'operating-hours', OperatingHoursViewSet,
                basename='operatinghours')
router.register(r'service-availability',
                ServiceAvailabilityViewSet, basename='serviceavailability')
router.register(r'gallery', ServiceGalleryViewSet, basename='servicegallery')
router.register(r'service-locations', ServiceLocationViewSet,
                basename='servicelocation')

app_name = 'catalog'

urlpatterns = [
    # API endpoints (no 'api/' prefix since it's already in main urls.py)
    path('', include(router.urls)),

    # Additional custom endpoints
    path('search/', SearchView.as_view(), name='search'),
    path('search/providers/', SearchProvidersView.as_view(),
         name='search-providers'),
    path('search/services/', SearchServicesView.as_view(), name='search-services'),

    # Enhanced search endpoints for real-time search
    path('search/enhanced/', EnhancedSearchView.as_view(), name='enhanced-search'),
]
