"""
Analytics Admin Interface
Admin configuration for performance monitoring models
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    PerformanceMetric, SystemHealthMetric, BusinessMetric,
    RealTimeMetric, PerformanceAlert, PerformanceDashboard
)


@admin.register(PerformanceMetric)
class PerformanceMetricAdmin(admin.ModelAdmin):
    """
    Admin interface for PerformanceMetric
    """
    list_display = [
        'metric_type', 'value', 'unit', 'endpoint', 'method',
        'status_code', 'timestamp', 'colored_status'
    ]
    list_filter = [
        'metric_type', 'method', 'status_code', 'timestamp'
    ]
    search_fields = ['endpoint', 'metric_type']
    readonly_fields = ['id', 'timestamp']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']

    def colored_status(self, obj):
        """Display colored status based on status code"""
        if obj.status_code:
            if obj.status_code < 400:
                color = 'green'
            elif obj.status_code < 500:
                color = 'orange'
            else:
                color = 'red'
            return format_html(
                '<span style="color: {};">{}</span>',
                color, obj.status_code
            )
        return '-'
    colored_status.short_description = 'Status'


@admin.register(SystemHealthMetric)
class SystemHealthMetricAdmin(admin.ModelAdmin):
    """
    Admin interface for SystemHealthMetric
    """
    list_display = [
        'health_type', 'value', 'unit', 'server_instance',
        'timestamp', 'health_indicator'
    ]
    list_filter = ['health_type', 'server_instance', 'timestamp']
    readonly_fields = ['id', 'timestamp']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']

    def health_indicator(self, obj):
        """Display health indicator based on value"""
        if obj.health_type in ['cpu_usage', 'memory_usage', 'disk_usage']:
            value = float(obj.value)
            if value < 70:
                color = 'green'
                status = 'Good'
            elif value < 85:
                color = 'orange'
                status = 'Warning'
            else:
                color = 'red'
                status = 'Critical'
            return format_html(
                '<span style="color: {};">{} ({}%)</span>',
                color, status, value
            )
        return f"{obj.value} {obj.unit}"
    health_indicator.short_description = 'Health Status'


@admin.register(RealTimeMetric)
class RealTimeMetricAdmin(admin.ModelAdmin):
    """
    Admin interface for RealTimeMetric
    """
    list_display = ['metric_type', 'value', 'unit', 'timestamp']
    list_filter = ['metric_type', 'timestamp']
    readonly_fields = ['id', 'timestamp']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']


@admin.register(PerformanceAlert)
class PerformanceAlertAdmin(admin.ModelAdmin):
    """
    Admin interface for PerformanceAlert
    """
    list_display = [
        'alert_type', 'severity', 'status', 'threshold_value',
        'actual_value', 'unit', 'triggered_at', 'alert_indicator'
    ]
    list_filter = ['alert_type', 'severity', 'status', 'triggered_at']
    search_fields = ['message', 'endpoint']
    readonly_fields = ['id', 'triggered_at']
    date_hierarchy = 'triggered_at'
    ordering = ['-triggered_at']
    actions = ['mark_acknowledged', 'mark_resolved']

    def alert_indicator(self, obj):
        """Display colored alert indicator"""
        colors = {
            'low': 'blue',
            'medium': 'orange',
            'high': 'red',
            'critical': 'darkred'
        }
        color = colors.get(obj.severity, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.severity.upper()
        )
    alert_indicator.short_description = 'Severity'

    def mark_acknowledged(self, request, queryset):
        """Mark selected alerts as acknowledged"""
        updated = queryset.filter(status='active').update(
            status='acknowledged',
            acknowledged_at=timezone.now(),
            acknowledged_by=request.user
        )
        self.message_user(request, f'{updated} alerts marked as acknowledged.')
    mark_acknowledged.short_description = 'Mark selected alerts as acknowledged'

    def mark_resolved(self, request, queryset):
        """Mark selected alerts as resolved"""
        updated = queryset.exclude(status='resolved').update(
            status='resolved',
            resolved_at=timezone.now()
        )
        self.message_user(request, f'{updated} alerts marked as resolved.')
    mark_resolved.short_description = 'Mark selected alerts as resolved'


@admin.register(PerformanceDashboard)
class PerformanceDashboardAdmin(admin.ModelAdmin):
    """
    Admin interface for PerformanceDashboard
    """
    list_display = [
        'name', 'dashboard_type', 'is_active', 'created_by',
        'created_at', 'updated_at'
    ]
    list_filter = ['dashboard_type', 'is_active', 'created_at']
    search_fields = ['name', 'dashboard_type']
    readonly_fields = ['id', 'created_at', 'updated_at']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']


@admin.register(BusinessMetric)
class BusinessMetricAdmin(admin.ModelAdmin):
    """
    Admin interface for BusinessMetric
    """
    list_display = [
        'business_type', 'value', 'unit', 'region',
        'category', 'timestamp'
    ]
    list_filter = ['business_type', 'region', 'category', 'timestamp']
    readonly_fields = ['id', 'timestamp']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
