#!/usr/bin/env python
"""
Test Service Catalog integration with authentication
Tests catalog endpoints with authenticated users
"""
import requests
import json

BASE_URL = 'http://192.168.2.65:8000'


def get_auth_token(email, password):
    """Get authentication token"""
    login_data = {
        'email': email,
        'password': password
    }

    try:
        response = requests.post(
            f'{BASE_URL}/api/auth/login/',
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            return data.get("access")
        else:
            print(f'Login failed: {response.text}')
            return None

    except Exception as e:
        print(f'Login error: {e}')
        return None


def test_catalog_endpoints(token, user_role):
    """Test catalog endpoints with authentication"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }

    print(f"\n🔍 Testing Catalog Endpoints for {user_role}")
    print("=" * 50)

    # Test endpoints
    endpoints = [
        '/api/catalog/categories/',
        '/api/catalog/services/',
        '/api/catalog/providers/',
        '/api/catalog/service-availability/',
    ]

    for endpoint in endpoints:
        try:
            response = requests.get(
                f'{BASE_URL}{endpoint}',
                headers=headers,
                timeout=10
            )

            print(f'{endpoint}: Status {response.status_code}')

            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    print(f'  ✅ Success! Found {len(data["results"])} items')
                elif isinstance(data, list):
                    print(f'  ✅ Success! Found {len(data)} items')
                else:
                    print(f'  ✅ Success! Response: {str(data)[:100]}...')
            else:
                print(f'  ❌ Failed: {response.text[:100]}...')

        except Exception as e:
            print(f'  ❌ Error: {e}')


def main():
    print("🏪 Testing Vierla Service Catalog Integration")
    print("=" * 60)

    # Test accounts
    test_accounts = [
        ('<EMAIL>', 'testpass123', 'Customer'),
        ('<EMAIL>', 'testpass123', 'Provider'),
    ]

    for email, password, role in test_accounts:
        print(f"\n🔐 Getting token for {role} ({email})")
        token = get_auth_token(email, password)

        if token:
            print(f"  ✅ Token obtained: {token[:50]}...")
            test_catalog_endpoints(token, role)
        else:
            print(f"  ❌ Failed to get token for {role}")

    print("\n✅ Service Catalog integration test complete!")


if __name__ == '__main__':
    main()
