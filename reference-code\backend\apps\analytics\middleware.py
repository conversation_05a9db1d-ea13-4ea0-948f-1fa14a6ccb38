"""
Performance Monitoring Middleware
Captures real-time metrics for food delivery app-analogous monitoring
"""
import time
import threading
from django.utils import timezone
from django.db import connection
from django.core.cache import cache
from django.conf import settings
from .models import PerformanceMetric, RealTimeMetric, PerformanceAlert
from .services import PerformanceMonitoringService
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitoringMiddleware:
    """
    Middleware to capture performance metrics for every request
    Tracks latency, RPS, database queries, cache hits, etc.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.monitoring_service = PerformanceMonitoringService()
        
    def __call__(self, request):
        # Start timing
        start_time = time.time()
        start_queries = len(connection.queries)
        
        # Process request
        response = self.get_response(request)
        
        # Calculate metrics
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000
        query_count = len(connection.queries) - start_queries
        
        # Capture metrics asynchronously
        threading.Thread(
            target=self._capture_metrics,
            args=(request, response, duration_ms, query_count, start_time)
        ).start()
        
        return response
    
    def _capture_metrics(self, request, response, duration_ms, query_count, start_time):
        """Capture performance metrics asynchronously"""
        try:
            # Basic request metrics
            user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
            session_id = request.session.session_key if hasattr(request, 'session') else None
            
            # API Latency Metric
            PerformanceMetric.objects.create(
                metric_type='api_latency',
                value=duration_ms,
                unit='ms',
                endpoint=request.path,
                method=request.method,
                status_code=response.status_code,
                user_id=user_id,
                session_id=session_id,
                metadata={
                    'query_count': query_count,
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'ip_address': request.META.get('REMOTE_ADDR', ''),
                }
            )
            
            # Database performance metrics
            if query_count > 0:
                # Calculate average query time
                total_query_time = sum(
                    float(query['time']) for query in connection.queries[-query_count:]
                )
                avg_query_time = (total_query_time / query_count) * 1000  # Convert to ms
                
                PerformanceMetric.objects.create(
                    metric_type='db_read_time',
                    value=avg_query_time,
                    unit='ms',
                    endpoint=request.path,
                    method=request.method,
                    user_id=user_id,
                    metadata={'query_count': query_count}
                )
            
            # Update real-time metrics
            self._update_realtime_metrics(request, duration_ms, response.status_code)
            
            # Check for performance alerts
            self._check_performance_alerts(duration_ms, response.status_code, request.path)
            
        except Exception as e:
            logger.error(f"Error capturing performance metrics: {e}")
    
    def _update_realtime_metrics(self, request, duration_ms, status_code):
        """Update real-time metrics for dashboard"""
        try:
            # Update current RPS (requests per second)
            current_minute = timezone.now().replace(second=0, microsecond=0)
            rps_key = f"rps_{current_minute.timestamp()}"
            current_rps = cache.get(rps_key, 0) + 1
            cache.set(rps_key, current_rps, 60)  # Store for 1 minute
            
            # Update 1-minute average response time
            avg_key = f"avg_response_{current_minute.timestamp()}"
            current_avg = cache.get(avg_key, {'total': 0, 'count': 0})
            current_avg['total'] += duration_ms
            current_avg['count'] += 1
            cache.set(avg_key, current_avg, 60)
            
            # Update error rate
            if status_code >= 400:
                error_key = f"errors_{current_minute.timestamp()}"
                current_errors = cache.get(error_key, 0) + 1
                cache.set(error_key, current_errors, 60)
            
            # Store real-time metrics every 10 seconds
            if int(timezone.now().timestamp()) % 10 == 0:
                RealTimeMetric.objects.create(
                    metric_type='current_rps',
                    value=current_rps,
                    unit='rps'
                )
                
                if current_avg['count'] > 0:
                    avg_response_time = current_avg['total'] / current_avg['count']
                    RealTimeMetric.objects.create(
                        metric_type='avg_response_time_1min',
                        value=avg_response_time,
                        unit='ms'
                    )
                
        except Exception as e:
            logger.error(f"Error updating real-time metrics: {e}")
    
    def _check_performance_alerts(self, duration_ms, status_code, endpoint):
        """Check if performance thresholds are exceeded"""
        try:
            # High latency alert (>2000ms)
            if duration_ms > 2000:
                PerformanceAlert.objects.create(
                    alert_type='high_latency',
                    severity='high' if duration_ms > 5000 else 'medium',
                    threshold_value=2000,
                    actual_value=duration_ms,
                    unit='ms',
                    message=f"High API latency detected: {duration_ms:.2f}ms on {endpoint}",
                    endpoint=endpoint
                )
            
            # High error rate alert
            if status_code >= 500:
                PerformanceAlert.objects.create(
                    alert_type='high_error_rate',
                    severity='critical',
                    threshold_value=0,
                    actual_value=status_code,
                    unit='status_code',
                    message=f"Server error detected: {status_code} on {endpoint}",
                    endpoint=endpoint
                )
                
        except Exception as e:
            logger.error(f"Error checking performance alerts: {e}")


class DatabasePerformanceMiddleware:
    """
    Middleware to monitor database performance specifically
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Reset queries for this request
        connection.queries_log.clear()
        
        response = self.get_response(request)
        
        # Analyze database performance
        threading.Thread(
            target=self._analyze_db_performance,
            args=(request, connection.queries)
        ).start()
        
        return response
    
    def _analyze_db_performance(self, request, queries):
        """Analyze database query performance"""
        try:
            if not queries:
                return
            
            # Calculate query statistics
            query_times = [float(query['time']) for query in queries]
            total_time = sum(query_times) * 1000  # Convert to ms
            avg_time = (total_time / len(queries)) if queries else 0
            max_time = max(query_times) * 1000 if queries else 0
            
            # Store database metrics
            PerformanceMetric.objects.create(
                metric_type='db_read_time',
                value=avg_time,
                unit='ms',
                endpoint=request.path,
                method=request.method,
                metadata={
                    'total_queries': len(queries),
                    'total_time_ms': total_time,
                    'max_query_time_ms': max_time,
                    'slow_queries': len([t for t in query_times if t > 0.1])  # >100ms
                }
            )
            
            # Alert for slow queries
            if max_time > 1000:  # >1 second
                PerformanceAlert.objects.create(
                    alert_type='database_slow',
                    severity='high',
                    threshold_value=1000,
                    actual_value=max_time,
                    unit='ms',
                    message=f"Slow database query detected: {max_time:.2f}ms on {request.path}",
                    endpoint=request.path
                )
                
        except Exception as e:
            logger.error(f"Error analyzing database performance: {e}")


class CachePerformanceMiddleware:
    """
    Middleware to monitor cache performance
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.cache_hits = 0
        self.cache_misses = 0
    
    def __call__(self, request):
        # Monitor cache operations during request
        original_get = cache.get
        original_set = cache.set
        
        hits = 0
        misses = 0
        
        def monitored_get(key, default=None, version=None):
            nonlocal hits, misses
            result = original_get(key, default, version)
            if result is not None and result != default:
                hits += 1
            else:
                misses += 1
            return result
        
        def monitored_set(key, value, timeout=None, version=None):
            return original_set(key, value, timeout, version)
        
        # Monkey patch cache methods
        cache.get = monitored_get
        cache.set = monitored_set
        
        response = self.get_response(request)
        
        # Restore original methods
        cache.get = original_get
        cache.set = original_set
        
        # Record cache metrics
        if hits + misses > 0:
            hit_rate = (hits / (hits + misses)) * 100
            threading.Thread(
                target=self._record_cache_metrics,
                args=(request, hit_rate, hits, misses)
            ).start()
        
        return response
    
    def _record_cache_metrics(self, request, hit_rate, hits, misses):
        """Record cache performance metrics"""
        try:
            PerformanceMetric.objects.create(
                metric_type='cache_hit_rate',
                value=hit_rate,
                unit='percentage',
                endpoint=request.path,
                method=request.method,
                metadata={
                    'cache_hits': hits,
                    'cache_misses': misses,
                    'total_operations': hits + misses
                }
            )
            
            # Alert for low cache hit rate
            if hit_rate < 70:  # Less than 70% hit rate
                PerformanceAlert.objects.create(
                    alert_type='low_cache_hit',
                    severity='medium',
                    threshold_value=70,
                    actual_value=hit_rate,
                    unit='percentage',
                    message=f"Low cache hit rate: {hit_rate:.1f}% on {request.path}",
                    endpoint=request.path
                )
                
        except Exception as e:
            logger.error(f"Error recording cache metrics: {e}")
