# Vierla Development Environment Guide

This guide provides comprehensive instructions for setting up and running the Vierla Beauty Services Marketplace in a development environment.

## 📋 Prerequisites

Before using the development scripts, ensure you have the following installed:

### Required Software
- **Python 3.11+** - [Download from python.org](https://python.org)
- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org) (for frontend)
- **Git** - [Download from git-scm.com](https://git-scm.com)

### Optional Database Software
- **PostgreSQL** - For production-like development
- **MySQL** - Alternative database option
- **SQLite** - Default (no installation required)

## 🚀 Quick Start

### 1. Initial Setup (First Time Only)

Run the setup script to configure your development environment:

**Windows:**
```cmd
.\scripts\development\setup-dev.bat
```

**Unix/Linux/macOS:**
```bash
./scripts/development/setup-dev.sh
```

This script will:
- Create and configure a Python virtual environment
- Install all Python dependencies
- Run database migrations
- Set up environment configuration
- Install frontend dependencies (if Node.js is available)
- Optionally create a Django superuser account

### 2. Start the Database (if needed)

**For SQLite (default):**
No action required - SQLite is file-based and doesn't need a separate service.

**For PostgreSQL:**
```cmd
# Windows
.\scripts\development\start-database.bat --type postgresql

# Unix/Linux/macOS
./scripts/development/start-database.sh --type postgresql
```

**For MySQL:**
```cmd
# Windows
.\scripts\development\start-database.bat --type mysql

# Unix/Linux/macOS
./scripts/development/start-database.sh --type mysql
```

### 3. Start the Backend Server

**Windows:**
```cmd
.\scripts\development\start-backend.bat
```

**Unix/Linux/macOS:**
```bash
./scripts/development/start-backend.sh
```

### 4. Access the Application

Once the backend is running, you can access:

- **Main Application**: http://localhost:8000
- **Admin Interface**: http://localhost:8000/admin
- **API Root**: http://localhost:8000/api
- **API Documentation**: http://localhost:8000/api/docs

## 🔧 Advanced Configuration

### Environment Variables

The scripts use environment variables from `code/backend/.env`:

```env
# Django Settings
DEBUG=True
SECRET_KEY=dev-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings
USE_SQLITE=True
DATABASE_URL=sqlite:///db.sqlite3

# Server Settings
VIERLA_HOST=localhost
VIERLA_PORT=8000
```

### Custom Server Configuration

**Start backend on different host/port:**
```cmd
# Windows
.\scripts\development\start-backend.bat --host 0.0.0.0 --port 8080

# Unix/Linux/macOS
./scripts/development/start-backend.sh --host 0.0.0.0 --port 8080
```

**Enable debug mode:**
```cmd
# Windows
.\scripts\development\start-backend.bat --debug

# Unix/Linux/macOS
./scripts/development/start-backend.sh --debug
```

### Database Configuration

**PostgreSQL Setup:**
1. Install PostgreSQL
2. Create database: `createdb vierla_dev`
3. Update `.env`:
   ```env
   USE_SQLITE=False
   DATABASE_URL=postgresql://username:password@localhost:5432/vierla_dev
   ```

**MySQL Setup:**
1. Install MySQL
2. Create database: `CREATE DATABASE vierla_dev;`
3. Update `.env`:
   ```env
   USE_SQLITE=False
   DATABASE_URL=mysql://username:password@localhost:3306/vierla_dev
   ```

## 📁 Project Structure

```
vierla-rebuild/
├── code/
│   ├── backend/           # Django backend application
│   │   ├── venv/         # Python virtual environment
│   │   ├── manage.py     # Django management script
│   │   ├── requirements.txt
│   │   └── .env          # Environment configuration
│   ├── frontend/         # React Native frontend
│   └── scripts/          # Development and deployment scripts
├── logs/                 # Application and script logs
└── docs/                 # Project documentation
```

## 🔍 Troubleshooting

### Common Issues

**1. Port Already in Use**
```
Error: Port 8000 is already in use
```
**Solution:** 
- Check running processes: `netstat -ano | findstr :8000` (Windows) or `lsof -i :8000` (Unix)
- Kill the process or use a different port: `--port 8001`

**2. Virtual Environment Issues**
```
Error: Virtual environment not found
```
**Solution:**
- Delete `code/backend/venv` directory
- Run setup script again: `.\scripts\development\setup-dev.bat`

**3. Database Connection Errors**
```
Error: Failed to connect to database
```
**Solution:**
- Ensure database service is running
- Check DATABASE_URL in `.env` file
- Verify database credentials and permissions

**4. Python Not Found**
```
Error: Python is not installed or not in PATH
```
**Solution:**
- Install Python 3.11+ from python.org
- Ensure Python is added to system PATH
- Restart terminal/command prompt

**5. Permission Denied (Unix/Linux)**
```
Error: Permission denied
```
**Solution:**
- Make scripts executable: `chmod +x scripts/development/*.sh`
- Check file permissions: `ls -la scripts/development/`

### Log Files

Check log files for detailed error information:
- **Setup logs**: `logs/setup-dev.log`
- **Backend logs**: `logs/backend-server.log`
- **Database logs**: `logs/database-service.log`

### Getting Help

1. **Check the logs** in the `logs/` directory
2. **Review error messages** for specific guidance
3. **Consult project documentation** in the `docs/` directory
4. **Check environment configuration** in `code/backend/.env`

## 🏗️ Development Workflow

### Daily Development Routine

1. **Start your development session:**
   ```bash
   # Start database (if using PostgreSQL/MySQL)
   ./scripts/development/start-database.sh
   
   # Start backend server
   ./scripts/development/start-backend.sh
   ```

2. **Make your changes** to the codebase

3. **Test your changes** using the running development server

4. **Stop the server** with `Ctrl+C` when done

### Making Database Changes

1. **Create migrations** after model changes:
   ```bash
   cd code/backend
   source venv/bin/activate  # Unix/Linux/macOS
   # or
   venv\Scripts\activate.bat  # Windows
   
   python manage.py makemigrations
   ```

2. **Apply migrations:**
   ```bash
   python manage.py migrate
   ```

3. **Restart the backend server** to see changes

### Adding New Dependencies

1. **Activate virtual environment:**
   ```bash
   cd code/backend
   source venv/bin/activate  # Unix/Linux/macOS
   # or
   venv\Scripts\activate.bat  # Windows
   ```

2. **Install new package:**
   ```bash
   pip install package-name
   ```

3. **Update requirements:**
   ```bash
   pip freeze > requirements.txt
   ```

## 🎯 Best Practices

1. **Always use the scripts** instead of manual commands
2. **Keep your virtual environment activated** when working on backend
3. **Check logs** when encountering issues
4. **Update dependencies regularly** but test thoroughly
5. **Use environment variables** for configuration
6. **Follow the established project structure**

## 📚 Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [React Native Documentation](https://reactnative.dev/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Python Virtual Environments](https://docs.python.org/3/tutorial/venv.html)
