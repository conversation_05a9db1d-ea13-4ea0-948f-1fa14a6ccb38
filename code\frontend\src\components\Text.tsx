/**
 * Text Component
 * Reusable text component with consistent typography
 */

import React from 'react';
import { Text as RNText, StyleSheet, TextProps as RNTextProps } from 'react-native';

interface TextProps extends RNTextProps {
  variant?: 'heading1' | 'heading2' | 'heading3' | 'h1' | 'h2' | 'h3' | 'body' | 'caption' | 'button';
  color?: 'primary' | 'secondary' | 'accent' | 'error' | 'success' | 'warning' | 'white';
  align?: 'left' | 'center' | 'right';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
}

export const Text: React.FC<TextProps> = ({
  children,
  variant = 'body',
  color = 'primary',
  align = 'left',
  weight = 'normal',
  style,
  ...props
}) => {
  const textStyle = [
    styles.base,
    styles[variant],
    styles[color],
    styles[`align${align.charAt(0).toUpperCase() + align.slice(1)}`],
    styles[weight],
    style,
  ];

  return (
    <RNText style={textStyle} {...props}>
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  base: {
    fontFamily: 'System', // Use system font
  },
  
  // Variants
  heading1: {
    fontSize: 32,
    lineHeight: 40,
    fontWeight: '700',
  },
  heading2: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: '600',
  },
  heading3: {
    fontSize: 20,
    lineHeight: 28,
    fontWeight: '600',
  },
  // Aliases for h1, h2, h3
  h1: {
    fontSize: 32,
    lineHeight: 40,
    fontWeight: '700',
  },
  h2: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: '600',
  },
  h3: {
    fontSize: 20,
    lineHeight: 28,
    fontWeight: '600',
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400',
  },
  caption: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400',
  },
  button: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
  },
  
  // Colors
  primary: {
    color: '#1C1C1E',
  },
  secondary: {
    color: '#8E8E93',
  },
  accent: {
    color: '#007AFF',
  },
  error: {
    color: '#FF3B30',
  },
  success: {
    color: '#34C759',
  },
  warning: {
    color: '#FF9500',
  },
  white: {
    color: '#FFFFFF',
  },
  
  // Alignment
  alignLeft: {
    textAlign: 'left',
  },
  alignCenter: {
    textAlign: 'center',
  },
  alignRight: {
    textAlign: 'right',
  },
  
  // Weight
  normal: {
    fontWeight: '400',
  },
  medium: {
    fontWeight: '500',
  },
  semibold: {
    fontWeight: '600',
  },
  bold: {
    fontWeight: '700',
  },
});
