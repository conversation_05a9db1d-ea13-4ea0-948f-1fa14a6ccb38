/**
 * Review Step Component
 * Final step of multi-step service creation form
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../../theme';
import { FormData, FormErrors } from '../MultiStepServiceForm';
import { ServiceCategory } from '../../../services/api';

interface ReviewStepProps {
  formData: FormData;
  errors: FormErrors;
  categories: ServiceCategory[];
  onUpdateFormData: (field: keyof FormData, value: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  onSubmit: () => void;
  loading: boolean;
  submitButtonText: string;
  isFirstStep: boolean;
  isLastStep: boolean;
  isEditing: boolean;
}

export const ReviewStep: React.FC<ReviewStepProps> = ({
  formData,
  categories,
  onPrevious,
  onSubmit,
  loading,
  submitButtonText,
  isFirstStep,
  isEditing,
}) => {
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || 'Unknown Category';
  };

  const formatPrice = () => {
    if (formData.price_type === 'fixed') {
      return `$${formData.base_price}`;
    } else {
      return `$${formData.base_price}${formData.max_price ? ` - $${formData.max_price}` : '+'}`;
    }
  };

  const formatDuration = () => {
    const duration = parseInt(formData.duration);
    if (duration >= 60) {
      const hours = Math.floor(duration / 60);
      const minutes = duration % 60;
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${duration}m`;
  };

  const renderReviewSection = (title: string, children: React.ReactNode) => (
    <View style={styles.reviewSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  const renderReviewItem = (label: string, value: string, icon?: string) => (
    <View style={styles.reviewItem}>
      <View style={styles.reviewItemHeader}>
        {icon && <Icon name={icon} size={16} color={colors.primary} />}
        <Text style={styles.reviewItemLabel}>{label}</Text>
      </View>
      <Text style={styles.reviewItemValue}>{value}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Review & Submit</Text>
          <Text style={styles.subtitle}>
            Please review your service details before submitting
          </Text>
        </View>

        {renderReviewSection('Basic Information', (
          <>
            {renderReviewItem('Service Name', formData.name, 'business')}
            {renderReviewItem('Category', getCategoryName(formData.category), 'category')}
            {formData.short_description && renderReviewItem(
              'Short Description', 
              formData.short_description, 
              'short-text'
            )}
          </>
        ))}

        {renderReviewSection('Pricing', (
          <>
            {renderReviewItem('Price', formatPrice(), 'attach-money')}
            {renderReviewItem(
              'Pricing Type', 
              formData.price_type === 'fixed' ? 'Fixed Price' : 'Price Range',
              'trending-up'
            )}
          </>
        ))}

        {renderReviewSection('Service Details', (
          <>
            {renderReviewItem('Duration', formatDuration(), 'schedule')}
            {formData.buffer_time && renderReviewItem(
              'Buffer Time', 
              `${formData.buffer_time} minutes`,
              'timer'
            )}
            {renderReviewItem('Description', formData.description, 'description')}
            {formData.requirements && renderReviewItem(
              'Requirements', 
              formData.requirements,
              'checklist'
            )}
            {formData.preparation_instructions && renderReviewItem(
              'Preparation Instructions', 
              formData.preparation_instructions,
              'info'
            )}
          </>
        ))}

        {isEditing && renderReviewSection('Settings', (
          <>
            {renderReviewItem(
              'Availability', 
              formData.is_available ? 'Available' : 'Not Available',
              formData.is_available ? 'visibility' : 'visibility-off'
            )}
            {renderReviewItem(
              'Popular Service', 
              formData.is_popular ? 'Yes' : 'No',
              'star'
            )}
          </>
        ))}

        <View style={styles.confirmationSection}>
          <View style={styles.confirmationCard}>
            <Icon name="check-circle-outline" size={32} color={colors.success} />
            <Text style={styles.confirmationTitle}>Ready to Submit</Text>
            <Text style={styles.confirmationText}>
              {isEditing 
                ? 'Your service changes will be saved and updated immediately.'
                : 'Your service will be created and made available to customers.'
              }
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.buttonSecondary]}
          onPress={onPrevious}
          disabled={isFirstStep || loading}
        >
          <Icon name="arrow-back" size={20} color={colors.primary} />
          <Text style={[styles.buttonText, styles.buttonTextSecondary]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.buttonPrimary, loading && styles.buttonDisabled]}
          onPress={onSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <>
              <Icon name="check" size={20} color={colors.white} />
              <Text style={[styles.buttonText, styles.buttonTextPrimary]}>
                {submitButtonText}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: spacing.lg,
  },
  title: {
    ...typography.h2,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  reviewSection: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    ...typography.h4,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  sectionContent: {
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  reviewItem: {
    padding: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  reviewItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  reviewItemLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    fontWeight: '600',
    marginLeft: spacing.xs,
    textTransform: 'uppercase',
  },
  reviewItemValue: {
    ...typography.body,
    color: colors.textPrimary,
    lineHeight: 20,
  },
  confirmationSection: {
    marginVertical: spacing.xl,
  },
  confirmationCard: {
    backgroundColor: colors.successLight,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.success,
  },
  confirmationTitle: {
    ...typography.h4,
    color: colors.success,
    fontWeight: '600',
    marginTop: spacing.sm,
    marginBottom: spacing.xs,
  },
  confirmationText: {
    ...typography.body,
    color: colors.success,
    textAlign: 'center',
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    minWidth: 120,
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    ...typography.body,
    fontWeight: '600',
    marginHorizontal: spacing.xs,
  },
  buttonTextPrimary: {
    color: colors.white,
  },
  buttonTextSecondary: {
    color: colors.primary,
  },
});
