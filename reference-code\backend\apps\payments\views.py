"""
Payment Views - MVP Critical Feature
Comprehensive payment processing API endpoints with Stripe integration
"""

from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from decimal import Decimal
import stripe
import json
import logging
import time
from django.utils import timezone

from .models import PaymentMethod, PaymentIntent, Transaction
from .serializers import (
    PaymentMethodSerializer,
    PaymentIntentSerializer,
    TransactionSerializer,
    ProcessPaymentSerializer,
    CreatePaymentIntentSerializer
)

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', '')

logger = logging.getLogger(__name__)


class PaymentConfigView(APIView):
    """Get payment configuration including Stripe publishable key"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Return payment configuration"""
        try:
            config = {
                'stripe_publishable_key': getattr(settings, 'STRIPE_PUBLISHABLE_KEY', ''),
                'supported_payment_methods': ['card', 'apple_pay', 'google_pay'],
                'currency': 'CAD',
                'service_fee_percentage': 5.0,  # 5%
                'tax_percentage': 13.0,  # 13% HST for Ontario
            }
            return Response(config, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting payment config: {str(e)}")
            return Response(
                {'error': 'Failed to get payment configuration'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PaymentMethodListCreateView(generics.ListCreateAPIView):
    """List and create payment methods for authenticated user"""

    serializer_class = PaymentMethodSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return PaymentMethod.objects.filter(
            user=self.request.user,
            is_active=True
        )

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class PaymentMethodDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a specific payment method"""

    serializer_class = PaymentMethodSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    lookup_url_kwarg = 'payment_method_id'

    def get_queryset(self):
        return PaymentMethod.objects.filter(
            user=self.request.user,
            is_active=True
        )

    def perform_destroy(self, instance):
        # Soft delete by marking as inactive
        instance.is_active = False
        instance.save()


class CreatePaymentIntentView(APIView):
    """Create a Stripe Payment Intent for booking payment"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """Create payment intent"""
        serializer = CreatePaymentIntentSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            data = serializer.validated_data
            amount_cents = int(data['amount'] * 100)  # Convert to cents

            # Create Stripe Payment Intent
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency=data.get('currency', 'cad'),
                customer=request.user.stripe_customer_id if hasattr(request.user, 'stripe_customer_id') else None,
                metadata={
                    'user_id': str(request.user.id),
                    'booking_id': data.get('booking_id', ''),
                }
            )

            # Save Payment Intent to database
            payment_intent = PaymentIntent.objects.create(
                stripe_payment_intent_id=intent.id,
                user=request.user,
                amount=data['amount'],
                currency=data.get('currency', 'CAD'),
                status=intent.status,
                client_secret=intent.client_secret,
                metadata=intent.metadata
            )

            serializer = PaymentIntentSerializer(payment_intent)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating payment intent: {str(e)}")
            return Response(
                {'error': f'Payment processing error: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error creating payment intent: {str(e)}")
            return Response(
                {'error': 'Failed to create payment intent'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProcessPaymentView(APIView):
    """Process payment for a booking"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """Process payment"""
        serializer = ProcessPaymentSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            data = serializer.validated_data

            # Create transaction record
            transaction = Transaction.objects.create(
                user=request.user,
                booking_id=data['booking_id'],
                amount=data['amount'],
                total_amount=data['amount'],
                status='processing'
            )

            # Process with Stripe
            # This is a simplified version - in production you'd handle the full Stripe flow

            transaction.status = 'completed'
            transaction.save()

            serializer = TransactionSerializer(transaction)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error processing payment: {str(e)}")
            return Response(
                {'error': 'Failed to process payment'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PaymentMethodListView(APIView):
    """List and create payment methods"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Get user's payment methods"""
        try:
            payment_methods = PaymentMethod.objects.filter(
                user=request.user,
                is_active=True
            ).order_by('-is_default', '-created_at')

            serializer = PaymentMethodSerializer(payment_methods, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error getting payment methods: {str(e)}")
            return Response(
                {'error': 'Failed to get payment methods'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """Add new payment method"""
        try:
            # In a real implementation, you'd create the payment method with Stripe first
            # then save the reference in your database

            data = request.data.copy()
            data['user'] = request.user.id

            serializer = PaymentMethodSerializer(data=data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

            # Create Stripe payment method (mock implementation)
            stripe_pm_id = f"pm_{int(time.time())}"

            payment_method = serializer.save(
                user=request.user,
                stripe_payment_method_id=stripe_pm_id
            )

            return Response(
                PaymentMethodSerializer(payment_method).data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            logger.error(f"Error adding payment method: {str(e)}")
            return Response(
                {'error': 'Failed to add payment method'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PaymentMethodDetailView(APIView):
    """Payment method detail operations"""

    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request, payment_method_id):
        """Delete payment method"""
        try:
            payment_method = PaymentMethod.objects.get(
                id=payment_method_id,
                user=request.user
            )

            # Detach from Stripe (mock implementation)
            # stripe.PaymentMethod.detach(payment_method.stripe_payment_method_id)

            payment_method.is_active = False
            payment_method.save()

            return Response(status=status.HTTP_204_NO_CONTENT)
        except PaymentMethod.DoesNotExist:
            return Response(
                {'error': 'Payment method not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error deleting payment method: {str(e)}")
            return Response(
                {'error': 'Failed to delete payment method'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request, payment_method_id):
        """Update payment method (e.g., set as default)"""
        try:
            payment_method = PaymentMethod.objects.get(
                id=payment_method_id,
                user=request.user
            )

            # If setting as default, unset other defaults
            if request.data.get('is_default'):
                PaymentMethod.objects.filter(
                    user=request.user,
                    is_default=True
                ).update(is_default=False)

                payment_method.is_default = True
                payment_method.save()

            serializer = PaymentMethodSerializer(payment_method)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except PaymentMethod.DoesNotExist:
            return Response(
                {'error': 'Payment method not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating payment method: {str(e)}")
            return Response(
                {'error': 'Failed to update payment method'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TransactionListView(APIView):
    """List user transactions with filtering and pagination"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Get user's transaction history"""
        try:
            # Get query parameters
            page = int(request.GET.get('page', 1))
            limit = min(int(request.GET.get('limit', 20)), 50)  # Max 50 per page
            status_filter = request.GET.get('status')
            type_filter = request.GET.get('type')
            date_from = request.GET.get('date_from')
            date_to = request.GET.get('date_to')

            # Build queryset
            queryset = Transaction.objects.filter(user=request.user)

            if status_filter:
                queryset = queryset.filter(status=status_filter)
            if type_filter:
                queryset = queryset.filter(type=type_filter)
            if date_from:
                queryset = queryset.filter(created_at__gte=date_from)
            if date_to:
                queryset = queryset.filter(created_at__lte=date_to)

            # Pagination
            total_count = queryset.count()
            offset = (page - 1) * limit
            transactions = queryset[offset:offset + limit]

            # Serialize data
            serializer = TransactionSerializer(transactions, many=True)

            return Response({
                'results': serializer.data,
                'total_count': total_count,
                'has_next': offset + limit < total_count,
                'has_previous': page > 1,
                'page': page,
                'limit': limit,
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting transactions: {str(e)}")
            return Response(
                {'error': 'Failed to get transactions'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RefundRequestView(APIView):
    """Request refund for a transaction"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, transaction_id):
        """Request refund"""
        try:
            transaction = Transaction.objects.get(
                id=transaction_id,
                user=request.user,
                status='completed'
            )

            amount = request.data.get('amount', transaction.total_amount)
            reason = request.data.get('reason', 'Customer requested refund')

            # Validate refund amount
            if amount > transaction.total_amount:
                return Response(
                    {'error': 'Refund amount cannot exceed transaction amount'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create refund with Stripe (mock implementation)
            stripe_refund_id = f"re_{int(time.time())}"

            # Create refund transaction
            refund_transaction = Transaction.objects.create(
                user=request.user,
                booking=transaction.booking,
                payment_method=transaction.payment_method,
                type='refund',
                status='processing',
                amount=amount,
                total_amount=amount,
                currency=transaction.currency,
                stripe_refund_id=stripe_refund_id,
                metadata={
                    'original_transaction_id': str(transaction.id),
                    'refund_reason': reason,
                }
            )

            # In a real implementation, process with Stripe
            # For now, mark as completed
            refund_transaction.status = 'completed'
            refund_transaction.processed_at = timezone.now()
            refund_transaction.save()

            serializer = TransactionSerializer(refund_transaction)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Transaction.DoesNotExist:
            return Response(
                {'error': 'Transaction not found or not eligible for refund'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}")
            return Response(
                {'error': 'Failed to process refund'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ConfirmPaymentIntentView(APIView):
    """Confirm a payment intent"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, payment_intent_id):
        """Confirm payment intent"""
        try:
            # Retrieve payment intent from Stripe
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)

            # Update local record
            payment_intent = PaymentIntent.objects.get(
                stripe_payment_intent_id=payment_intent_id,
                user=request.user
            )
            payment_intent.status = intent.status
            payment_intent.save()

            serializer = PaymentIntentSerializer(payment_intent)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except PaymentIntent.DoesNotExist:
            return Response(
                {'error': 'Payment intent not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error confirming payment intent: {str(e)}")
            return Response(
                {'error': 'Failed to confirm payment intent'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProcessApplePayView(APIView):
    """Process Apple Pay payments"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """Process Apple Pay payment"""
        # Placeholder for Apple Pay processing
        return Response(
            {'message': 'Apple Pay processing not yet implemented'},
            status=status.HTTP_501_NOT_IMPLEMENTED
        )


class ProcessGooglePayView(APIView):
    """Process Google Pay payments"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """Process Google Pay payment"""
        # Placeholder for Google Pay processing
        return Response(
            {'message': 'Google Pay processing not yet implemented'},
            status=status.HTTP_501_NOT_IMPLEMENTED
        )


class TransactionListView(generics.ListAPIView):
    """List user transactions"""

    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Transaction.objects.filter(user=self.request.user)


class TransactionDetailView(generics.RetrieveAPIView):
    """Retrieve transaction details"""

    serializer_class = TransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    lookup_url_kwarg = 'transaction_id'

    def get_queryset(self):
        return Transaction.objects.filter(user=self.request.user)


@method_decorator(csrf_exempt, name='dispatch')
class StripeWebhookView(APIView):
    """Handle Stripe webhooks"""

    permission_classes = []  # No authentication for webhooks

    def post(self, request):
        """Handle Stripe webhook"""
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError:
            logger.error("Invalid payload in Stripe webhook")
            return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError:
            logger.error("Invalid signature in Stripe webhook")
            return HttpResponse(status=400)

        # Handle the event
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            # Update local payment intent status
            try:
                local_intent = PaymentIntent.objects.get(
                    stripe_payment_intent_id=payment_intent['id']
                )
                local_intent.status = 'succeeded'
                local_intent.save()
            except PaymentIntent.DoesNotExist:
                logger.warning(f"Payment intent {payment_intent['id']} not found locally")

        return HttpResponse(status=200)
