/**
 * Button Component Test Suite
 * Simplified tests for the reusable Button component
 */

import React from 'react';
import { Button } from '../Button';

// Mock React Native components for testing
jest.mock('react-native', () => ({
  TouchableOpacity: 'TouchableOpacity',
  View: 'View',
  Text: 'Text',
  ActivityIndicator: 'ActivityIndicator',
  StyleSheet: {
    create: (styles: any) => styles,
  },
}));

describe('Button Component', () => {
  it('renders without crashing', () => {
    const mockOnPress = jest.fn();
    expect(() => {
      React.createElement(Button, { title: "Test Button", onPress: mockOnPress });
    }).not.toThrow();
  });

  it('accepts required props', () => {
    const mockOnPress = jest.fn();
    const element = React.createElement(Button, { title: "Test Button", onPress: mockOnPress });
    expect(element.props.title).toBe("Test Button");
    expect(element.props.onPress).toBe(mockOnPress);
  });

  it('accepts optional variant prop', () => {
    const mockOnPress = jest.fn();
    const element = React.createElement(Button, {
      title: "Test Button",
      onPress: mockOnPress,
      variant: "outline"
    });
    expect(element.props.variant).toBe("outline");
  });

  it('accepts loading prop', () => {
    const mockOnPress = jest.fn();
    const element = React.createElement(Button, {
      title: "Test Button",
      onPress: mockOnPress,
      loading: true
    });
    expect(element.props.loading).toBe(true);
  });

  it('accepts disabled prop', () => {
    const mockOnPress = jest.fn();
    const element = React.createElement(Button, {
      title: "Test Button",
      onPress: mockOnPress,
      disabled: true
    });
    expect(element.props.disabled).toBe(true);
  });
});
