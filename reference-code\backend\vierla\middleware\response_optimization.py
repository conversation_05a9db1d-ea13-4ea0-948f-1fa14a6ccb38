"""
API Response Optimization Middleware for Vierla Backend
Based on Backend Agent Consultation for Production Performance
"""

import gzip
import json
import time
from django.http import HttpResponse, JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
from django.utils.cache import get_cache_key
import hashlib
from typing import Dict, Any, Optional

class ResponseCompressionMiddleware(MiddlewareMixin):
    """
    Middleware to compress API responses for better performance.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.min_length = getattr(settings, 'COMPRESSION_MIN_LENGTH', 1024)  # 1KB minimum
        self.compression_level = getattr(settings, 'COMPRESSION_LEVEL', 6)
        super().__init__(get_response)
    
    def process_response(self, request, response):
        """Compress response if appropriate."""
        # Only compress if client accepts gzip
        if not self._accepts_gzip(request):
            return response
        
        # Only compress JSON responses and large responses
        if not self._should_compress(response):
            return response
        
        # Compress the response
        compressed_response = self._compress_response(response)
        return compressed_response
    
    def _accepts_gzip(self, request) -> bool:
        """Check if client accepts gzip encoding."""
        accept_encoding = request.META.get('HTTP_ACCEPT_ENCODING', '')
        return 'gzip' in accept_encoding.lower()
    
    def _should_compress(self, response) -> bool:
        """Determine if response should be compressed."""
        # Don't compress if already compressed
        if response.get('Content-Encoding'):
            return False
        
        # Only compress JSON responses
        content_type = response.get('Content-Type', '')
        if 'application/json' not in content_type:
            return False
        
        # Only compress if response is large enough
        if len(response.content) < self.min_length:
            return False
        
        return True
    
    def _compress_response(self, response) -> HttpResponse:
        """Compress the response content."""
        try:
            # Compress the content
            compressed_content = gzip.compress(
                response.content, 
                compresslevel=self.compression_level
            )
            
            # Create new response with compressed content
            compressed_response = HttpResponse(
                compressed_content,
                content_type=response.get('Content-Type'),
                status=response.status_code
            )
            
            # Copy headers
            for header, value in response.items():
                compressed_response[header] = value
            
            # Set compression headers
            compressed_response['Content-Encoding'] = 'gzip'
            compressed_response['Content-Length'] = len(compressed_content)
            compressed_response['Vary'] = 'Accept-Encoding'
            
            # Add compression ratio for monitoring
            original_size = len(response.content)
            compressed_size = len(compressed_content)
            compression_ratio = (1 - compressed_size / original_size) * 100
            compressed_response['X-Compression-Ratio'] = f"{compression_ratio:.1f}%"
            
            return compressed_response
            
        except Exception as e:
            # If compression fails, return original response
            print(f"Compression failed: {e}")
            return response


class ResponseCachingMiddleware(MiddlewareMixin):
    """
    Middleware to cache API responses for better performance.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.cache_timeout = getattr(settings, 'API_CACHE_TIMEOUT', 300)  # 5 minutes
        self.cacheable_methods = ['GET']
        self.cacheable_paths = ['/api/catalog/', '/api/services/', '/api/providers/']
        super().__init__(get_response)
    
    def process_request(self, request):
        """Check cache for existing response."""
        if not self._should_cache_request(request):
            return None
        
        cache_key = self._generate_cache_key(request)
        cached_response = cache.get(cache_key)
        
        if cached_response:
            # Create response from cached data
            response = JsonResponse(cached_response['data'])
            response.status_code = cached_response['status_code']
            
            # Add cache headers
            response['X-Cache'] = 'HIT'
            response['X-Cache-Key'] = cache_key[:16] + '...'
            
            return response
        
        return None
    
    def process_response(self, request, response):
        """Cache successful responses."""
        if not self._should_cache_response(request, response):
            return response
        
        try:
            # Parse JSON response
            response_data = json.loads(response.content.decode('utf-8'))
            
            # Create cache entry
            cache_entry = {
                'data': response_data,
                'status_code': response.status_code,
                'timestamp': time.time()
            }
            
            # Cache the response
            cache_key = self._generate_cache_key(request)
            cache.set(cache_key, cache_entry, self.cache_timeout)
            
            # Add cache headers
            response['X-Cache'] = 'MISS'
            response['X-Cache-Key'] = cache_key[:16] + '...'
            response['Cache-Control'] = f'max-age={self.cache_timeout}'
            
        except (json.JSONDecodeError, UnicodeDecodeError):
            # If response is not JSON, don't cache
            pass
        
        return response
    
    def _should_cache_request(self, request) -> bool:
        """Determine if request should be cached."""
        # Only cache GET requests
        if request.method not in self.cacheable_methods:
            return False
        
        # Only cache specific API paths
        if not any(request.path.startswith(path) for path in self.cacheable_paths):
            return False
        
        # Don't cache authenticated requests with user-specific data
        if hasattr(request, 'user') and request.user.is_authenticated and 'user' in request.path:
            return False
        
        return True
    
    def _should_cache_response(self, request, response) -> bool:
        """Determine if response should be cached."""
        # Only cache successful responses
        if response.status_code != 200:
            return False
        
        # Only cache if request was cacheable
        if not self._should_cache_request(request):
            return False
        
        # Only cache JSON responses
        content_type = response.get('Content-Type', '')
        if 'application/json' not in content_type:
            return False
        
        return True
    
    def _generate_cache_key(self, request) -> str:
        """Generate cache key for request."""
        # Include path, query parameters, and relevant headers
        key_parts = [
            request.path,
            request.GET.urlencode(),
            request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
        ]
        
        key_string = '|'.join(key_parts)
        return f"api_cache:{hashlib.md5(key_string.encode()).hexdigest()}"


class PaginationOptimizationMiddleware(MiddlewareMixin):
    """
    Middleware to optimize pagination responses.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.default_page_size = getattr(settings, 'DEFAULT_PAGE_SIZE', 20)
        self.max_page_size = getattr(settings, 'MAX_PAGE_SIZE', 100)
        super().__init__(get_response)
    
    def process_request(self, request):
        """Optimize pagination parameters."""
        if 'page_size' in request.GET:
            try:
                page_size = int(request.GET['page_size'])
                # Limit page size to prevent abuse
                if page_size > self.max_page_size:
                    # Modify the request to use max page size
                    request.GET = request.GET.copy()
                    request.GET['page_size'] = str(self.max_page_size)
            except ValueError:
                # Invalid page_size, use default
                request.GET = request.GET.copy()
                request.GET['page_size'] = str(self.default_page_size)
        
        return None
    
    def process_response(self, request, response):
        """Add pagination optimization headers."""
        if self._is_paginated_response(response):
            # Add pagination performance headers
            response['X-Pagination-Optimized'] = 'true'
            
            # Add prefetch hints for next page
            if 'next' in response.content.decode('utf-8'):
                response['Link'] = '</api/next-page>; rel="prefetch"'
        
        return response
    
    def _is_paginated_response(self, response) -> bool:
        """Check if response contains pagination data."""
        try:
            content = response.content.decode('utf-8')
            return 'count' in content and 'results' in content
        except UnicodeDecodeError:
            return False


class APIRateLimitingMiddleware(MiddlewareMixin):
    """
    Simple rate limiting middleware for API endpoints.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limit = getattr(settings, 'API_RATE_LIMIT', 1000)  # requests per hour
        self.rate_window = 3600  # 1 hour in seconds
        super().__init__(get_response)
    
    def process_request(self, request):
        """Check rate limits for API requests."""
        if not request.path.startswith('/api/'):
            return None
        
        client_ip = self._get_client_ip(request)
        rate_key = f"rate_limit:{client_ip}"
        
        # Get current request count
        current_requests = cache.get(rate_key, 0)
        
        if current_requests >= self.rate_limit:
            # Rate limit exceeded
            response = JsonResponse({
                'error': 'Rate limit exceeded',
                'detail': f'Maximum {self.rate_limit} requests per hour allowed',
                'retry_after': self.rate_window
            }, status=429)
            
            response['Retry-After'] = str(self.rate_window)
            response['X-RateLimit-Limit'] = str(self.rate_limit)
            response['X-RateLimit-Remaining'] = '0'
            response['X-RateLimit-Reset'] = str(int(time.time()) + self.rate_window)
            
            return response
        
        # Increment request count
        cache.set(rate_key, current_requests + 1, self.rate_window)
        
        return None
    
    def process_response(self, request, response):
        """Add rate limiting headers."""
        if request.path.startswith('/api/'):
            client_ip = self._get_client_ip(request)
            rate_key = f"rate_limit:{client_ip}"
            current_requests = cache.get(rate_key, 0)
            
            response['X-RateLimit-Limit'] = str(self.rate_limit)
            response['X-RateLimit-Remaining'] = str(max(0, self.rate_limit - current_requests))
            response['X-RateLimit-Reset'] = str(int(time.time()) + self.rate_window)
        
        return response
    
    def _get_client_ip(self, request) -> str:
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip or 'unknown'
