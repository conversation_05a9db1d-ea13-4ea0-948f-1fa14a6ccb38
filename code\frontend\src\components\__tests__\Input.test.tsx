/**
 * Input Component Test Suite
 * Simplified tests for the reusable Input component
 */

import React from 'react';
import { Input } from '../Input';

// Mock React Native components for testing
jest.mock('react-native', () => ({
  View: 'View',
  Text: 'Text',
  TextInput: 'TextInput',
  StyleSheet: {
    create: (styles: any) => styles,
  },
}));

describe('Input Component', () => {
  it('renders without crashing', () => {
    const mockOnChangeText = jest.fn();
    expect(() => {
      React.createElement(Input, {
        label: "Email",
        value: "",
        onChangeText: mockOnChangeText,
        placeholder: "Enter email"
      });
    }).not.toThrow();
  });

  it('accepts required props', () => {
    const mockOnChangeText = jest.fn();
    const element = React.createElement(Input, {
      label: "Email",
      value: "<EMAIL>",
      onChangeText: mockOnChangeText,
      placeholder: "Enter email"
    });
    expect(element.props.label).toBe("Email");
    expect(element.props.value).toBe("<EMAIL>");
    expect(element.props.onChangeText).toBe(mockOnChangeText);
    expect(element.props.placeholder).toBe("Enter email");
  });

  it('accepts optional props', () => {
    const mockOnChangeText = jest.fn();
    const element = React.createElement(Input, {
      label: "Password",
      value: "",
      onChangeText: mockOnChangeText,
      placeholder: "Enter password",
      secureTextEntry: true,
      error: "Password is required",
      leftIcon: "lock-closed"
    });
    expect(element.props.secureTextEntry).toBe(true);
    expect(element.props.error).toBe("Password is required");
    expect(element.props.leftIcon).toBe("lock-closed");
  });
});
