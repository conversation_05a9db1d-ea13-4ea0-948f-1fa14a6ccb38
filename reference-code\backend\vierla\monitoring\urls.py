"""
URL Configuration for Performance Monitoring Dashboard
Based on Backend Agent Consultation for Production Monitoring
"""

from django.urls import path
from . import views

app_name = 'monitoring'

urlpatterns = [
    # Main performance dashboard
    path('dashboard/', views.performance_dashboard, name='performance_dashboard'),
    
    # Detailed API metrics
    path('api-metrics/', views.api_metrics, name='api_metrics'),
    
    # Database performance metrics
    path('database/', views.database_metrics, name='database_metrics'),
    
    # System resource monitoring
    path('system/', views.system_resources, name='system_resources'),
    
    # Health check endpoint (handled by middleware but documented here)
    # path('health/', views.health_check, name='health_check'),
]
