#!/usr/bin/env python
"""
Create Booking History Database Seeder
Creates realistic booking history with reviews and payment records
"""

import os
import sys
import django
import random
from decimal import Decimal
from datetime import datetime, timedelta
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from django.utils import timezone
from apps.bookings.models import Booking
from apps.catalog.models import Service, ServiceProvider
from apps.reviews.models import Review

User = get_user_model()

# Realistic review templates
REVIEW_TEMPLATES = {
    5: [  # 5-star reviews
        "Absolutely amazing service! {provider_name} exceeded all my expectations. The {service_name} was perfect and I couldn't be happier with the results.",
        "Outstanding experience from start to finish! {provider_name} is incredibly skilled and professional. My {service_name} turned out exactly how I wanted.",
        "Best {service_name} I've ever had! {provider_name} is so talented and made me feel completely comfortable throughout the entire process.",
        "Wow! I'm blown away by the quality of service. {provider_name} is a true artist and the {service_name} was flawless. Definitely coming back!",
        "Perfect experience! {provider_name} listened to exactly what I wanted and delivered beyond my expectations. The {service_name} is gorgeous!",
        "I can't recommend {provider_name} enough! The {service_name} was incredible and the attention to detail was amazing. Five stars all the way!",
        "Exceptional service and results! {provider_name} is so professional and skilled. My {service_name} looks absolutely beautiful.",
        "Amazing work by {provider_name}! The {service_name} was exactly what I was looking for. Clean, professional, and fantastic results."
    ],
    4: [  # 4-star reviews
        "Really great {service_name} by {provider_name}! Very professional and the results look fantastic. Would definitely recommend.",
        "Excellent service! {provider_name} did a wonderful job with my {service_name}. Very happy with the outcome and will be back.",
        "Great experience overall! {provider_name} was skilled and professional. The {service_name} turned out really well.",
        "Very pleased with my {service_name}! {provider_name} was knowledgeable and the service was top quality. Highly recommend.",
        "Fantastic work by {provider_name}! The {service_name} looks great and the whole experience was very positive.",
        "Really happy with the results! {provider_name} was professional and did an excellent job with my {service_name}.",
        "Great {service_name} and wonderful service! {provider_name} was skilled and made sure I was comfortable throughout.",
        "Very good experience! {provider_name} delivered exactly what I asked for. The {service_name} looks beautiful."
    ],
    3: [  # 3-star reviews
        "Good {service_name} overall. {provider_name} was professional and the results were decent. Nothing spectacular but satisfied.",
        "Average experience. The {service_name} was okay and {provider_name} was nice enough. Results were as expected.",
        "Decent service from {provider_name}. The {service_name} turned out fine, though not exactly what I had in mind.",
        "Okay experience. {provider_name} was professional but the {service_name} was just average. Might try elsewhere next time.",
        "The {service_name} was alright. {provider_name} did an okay job but nothing that really stood out to me.",
        "Fair service. {provider_name} was nice and the {service_name} was decent quality. Could have been better though.",
        "Average {service_name}. {provider_name} was professional but the results were just okay. Not bad, not great.",
        "Decent experience overall. The {service_name} was fine and {provider_name} was pleasant to work with."
    ],
    2: [  # 2-star reviews
        "Disappointing experience. The {service_name} didn't turn out as expected and {provider_name} seemed rushed.",
        "Not great. {provider_name} was okay but the {service_name} quality was below my expectations. Won't be returning.",
        "Below average service. The {service_name} was not what I asked for and {provider_name} didn't seem to listen carefully.",
        "Unsatisfied with my {service_name}. {provider_name} was nice but the execution was poor. Expected much better.",
        "Poor quality {service_name}. {provider_name} seemed inexperienced and the results were disappointing.",
        "Not happy with the service. The {service_name} was subpar and {provider_name} didn't meet my expectations.",
        "Mediocre experience. {provider_name} was pleasant but the {service_name} quality was lacking. Wouldn't recommend.",
        "Disappointing results. The {service_name} didn't look good and {provider_name} seemed to rush through it."
    ],
    1: [  # 1-star reviews
        "Terrible experience! The {service_name} was completely wrong and {provider_name} was unprofessional. Waste of money.",
        "Awful service. {provider_name} damaged my hair/nails and was rude when I complained. Never going back!",
        "Worst {service_name} ever! {provider_name} clearly doesn't know what they're doing. Had to get it fixed elsewhere.",
        "Horrible experience. {provider_name} was late, unprofessional, and the {service_name} was a disaster. Avoid at all costs!",
        "Complete disaster! The {service_name} was nothing like what I asked for and {provider_name} was dismissive of my concerns.",
        "Terrible quality and service. {provider_name} ruined my {service_name} and refused to fix it. Very unprofessional.",
        "Worst experience ever! {provider_name} was rude and the {service_name} was completely botched. Don't waste your time.",
        "Absolutely awful! The {service_name} was a complete mess and {provider_name} had no idea what they were doing."
    ]
}

def generate_booking_number():
    """Generate a unique booking number"""
    timestamp = int(datetime.now().timestamp())
    random_suffix = random.randint(100, 999)
    return f"VRL{timestamp}{random_suffix}"

def get_random_past_datetime(days_back_min=7, days_back_max=365):
    """Generate a random datetime in the past"""
    days_back = random.randint(days_back_min, days_back_max)
    base_date = timezone.now() - timedelta(days=days_back)
    
    # Random time between 9 AM and 7 PM
    hour = random.randint(9, 19)
    minute = random.choice([0, 15, 30, 45])
    
    return base_date.replace(hour=hour, minute=minute, second=0, microsecond=0)

def get_booking_status_weights():
    """Return weighted probabilities for booking statuses"""
    return {
        'completed': 0.70,  # 70% completed bookings
        'cancelled': 0.15,  # 15% cancelled
        'no_show': 0.05,    # 5% no-shows
        'confirmed': 0.05,  # 5% upcoming confirmed
        'pending': 0.05     # 5% pending
    }

def get_payment_status_for_booking_status(booking_status):
    """Get appropriate payment status based on booking status"""
    if booking_status == 'completed':
        return 'paid'
    elif booking_status == 'cancelled':
        return random.choice(['refunded', 'refunded', 'paid'])  # Some cancellations still paid
    elif booking_status == 'no_show':
        return 'paid'  # Usually still charged for no-shows
    elif booking_status in ['confirmed', 'pending']:
        return random.choice(['paid', 'pending'])
    else:
        return 'pending'

def generate_review_for_booking(booking, customer, provider, service):
    """Generate a realistic review for a completed booking"""
    # Only create reviews for completed bookings, and not all of them
    if booking.status != 'completed' or random.random() > 0.6:  # 60% chance of review
        return None
    
    # Weight ratings toward higher scores (realistic distribution)
    rating_weights = [0.05, 0.10, 0.15, 0.30, 0.40]  # 1-5 stars
    rating = random.choices(range(1, 6), weights=rating_weights)[0]
    
    # Get appropriate review template
    review_templates = REVIEW_TEMPLATES[rating]
    review_text = random.choice(review_templates)
    
    # Format the review with actual names
    formatted_review = review_text.format(
        provider_name=provider.business_name,
        service_name=service.name.lower()
    )
    
    # Create review
    review = Review.objects.create(
        booking=booking,
        customer=customer,
        provider=provider,
        service=service,
        rating=rating,
        comment=formatted_review,
        is_verified=True,  # All reviews are verified for demo
        is_featured=rating >= 4 and random.random() < 0.3,  # 30% chance for 4-5 star reviews
        created_at=booking.completed_at + timedelta(hours=random.randint(1, 72))
    )
    
    return review

def create_booking_with_history(customer, service, provider):
    """Create a single booking with realistic history"""
    try:
        # Generate booking details
        scheduled_datetime = get_random_past_datetime()
        
        # Determine booking status
        status_weights = get_booking_status_weights()
        status = random.choices(
            list(status_weights.keys()),
            weights=list(status_weights.values())
        )[0]
        
        # Calculate pricing with realistic variations
        base_price = service.base_price
        additional_charges = Decimal('0.00')
        
        # Sometimes add tip or additional services
        if status == 'completed' and random.random() < 0.4:  # 40% chance of tip
            tip_percentage = random.choice([0.15, 0.18, 0.20, 0.25])
            additional_charges = base_price * Decimal(str(tip_percentage))
        
        # Calculate tax (13% HST for Ontario)
        subtotal = base_price + additional_charges
        tax_amount = subtotal * Decimal('0.13')
        total_amount = subtotal + tax_amount
        
        # Create booking
        booking = Booking.objects.create(
            booking_number=generate_booking_number(),
            customer=customer,
            provider=provider,
            service=service,
            status=status,
            payment_status=get_payment_status_for_booking_status(status),
            scheduled_datetime=scheduled_datetime,
            duration_minutes=service.duration,
            end_datetime=scheduled_datetime + timedelta(minutes=service.duration),
            location_type='salon',  # Most bookings at salon
            service_address=provider.address,
            service_latitude=provider.latitude,
            service_longitude=provider.longitude,
            base_price=base_price,
            additional_charges=additional_charges,
            tax_amount=tax_amount,
            total_amount=total_amount,
            customer_notes=random.choice([
                '', '', '',  # Most bookings have no notes
                'First time client, please be gentle!',
                'I have sensitive skin, please use gentle products.',
                'Looking forward to trying something new!',
                'Please take your time, I\'m not in a rush.',
                'I have an event this weekend, want to look perfect!',
                'Regular maintenance appointment.',
                'Bringing inspiration photos to show you.',
                'Please call if you need to reschedule.'
            ]),
            created_at=scheduled_datetime - timedelta(days=random.randint(1, 14))
        )
        
        # Set appropriate timestamps based on status
        if status in ['completed', 'cancelled', 'no_show']:
            booking.confirmed_at = booking.created_at + timedelta(hours=random.randint(1, 24))
            
            if status == 'completed':
                booking.started_at = booking.scheduled_datetime
                booking.completed_at = booking.scheduled_datetime + timedelta(minutes=service.duration)
            elif status == 'cancelled':
                booking.cancelled_at = booking.scheduled_datetime - timedelta(hours=random.randint(2, 48))
                booking.cancelled_by = random.choice([customer, provider.user])
                booking.cancellation_reason = random.choice([
                    'Schedule conflict',
                    'Personal emergency',
                    'Weather conditions',
                    'Feeling unwell',
                    'Found another provider',
                    'Changed mind about service',
                    'Provider unavailable',
                    'Double booking error'
                ])
        elif status == 'confirmed':
            booking.confirmed_at = booking.created_at + timedelta(hours=random.randint(1, 12))
            # Make these future bookings
            booking.scheduled_datetime = timezone.now() + timedelta(days=random.randint(1, 30))
            booking.end_datetime = booking.scheduled_datetime + timedelta(minutes=service.duration)
        
        booking.save()
        
        # Create review for completed bookings
        review = None
        if status == 'completed':
            review = generate_review_for_booking(booking, customer, provider, service)
        
        return booking, review
        
    except Exception as e:
        print(f"❌ Error creating booking: {e}")
        return None, None

def main():
    """Main function to create booking history"""
    print("🚀 Creating Booking History Database")
    print("=" * 60)
    
    # Get all customers and services
    customers = list(User.objects.filter(role='customer', is_active=True))
    services = list(Service.objects.filter(is_active=True).select_related('provider'))
    
    if not customers:
        print("❌ No customers found. Please run customer seeder first.")
        return
    
    if not services:
        print("❌ No services found. Please run service seeder first.")
        return
    
    print(f"📊 Found {len(customers)} customers and {len(services)} services")
    
    # Create bookings - aim for 3-8 bookings per customer
    total_bookings = 0
    total_reviews = 0
    
    for customer in customers:
        # Each customer gets 3-8 bookings
        num_bookings = random.randint(3, 8)
        customer_bookings = 0
        customer_reviews = 0
        
        print(f"👤 Creating bookings for {customer.first_name} {customer.last_name}")
        
        for _ in range(num_bookings):
            # Select random service
            service = random.choice(services)
            provider = service.provider
            
            booking, review = create_booking_with_history(customer, service, provider)
            
            if booking:
                customer_bookings += 1
                total_bookings += 1
                
                if review:
                    customer_reviews += 1
                    total_reviews += 1
        
        print(f"   ✅ Created {customer_bookings} bookings, {customer_reviews} reviews")
    
    print(f"\n📊 Summary")
    print("=" * 60)
    print(f"Total customers: {len(customers)}")
    print(f"Total bookings created: {total_bookings}")
    print(f"Total reviews created: {total_reviews}")
    print(f"Average bookings per customer: {total_bookings / len(customers):.1f}")
    print(f"Review rate: {(total_reviews / total_bookings * 100):.1f}%")
    
    if total_bookings > 0:
        print(f"\n🎉 Successfully created {total_bookings} booking records!")
        print("📝 Booking history includes:")
        print("   - Realistic booking statuses and timestamps")
        print("   - Payment records and pricing")
        print("   - Customer reviews and ratings")
        print("   - Cancellation reasons and notes")
        print("   - Past and upcoming appointments")
    else:
        print("\n⚠️ No bookings were created. Please check the errors above.")

if __name__ == "__main__":
    main()
