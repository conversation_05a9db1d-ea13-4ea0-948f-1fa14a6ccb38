"""
Location-based utilities for Vierla Beauty Services Marketplace
Enhanced geospatial calculations and mobile-optimized location services
"""
import math
from typing import List, Tuple, Dict, Any
from django.db.models import QuerySet
from .models import ServiceProvider, Service


class LocationUtils:
    """
    Utility class for location-based calculations and searches
    Optimized for mobile applications with battery-aware algorithms
    """
    
    EARTH_RADIUS_KM = 6371  # Earth's radius in kilometers
    
    @staticmethod
    def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the great circle distance between two points using Haversine formula
        
        Args:
            lat1, lon1: Latitude and longitude of first point
            lat2, lon2: Latitude and longitude of second point
            
        Returns:
            Distance in kilometers
        """
        # Convert decimal degrees to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        return c * LocationUtils.EARTH_RADIUS_KM
    
    @staticmethod
    def calculate_bearing(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """
        Calculate the bearing (direction) from point 1 to point 2
        
        Returns:
            Bearing in degrees (0-360)
        """
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        dlon = lon2 - lon1
        y = math.sin(dlon) * math.cos(lat2)
        x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dlon)
        
        bearing = math.atan2(y, x)
        bearing = math.degrees(bearing)
        bearing = (bearing + 360) % 360
        
        return bearing
    
    @staticmethod
    def get_bounding_box(lat: float, lon: float, radius_km: float) -> Dict[str, float]:
        """
        Calculate bounding box coordinates for a given center point and radius
        Useful for database queries to pre-filter results before distance calculation
        
        Returns:
            Dictionary with min/max latitude and longitude
        """
        # Approximate degrees per kilometer
        lat_degree_km = 111.0  # 1 degree latitude ≈ 111 km
        lon_degree_km = 111.0 * math.cos(math.radians(lat))  # Longitude varies by latitude
        
        lat_delta = radius_km / lat_degree_km
        lon_delta = radius_km / lon_degree_km
        
        return {
            'min_lat': lat - lat_delta,
            'max_lat': lat + lat_delta,
            'min_lon': lon - lon_delta,
            'max_lon': lon + lon_delta
        }
    
    @staticmethod
    def filter_providers_by_location(
        providers: QuerySet,
        user_lat: float,
        user_lon: float,
        radius_km: float = 25,
        max_results: int = 50
    ) -> List[Tuple[Any, float]]:
        """
        Filter providers by location and return with distances
        Optimized for mobile performance
        
        Returns:
            List of tuples (provider, distance_km) sorted by distance
        """
        # Pre-filter using bounding box for performance
        bbox = LocationUtils.get_bounding_box(user_lat, user_lon, radius_km)
        
        filtered_providers = providers.filter(
            latitude__gte=bbox['min_lat'],
            latitude__lte=bbox['max_lat'],
            longitude__gte=bbox['min_lon'],
            longitude__lte=bbox['max_lon'],
            latitude__isnull=False,
            longitude__isnull=False
        )
        
        # Calculate exact distances
        provider_distances = []
        for provider in filtered_providers:
            distance = LocationUtils.calculate_distance(
                user_lat, user_lon,
                float(provider.latitude), float(provider.longitude)
            )
            
            if distance <= radius_km:
                provider_distances.append((provider, distance))
        
        # Sort by distance and limit results
        provider_distances.sort(key=lambda x: x[1])
        return provider_distances[:max_results]
    
    @staticmethod
    def filter_services_by_location(
        services: QuerySet,
        user_lat: float,
        user_lon: float,
        radius_km: float = 25,
        max_results: int = 50
    ) -> List[Tuple[Any, float]]:
        """
        Filter services by provider location and return with distances
        
        Returns:
            List of tuples (service, distance_km) sorted by distance
        """
        # Pre-filter using bounding box for performance
        bbox = LocationUtils.get_bounding_box(user_lat, user_lon, radius_km)
        
        filtered_services = services.filter(
            provider__latitude__gte=bbox['min_lat'],
            provider__latitude__lte=bbox['max_lat'],
            provider__longitude__gte=bbox['min_lon'],
            provider__longitude__lte=bbox['max_lon'],
            provider__latitude__isnull=False,
            provider__longitude__isnull=False
        ).select_related('provider')
        
        # Calculate exact distances
        service_distances = []
        for service in filtered_services:
            provider = service.provider
            distance = LocationUtils.calculate_distance(
                user_lat, user_lon,
                float(provider.latitude), float(provider.longitude)
            )
            
            if distance <= radius_km:
                service_distances.append((service, distance))
        
        # Sort by distance and limit results
        service_distances.sort(key=lambda x: x[1])
        return service_distances[:max_results]
    
    @staticmethod
    def get_location_summary(lat: float, lon: float) -> Dict[str, Any]:
        """
        Get location summary information for mobile display
        This would integrate with geocoding services in production
        
        Returns:
            Dictionary with location information
        """
        # Placeholder implementation
        # In production, this would use Google Maps API or similar
        return {
            'latitude': lat,
            'longitude': lon,
            'formatted_address': f"Location: {lat:.4f}, {lon:.4f}",
            'city': "Unknown",
            'state': "Unknown",
            'country': "Unknown"
        }
    
    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """
        Validate latitude and longitude coordinates
        
        Returns:
            True if coordinates are valid, False otherwise
        """
        try:
            lat = float(lat)
            lon = float(lon)
            return -90 <= lat <= 90 and -180 <= lon <= 180
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def format_distance(distance_km: float) -> str:
        """
        Format distance for mobile display
        
        Returns:
            Formatted distance string
        """
        if distance_km < 1:
            return f"{int(distance_km * 1000)}m"
        elif distance_km < 10:
            return f"{distance_km:.1f}km"
        else:
            return f"{int(distance_km)}km"
    
    @staticmethod
    def get_travel_time_estimate(distance_km: float, mode: str = 'driving') -> int:
        """
        Estimate travel time based on distance and mode
        Simplified calculation for mobile display
        
        Args:
            distance_km: Distance in kilometers
            mode: Travel mode ('driving', 'walking', 'transit')
            
        Returns:
            Estimated travel time in minutes
        """
        # Simplified speed estimates (km/h)
        speeds = {
            'driving': 40,  # City driving average
            'walking': 5,   # Walking speed
            'transit': 25,  # Public transit average
            'cycling': 15   # Cycling speed
        }
        
        speed = speeds.get(mode, speeds['driving'])
        time_hours = distance_km / speed
        return max(1, int(time_hours * 60))  # Minimum 1 minute
