"""
Login Authentication Tests
Tests for EPIC-AD-HOC-02: Critical Login Fixes & Onboarding Implementation

This test suite covers:
1. Login authentication view functionality
2. Authentication serializer validation
3. Error response consistency
4. Account lockout mechanism
5. JWT token generation
6. Rate limiting behavior
"""

from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, MagicMock
import json
from datetime import timedelta
from django.utils import timezone

from authentication.models import User
from authentication.serializers import UserLoginSerializer
from authentication.views import CustomTokenObtainPairView

User = get_user_model()


class LoginAuthenticationViewTests(APITestCase):
    """Test cases for login authentication view"""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        
        # Create test user
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            password='validPassword123',
            role='customer',
            is_verified=True
        )
        
        self.valid_credentials = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        self.invalid_credentials = {
            'email': '<EMAIL>',
            'password': 'wrongPassword'
        }

    def test_successful_login_with_valid_credentials(self):
        """Test successful login with valid credentials returns 200 and tokens"""
        response = self.client.post(self.login_url, self.valid_credentials)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)
        self.assertEqual(response.data['user']['email'], '<EMAIL>')

    def test_invalid_credentials_returns_400_status(self):
        """Test invalid credentials return 400 status with proper error message"""
        response = self.client.post(self.login_url, self.invalid_credentials)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)
        self.assertEqual(response.data['detail'], 'Invalid credentials')

    def test_nonexistent_user_returns_400_status(self):
        """Test non-existent user returns 400 status with same error message"""
        credentials = {
            'email': '<EMAIL>',
            'password': 'anyPassword'
        }
        response = self.client.post(self.login_url, credentials)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)
        self.assertEqual(response.data['detail'], 'Invalid credentials')

    def test_malformed_email_returns_validation_error(self):
        """Test malformed email returns proper validation error"""
        credentials = {
            'email': 'invalid-email',
            'password': 'validPassword123'
        }
        response = self.client.post(self.login_url, credentials)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # Should contain validation error for email field

    def test_missing_email_returns_validation_error(self):
        """Test missing email field returns validation error"""
        credentials = {
            'password': 'validPassword123'
        }
        response = self.client.post(self.login_url, credentials)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_missing_password_returns_validation_error(self):
        """Test missing password field returns validation error"""
        credentials = {
            'email': '<EMAIL>'
        }
        response = self.client.post(self.login_url, credentials)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_empty_credentials_returns_validation_error(self):
        """Test empty credentials return validation error"""
        response = self.client.post(self.login_url, {})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class AccountLockoutTests(APITestCase):
    """Test cases for account lockout functionality"""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        
        # Create test user
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            password='validPassword123',
            role='customer',
            is_verified=True
        )

    def test_account_lockout_after_failed_attempts(self):
        """Test account gets locked after multiple failed login attempts"""
        invalid_credentials = {
            'email': '<EMAIL>',
            'password': 'wrongPassword'
        }
        
        # Make multiple failed attempts
        for _ in range(5):
            response = self.client.post(self.login_url, invalid_credentials)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Check if account is locked
        self.test_user.refresh_from_db()
        # This test will verify the account lockout mechanism
        # Implementation will be added in CODE-01 phase

    def test_locked_account_returns_423_status(self):
        """Test locked account returns 423 status code"""
        # Manually lock the account
        self.test_user.is_account_locked = True
        self.test_user.save()
        
        valid_credentials = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        response = self.client.post(self.login_url, valid_credentials)
        
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)
        self.assertIn('detail', response.data)

    def test_account_unlock_after_timeout(self):
        """Test account unlocks after timeout period"""
        # This test will verify automatic account unlocking
        # Implementation will be added in CODE-01 phase
        pass


class LoginSerializerTests(TestCase):
    """Test cases for login serializer"""
    
    def setUp(self):
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            password='validPassword123',
            role='customer',
            is_verified=True
        )

    def test_valid_credentials_serializer_validation(self):
        """Test serializer validates correct credentials"""
        data = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        serializer = UserLoginSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['user'], self.test_user)

    def test_invalid_credentials_serializer_validation(self):
        """Test serializer rejects invalid credentials"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongPassword'
        }
        
        serializer = UserLoginSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('detail', serializer.errors)

    def test_email_normalization_in_serializer(self):
        """Test email is normalized to lowercase in serializer"""
        data = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        serializer = UserLoginSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['user'], self.test_user)


class JWTTokenGenerationTests(APITestCase):
    """Test cases for JWT token generation"""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            password='validPassword123',
            role='service_provider',
            is_verified=True
        )

    def test_jwt_tokens_generated_on_successful_login(self):
        """Test JWT tokens are generated on successful login"""
        credentials = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        response = self.client.post(self.login_url, credentials)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        
        # Verify tokens are valid JWT format
        access_token = response.data['access']
        refresh_token = response.data['refresh']
        
        self.assertTrue(isinstance(access_token, str))
        self.assertTrue(isinstance(refresh_token, str))
        self.assertTrue(len(access_token) > 0)
        self.assertTrue(len(refresh_token) > 0)

    def test_jwt_token_contains_user_claims(self):
        """Test JWT token contains proper user claims"""
        credentials = {
            'email': '<EMAIL>',
            'password': 'validPassword123'
        }
        
        response = self.client.post(self.login_url, credentials)
        
        # This test will verify JWT token claims
        # Implementation will be added in CODE-01 phase
        self.assertEqual(response.status_code, status.HTTP_200_OK)


@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_RATES': {
            'login': '3/min',
        }
    }
)
class RateLimitingTests(APITestCase):
    """Test cases for rate limiting functionality"""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')

    def test_rate_limiting_blocks_excessive_requests(self):
        """Test rate limiting blocks excessive login requests"""
        credentials = {
            'email': '<EMAIL>',
            'password': 'anyPassword'
        }
        
        # This test will verify rate limiting functionality
        # Implementation will be added in CODE-01 phase
        pass


class ErrorResponseConsistencyTests(APITestCase):
    """Test cases for error response format consistency"""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')

    def test_error_response_format_consistency(self):
        """Test all error responses follow consistent format"""
        # This test will verify consistent error response format
        # Implementation will be added in CODE-01 phase
        pass

    def test_error_message_localization(self):
        """Test error messages support localization"""
        # This test will verify error message localization
        # Implementation will be added in CODE-01 phase
        pass
