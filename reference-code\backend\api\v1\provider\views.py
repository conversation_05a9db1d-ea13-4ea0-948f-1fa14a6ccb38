"""
Provider API Views - Enhanced based on Backend Agent feedback
Role-based API endpoints specifically designed for service provider functionality
"""

from rest_framework import viewsets, status, permissions
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle
from django.db.models import Q, Count, Avg, Sum
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter
from datetime import timedelta

from apps.catalog.models import Service, ServiceProvider
from apps.bookings.models import Booking
from .serializers import (
    ProviderDashboardSerializer,
    ProviderStoreSerializer,
    ProviderBookingSerializer,
    ProviderAnalyticsSerializer,
    ProviderServiceSerializer,
    ProviderProfileSerializer,
)
from .permissions import IsProviderUser, IsProviderOwner
from .throttling import ProviderAPIThrottle


class ProviderDashboardViewSet(viewsets.ViewSet):
    """
    Provider dashboard with business metrics and overview
    Enhanced with analytics pre-calculation using Celery
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]
    throttle_classes = [ProviderAPIThrottle]
    
    @extend_schema(
        summary="Get provider dashboard data",
        description="Comprehensive dashboard with business metrics and insights"
    )
    @action(detail=False, methods=['get'])
    def overview(self, request):
        """Get dashboard overview with key metrics"""
        provider = request.user.provider_profile
        
        # Check cache first
        cache_key = f'provider_dashboard_{provider.id}'
        cached_data = cache.get(cache_key)
        
        if cached_data is None:
            # Calculate metrics
            today = timezone.now().date()
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)
            
            # Booking metrics
            total_bookings = Booking.objects.filter(
                service__provider=provider
            ).count()
            
            weekly_bookings = Booking.objects.filter(
                service__provider=provider,
                created_at__date__gte=week_ago
            ).count()
            
            monthly_revenue = Booking.objects.filter(
                service__provider=provider,
                created_at__date__gte=month_ago,
                status='completed'
            ).aggregate(total=Sum('total_amount'))['total'] or 0
            
            # Service metrics
            active_services = Service.objects.filter(
                provider=provider,
                is_active=True
            ).count()
            
            # Rating metrics - get from Review model
            from apps.reviews.models import Review
            avg_rating = Review.objects.filter(
                provider=provider
            ).aggregate(avg=Avg('rating'))['avg'] or 0
            
            # Upcoming appointments
            upcoming_appointments = Booking.objects.filter(
                service__provider=provider,
                scheduled_datetime__gte=timezone.now(),
                status__in=['confirmed', 'pending']
            ).order_by('scheduled_datetime')[:5]
            
            dashboard_data = {
                'total_bookings': total_bookings,
                'weekly_bookings': weekly_bookings,
                'monthly_revenue': float(monthly_revenue),
                'active_services': active_services,
                'average_rating': round(float(avg_rating), 2),
                'upcoming_appointments': ProviderBookingSerializer(
                    upcoming_appointments, many=True
                ).data,
                'last_updated': timezone.now().isoformat(),
            }
            
            # Cache for 15 minutes
            cache.set(cache_key, dashboard_data, 60 * 15)
            cached_data = dashboard_data
        
        return Response(cached_data)
    
    @action(detail=False, methods=['get'])
    def earnings(self, request):
        """Get detailed earnings breakdown"""
        provider = request.user.provider_profile
        period = request.query_params.get('period', 'month')  # week, month, year
        
        # Calculate earnings based on period
        if period == 'week':
            start_date = timezone.now().date() - timedelta(days=7)
        elif period == 'year':
            start_date = timezone.now().date() - timedelta(days=365)
        else:  # month
            start_date = timezone.now().date() - timedelta(days=30)
        
        earnings = Booking.objects.filter(
            service__provider=provider,
            created_at__date__gte=start_date,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )
        
        return Response({
            'period': period,
            'total_earnings': float(earnings['total'] or 0),
            'total_bookings': earnings['count'],
            'average_per_booking': float(earnings['total'] or 0) / max(earnings['count'], 1),
        })


class ProviderStoreViewSet(viewsets.ModelViewSet):
    """
    Provider store management with customization features
    Enhanced with Instagram portfolio integration
    """
    serializer_class = ProviderStoreSerializer
    permission_classes = [permissions.IsAuthenticated, IsProviderUser, IsProviderOwner]
    throttle_classes = [ProviderAPIThrottle]
    
    def get_queryset(self):
        """Provider can only manage their own store"""
        return ServiceProvider.objects.filter(user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def customize(self, request, pk=None):
        """Customize store appearance and settings"""
        store = self.get_object()
        customization_data = request.data
        
        # Update store customization
        if 'theme_colors' in customization_data:
            store.theme_colors = customization_data['theme_colors']
        
        if 'layout_preferences' in customization_data:
            store.layout_preferences = customization_data['layout_preferences']
        
        if 'instagram_portfolio' in customization_data:
            store.instagram_portfolio = customization_data['instagram_portfolio']
        
        store.save()
        
        # Clear cache
        cache.delete(f'provider_store_{store.id}')
        
        return Response(self.get_serializer(store).data)
    
    @action(detail=True, methods=['get'])
    def portfolio(self, request, pk=None):
        """Get Instagram portfolio integration"""
        store = self.get_object()
        
        # This would integrate with Instagram API
        # For now, return placeholder data
        portfolio_data = {
            'instagram_connected': bool(store.instagram_portfolio),
            'recent_posts': [],
            'follower_count': 0,
            'engagement_rate': 0,
        }
        
        return Response(portfolio_data)


class ProviderBookingViewSet(viewsets.ModelViewSet):
    """
    Provider booking management with schedule optimization
    Enhanced with real-time updates and customer communication
    """
    serializer_class = ProviderBookingSerializer
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]
    throttle_classes = [ProviderAPIThrottle]
    
    def get_queryset(self):
        """Provider can only see bookings for their services"""
        return Booking.objects.select_related(
            'service', 'customer'
        ).filter(
            service__provider__user=self.request.user
        ).order_by('-created_at')
    
    @action(detail=False, methods=['get'])
    def schedule(self, request):
        """Get provider's schedule with availability"""
        date_str = request.query_params.get('date')
        if date_str:
            try:
                target_date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response({'error': 'Invalid date format'}, status=400)
        else:
            target_date = timezone.now().date()
        
        # Get bookings for the date
        bookings = self.get_queryset().filter(
            scheduled_datetime__date=target_date,
            status__in=['confirmed', 'pending']
        ).order_by('scheduled_datetime')
        
        schedule_data = {
            'date': target_date.isoformat(),
            'bookings': self.get_serializer(bookings, many=True).data,
            'available_slots': [],  # This would calculate available time slots
            'total_bookings': bookings.count(),
        }
        
        return Response(schedule_data)
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm a pending booking"""
        booking = self.get_object()
        
        if booking.status != 'pending':
            return Response(
                {'error': 'Only pending bookings can be confirmed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking.status = 'confirmed'
        booking.confirmed_at = timezone.now()
        booking.save()
        
        # Send notification to customer (would be implemented)
        # send_booking_confirmation_notification(booking)
        
        return Response(self.get_serializer(booking).data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Mark booking as completed"""
        booking = self.get_object()
        
        if booking.status != 'confirmed':
            return Response(
                {'error': 'Only confirmed bookings can be completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        booking.status = 'completed'
        booking.completed_at = timezone.now()
        booking.save()
        
        return Response(self.get_serializer(booking).data)


class ProviderAnalyticsViewSet(viewsets.ViewSet):
    """
    Provider analytics with pre-calculated metrics
    Enhanced with business insights and performance tracking
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]
    throttle_classes = [ProviderAPIThrottle]
    
    @action(detail=False, methods=['get'])
    def performance(self, request):
        """Get performance analytics"""
        provider = request.user.provider_profile
        period = request.query_params.get('period', 'month')
        
        # This would use pre-calculated analytics from Celery tasks
        cache_key = f'provider_analytics_{provider.id}_{period}'
        analytics_data = cache.get(cache_key)
        
        if analytics_data is None:
            # Calculate analytics (would be done by Celery in production)
            analytics_data = {
                'booking_trends': [],
                'revenue_trends': [],
                'customer_satisfaction': 0,
                'popular_services': [],
                'peak_hours': [],
                'conversion_rate': 0,
            }
            
            # Cache for 1 hour
            cache.set(cache_key, analytics_data, 60 * 60)
        
        return Response(analytics_data)
    
    @action(detail=False, methods=['get'])
    def insights(self, request):
        """Get business insights and recommendations"""
        provider = request.user.provider_profile
        
        # Generate insights based on data
        insights = {
            'recommendations': [
                'Consider adding evening availability to increase bookings',
                'Your nail art service has high demand - consider expanding',
                'Customer reviews mention quick service - highlight this in your profile',
            ],
            'growth_opportunities': [
                'Add new service categories',
                'Optimize pricing for peak hours',
                'Improve customer retention',
            ],
            'performance_summary': {
                'strong_points': ['High customer satisfaction', 'Quick response time'],
                'improvement_areas': ['Weekend availability', 'Service variety'],
            }
        }
        
        return Response(insights)


# Additional View Classes for URL patterns
class ProviderProfileView(APIView):
    """
    Provider profile management API
    Handles provider profile CRUD operations
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        """Get provider profile data"""
        try:
            provider = request.user.provider_profile
            serializer = ProviderProfileSerializer(provider)
            return Response(serializer.data)
        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch profile: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request):
        """Update provider profile"""
        try:
            provider = request.user.provider_profile
            serializer = ProviderProfileSerializer(provider, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            else:
                return Response(
                    {'error': 'Invalid data', 'details': serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to update profile: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BusinessSettingsView(APIView):
    """
    Provider business settings API
    Handles provider business configuration and preferences
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        """Get provider business settings"""
        try:
            provider = request.user.provider_profile

            # Build settings response from provider model
            settings = {
                'notifications': {
                    'newBookings': True,  # Default values - could be stored in a separate model
                    'cancellations': True,
                    'reviews': True,
                    'promotions': False,
                    'systemUpdates': True,
                },
                'availability': {
                    'autoAcceptBookings': False,
                    'requireDeposit': False,
                    'depositPercentage': 25,
                    'bufferTime': 15,  # minutes between bookings
                },
                'communication': {
                    'allowDirectMessages': True,
                    'autoResponders': False,
                    'businessPhone': bool(provider.business_phone),
                },
                'privacy': {
                    'showExactLocation': True,
                    'showPersonalInfo': True,
                    'allowReviews': True,
                },
                'business': {
                    'businessName': provider.business_name,
                    'businessPhone': provider.business_phone,
                    'businessEmail': provider.business_email,
                    'website': provider.website,
                    'instagramHandle': provider.instagram_handle,
                    'isActive': provider.is_active,
                }
            }

            return Response(settings)
        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch settings: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request):
        """Update provider business settings"""
        try:
            provider = request.user.provider_profile
            data = request.data

            # Update business information if provided
            if 'business' in data:
                business_data = data['business']
                if 'businessName' in business_data:
                    provider.business_name = business_data['businessName']
                if 'businessPhone' in business_data:
                    provider.business_phone = business_data['businessPhone']
                if 'businessEmail' in business_data:
                    provider.business_email = business_data['businessEmail']
                if 'website' in business_data:
                    provider.website = business_data['website']
                if 'instagramHandle' in business_data:
                    provider.instagram_handle = business_data['instagramHandle']
                if 'isActive' in business_data:
                    provider.is_active = business_data['isActive']

                provider.save()

            # Note: Other settings (notifications, availability, etc.) would typically
            # be stored in separate models or JSON fields. For now, we'll just return success.

            return Response({'message': 'Settings updated successfully'})
        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to update settings: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BulkUpdateServicesView(APIView):
    """
    Bulk update services for provider
    Allows updating multiple services at once
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def post(self, request):
        """Bulk update services"""
        try:
            provider = request.user.provider_profile
            services_data = request.data.get('services', [])

            if not services_data:
                return Response(
                    {'error': 'No services data provided'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            updated_services = []
            errors = []

            for service_data in services_data:
                service_id = service_data.get('id')
                if not service_id:
                    errors.append({'error': 'Service ID is required'})
                    continue

                try:
                    service = Service.objects.get(id=service_id, provider=provider)

                    # Update allowed fields
                    if 'name' in service_data:
                        service.name = service_data['name']
                    if 'description' in service_data:
                        service.description = service_data['description']
                    if 'base_price' in service_data:
                        service.base_price = service_data['base_price']
                    if 'duration' in service_data:
                        service.duration = service_data['duration']
                    if 'is_active' in service_data:
                        service.is_active = service_data['is_active']

                    service.save()
                    updated_services.append({
                        'id': str(service.id),
                        'name': service.name,
                        'updated': True
                    })

                except Service.DoesNotExist:
                    errors.append({
                        'id': service_id,
                        'error': 'Service not found or not owned by provider'
                    })
                except Exception as e:
                    errors.append({
                        'id': service_id,
                        'error': str(e)
                    })

            return Response({
                'updated_services': updated_services,
                'errors': errors,
                'total_updated': len(updated_services),
                'total_errors': len(errors)
            })

        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Bulk update failed: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ToggleServiceActiveView(APIView):
    """
    Toggle service active status
    Allows providers to activate/deactivate individual services
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def post(self, request, service_id):
        """Toggle service active status"""
        try:
            provider = request.user.provider_profile
            service = Service.objects.get(id=service_id, provider=provider)

            # Toggle the active status
            service.is_active = not service.is_active
            service.save()

            return Response({
                'service_id': str(service.id),
                'service_name': service.name,
                'is_active': service.is_active,
                'message': f'Service {"activated" if service.is_active else "deactivated"} successfully'
            })

        except Service.DoesNotExist:
            return Response(
                {'error': 'Service not found or not owned by provider'},
                status=status.HTTP_404_NOT_FOUND
            )
        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to toggle service: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProviderServicesView(APIView):
    """
    Provider services management API
    Handles CRUD operations for provider services
    """
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        """Get all services for the provider"""
        try:
            provider = request.user.provider_profile
            services = Service.objects.filter(provider=provider).order_by('-created_at')

            services_data = []
            for service in services:
                services_data.append({
                    'id': str(service.id),
                    'name': service.name,
                    'description': service.description,
                    'base_price': float(service.base_price),
                    'duration': service.duration,
                    'is_active': service.is_active,
                    'category': service.category.name if service.category else None,
                    'created_at': service.created_at.isoformat(),
                    'updated_at': service.updated_at.isoformat(),
                })

            return Response({
                'services': services_data,
                'total_services': len(services_data),
                'active_services': len([s for s in services_data if s['is_active']]),
                'inactive_services': len([s for s in services_data if not s['is_active']]),
            })

        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch services: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def post(self, request):
        """Create a new service"""
        try:
            provider = request.user.provider_profile
            data = request.data

            # Validate required fields
            required_fields = ['name', 'description', 'base_price', 'duration']
            for field in required_fields:
                if field not in data:
                    return Response(
                        {'error': f'Field "{field}" is required'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Create the service
            service = Service.objects.create(
                provider=provider,
                name=data['name'],
                description=data['description'],
                base_price=data['base_price'],
                duration=data['duration'],
                is_active=data.get('is_active', True),
                category_id=data.get('category_id') if data.get('category_id') else None,
            )

            return Response({
                'service': {
                    'id': str(service.id),
                    'name': service.name,
                    'description': service.description,
                    'base_price': float(service.base_price),
                    'duration': service.duration,
                    'is_active': service.is_active,
                    'category': service.category.name if service.category else None,
                    'created_at': service.created_at.isoformat(),
                },
                'message': 'Service created successfully'
            }, status=status.HTTP_201_CREATED)

        except ServiceProvider.DoesNotExist:
            return Response(
                {'error': 'Provider profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to create service: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PricingOptimizationView(APIView):
    """Pricing optimization view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Pricing optimization'})


class BookingCalendarView(APIView):
    """Booking calendar view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Booking calendar'})


class AvailabilityManagementView(APIView):
    """Availability management view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Availability management'})


class BulkBookingActionsView(APIView):
    """Bulk booking actions view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def post(self, request):
        return Response({'message': 'Bulk booking actions'})


class CustomerMessagesView(APIView):
    """Customer messages view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Customer messages'})


class CustomerHistoryView(APIView):
    """Customer history view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request, customer_id):
        return Response({'message': f'Customer {customer_id} history'})


class EarningsView(APIView):
    """Earnings view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Earnings'})


class PayoutsView(APIView):
    """Payouts view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Payouts'})


class TaxReportsView(APIView):
    """Tax reports view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Tax reports'})


class PromotionsView(APIView):
    """Promotions view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Promotions'})


class SocialMediaIntegrationView(APIView):
    """Social media integration view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Social media integration'})


class CustomerBehaviorInsightsView(APIView):
    """Customer behavior insights view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Customer behavior insights'})


class MarketTrendsView(APIView):
    """Market trends view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Market trends'})


class CompetitorAnalysisView(APIView):
    """Competitor analysis view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Competitor analysis'})


class VerificationStatusView(APIView):
    """Verification status view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Verification status'})


class VerificationDocumentsView(APIView):
    """Verification documents view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Verification documents'})


class OptimizationRecommendationsView(APIView):
    """Optimization recommendations view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'Optimization recommendations'})


class ABTestingView(APIView):
    """A/B testing view"""
    permission_classes = [permissions.IsAuthenticated, IsProviderUser]

    def get(self, request):
        return Response({'message': 'A/B testing'})
