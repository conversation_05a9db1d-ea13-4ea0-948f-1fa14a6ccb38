import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';

interface ServiceStatsProps {
  totalServices: number;
  activeServices: number;
  inactiveServices: number;
  popularServices?: number;
  totalRevenue?: number;
  averagePrice?: number;
  totalBookings?: number;
  isVerified?: boolean;
  serviceLimit?: number | null;
  servicesRemaining?: number | null;
  onViewAll?: () => void;
  compact?: boolean;
}

export const ServiceStats: React.FC<ServiceStatsProps> = ({
  totalServices,
  activeServices,
  inactiveServices,
  popularServices,
  totalRevenue,
  averagePrice,
  totalBookings,
  isVerified = false,
  serviceLimit,
  servicesRemaining,
  onViewAll,
  compact = false,
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const renderStatCard = (
    icon: string,
    value: string | number,
    label: string,
    color: string = colors.primary,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={[styles.statCard, compact && styles.compactStatCard]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <Icon name={icon} size={compact ? 20 : 24} color={color} />
      <Text style={[styles.statValue, compact && styles.compactStatValue]}>
        {value}
      </Text>
      <Text style={[styles.statLabel, compact && styles.compactStatLabel]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderServiceOverview = () => (
    <View style={styles.overviewContainer}>
      <View style={styles.overviewHeader}>
        <Text style={styles.overviewTitle}>Service Overview</Text>
        {onViewAll && (
          <TouchableOpacity onPress={onViewAll}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.overviewStats}>
        {renderStatCard('room-service', totalServices, 'Total', colors.primary)}
        {renderStatCard('visibility', activeServices, 'Active', colors.success)}
        {renderStatCard('visibility-off', inactiveServices, 'Inactive', colors.error)}
        {popularServices !== undefined && 
          renderStatCard('star', popularServices, 'Popular', colors.warning)
        }
      </View>

      {/* Service Limit Warning */}
      {serviceLimit && servicesRemaining !== null && (
        <View style={styles.limitWarning}>
          <Icon name="info" size={16} color={colors.warning} />
          <Text style={styles.limitText}>
            {servicesRemaining} of {serviceLimit} services remaining
          </Text>
          {!isVerified && (
            <Text style={styles.verifyText}>
              Verify your account to add unlimited services
            </Text>
          )}
        </View>
      )}
    </View>
  );

  const renderRevenueStats = () => {
    if (totalRevenue === undefined && averagePrice === undefined && totalBookings === undefined) {
      return null;
    }

    return (
      <View style={styles.revenueContainer}>
        <Text style={styles.sectionTitle}>Performance</Text>
        <View style={styles.revenueStats}>
          {totalRevenue !== undefined && 
            renderStatCard('attach-money', formatCurrency(totalRevenue), 'Revenue', colors.success)
          }
          {averagePrice !== undefined && 
            renderStatCard('trending-up', formatCurrency(averagePrice), 'Avg Price', colors.primary)
          }
          {totalBookings !== undefined && 
            renderStatCard('event', totalBookings, 'Bookings', colors.secondary)
          }
        </View>
      </View>
    );
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactStats}>
          {renderStatCard('room-service', totalServices, 'Total')}
          {renderStatCard('visibility', activeServices, 'Active', colors.success)}
          {renderStatCard('visibility-off', inactiveServices, 'Inactive', colors.error)}
        </View>
        
        {serviceLimit && servicesRemaining !== null && (
          <View style={styles.compactLimitInfo}>
            <Icon name="info" size={14} color={colors.warning} />
            <Text style={styles.compactLimitText}>
              {servicesRemaining} remaining
            </Text>
          </View>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderServiceOverview()}
      {renderRevenueStats()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  compactContainer: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  overviewContainer: {
    marginBottom: spacing.lg,
  },
  overviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  overviewTitle: {
    ...typography.h3,
    color: colors.textPrimary,
  },
  viewAllText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  overviewStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  compactStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.md,
    marginHorizontal: spacing.xs,
  },
  compactStatCard: {
    paddingVertical: spacing.sm,
    marginHorizontal: 0,
  },
  statValue: {
    ...typography.h2,
    color: colors.textPrimary,
    marginTop: spacing.xs,
  },
  compactStatValue: {
    ...typography.h3,
    marginTop: spacing.xs,
  },
  statLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    textAlign: 'center',
  },
  compactStatLabel: {
    fontSize: 10,
  },
  limitWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    padding: spacing.md,
    backgroundColor: colors.warningLight,
    borderRadius: 8,
  },
  limitText: {
    ...typography.caption,
    color: colors.warning,
    marginLeft: spacing.xs,
    flex: 1,
  },
  verifyText: {
    ...typography.caption,
    color: colors.warning,
    fontStyle: 'italic',
    marginTop: spacing.xs,
  },
  compactLimitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.sm,
    backgroundColor: colors.warningLight,
    borderRadius: 6,
  },
  compactLimitText: {
    ...typography.caption,
    color: colors.warning,
    marginLeft: spacing.xs,
    fontSize: 10,
  },
  revenueContainer: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.lg,
  },
  sectionTitle: {
    ...typography.h3,
    color: colors.textPrimary,
    marginBottom: spacing.md,
  },
  revenueStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
