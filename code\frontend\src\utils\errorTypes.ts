/**
 * Standardized Error Types and Interfaces for Vierla Application
 * 
 * This file defines the core error types, interfaces, and enums used
 * throughout the application for consistent error handling.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

export enum ErrorType {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorVariant {
  TOAST = 'toast',
  MODAL = 'modal',
  INLINE = 'inline',
  BANNER = 'banner'
}

export interface ErrorContext {
  screen?: string;
  component?: string;
  action?: string;
  userId?: string;
  timestamp?: number;
  metadata?: Record<string, any>;
}

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage?: string;
  code?: string;
  context?: ErrorContext;
  originalError?: Error;
  timestamp: number;
  retryable?: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export interface ErrorDisplayProps {
  error: Error | string | AppError;
  title?: string;
  description?: string;
  severity?: ErrorSeverity;
  variant?: ErrorVariant;
  showIcon?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  onRetry?: () => void;
  retryLabel?: string;
  testID?: string;
  style?: any;
}

export interface ToastConfig {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onPress: () => void;
  };
  onDismiss?: () => void;
  enableHaptics?: boolean;
  testID?: string;
}

export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableHaptics: boolean;
  enableUserNotification: boolean;
  maxRetries: number;
  retryDelay: number;
  enableToasts: boolean;
  enableModals: boolean;
}

export interface UseErrorHandlerReturn {
  handleError: (error: Error, context?: ErrorContext) => AppError;
  handleNetworkError: (error: Error, context?: ErrorContext) => AppError;
  handleValidationError: (field: string, message: string, context?: ErrorContext) => AppError;
  handleAuthError: (error: Error, context?: ErrorContext) => AppError;
  showErrorAlert: (title: string, message: string, actions?: Array<{text: string; onPress?: () => void}>) => void;
  clearErrors: () => void;
}

export interface UseToastReturn {
  showSuccess: (title: string, message?: string, options?: Partial<ToastConfig>) => void;
  showError: (title: string, message?: string, options?: Partial<ToastConfig>) => void;
  showWarning: (title: string, message?: string, options?: Partial<ToastConfig>) => void;
  showInfo: (title: string, message?: string, options?: Partial<ToastConfig>) => void;
  dismissAll: () => void;
}

// Error message templates
export const ERROR_MESSAGES = {
  NETWORK: {
    OFFLINE: 'You appear to be offline. Please check your connection.',
    TIMEOUT: 'Request timed out. Please try again.',
    SERVER_ERROR: 'Server error occurred. Please try again later.',
    CONNECTION_FAILED: 'Connection failed. Tap to retry.',
  },
  AUTHENTICATION: {
    EXPIRED: 'Your session has expired. Please log in again.',
    INVALID_CREDENTIALS: 'Invalid email or password. Please try again.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    ACCOUNT_LOCKED: 'Your account has been temporarily locked.',
  },
  VALIDATION: {
    REQUIRED_FIELD: 'This field is required.',
    INVALID_EMAIL: 'Please enter a valid email address.',
    INVALID_PHONE: 'Please enter a valid phone number.',
    PASSWORD_TOO_SHORT: 'Password must be at least 8 characters long.',
    PASSWORDS_DONT_MATCH: 'Passwords do not match.',
  },
  SERVER: {
    INTERNAL_ERROR: 'Something went wrong on our end. Our team has been notified.',
    SERVICE_UNAVAILABLE: 'Service is temporarily unavailable. Please try again later.',
    MAINTENANCE: 'The app is currently under maintenance. Please try again later.',
  },
  CLIENT: {
    BAD_REQUEST: 'Invalid request. Please check your input and try again.',
    NOT_FOUND: 'The requested resource was not found.',
    FORBIDDEN: 'You do not have permission to access this resource.',
  },
  UNKNOWN: {
    GENERIC: 'An unexpected error occurred. Please try again.',
  }
} as const;

// Error icons mapping
export const ERROR_ICONS = {
  [ErrorType.NETWORK]: 'wifi-off',
  [ErrorType.AUTHENTICATION]: 'lock-closed',
  [ErrorType.VALIDATION]: 'alert-circle',
  [ErrorType.SERVER]: 'server',
  [ErrorType.CLIENT]: 'information-circle',
  [ErrorType.UNKNOWN]: 'alert-triangle',
} as const;

// Severity colors mapping
export const SEVERITY_COLORS = {
  [ErrorSeverity.LOW]: '#6B7280',      // Gray
  [ErrorSeverity.MEDIUM]: '#F59E0B',   // Yellow
  [ErrorSeverity.HIGH]: '#EF4444',     // Red
  [ErrorSeverity.CRITICAL]: '#DC2626', // Dark Red
} as const;

// Toast type colors
export const TOAST_COLORS = {
  success: '#10B981',  // Green
  error: '#EF4444',    // Red
  warning: '#F59E0B',  // Yellow
  info: '#3B82F6',     // Blue
} as const;

// Default configuration
export const DEFAULT_ERROR_CONFIG: ErrorHandlerConfig = {
  enableLogging: true,
  enableHaptics: true,
  enableUserNotification: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableToasts: true,
  enableModals: true,
};

// Utility function to create AppError
export const createAppError = (
  error: Error | string,
  type: ErrorType = ErrorType.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  context?: ErrorContext
): AppError => {
  const message = typeof error === 'string' ? error : error.message;
  const originalError = typeof error === 'string' ? undefined : error;
  
  return {
    id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
    severity,
    message,
    originalError,
    context,
    timestamp: Date.now(),
    retryable: type === ErrorType.NETWORK || type === ErrorType.SERVER,
    retryCount: 0,
    maxRetries: DEFAULT_ERROR_CONFIG.maxRetries,
  };
};

// Utility function to determine error type from HTTP status
export const getErrorTypeFromStatus = (status: number): ErrorType => {
  if (status >= 500) return ErrorType.SERVER;
  if (status === 401 || status === 403) return ErrorType.AUTHENTICATION;
  if (status >= 400) return ErrorType.CLIENT;
  return ErrorType.UNKNOWN;
};

// Utility function to determine error severity
export const getErrorSeverity = (type: ErrorType, status?: number): ErrorSeverity => {
  switch (type) {
    case ErrorType.CRITICAL:
      return ErrorSeverity.CRITICAL;
    case ErrorType.AUTHENTICATION:
      return ErrorSeverity.HIGH;
    case ErrorType.SERVER:
      return status && status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM;
    case ErrorType.NETWORK:
      return ErrorSeverity.MEDIUM;
    case ErrorType.VALIDATION:
      return ErrorSeverity.LOW;
    default:
      return ErrorSeverity.MEDIUM;
  }
};
