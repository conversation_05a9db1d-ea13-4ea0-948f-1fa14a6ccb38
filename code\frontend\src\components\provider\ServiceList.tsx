import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { ServiceCard } from './ServiceCard';
import { Service } from '../../services/api';

interface ServiceListProps {
  services: Service[];
  loading?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  onServicePress?: (service: Service) => void;
  onEditService?: (service: Service) => void;
  onToggleServiceStatus?: (service: Service) => void;
  onDeleteService?: (service: Service) => void;
  onBulkAction?: (serviceIds: string[], action: 'activate' | 'deactivate' | 'delete') => void;
  showActions?: boolean;
  compact?: boolean;
  selectable?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyActionText?: string;
  onEmptyAction?: () => void;
}

export const ServiceList: React.FC<ServiceListProps> = ({
  services,
  loading = false,
  refreshing = false,
  onRefresh,
  onServicePress,
  onEditService,
  onToggleServiceStatus,
  onDeleteService,
  onBulkAction,
  showActions = true,
  compact = false,
  selectable = false,
  emptyTitle = 'No Services Found',
  emptyDescription = 'Create your first service to get started',
  emptyActionText = 'Add Service',
  onEmptyAction,
}) => {
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);

  const toggleSelection = (serviceId: string) => {
    setSelectedServices(prev =>
      prev.includes(serviceId)
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const selectAll = () => {
    if (selectedServices.length === services.length) {
      setSelectedServices([]);
    } else {
      setSelectedServices(services.map(s => s.id));
    }
  };

  const exitSelectionMode = () => {
    setSelectionMode(false);
    setSelectedServices([]);
  };

  const handleBulkAction = (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedServices.length === 0) {
      Alert.alert('Error', 'Please select services first');
      return;
    }

    const actionText = action === 'activate' ? 'activate' : action === 'deactivate' ? 'deactivate' : 'delete';
    
    Alert.alert(
      `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Services`,
      `Are you sure you want to ${actionText} ${selectedServices.length} service(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: actionText.charAt(0).toUpperCase() + actionText.slice(1),
          style: action === 'delete' ? 'destructive' : 'default',
          onPress: () => {
            onBulkAction?.(selectedServices, action);
            exitSelectionMode();
          },
        },
      ]
    );
  };

  const renderServiceItem = ({ item }: { item: Service }) => (
    <ServiceCard
      service={item}
      onPress={() => onServicePress?.(item)}
      onEdit={() => onEditService?.(item)}
      onToggleStatus={() => onToggleServiceStatus?.(item)}
      onDelete={() => onDeleteService?.(item)}
      showActions={showActions && !selectionMode}
      compact={compact}
      selectable={selectionMode}
      selected={selectedServices.includes(item.id)}
      onSelect={() => toggleSelection(item.id)}
    />
  );

  const renderHeader = () => {
    if (!selectable || services.length === 0) return null;

    return (
      <View style={styles.header}>
        {selectionMode ? (
          <View style={styles.selectionHeader}>
            <TouchableOpacity style={styles.selectAllButton} onPress={selectAll}>
              <Text style={styles.selectAllText}>
                {selectedServices.length === services.length ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cancelButton} onPress={exitSelectionMode}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.selectionModeButton}
            onPress={() => setSelectionMode(true)}
          >
            <Icon name="checklist" size={20} color={colors.primary} />
            <Text style={styles.selectionModeText}>Select Services</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderBulkActions = () => {
    if (!selectionMode || selectedServices.length === 0) return null;

    return (
      <View style={styles.bulkActions}>
        <Text style={styles.selectedCount}>{selectedServices.length} selected</Text>
        <View style={styles.bulkActionButtons}>
          <TouchableOpacity
            style={styles.bulkActionButton}
            onPress={() => handleBulkAction('activate')}
          >
            <Icon name="visibility" size={16} color={colors.success} />
            <Text style={[styles.bulkActionText, { color: colors.success }]}>Activate</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.bulkActionButton}
            onPress={() => handleBulkAction('deactivate')}
          >
            <Icon name="visibility-off" size={16} color={colors.warning} />
            <Text style={[styles.bulkActionText, { color: colors.warning }]}>Deactivate</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.bulkActionButton}
            onPress={() => handleBulkAction('delete')}
          >
            <Icon name="delete" size={16} color={colors.error} />
            <Text style={[styles.bulkActionText, { color: colors.error }]}>Delete</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="room-service" size={64} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>{emptyTitle}</Text>
      <Text style={styles.emptyDescription}>{emptyDescription}</Text>
      {onEmptyAction && (
        <TouchableOpacity style={styles.emptyButton} onPress={onEmptyAction}>
          <Text style={styles.emptyButtonText}>{emptyActionText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={services}
        renderItem={renderServiceItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderBulkActions}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          onRefresh ? (
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          ) : undefined
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[
          styles.listContainer,
          services.length === 0 && styles.emptyListContainer,
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: spacing.lg,
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  header: {
    marginBottom: spacing.md,
  },
  selectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
  },
  selectAllButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  selectAllText: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
  },
  cancelButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  cancelButtonText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  selectionModeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  selectionModeText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  bulkActions: {
    marginTop: spacing.md,
    padding: spacing.lg,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  selectedCount: {
    ...typography.body,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  bulkActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  bulkActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: colors.white,
    borderRadius: 6,
    minWidth: 80,
    justifyContent: 'center',
  },
  bulkActionText: {
    ...typography.caption,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    ...typography.h2,
    color: colors.textPrimary,
    marginTop: spacing.lg,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  emptyButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  emptyButtonText: {
    ...typography.body,
    color: colors.white,
    fontWeight: '600',
  },
});
