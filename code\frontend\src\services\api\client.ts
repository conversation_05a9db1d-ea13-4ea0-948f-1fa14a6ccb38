/**
 * API Client Configuration
 * Axios instance with interceptors for authentication and error handling
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

// API Configuration
const API_BASE_URL = __DEV__
  ? 'http://127.0.0.1:8000/api' // Development backend (localhost)
  : 'https://api.vierla.com/api'; // Production backend

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig) => {
    try {
      const token = await AsyncStorage.getItem('access_token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized - token expired
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          });

          const { access, refresh } = response.data;
          await AsyncStorage.setItem('access_token', access);
          await AsyncStorage.setItem('refresh_token', refresh);

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
        // Navigation to login screen would be handled by the app's auth state
        console.error('Token refresh failed:', refreshError);
      }
    }

    // Enhance error object with additional information
    if (error.response) {
      const enhancedError = {
        ...error,
        isNetworkError: false,
        isServerError: error.response.status >= 500,
        isClientError: error.response.status >= 400 && error.response.status < 500,
        isAuthError: error.response.status === 401 || error.response.status === 403,
        isAccountLocked: error.response.status === 423,
        isRateLimited: error.response.status === 429,
        errorCode: error.response.data?.error_code,
        retryAfter: error.response.data?.retry_after,
      };
      return Promise.reject(enhancedError);
    } else if (error.request) {
      // Network error
      const networkError = {
        ...error,
        isNetworkError: true,
        isServerError: false,
        isClientError: false,
        isAuthError: false,
        isAccountLocked: false,
        isRateLimited: false,
        message: 'Network error. Please check your internet connection.',
      };
      return Promise.reject(networkError);
    }

    return Promise.reject(error);
  }
);

export { apiClient, API_BASE_URL };
