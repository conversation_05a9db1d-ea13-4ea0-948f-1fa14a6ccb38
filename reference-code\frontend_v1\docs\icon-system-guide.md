# Vierla Icon System Guide

## Overview

The Vierla Icon System implements a **dual-iconography strategy** that combines neutral UI icons with evocative service category icons, following strict Visual DNA guidelines to ensure consistency and professional appearance.

## Visual DNA Standards

All icons in the Vierla system adhere to these core principles:

- **Stroke Weight**: 2px consistent across all line icons
- **Corner Radius**: 60% continuous curve for rounded elements  
- **Level of Detail**: Balanced abstraction, avoiding hyper-detailed illustrations
- **End Caps**: Rounded line endings for modern, friendly aesthetic

## Icon Categories

### 1. Service Category Icons (Evocative)

These icons represent beauty services with clear, recognizable metaphors:

```typescript
import { ServiceCategoryIcon } from '../components/ui/IconLibrary';

// Usage
<ServiceCategoryIcon 
  category="hair" 
  size={32} 
  color="#7C9A85" 
  active={false} 
/>
```

**Available Categories:**
- `hair` - Scissors (universal hair symbol)
- `nails` - Nail polish bottle
- `lashes` - Eye with prominent lashes
- `braiding` - Stylized three-strand braid
- `skincare` - Droplet for serums/treatments
- `eyebrows` - Tweezers representation
- `massage` - Spa/wellness flower symbol
- `makeup` - Makeup palette
- `fitness` - Fitness/wellness
- `wellness` - Natural/organic leaf symbol

### 2. Navigation Icons (Neutral UI)

Clean, universally understood icons for interface navigation:

```typescript
import { NavigationIcon } from '../components/ui/IconLibrary';

// Usage
<NavigationIcon 
  name="home" 
  active={true} 
  size={24} 
  color="#6B7280" 
/>
```

**Available Navigation Icons:**
- `home` / `homeActive` - Simple house outline/filled
- `search` / `searchActive` - Magnifying glass
- `bookings` / `bookingsActive` - Calendar for appointments
- `messages` / `messagesActive` - Chat bubble
- `profile` / `profileActive` - User silhouette
- `settings` / `settingsActive` - Gear/cog
- `menu` - Hamburger menu
- `back` - Left chevron
- `forward` - Right chevron
- `close` - X for close/cancel

### 3. Action Icons

Common user actions with consistent styling:

```typescript
import { ActionIcon } from '../components/ui/IconLibrary';

// Usage
<ActionIcon 
  action="edit" 
  size={20} 
  color="#6B7280" 
/>
```

**Available Actions:**
- `add` - Plus sign
- `remove` - Minus sign
- `edit` - Pencil
- `delete` - Trash can
- `save` - Save/bookmark
- `share` - Standard share icon
- `filter` - Funnel
- `sort` - Up/down arrows
- `copy` - Copy to clipboard
- `download` - Download arrow
- `upload` - Upload arrow
- `refresh` - Refresh/reload

### 4. Status Icons

Informational icons with semantic colors:

```typescript
import { StatusIcon } from '../components/ui/IconLibrary';

// Usage (colors are automatic)
<StatusIcon status="success" size={24} />
```

**Available Status Icons:**
- `success` - Green checkmark circle
- `error` - Red close circle
- `warning` - Amber alert circle
- `info` - Blue information circle
- `help` - Gray help circle

### 5. Social Icons

Interactive social actions with active states:

```typescript
import { SocialIcon } from '../components/ui/IconLibrary';

// Usage
<SocialIcon 
  type="favorite" 
  active={true} 
  size={20} 
/>
```

**Available Social Icons:**
- `favorite` / `favoriteActive` - Heart (red when active)
- `star` / `starActive` - Star (amber when active)
- `bookmark` / `bookmarkActive` - Bookmark (sage green when active)

### 6. Star Rating Component

Specialized component for displaying ratings:

```typescript
import { StarRating } from '../components/ui/IconLibrary';

// Usage
<StarRating 
  rating={4.5} 
  maxRating={5} 
  size={16} 
  color="#F59E0B" 
  emptyColor="#E5E7EB" 
/>
```

## Basic Icon Component

For direct icon usage:

```typescript
import { Icon } from '../components/ui/IconLibrary';

// Usage
<Icon 
  name="hairServices" 
  size={24} 
  color="#7C9A85" 
/>
```

## Responsive Sizing

All icon components automatically use responsive sizing:

```typescript
// Automatically scales based on screen size
<ServiceCategoryIcon category="hair" size={32} />

// Manual responsive sizing
import { getResponsiveIconSize } from '../utils/responsiveUtils';
const iconSize = getResponsiveIconSize(24);
```

## Accessibility

All icons include semantic descriptions for screen readers:

```typescript
import { getIconDescription } from '../components/ui/IconLibrary';

const description = getIconDescription('hairServices'); // Returns "Hair services"
```

## Color Guidelines

### Primary Colors
- **Sage Green**: `#7C9A85` - Primary brand color for active states
- **Light Sage**: `#A8C4B0` - Lighter variant for backgrounds
- **Dark Sage**: `#2A4B32` - Darker variant for emphasis

### Neutral Colors
- **Primary Text**: `#1F2937` - Main text and icons
- **Secondary Text**: `#6B7280` - Secondary text and inactive icons
- **Light Gray**: `#E5E7EB` - Borders and empty states

### Semantic Colors
- **Success**: `#10B981` - Green for success states
- **Error**: `#EF4444` - Red for errors
- **Warning**: `#F59E0B` - Amber for warnings
- **Info**: `#3B82F6` - Blue for information

## Best Practices

### 1. Consistency
- Always use the appropriate specialized component (ServiceCategoryIcon, NavigationIcon, etc.)
- Maintain consistent sizing within the same context
- Use semantic colors appropriately

### 2. Active States
- Implement active states for navigation and interactive icons
- Use filled variants for active states, outline for inactive

### 3. Accessibility
- Always provide meaningful alt text
- Ensure sufficient color contrast (4.5:1 minimum)
- Use large enough tap targets (44x44px minimum)

### 4. Performance
- Icons are automatically optimized with responsive sizing
- SVG format ensures crisp rendering at all sizes
- Minimal bundle impact with tree-shaking support

## Testing

Test icon implementations with:

```typescript
import { iconExists, getIconName } from '../components/ui/IconLibrary';

// Check if icon exists
const exists = iconExists('hairServices'); // true

// Get icon name with active state
const iconName = getIconName('home', true); // 'homeActive'
```

## Migration Guide

When updating from the old icon system:

1. Replace direct `Icon` usage with specialized components
2. Update icon names to match new semantic naming
3. Add active state handling for interactive icons
4. Update colors to use the new color palette

## Icon Showcase

Use the `IconShowcase` component to view all available icons:

```typescript
import { IconShowcase } from '../components/ui/IconShowcase';

// Display all icons with interactive demo
<IconShowcase onIconPress={(iconName) => console.log(iconName)} />
```

This comprehensive icon system ensures visual consistency, accessibility compliance, and scalable maintenance across the entire Vierla application.
