#!/usr/bin/env python
"""
Test script for messaging flow functionality
Tests the complete messaging API endpoints and WebSocket functionality
"""

import os
import sys
import django
import asyncio
import json
import requests
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from apps.messaging.models import Conversation, Message

User = get_user_model()

class MessagingFlowTester:
    def __init__(self):
        self.api_client = APIClient()
        self.base_url = 'http://************:8000'
        self.customer_user = None
        self.provider_user = None
        self.customer_token = None
        self.provider_token = None
        
    def setup_test_users(self):
        """Setup test users for messaging flow"""
        print("🔧 Setting up test users...")
        
        # Get or create customer
        try:
            self.customer_user = User.objects.get(email='<EMAIL>')
            print(f"   Found customer: {self.customer_user.email}")
        except User.DoesNotExist:
            print("   Customer not found. Please run populate_mock_messaging_data first.")
            return False
            
        # Get or create provider
        try:
            self.provider_user = User.objects.get(email='<EMAIL>')
            print(f"   Found provider: {self.provider_user.email}")
        except User.DoesNotExist:
            print("   Provider not found. Please run populate_mock_messaging_data first.")
            return False
            
        # Generate JWT tokens
        customer_refresh = RefreshToken.for_user(self.customer_user)
        self.customer_token = str(customer_refresh.access_token)
        
        provider_refresh = RefreshToken.for_user(self.provider_user)
        self.provider_token = str(provider_refresh.access_token)
        
        print("   ✅ Test users and tokens ready")
        return True
        
    @override_settings(ALLOWED_HOSTS=['testserver', '************', 'localhost'])
    def test_conversations_api(self):
        """Test conversations API endpoint"""
        print("\n📋 Testing Conversations API...")

        # Test as customer
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.customer_token}')

        try:
            response = self.api_client.get('/api/v1/shared/messaging/')
            print(f"   Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()

                # Handle paginated response
                conversations = data.get('results', data) if isinstance(data, dict) else data

                if isinstance(conversations, list):
                    print(f"   Found {len(conversations)} conversations")
                    for conv in conversations:
                        title = conv.get('title') or f"Conversation with {conv.get('other_participant', {}).get('first_name', 'Unknown')}"
                        print(f"     - Conversation {conv['id']}: {title}")
                    return True
                else:
                    print(f"   ❌ Expected list but got {type(conversations)}")
                    return False
            else:
                print(f"   ❌ Error: {response.content}")
                return False

        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
            
    @override_settings(ALLOWED_HOSTS=['testserver', '************', 'localhost'])
    def test_messages_api(self):
        """Test messages API endpoint"""
        print("\n💬 Testing Messages API...")

        # Get first conversation
        conversations = Conversation.objects.filter(participants=self.customer_user)
        if not conversations.exists():
            print("   ❌ No conversations found")
            return False

        conversation = conversations.first()
        print(f"   Testing conversation {conversation.id}")

        # Test getting messages
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.customer_token}')

        try:
            response = self.api_client.get(f'/api/v1/shared/messaging/{conversation.id}/messages/')
            print(f"   Status Code: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                print(f"   Found {len(data.get('messages', []))} messages")
                for msg in data.get('messages', []):
                    print(f"     - Message {msg['id']}: {msg['content'][:50]}...")
                return True
            else:
                print(f"   ❌ Error: {response.content}")
                return False

        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
            
    @override_settings(ALLOWED_HOSTS=['testserver', '************', 'localhost'])
    def test_send_message_api(self):
        """Test sending a message via API"""
        print("\n📤 Testing Send Message API...")

        # Get first conversation
        conversations = Conversation.objects.filter(participants=self.customer_user)
        if not conversations.exists():
            print("   ❌ No conversations found")
            return False

        conversation = conversations.first()

        # Send a test message
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.customer_token}')

        message_data = {
            'message': f'Test message sent at {datetime.now().strftime("%H:%M:%S")}',
            'message_type': 'text'
        }

        try:
            response = self.api_client.post(
                f'/api/v1/shared/messaging/{conversation.id}/send_message/',
                data=message_data,
                format='json'
            )
            print(f"   Status Code: {response.status_code}")

            if response.status_code in [200, 201]:
                data = response.json()
                print(f"   ✅ Message sent successfully: {data.get('id')}")
                return True
            else:
                print(f"   ❌ Error: {response.content}")
                return False

        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return False
            
    def test_websocket_connection(self):
        """Test WebSocket connection (basic connectivity)"""
        print("\n🔌 Testing WebSocket Connection...")
        
        # Get first conversation
        conversations = Conversation.objects.filter(participants=self.customer_user)
        if not conversations.exists():
            print("   ❌ No conversations found")
            return False
            
        conversation = conversations.first()
        ws_url = f'ws://************:8000/ws/messaging/{conversation.id}/'
        
        print(f"   WebSocket URL: {ws_url}")
        print("   ℹ️  WebSocket testing requires manual verification")
        print("   ℹ️  Use the frontend app to test real-time messaging")
        
        return True
        
    def test_database_integrity(self):
        """Test database integrity and relationships"""
        print("\n🗄️  Testing Database Integrity...")
        
        # Check conversations
        conversations = Conversation.objects.all()
        print(f"   Total conversations: {conversations.count()}")
        
        # Check messages
        messages = Message.objects.all()
        print(f"   Total messages: {messages.count()}")
        
        # Check relationships
        for conv in conversations:
            participants = conv.participants.all()
            message_count = conv.messages.count()
            print(f"   Conversation {conv.id}: {participants.count()} participants, {message_count} messages")
            
        return True
        
    def run_all_tests(self):
        """Run all messaging flow tests"""
        print("🚀 Starting Messaging Flow Tests")
        print("=" * 50)
        
        if not self.setup_test_users():
            print("❌ Failed to setup test users")
            return False
            
        tests = [
            self.test_database_integrity,
            self.test_conversations_api,
            self.test_messages_api,
            self.test_send_message_api,
            self.test_websocket_connection,
        ]
        
        results = []
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                print(f"   ❌ Test failed with exception: {e}")
                results.append(False)
                
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)
        
        passed = sum(results)
        total = len(results)
        
        print(f"✅ Passed: {passed}/{total}")
        if passed == total:
            print("🎉 All tests passed!")
        else:
            print("⚠️  Some tests failed. Check output above.")
            
        return passed == total

if __name__ == '__main__':
    tester = MessagingFlowTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
