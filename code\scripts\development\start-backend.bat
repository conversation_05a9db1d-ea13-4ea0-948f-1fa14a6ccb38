@echo off
REM ============================================================================
REM Vierla Backend Server Startup Script (Windows)
REM ============================================================================
REM This script starts the Django backend server for the Vierla application
REM with proper environment configuration and error handling.
REM
REM Usage: .\scripts\development\start-backend.bat [options]
REM Options:
REM   --host <host>     Server host (default: localhost)
REM   --port <port>     Server port (default: 8000)
REM   --debug           Enable debug mode
REM   --help            Show this help message
REM ============================================================================

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_NAME=Vierla Backend Server
set SCRIPT_VERSION=1.0.0
set LOG_FILE=logs\backend-server.log
set BACKEND_DIR=code\backend

REM Default configuration
set DEFAULT_HOST=localhost
set DEFAULT_PORT=8000
set SERVER_HOST=%DEFAULT_HOST%
set SERVER_PORT=%DEFAULT_PORT%
set DEBUG_MODE=false

REM Colors for output
set COLOR_GREEN=[92m
set COLOR_YELLOW=[93m
set COLOR_RED=[91m
set COLOR_BLUE=[94m
set COLOR_CYAN=[96m
set COLOR_RESET=[0m

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :start_server
if "%~1"=="--host" (
    set SERVER_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set SERVER_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--debug" (
    set DEBUG_MODE=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo %COLOR_RED%Unknown option: %~1%COLOR_RESET%
goto :show_help

:show_help
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo %COLOR_BLUE%%SCRIPT_NAME% v%SCRIPT_VERSION%%COLOR_RESET%
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo.
echo Usage: %COLOR_CYAN%.\scripts\development\start-backend.bat [options]%COLOR_RESET%
echo.
echo Options:
echo   %COLOR_YELLOW%--host ^<host^>%COLOR_RESET%     Server host (default: %DEFAULT_HOST%)
echo   %COLOR_YELLOW%--port ^<port^>%COLOR_RESET%     Server port (default: %DEFAULT_PORT%)
echo   %COLOR_YELLOW%--debug%COLOR_RESET%           Enable debug mode
echo   %COLOR_YELLOW%--help%COLOR_RESET%            Show this help message
echo.
echo Examples:
echo   %COLOR_CYAN%.\scripts\development\start-backend.bat%COLOR_RESET%
echo   %COLOR_CYAN%.\scripts\development\start-backend.bat --host 0.0.0.0 --port 8080%COLOR_RESET%
echo   %COLOR_CYAN%.\scripts\development\start-backend.bat --debug%COLOR_RESET%
echo.
exit /b 0

:start_server
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo %COLOR_BLUE%%SCRIPT_NAME% v%SCRIPT_VERSION%%COLOR_RESET%
echo %COLOR_BLUE%============================================================================%COLOR_RESET%
echo.

REM Create logs directory if it doesn't exist
if not exist logs mkdir logs

REM Log function
call :log "INFO" "Starting backend server startup sequence"

REM Check if backend directory exists
if not exist "%BACKEND_DIR%" (
    call :log "ERROR" "Backend directory not found"
    echo %COLOR_RED%Error: Backend directory not found. Please run setup first.%COLOR_RESET%
    echo Run: %COLOR_CYAN%.\scripts\development\setup-dev.bat%COLOR_RESET%
    exit /b 1
)

REM Navigate to backend directory
cd %BACKEND_DIR%

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    call :log "ERROR" "Virtual environment not found"
    echo %COLOR_RED%Error: Virtual environment not found. Please run setup first.%COLOR_RESET%
    echo Run: %COLOR_CYAN%.\scripts\development\setup-dev.bat%COLOR_RESET%
    exit /b 1
)

REM Load environment variables from .env file
if exist ".env" (
    call :log "INFO" "Loading environment configuration from .env"
    echo %COLOR_YELLOW%Loading environment configuration...%COLOR_RESET%
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if not "%%a"=="" if not "%%a:~0,1%"=="#" (
            set "%%a=%%b"
        )
    )
) else (
    call :log "WARNING" "No .env file found, using defaults"
    echo %COLOR_YELLOW%Warning: No .env file found, using default configuration%COLOR_RESET%
)

REM Override with command line arguments
if defined VIERLA_HOST if "%SERVER_HOST%"=="%DEFAULT_HOST%" set SERVER_HOST=%VIERLA_HOST%
if defined VIERLA_PORT if "%SERVER_PORT%"=="%DEFAULT_PORT%" set SERVER_PORT=%VIERLA_PORT%

REM Activate virtual environment
call :log "INFO" "Activating virtual environment"
echo %COLOR_YELLOW%Activating virtual environment...%COLOR_RESET%
call venv\Scripts\activate.bat

REM Check if Django is installed
python -c "import django" >nul 2>&1
if errorlevel 1 (
    call :log "ERROR" "Django not found in virtual environment"
    echo %COLOR_RED%Error: Django not found. Please run setup first.%COLOR_RESET%
    echo Run: %COLOR_CYAN%.\scripts\development\setup-dev.bat%COLOR_RESET%
    exit /b 1
)

REM Run database migrations (in case there are new ones)
call :log "INFO" "Checking for database migrations"
echo %COLOR_YELLOW%Checking for database migrations...%COLOR_RESET%
python manage.py migrate --check >nul 2>&1
if errorlevel 1 (
    echo %COLOR_YELLOW%Running pending migrations...%COLOR_RESET%
    python manage.py migrate
    if errorlevel 1 (
        call :log "ERROR" "Failed to run database migrations"
        echo %COLOR_RED%Error: Failed to run database migrations%COLOR_RESET%
        exit /b 1
    )
)

REM Collect static files (if needed)
if "%DEBUG_MODE%"=="false" (
    call :log "INFO" "Collecting static files"
    echo %COLOR_YELLOW%Collecting static files...%COLOR_RESET%
    python manage.py collectstatic --noinput
)

REM Check if port is available
netstat -an | findstr ":%SERVER_PORT% " >nul 2>&1
if not errorlevel 1 (
    call :log "WARNING" "Port %SERVER_PORT% is already in use"
    echo %COLOR_YELLOW%Warning: Port %SERVER_PORT% is already in use%COLOR_RESET%
    echo %COLOR_YELLOW%The server may fail to start or another instance may be running%COLOR_RESET%
)

REM Display server information
call :log "INFO" "Starting Django development server"
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo %COLOR_GREEN%Starting Vierla Backend Server%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.
echo %COLOR_CYAN%Server Configuration:%COLOR_RESET%
echo   Host: %COLOR_YELLOW%http://%SERVER_HOST%:%SERVER_PORT%%COLOR_RESET%
echo   Debug Mode: %COLOR_YELLOW%!DEBUG!%COLOR_RESET%
echo   Environment: %COLOR_YELLOW%Development%COLOR_RESET%
echo   Log File: %COLOR_YELLOW%..\..\%LOG_FILE%%COLOR_RESET%
echo.
echo %COLOR_CYAN%Available Endpoints:%COLOR_RESET%
echo   Application: %COLOR_BLUE%http://%SERVER_HOST%:%SERVER_PORT%/%COLOR_RESET%
echo   Admin Panel: %COLOR_BLUE%http://%SERVER_HOST%:%SERVER_PORT%/admin/%COLOR_RESET%
echo   API Root: %COLOR_BLUE%http://%SERVER_HOST%:%SERVER_PORT%/api/%COLOR_RESET%
echo   API Docs: %COLOR_BLUE%http://%SERVER_HOST%:%SERVER_PORT%/api/docs/%COLOR_RESET%
echo.
echo %COLOR_YELLOW%Press Ctrl+C to stop the server%COLOR_RESET%
echo %COLOR_GREEN%============================================================================%COLOR_RESET%
echo.

REM Start the Django development server
call :log "INFO" "Django server starting on %SERVER_HOST%:%SERVER_PORT%"
python manage.py runserver %SERVER_HOST%:%SERVER_PORT%

REM Log server shutdown
call :log "INFO" "Django server stopped"
echo.
echo %COLOR_YELLOW%Server stopped. Thank you for using Vierla!%COLOR_RESET%

REM Navigate back to project root
cd ..\..

exit /b 0

REM ============================================================================
REM Helper Functions
REM ============================================================================

:log
REM Log function: call :log "LEVEL" "MESSAGE"
set LOG_LEVEL=%~1
set LOG_MESSAGE=%~2
set LOG_TIMESTAMP=%date% %time%
echo [%LOG_TIMESTAMP%] [%LOG_LEVEL%] %LOG_MESSAGE% >> ..\..\%LOG_FILE%
goto :eof
