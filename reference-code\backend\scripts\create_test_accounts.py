#!/usr/bin/env python3
"""
Vierla Test Accounts Creation Script
Creates 5 service provider accounts per service category with complete setup
"""

import os
import sys
import django
from decimal import Decimal
from django.contrib.auth.hashers import make_password

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User
from apps.catalog.models import ServiceCategory, ServiceProvider, Service

# Service Categories Configuration
SERVICE_CATEGORIES = [
    {
        'name': 'Barber',
        'slug': 'barber',
        'description': 'Professional mens grooming, haircuts, and beard services',
        'icon': '✂️',
        'color': '#2A4B32',  # Dark Sage Green
    },
    {
        'name': 'Salon',
        'slug': 'salon',
        'description': 'Professional hair cutting, styling, coloring, and treatments',
        'icon': '💇‍♀️',
        'color': '#2A4B32',  # Dark <PERSON>
    },
    {
        'name': 'Nail Services', 
        'slug': 'nail-services',
        'description': 'Manicures, pedicures, nail art, and nail care',
        'icon': '💅',
        'color': '#2A4B32',
    },
    {
        'name': 'Lash Services',
        'slug': 'lash-services', 
        'description': 'Eyelash extensions, lifts, tinting, and brow services',
        'icon': '👁️',
        'color': '#2A4B32',
    },
    {
        'name': 'Braiding',
        'slug': 'braiding',
        'description': 'Professional braiding and protective styling',
        'icon': '🤎',
        'color': '#2A4B32',
    },
    {
        'name': 'Massage',
        'slug': 'massage',
        'description': 'Therapeutic and relaxation massage services',
        'icon': '💆‍♀️',
        'color': '#2A4B32',
    },
    {
        'name': 'Skincare',
        'slug': 'skincare',
        'description': 'Facial treatments, skincare consultations, and beauty treatments',
        'icon': '🧴',
        'color': '#2A4B32',
    }
]

# Ottawa and Toronto locations for realistic addresses
LOCATIONS = {
    'ottawa': [
        {'address': '123 Bank St, Ottawa, ON K1P 1A1', 'lat': 45.4215, 'lng': -75.6972, 'city': 'Ottawa'},
        {'address': '456 Rideau St, Ottawa, ON K1N 5Y4', 'lat': 45.4274, 'lng': -75.6920, 'city': 'Ottawa'},
        {'address': '789 Somerset St W, Ottawa, ON K1R 6P8', 'lat': 45.4112, 'lng': -75.7081, 'city': 'Ottawa'},
        {'address': '321 Sparks St, Ottawa, ON K1R 7S8', 'lat': 45.4206, 'lng': -75.7033, 'city': 'Ottawa'},
        {'address': '654 Elgin St, Ottawa, ON K1G 0E6', 'lat': 45.4089, 'lng': -75.6903, 'city': 'Ottawa'},
    ],
    'toronto': [
        {'address': '100 Queen St W, Toronto, ON M5H 2N2', 'lat': 43.6532, 'lng': -79.3832, 'city': 'Toronto'},
        {'address': '200 King St W, Toronto, ON M5H 3T4', 'lat': 43.6481, 'lng': -79.3889, 'city': 'Toronto'},
        {'address': '300 Bloor St W, Toronto, ON M5S 1W3', 'lat': 43.6677, 'lng': -79.3948, 'city': 'Toronto'},
        {'address': '400 Yonge St, Toronto, ON M5B 2H4', 'lat': 43.6591, 'lng': -79.3802, 'city': 'Toronto'},
        {'address': '500 College St, Toronto, ON M6G 1A5', 'lat': 43.6577, 'lng': -79.4103, 'city': 'Toronto'},
    ]
}

def create_service_categories():
    """Create service categories if they don't exist"""
    print("🎨 Creating Service Categories...")
    
    created_categories = {}
    for i, cat_data in enumerate(SERVICE_CATEGORIES):
        category, created = ServiceCategory.objects.get_or_create(
            slug=cat_data['slug'],
            defaults={
                'name': cat_data['name'],
                'description': cat_data['description'],
                'icon': cat_data['icon'],
                'color': cat_data['color'],
                'is_active': True,
                'is_popular': True,
                'sort_order': i + 1
            }
        )
        created_categories[cat_data['slug']] = category
        status = "✅ Created" if created else "📋 Exists"
        print(f"  {status}: {category.name}")
    
    return created_categories

def create_test_providers(categories):
    """Create 5 service provider accounts per category"""
    print("\n👥 Creating Service Provider Accounts...")
    
    all_locations = LOCATIONS['ottawa'] + LOCATIONS['toronto']
    created_accounts = []
    
    for category_slug, category in categories.items():
        print(f"\n📂 Creating providers for {category.name}:")
        
        for i in range(5):
            # Use different locations for variety
            location = all_locations[i % len(all_locations)]
            
            # Create unique email and business name
            email = f"{category_slug}_provider_{i+1}@vierla.test"
            business_name = f"{category.name} Studio {i+1}"
            
            # Create user account
            user, user_created = User.objects.get_or_create(
                email=email,
                defaults={
                    'username': email,  # Use email as username to avoid conflicts
                    'first_name': f'Provider{i+1}',
                    'last_name': f'{category.name.split()[0]}',
                    'role': User.UserRole.SERVICE_PROVIDER,
                    'password': make_password('TestPass123!'),
                    'is_active': True,
                    'is_verified': True
                }
            )
            
            # Create service provider profile
            provider, provider_created = ServiceProvider.objects.get_or_create(
                user=user,
                defaults={
                    'business_name': business_name,
                    'business_description': f'Professional {category.name.lower()} services in {location["city"]}',
                    'business_phone': f'+1613555{i:04d}',
                    'business_email': email,
                    'address': location['address'],
                    'city': location['city'],
                    'state': 'ON',
                    'zip_code': 'K1A 0A6' if location['city'] == 'Ottawa' else 'M5H 2N2',
                    'country': 'Canada',
                    'latitude': Decimal(str(location['lat'])),
                    'longitude': Decimal(str(location['lng'])),
                    'years_of_experience': (i + 1) * 2,  # 2, 4, 6, 8, 10 years
                    'is_active': True,
                    'is_verified': True,
                    'is_featured': i == 0,  # Make first provider featured
                    'rating': Decimal('4.5') + Decimal(str(i * 0.1)),  # 4.5 to 4.9
                    'review_count': (i + 1) * 10  # 10, 20, 30, 40, 50 reviews
                }
            )
            
            # Add category to provider
            provider.categories.add(category)
            
            # Store account info
            account_info = {
                'email': email,
                'password': 'TestPass123!',
                'business_name': business_name,
                'category': category.name,
                'city': location['city'],
                'provider_id': str(provider.id),
                'user_id': str(user.id)
            }
            created_accounts.append(account_info)
            
            status = "✅ Created" if (user_created and provider_created) else "📋 Exists"
            print(f"  {status}: {business_name} ({email})")
    
    return created_accounts

def create_sample_services(categories):
    """Create sample services for each provider"""
    print("\n🛍️ Creating Sample Services...")
    
    # Service templates by category
    service_templates = {
        'barber': [
            {'name': 'Classic Haircut', 'price': 35.00, 'duration': 45},
            {'name': 'Beard Trim', 'price': 25.00, 'duration': 30},
            {'name': 'Hot Towel Shave', 'price': 45.00, 'duration': 60},
        ],
        'salon': [
            {'name': 'Haircut & Style', 'price': 65.00, 'duration': 60},
            {'name': 'Hair Color', 'price': 120.00, 'duration': 120},
            {'name': 'Highlights', 'price': 150.00, 'duration': 180},
        ],
        'nail-services': [
            {'name': 'Manicure', 'price': 35.00, 'duration': 45},
            {'name': 'Pedicure', 'price': 45.00, 'duration': 60},
            {'name': 'Gel Polish', 'price': 25.00, 'duration': 30},
        ],
        'lash-services': [
            {'name': 'Lash Extensions', 'price': 80.00, 'duration': 90},
            {'name': 'Lash Lift', 'price': 60.00, 'duration': 60},
            {'name': 'Brow Shaping', 'price': 30.00, 'duration': 30},
        ],
        'braiding': [
            {'name': 'Box Braids', 'price': 200.00, 'duration': 240},
            {'name': 'Cornrows', 'price': 80.00, 'duration': 120},
            {'name': 'Twist Out', 'price': 60.00, 'duration': 90},
        ],
        'massage': [
            {'name': 'Swedish Massage', 'price': 90.00, 'duration': 60},
            {'name': 'Deep Tissue', 'price': 110.00, 'duration': 60},
            {'name': 'Hot Stone', 'price': 130.00, 'duration': 90},
        ],
        'skincare': [
            {'name': 'Facial Treatment', 'price': 85.00, 'duration': 75},
            {'name': 'Chemical Peel', 'price': 120.00, 'duration': 60},
            {'name': 'Microdermabrasion', 'price': 100.00, 'duration': 45},
        ]
    }
    
    for category_slug, category in categories.items():
        providers = ServiceProvider.objects.filter(categories=category)
        templates = service_templates.get(category_slug, [])
        
        for provider in providers:
            for template in templates:
                service, created = Service.objects.get_or_create(
                    provider=provider,
                    name=template['name'],
                    defaults={
                        'category': category,
                        'description': f'Professional {template["name"].lower()} service',
                        'short_description': template['name'],
                        'mobile_description': template['name'],
                        'base_price': Decimal(str(template['price'])),
                        'duration': template['duration'],
                        'is_active': True,
                        'is_available': True,
                        'is_popular': template['name'] in ['Classic Haircut', 'Haircut & Style', 'Manicure', 'Lash Extensions']
                    }
                )
                
                if created:
                    print(f"  ✅ Created: {template['name']} for {provider.business_name}")

def create_customer_accounts():
    """Create customer test accounts"""
    print("\n👤 Creating Customer Test Accounts...")

    customer_accounts = [
        {
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Customer',
            'city': 'Ottawa'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Jane',
            'last_name': 'Smith',
            'city': 'Toronto'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Mike',
            'last_name': 'Johnson',
            'city': 'Ottawa'
        },
        {
            'email': '<EMAIL>',
            'first_name': 'Sarah',
            'last_name': 'Wilson',
            'city': 'Toronto'
        }
    ]

    created_customers = []

    for customer_data in customer_accounts:
        user, user_created = User.objects.get_or_create(
            email=customer_data['email'],
            defaults={
                'username': customer_data['email'],
                'first_name': customer_data['first_name'],
                'last_name': customer_data['last_name'],
                'role': User.UserRole.CUSTOMER,
                'password': make_password('TestPass123!'),
                'is_active': True,
                'is_verified': True
            }
        )

        created_customers.append({
            'email': customer_data['email'],
            'password': 'TestPass123!',
            'first_name': customer_data['first_name'],
            'last_name': customer_data['last_name'],
            'city': customer_data['city'],
            'user_id': str(user.id)
        })

        status = "✅ Created" if user_created else "📋 Exists"
        print(f"  {status}: {customer_data['first_name']} {customer_data['last_name']} ({customer_data['email']})")

    return created_customers

def main():
    """Main execution function"""
    print("🚀 Vierla Test Accounts Creation")
    print("=" * 50)

    try:
        # Create categories
        categories = create_service_categories()

        # Create customer accounts
        customer_accounts = create_customer_accounts()

        # Create providers
        provider_accounts = create_test_providers(categories)

        # Create services
        create_sample_services(categories)

        total_accounts = len(customer_accounts) + len(provider_accounts)
        print(f"\n✅ Successfully created {total_accounts} test accounts!")
        print(f"📊 Categories: {len(categories)}")
        print(f"👤 Customers: {len(customer_accounts)}")
        print(f"👥 Providers: {len(provider_accounts)}")
        print(f"🛍️ Services: {Service.objects.count()}")

        return customer_accounts + provider_accounts

    except Exception as e:
        print(f"❌ Error creating test accounts: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == '__main__':
    accounts = main()
