#!/usr/bin/env python
"""
Quick authentication <NAME_EMAIL>
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User
import requests

def check_user_in_database():
    """Check if user exists in database and test passwords"""
    print("=" * 60)
    print("DATABASE CHECK")
    print("=" * 60)
    
    try:
        user = User.objects.filter(email='<EMAIL>').first()
        if user:
            print(f"✅ User exists: {user.email}")
            print(f"   - Role: {user.role}")
            print(f"   - Is active: {user.is_active}")
            print(f"   - Is verified: {user.is_verified}")
            print(f"   - Account status: {user.account_status}")
            print("\n🔐 Password Tests:")
            
            passwords_to_test = [
                'testpass123',      # Backend default
                'Testpass123',      # Documentation version
                'Testpass123!',     # User's version
                'password123'       # Old version
            ]
            
            for pwd in passwords_to_test:
                result = user.check_password(pwd)
                status = "✅ VALID" if result else "❌ INVALID"
                print(f"   - '{pwd}': {status}")
                
            return user
        else:
            print("❌ User does not exist in database")
            return None
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        return None

def test_api_login():
    """Test API login endpoint"""
    print("\n" + "=" * 60)
    print("API LOGIN TEST")
    print("=" * 60)
    
    api_url = "http://192.168.2.65:8000/api/auth/login/"
    
    passwords_to_test = [
        'testpass123',      # Backend default
        'Testpass123',      # Documentation version
        'Testpass123!',     # User's version
        'password123'       # Old version
    ]
    
    for password in passwords_to_test:
        print(f"\n🔍 Testing password: '{password}'")
        
        try:
            response = requests.post(api_url, json={
                'email': '<EMAIL>',
                'password': password
            }, headers={
                'Content-Type': 'application/json'
            }, timeout=10)
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ LOGIN SUCCESS!")
                data = response.json()
                print(f"   - Access token: {data.get('access', 'N/A')[:50]}...")
                print(f"   - User role: {data.get('user', {}).get('role', 'N/A')}")
                return True
            else:
                print(f"   ❌ LOGIN FAILED")
                try:
                    error_data = response.json()
                    print(f"   - Error: {error_data}")
                except:
                    print(f"   - Response: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ Request error: {e}")
    
    return False

def main():
    print("VIERLA CUSTOMER AUTHENTICATION CHECK")
    print("Testing <EMAIL> login functionality")
    
    # Check database
    user = check_user_in_database()
    
    # Test API
    api_success = test_api_login()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Database user exists: {'✅ YES' if user else '❌ NO'}")
    print(f"API login works: {'✅ YES' if api_success else '❌ NO'}")
    
    if not user:
        print("\n🔧 RECOMMENDATION: Run 'python manage.py create_test_accounts --force'")
    elif not api_success:
        print("\n🔧 RECOMMENDATION: Check API endpoint and password requirements")
    else:
        print("\n✅ Authentication system is working correctly!")

if __name__ == '__main__':
    main()
