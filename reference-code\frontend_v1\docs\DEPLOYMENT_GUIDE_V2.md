# Vierla Frontend v2 - Comprehensive Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the enhanced Vierla Frontend v2 application to various environments including staging, production, and development.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Build Configuration](#build-configuration)
4. [Deployment Environments](#deployment-environments)
5. [CI/CD Pipeline](#cicd-pipeline)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Troubleshooting](#troubleshooting)
8. [Rollback Procedures](#rollback-procedures)

## Prerequisites

### Required Tools
- Node.js 18.x or higher
- npm 9.x or higher
- Expo CLI 6.x
- Docker 20.x or higher
- kubectl 1.28.x
- AWS CLI 2.x (for AWS deployments)

### Required Accounts
- GitHub account with repository access
- Docker Hub or container registry access
- Cloud provider account (AWS, GCP, or Azure)
- Expo account for OTA updates
- Sentry account for error monitoring

### Environment Variables
```bash
# Required for all environments
EXPO_PUBLIC_API_URL=https://api.vierla.com
EXPO_PUBLIC_WS_URL=wss://api.vierla.com
EXPO_PUBLIC_ENVIRONMENT=production
EXPO_PUBLIC_APP_VERSION=2.0.0

# Analytics and Monitoring
EXPO_PUBLIC_SENTRY_DSN=https://<EMAIL>/project
EXPO_PUBLIC_ANALYTICS_ID=GA-XXXXXXXXX

# Payment Processing
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_xxxxxxxxx

# Maps and Location
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Firebase Configuration
EXPO_PUBLIC_FIREBASE_CONFIG={"apiKey":"...","authDomain":"..."}
```

## Environment Setup

### Development Environment
```bash
# Clone repository
git clone https://github.com/vierla/frontend-v2.git
cd frontend-v2

# Install dependencies
npm install

# Setup environment variables
cp .env.example .env.development
# Edit .env.development with development values

# Start development server
npm run dev
```

### Staging Environment
```bash
# Build for staging
npm run build:staging

# Deploy to staging
npm run deploy:staging
```

### Production Environment
```bash
# Build for production
npm run build:production

# Deploy to production
npm run deploy:production
```

## Build Configuration

### Web Build
```bash
# Standard web build
npm run build:web

# Optimized production web build
npm run build:web:production
```

### Mobile Builds

#### Android
```bash
# Development build
eas build --platform android --profile development

# Production build
eas build --platform android --profile production
```

#### iOS
```bash
# Development build
eas build --platform ios --profile development

# Production build
eas build --platform ios --profile production
```

### Docker Build
```bash
# Build Docker image
docker build -f Dockerfile.production -t vierla/frontend:v2.0.0 .

# Push to registry
docker push vierla/frontend:v2.0.0
```

## Deployment Environments

### Kubernetes Deployment

#### Staging Deployment
```bash
# Apply staging configuration
kubectl apply -f deployment/staging.yml

# Verify deployment
kubectl get pods -n vierla-staging
kubectl logs -f deployment/vierla-frontend -n vierla-staging
```

#### Production Deployment
```bash
# Apply production configuration
kubectl apply -f deployment/production.yml

# Verify deployment
kubectl get pods -n vierla-production
kubectl logs -f deployment/vierla-frontend -n vierla-production

# Check service status
kubectl get svc -n vierla-production
```

### AWS ECS Deployment
```bash
# Update ECS service
aws ecs update-service \
  --cluster vierla-production \
  --service vierla-frontend \
  --task-definition vierla-frontend:latest \
  --desired-count 3

# Monitor deployment
aws ecs describe-services \
  --cluster vierla-production \
  --services vierla-frontend
```

### Vercel Deployment
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod

# Set environment variables
vercel env add EXPO_PUBLIC_API_URL production
vercel env add EXPO_PUBLIC_ENVIRONMENT production
```

## CI/CD Pipeline

### GitHub Actions Workflow

The deployment pipeline includes:

1. **Code Quality Checks**
   - ESLint and TypeScript validation
   - Prettier formatting check
   - Security audit
   - License compliance check

2. **Testing Suite**
   - Unit tests with Jest
   - Integration tests
   - E2E tests with Detox
   - Performance tests
   - Accessibility tests

3. **Build Process**
   - Multi-platform builds (web, iOS, Android)
   - Docker image creation
   - Asset optimization
   - Bundle analysis

4. **Security Scanning**
   - Dependency vulnerability scan
   - Container image scanning
   - SAST (Static Application Security Testing)

5. **Deployment**
   - Staging deployment
   - Production deployment (on tag)
   - OTA updates for mobile apps

### Manual Deployment Commands

#### Deploy to Staging
```bash
# Trigger staging deployment
git push origin main

# Or manually deploy
npm run deploy:staging
```

#### Deploy to Production
```bash
# Create and push release tag
git tag v2.0.0
git push origin v2.0.0

# Or manually deploy
npm run deploy:production
```

## Monitoring and Logging

### Application Monitoring
- **Sentry**: Error tracking and performance monitoring
- **Google Analytics**: User behavior analytics
- **Custom Metrics**: Business-specific KPIs

### Infrastructure Monitoring
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **AlertManager**: Alert notifications

### Log Aggregation
- **ELK Stack**: Elasticsearch, Logstash, Kibana
- **CloudWatch**: AWS native logging
- **Datadog**: Comprehensive monitoring platform

### Health Checks
```bash
# Application health check
curl https://app.vierla.com/health

# Kubernetes health check
kubectl get pods -n vierla-production
kubectl describe pod <pod-name> -n vierla-production
```

## Performance Optimization

### Bundle Optimization
```bash
# Analyze bundle size
npm run analyze

# Optimize images
npm run optimize:images

# Generate service worker
npm run build:sw
```

### CDN Configuration
- Static assets served via CloudFront
- Image optimization with WebP format
- Gzip compression enabled
- Cache headers configured

### Database Optimization
- Connection pooling
- Query optimization
- Caching strategies
- Read replicas for scaling

## Security Considerations

### SSL/TLS Configuration
- TLS 1.3 minimum
- HSTS headers enabled
- Certificate auto-renewal

### API Security
- JWT token validation
- Rate limiting
- CORS configuration
- Input sanitization

### Container Security
- Non-root user execution
- Minimal base images
- Regular security updates
- Vulnerability scanning

## Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build

# Check for dependency conflicts
npm ls
npm audit fix
```

#### Deployment Issues
```bash
# Check deployment status
kubectl rollout status deployment/vierla-frontend -n vierla-production

# View logs
kubectl logs -f deployment/vierla-frontend -n vierla-production

# Describe pod for detailed info
kubectl describe pod <pod-name> -n vierla-production
```

#### Performance Issues
```bash
# Check resource usage
kubectl top pods -n vierla-production

# Scale deployment
kubectl scale deployment vierla-frontend --replicas=5 -n vierla-production
```

### Debug Commands
```bash
# Connect to running container
kubectl exec -it <pod-name> -n vierla-production -- /bin/sh

# Port forward for local debugging
kubectl port-forward svc/vierla-frontend-service 3000:80 -n vierla-production

# Check service endpoints
kubectl get endpoints -n vierla-production
```

## Rollback Procedures

### Kubernetes Rollback
```bash
# View rollout history
kubectl rollout history deployment/vierla-frontend -n vierla-production

# Rollback to previous version
kubectl rollout undo deployment/vierla-frontend -n vierla-production

# Rollback to specific revision
kubectl rollout undo deployment/vierla-frontend --to-revision=2 -n vierla-production
```

### Docker Image Rollback
```bash
# Update deployment with previous image
kubectl set image deployment/vierla-frontend \
  vierla-frontend=vierla/frontend:v1.9.0 \
  -n vierla-production
```

### Database Rollback
```bash
# Restore from backup
pg_restore -h localhost -U postgres -d vierla_production backup_file.sql

# Or use automated backup restoration
kubectl apply -f database/restore-job.yml
```

## Maintenance

### Regular Tasks
- **Weekly**: Security updates and dependency updates
- **Monthly**: Performance review and optimization
- **Quarterly**: Disaster recovery testing

### Backup Procedures
- **Database**: Daily automated backups with 30-day retention
- **Static Assets**: Replicated across multiple regions
- **Configuration**: Version controlled in Git

### Update Procedures
```bash
# Update dependencies
npm update
npm audit fix

# Update Docker base image
docker pull node:18-alpine
docker build --no-cache -t vierla/frontend:latest .

# Update Kubernetes manifests
kubectl apply -f deployment/production.yml
```

## Support and Documentation

### Additional Resources
- [API Documentation](./API_DOCUMENTATION.md)
- [Architecture Guide](./ARCHITECTURE_GUIDE.md)
- [Development Guide](./DEVELOPMENT_GUIDE.md)
- [Testing Guide](./TESTING_GUIDE.md)

### Support Contacts
- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Emergency Contact**: +1-555-VIERLA-1

### Monitoring Dashboards
- **Application**: https://grafana.vierla.com/d/app-dashboard
- **Infrastructure**: https://grafana.vierla.com/d/infra-dashboard
- **Business Metrics**: https://analytics.vierla.com

---

**Note**: This deployment guide is for Vierla Frontend v2. For previous versions, refer to the legacy documentation in the `docs/legacy/` directory.
