#!/usr/bin/env python3
"""
Vierla Enhanced Performance Monitor
Monitors backend health, performance metrics, and catches all errors/warnings
"""

import time
import requests
import sys
import json
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Optional

class VierlaMonitor:
    def __init__(self):
        self.backend_url = 'http://192.168.2.65:8000'
        self.error_patterns = [
            r'ERROR',
            r'CRITICAL',
            r'FATAL',
            r'WARNING',
            r'Exception',
            r'Traceback',
            r'ENOENT',
            r'InternalBytecode',
            r'session.*expir',
            r'404.*Not Found',
            r'500.*Internal Server Error',
            r'Connection refused',
            r'Timeout',
        ]
        self.stats = {
            'total_checks': 0,
            'healthy_checks': 0,
            'error_checks': 0,
            'warnings_detected': 0,
            'avg_response_time': 0,
            'max_response_time': 0,
            'min_response_time': float('inf')
        }

    def check_backend_health(self) -> Dict:
        """Enhanced backend health check with detailed metrics"""
        try:
            start_time = time.time()
            response = requests.get(f'{self.backend_url}/api/health/', timeout=5)
            response_time = (time.time() - start_time) * 1000

            # Update response time stats
            self.stats['total_checks'] += 1
            if response_time < self.stats['min_response_time']:
                self.stats['min_response_time'] = response_time
            if response_time > self.stats['max_response_time']:
                self.stats['max_response_time'] = response_time

            # Calculate average response time
            if self.stats['total_checks'] > 0:
                self.stats['avg_response_time'] = (
                    (self.stats['avg_response_time'] * (self.stats['total_checks'] - 1) + response_time)
                    / self.stats['total_checks']
                )

            if response.status_code == 200:
                self.stats['healthy_checks'] += 1
                try:
                    health_data = response.json()
                    return {
                        'status': 'HEALTHY',
                        'response_time': response_time,
                        'details': health_data,
                        'timestamp': datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    return {
                        'status': 'HEALTHY',
                        'response_time': response_time,
                        'details': {'message': 'Health endpoint responding'},
                        'timestamp': datetime.now().isoformat()
                    }
            else:
                self.stats['error_checks'] += 1
                return {
                    'status': f'ERROR_{response.status_code}',
                    'response_time': response_time,
                    'details': {'error': f'HTTP {response.status_code}'},
                    'timestamp': datetime.now().isoformat()
                }

        except requests.exceptions.ConnectionError:
            self.stats['error_checks'] += 1
            return {
                'status': 'CONNECTION_ERROR',
                'response_time': None,
                'details': {'error': 'Server not responding'},
                'timestamp': datetime.now().isoformat()
            }
        except requests.exceptions.Timeout:
            self.stats['error_checks'] += 1
            return {
                'status': 'TIMEOUT',
                'response_time': None,
                'details': {'error': 'Request timeout (>5s)'},
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.stats['error_checks'] += 1
            return {
                'status': 'MONITOR_ERROR',
                'response_time': None,
                'details': {'error': str(e)},
                'timestamp': datetime.now().isoformat()
            }

    def scan_for_errors(self, text: str) -> List[str]:
        """Scan text for error patterns"""
        errors_found = []
        for pattern in self.error_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                errors_found.extend([f"{pattern}: {match}" for match in matches])
                self.stats['warnings_detected'] += len(matches)
        return errors_found

    def print_status(self, health_result: Dict):
        """Print formatted status with enhanced information"""
        timestamp = time.strftime("%H:%M:%S")
        status = health_result['status']
        response_time = health_result.get('response_time')

        if status == 'HEALTHY':
            if response_time:
                print(f'[{timestamp}] ✅ Backend: {status} ({response_time:.0f}ms)')
            else:
                print(f'[{timestamp}] ✅ Backend: {status}')
        else:
            print(f'[{timestamp}] ❌ Backend: {status}')
            if health_result.get('details', {}).get('error'):
                print(f'    └─ Error: {health_result["details"]["error"]}')

    def print_stats(self):
        """Print monitoring statistics"""
        if self.stats['total_checks'] > 0:
            uptime_percentage = (self.stats['healthy_checks'] / self.stats['total_checks']) * 100
            print(f'\n📊 MONITORING STATS:')
            print(f'   Total Checks: {self.stats["total_checks"]}')
            print(f'   Uptime: {uptime_percentage:.1f}% ({self.stats["healthy_checks"]}/{self.stats["total_checks"]})')
            print(f'   Avg Response: {self.stats["avg_response_time"]:.0f}ms')
            print(f'   Min/Max Response: {self.stats["min_response_time"]:.0f}ms / {self.stats["max_response_time"]:.0f}ms')
            print(f'   Warnings Detected: {self.stats["warnings_detected"]}')
            print('-' * 50)

    def monitor_backend(self):
        """Main monitoring loop with enhanced error detection"""
        print('=== VIERLA ENHANCED PERFORMANCE MONITOR ===')
        print('Monitoring backend health, performance, and error detection...')
        print(f'Started at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print(f'Backend URL: {self.backend_url}')
        print('-' * 50)

        check_count = 0
        while True:
            try:
                # Perform health check
                health_result = self.check_backend_health()
                self.print_status(health_result)

                check_count += 1

                # Print stats every 10 checks (5 minutes)
                if check_count % 10 == 0:
                    self.print_stats()

                time.sleep(30)

            except KeyboardInterrupt:
                print('\n=== Monitor stopped by user ===')
                self.print_stats()
                break
            except Exception as e:
                print(f'[{time.strftime("%H:%M:%S")}] 🔥 Monitor Critical Error: {e}')
                time.sleep(30)

if __name__ == '__main__':
    monitor = VierlaMonitor()
    monitor.monitor_backend()
