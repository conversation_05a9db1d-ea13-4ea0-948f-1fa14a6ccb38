/**
 * Switch Component
 * shadcn/ui inspired switch toggle component
 */

import React, { useEffect, useRef } from 'react';
import {
  TouchableOpacity,
  Animated,
  StyleSheet,
  ViewStyle,
} from 'react-native';

import { colors, spacing } from '../../theme';
import { cn } from '../../lib/utils';

export interface SwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
  testID?: string;
}

const SWITCH_SIZES = {
  sm: {
    width: 32,
    height: 18,
    thumbSize: 14,
    padding: 2,
  },
  md: {
    width: 44,
    height: 24,
    thumbSize: 20,
    padding: 2,
  },
  lg: {
    width: 56,
    height: 32,
    thumbSize: 28,
    padding: 2,
  },
};

export const Switch: React.FC<SwitchProps> = ({
  value,
  onValueChange,
  disabled = false,
  size = 'md',
  style,
  testID = 'switch',
}) => {
  const animatedValue = useRef(new Animated.Value(value ? 1 : 0)).current;
  const sizeConfig = SWITCH_SIZES[size];

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: value ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, animatedValue]);

  const handlePress = () => {
    if (!disabled) {
      onValueChange(!value);
    }
  };

  const trackColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      disabled ? colors.background.light : colors.primaryLight,
      disabled ? colors.primaryLight : colors.primary,
    ],
  });

  const thumbTranslateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      sizeConfig.padding,
      sizeConfig.width - sizeConfig.thumbSize - sizeConfig.padding,
    ],
  });

  const thumbColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [
      disabled ? colors.text.secondary : colors.white,
      colors.white,
    ],
  });

  const switchStyle = {
    width: sizeConfig.width,
    height: sizeConfig.height,
    borderRadius: sizeConfig.height / 2,
  };

  const thumbStyle = {
    width: sizeConfig.thumbSize,
    height: sizeConfig.thumbSize,
    borderRadius: sizeConfig.thumbSize / 2,
    transform: [{ translateX: thumbTranslateX }],
  };

  return (
    <TouchableOpacity
      style={cn(styles.container, style)}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.8}
      testID={testID}
    >
      <Animated.View
        style={[
          styles.track,
          switchStyle,
          {
            backgroundColor: trackColor,
            opacity: disabled ? 0.5 : 1,
          },
        ]}
      >
        <Animated.View
          style={[
            styles.thumb,
            thumbStyle,
            {
              backgroundColor: thumbColor,
            },
          ]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
  },
  track: {
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  thumb: {
    position: 'absolute',
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
});
