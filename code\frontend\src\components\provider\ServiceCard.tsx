import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { Service } from '../../services/api';

interface ServiceCardProps {
  service: Service;
  onPress?: () => void;
  onEdit?: () => void;
  onToggleStatus?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
  compact?: boolean;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: () => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onPress,
  onEdit,
  onToggleStatus,
  onDelete,
  showActions = true,
  compact = false,
  selectable = false,
  selected = false,
  onSelect,
}) => {
  const formatPrice = () => {
    if (service.price_type === 'range' && service.max_price) {
      return `$${service.base_price.toFixed(2)} - $${service.max_price.toFixed(2)}`;
    }
    return `$${service.base_price.toFixed(2)}`;
  };

  const formatDuration = () => {
    const hours = Math.floor(service.duration / 60);
    const minutes = service.duration % 60;

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${minutes}m`;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        compact && styles.compactContainer,
        selected && styles.selectedContainer,
      ]}
      onPress={selectable ? onSelect : onPress}
      activeOpacity={0.7}
    >
      {/* Selection Checkbox */}
      {selectable && (
        <TouchableOpacity style={styles.checkbox} onPress={onSelect}>
          <Icon
            name={selected ? 'check-box' : 'check-box-outline-blank'}
            size={24}
            color={colors.primary}
          />
        </TouchableOpacity>
      )}

      {/* Service Image */}
      {service.image && !compact && (
        <Image source={{ uri: service.image }} style={styles.image} />
      )}

      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={[styles.title, compact && styles.compactTitle]} numberOfLines={1}>
              {service.name}
            </Text>
            <Text style={styles.category}>{service.category.name}</Text>
          </View>
          
          <View style={styles.badges}>
            {service.is_popular && (
              <View style={styles.popularBadge}>
                <Icon name="star" size={12} color={colors.warning} />
                <Text style={styles.popularText}>Popular</Text>
              </View>
            )}
            <View style={[
              styles.statusBadge,
              { backgroundColor: service.is_available ? colors.success : colors.error }
            ]}>
              <Text style={styles.statusText}>
                {service.is_available ? 'Active' : 'Inactive'}
              </Text>
            </View>
          </View>
        </View>

        {/* Description */}
        {!compact && (
          <Text style={styles.description} numberOfLines={2}>
            {service.description}
          </Text>
        )}

        {/* Details */}
        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Icon name="attach-money" size={16} color={colors.success} />
            <Text style={styles.detailText}>{formatPrice()}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Icon name="schedule" size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>{formatDuration()}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Icon name="event" size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>{service.booking_count} bookings</Text>
          </View>
        </View>

        {/* Actions */}
        {showActions && !selectable && (
          <View style={styles.actions}>
            {onEdit && (
              <TouchableOpacity style={styles.actionButton} onPress={onEdit}>
                <Icon name="edit" size={18} color={colors.primary} />
                <Text style={styles.actionText}>Edit</Text>
              </TouchableOpacity>
            )}
            
            {onToggleStatus && (
              <TouchableOpacity style={styles.actionButton} onPress={onToggleStatus}>
                <Icon
                  name={service.is_available ? 'visibility-off' : 'visibility'}
                  size={18}
                  color={colors.primary}
                />
                <Text style={styles.actionText}>
                  {service.is_available ? 'Disable' : 'Enable'}
                </Text>
              </TouchableOpacity>
            )}
            
            {onDelete && (
              <TouchableOpacity style={styles.actionButton} onPress={onDelete}>
                <Icon name="delete" size={18} color={colors.error} />
                <Text style={[styles.actionText, { color: colors.error }]}>Delete</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  compactContainer: {
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  selectedContainer: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  checkbox: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    zIndex: 1,
  },
  image: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: spacing.md,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  titleContainer: {
    flex: 1,
    marginRight: spacing.md,
  },
  title: {
    ...typography.h3,
    color: colors.textPrimary,
    marginBottom: spacing.xs,
  },
  compactTitle: {
    ...typography.body,
    fontWeight: '600',
  },
  category: {
    ...typography.caption,
    color: colors.primary,
    fontWeight: '600',
  },
  badges: {
    alignItems: 'flex-end',
  },
  popularBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warningLight,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginBottom: spacing.xs,
  },
  popularText: {
    ...typography.caption,
    color: colors.warning,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    ...typography.caption,
    color: colors.white,
    fontWeight: '600',
  },
  description: {
    ...typography.body,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  detailText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  actionText: {
    ...typography.caption,
    color: colors.primary,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
});
