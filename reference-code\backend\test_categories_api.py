#!/usr/bin/env python3
"""
Test Categories API
"""

import requests
import json

def test_categories_api():
    """Test the categories API endpoint"""
    try:
        response = requests.get('http://192.168.2.65:8000/api/catalog/categories/')
        print(f'Status: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'Data type: {type(data)}')
            print(f'Data: {json.dumps(data, indent=2)}')

            if isinstance(data, list):
                print(f'Count: {len(data)}')
                if len(data) > 0:
                    print(f'First item: {json.dumps(data[0], indent=2)}')
            elif isinstance(data, dict):
                print(f'Dict keys: {list(data.keys())}')
                if 'results' in data:
                    print(f'Results count: {len(data["results"])}')
                    if len(data["results"]) > 0:
                        print(f'First result: {json.dumps(data["results"][0], indent=2)}')
            else:
                print('Unexpected data format')
        else:
            print(f'Error: {response.text}')
            
    except Exception as e:
        print(f'Exception: {e}')

if __name__ == '__main__':
    test_categories_api()
