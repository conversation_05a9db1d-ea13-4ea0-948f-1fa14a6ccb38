/**
 * Multi-Step Service Form Component
 * Enhanced service creation form with multiple steps, validation, auto-save, and progress indicators
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  BackHandler,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { colors, typography, spacing } from '../../theme';
import { Service, ServiceCreateData, ServiceUpdateData, ServiceCategory } from '../../services/api';

// Import step components
import { BasicInfoStep } from './steps/BasicInfoStep';
import { PricingStep } from './steps/PricingStep';
import { DetailsStep } from './steps/DetailsStep';
import { ReviewStep } from './steps/ReviewStep';
import { ProgressIndicator } from './ProgressIndicator';

export interface FormData {
  // Basic Info
  name: string;
  short_description: string;
  category: string;
  
  // Pricing
  base_price: string;
  price_type: 'fixed' | 'range';
  max_price: string;
  
  // Details
  description: string;
  duration: string;
  buffer_time: string;
  requirements: string;
  preparation_instructions: string;
  
  // Settings (for editing)
  is_available: boolean;
  is_popular: boolean;
}

export interface FormErrors {
  [key: string]: string;
}

interface MultiStepServiceFormProps {
  categories: ServiceCategory[];
  initialData?: Service;
  onSubmit: (data: ServiceCreateData | ServiceUpdateData) => void;
  onCancel: () => void;
  loading: boolean;
  submitButtonText: string;
  isEditing?: boolean;
}

const STEPS = [
  { id: 'basic', title: 'Basic Info', description: 'Service name and category' },
  { id: 'pricing', title: 'Pricing', description: 'Set your service pricing' },
  { id: 'details', title: 'Details', description: 'Service description and requirements' },
  { id: 'review', title: 'Review', description: 'Review and submit' },
];

const AUTO_SAVE_KEY = 'service_form_draft';
const AUTO_SAVE_INTERVAL = 30000; // 30 seconds

export const MultiStepServiceForm: React.FC<MultiStepServiceFormProps> = ({
  categories,
  initialData,
  onSubmit,
  onCancel,
  loading,
  submitButtonText,
  isEditing = false,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    short_description: '',
    category: '',
    base_price: '',
    price_type: 'fixed',
    max_price: '',
    description: '',
    duration: '',
    buffer_time: '15',
    requirements: '',
    preparation_instructions: '',
    is_available: true,
    is_popular: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      // Editing existing service
      setFormData({
        name: initialData.name,
        short_description: initialData.short_description || '',
        category: initialData.category.id,
        base_price: initialData.base_price.toString(),
        price_type: initialData.price_type,
        max_price: initialData.max_price?.toString() || '',
        description: initialData.description,
        duration: initialData.duration.toString(),
        buffer_time: initialData.buffer_time.toString(),
        requirements: initialData.requirements || '',
        preparation_instructions: initialData.preparation_instructions || '',
        is_available: initialData.is_available,
        is_popular: initialData.is_popular,
      });
    } else {
      // Creating new service - try to load draft
      loadDraft();
    }
  }, [initialData]);

  // Auto-save functionality
  useEffect(() => {
    if (!isEditing && hasUnsavedChanges) {
      const interval = setInterval(() => {
        saveDraft();
      }, AUTO_SAVE_INTERVAL);

      return () => clearInterval(interval);
    }
  }, [formData, hasUnsavedChanges, isEditing]);

  // Handle back button
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [currentStep, hasUnsavedChanges]);

  const loadDraft = async () => {
    try {
      const draft = await AsyncStorage.getItem(AUTO_SAVE_KEY);
      if (draft) {
        const draftData = JSON.parse(draft);
        Alert.alert(
          'Draft Found',
          'We found a saved draft of your service. Would you like to continue where you left off?',
          [
            {
              text: 'Start Fresh',
              style: 'cancel',
              onPress: () => clearDraft(),
            },
            {
              text: 'Continue Draft',
              onPress: () => {
                setFormData(draftData.formData);
                setCurrentStep(draftData.currentStep || 0);
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  };

  const saveDraft = async () => {
    try {
      const draft = {
        formData,
        currentStep,
        timestamp: Date.now(),
      };
      await AsyncStorage.setItem(AUTO_SAVE_KEY, JSON.stringify(draft));
    } catch (error) {
      console.error('Error saving draft:', error);
    }
  };

  const clearDraft = async () => {
    try {
      await AsyncStorage.removeItem(AUTO_SAVE_KEY);
    } catch (error) {
      console.error('Error clearing draft:', error);
    }
  };

  const handleBackPress = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
      return true;
    }
    
    if (hasUnsavedChanges) {
      Alert.alert(
        'Unsaved Changes',
        'You have unsaved changes. Are you sure you want to leave?',
        [
          { text: 'Stay', style: 'cancel' },
          { 
            text: 'Leave', 
            style: 'destructive', 
            onPress: () => {
              clearDraft();
              onCancel();
            }
          },
        ]
      );
      return true;
    }
    
    return false;
  };

  const updateFormData = useCallback((field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  const validateCurrentStep = (): boolean => {
    const newErrors: FormErrors = {};

    switch (currentStep) {
      case 0: // Basic Info
        if (!formData.name.trim()) {
          newErrors.name = 'Service name is required';
        } else if (formData.name.trim().length < 3) {
          newErrors.name = 'Service name must be at least 3 characters';
        }
        
        if (!formData.category) {
          newErrors.category = 'Please select a category';
        }
        break;

      case 1: // Pricing
        const basePrice = parseFloat(formData.base_price);
        if (!formData.base_price || isNaN(basePrice)) {
          newErrors.base_price = 'Base price is required';
        } else if (basePrice <= 0) {
          newErrors.base_price = 'Base price must be greater than 0';
        }

        if (formData.price_type === 'range') {
          const maxPrice = parseFloat(formData.max_price);
          if (!formData.max_price || isNaN(maxPrice)) {
            newErrors.max_price = 'Max price is required for range pricing';
          } else if (maxPrice <= basePrice) {
            newErrors.max_price = 'Max price must be greater than base price';
          }
        }
        break;

      case 2: // Details
        if (!formData.description.trim()) {
          newErrors.description = 'Description is required';
        } else if (formData.description.trim().length < 10) {
          newErrors.description = 'Description must be at least 10 characters';
        }

        const duration = parseInt(formData.duration);
        if (!formData.duration || isNaN(duration)) {
          newErrors.duration = 'Duration is required';
        } else if (duration <= 0) {
          newErrors.duration = 'Duration must be greater than 0';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep < STEPS.length - 1) {
        setCurrentStep(prev => prev + 1);
        saveDraft(); // Save progress
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = () => {
    if (!validateCurrentStep()) {
      return;
    }

    const submitData: ServiceCreateData | ServiceUpdateData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      short_description: formData.short_description.trim() || undefined,
      category: formData.category,
      base_price: parseFloat(formData.base_price),
      price_type: formData.price_type,
      max_price: formData.price_type === 'range' ? parseFloat(formData.max_price) : undefined,
      duration: parseInt(formData.duration),
      buffer_time: parseInt(formData.buffer_time) || 15,
      requirements: formData.requirements.trim() || undefined,
      preparation_instructions: formData.preparation_instructions.trim() || undefined,
    };

    if (isEditing) {
      (submitData as ServiceUpdateData).is_available = formData.is_available;
      (submitData as ServiceUpdateData).is_popular = formData.is_popular;
    }

    // Clear draft on successful submission
    clearDraft();
    setHasUnsavedChanges(false);
    onSubmit(submitData);
  };

  const renderCurrentStep = () => {
    const stepProps = {
      formData,
      errors,
      categories,
      onUpdateFormData: updateFormData,
      onNext: handleNext,
      onPrevious: handlePrevious,
      onSubmit: handleSubmit,
      loading,
      submitButtonText,
      isFirstStep: currentStep === 0,
      isLastStep: currentStep === STEPS.length - 1,
      isEditing,
    };

    switch (currentStep) {
      case 0:
        return <BasicInfoStep {...stepProps} />;
      case 1:
        return <PricingStep {...stepProps} />;
      case 2:
        return <DetailsStep {...stepProps} />;
      case 3:
        return <ReviewStep {...stepProps} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <ProgressIndicator
        steps={STEPS}
        currentStep={currentStep}
        onStepPress={(stepIndex) => {
          // Allow navigation to previous steps only
          if (stepIndex < currentStep) {
            setCurrentStep(stepIndex);
          }
        }}
      />
      
      <View style={styles.content}>
        {renderCurrentStep()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
});
