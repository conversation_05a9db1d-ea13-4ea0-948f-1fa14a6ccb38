/**
 * Enhanced Input Component
 * shadcn/ui inspired input component with improved design and accessibility
 */

import React, { useState, forwardRef } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextInputProps,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Text } from './Text';
import { theme } from '../../theme';
import { cn } from '../../lib/utils';

export interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  secureTextEntry?: boolean;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  required?: boolean;
  disabled?: boolean;
}

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  error,
  helperText,
  secureTextEntry,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  style,
  variant = 'default',
  size = 'md',
  required = false,
  disabled = false,
  ...props
}, ref) => {
  const [isSecure, setIsSecure] = useState(secureTextEntry);
  const [isFocused, setIsFocused] = useState(false);
  const [labelAnimation] = useState(new Animated.Value(props.value ? 1 : 0));

  const toggleSecureEntry = () => {
    setIsSecure(!isSecure);
  };

  const handleFocus = (e: any) => {
    setIsFocused(true);
    if (label) {
      Animated.timing(labelAnimation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    props.onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    if (label && !props.value) {
      Animated.timing(labelAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    props.onBlur?.(e);
  };

  const inputContainerStyle = cn(
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
    disabled && styles.disabled
  );

  const inputStyle = cn(
    styles.input,
    styles[`input_${size}`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
    disabled && styles.inputDisabled
  );

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;
  const iconColor = error
    ? theme.colors.error
    : isFocused
      ? theme.colors.primary
      : theme.colors.text.secondary;

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <View style={styles.labelContainer}>
          <Text variant="label" style={styles.label}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
        </View>
      )}
      
      <View style={inputContainerStyle}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            <Ionicons
              name={leftIcon}
              size={iconSize}
              color={iconColor}
            />
          </View>
        )}
        
        <TextInput
          ref={ref}
          style={[inputStyle, style]}
          secureTextEntry={isSecure}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled}
          placeholderTextColor={theme.colors.text.tertiary}
          {...props}
        />
        
        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={secureTextEntry ? toggleSecureEntry : onRightIconPress}
            disabled={disabled}
          >
            <Ionicons
              name={
                secureTextEntry
                  ? isSecure
                    ? 'eye-off'
                    : 'eye'
                  : rightIcon!
              }
              size={iconSize}
              color={iconColor}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {(error || helperText) && (
        <View style={styles.messageContainer}>
          <Text
            variant="caption"
            style={[
              styles.message,
              error ? styles.errorMessage : styles.helperMessage
            ]}
          >
            {error || helperText}
          </Text>
        </View>
      )}
    </View>
  );
});

Input.displayName = 'Input';

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  labelContainer: {
    marginBottom: theme.spacing.xs,
  },
  label: {
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  required: {
    color: theme.colors.error,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    backgroundColor: theme.colors.background.primary,
  },
  // Variants
  default: {
    borderColor: theme.colors.text.tertiary,
  },
  filled: {
    backgroundColor: theme.colors.background.secondary,
    borderColor: 'transparent',
  },
  outline: {
    borderColor: theme.colors.primary,
    backgroundColor: 'transparent',
  },
  // Sizes
  sm: {
    minHeight: 36,
    paddingHorizontal: theme.spacing.sm,
  },
  md: {
    minHeight: 44,
    paddingHorizontal: theme.spacing.md,
  },
  lg: {
    minHeight: 52,
    paddingHorizontal: theme.spacing.lg,
  },
  // States
  focused: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  error: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  disabled: {
    backgroundColor: theme.colors.background.light,
    borderColor: theme.colors.text.tertiary,
  },
  input: {
    flex: 1,
    fontSize: theme.typography.fontSize.base,
    lineHeight: theme.typography.lineHeight.normal * theme.typography.fontSize.base,
    color: theme.colors.text.primary,
    paddingVertical: 0, // Remove default padding
  },
  input_sm: {
    fontSize: theme.typography.fontSize.sm,
  },
  input_md: {
    fontSize: theme.typography.fontSize.base,
  },
  input_lg: {
    fontSize: theme.typography.fontSize.lg,
  },
  inputWithLeftIcon: {
    marginLeft: theme.spacing.sm,
  },
  inputWithRightIcon: {
    marginRight: theme.spacing.sm,
  },
  inputDisabled: {
    color: theme.colors.text.tertiary,
  },
  leftIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: theme.spacing.xs,
  },
  rightIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: theme.spacing.xs,
    paddingLeft: theme.spacing.xs,
  },
  messageContainer: {
    marginTop: theme.spacing.xs,
  },
  message: {
    fontSize: theme.typography.fontSize.sm,
  },
  errorMessage: {
    color: theme.colors.error,
  },
  helperMessage: {
    color: theme.colors.text.secondary,
  },
});
