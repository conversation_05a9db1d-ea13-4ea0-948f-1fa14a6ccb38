"""
Customer API URLs - Enhanced based on Backend Agent feedback
Custom URL patterns for customer-specific endpoints
"""

from django.urls import path
from . import views

app_name = 'customer'

urlpatterns = [
    # Dashboard and profile endpoints
    path('dashboard/', views.CustomerDashboardView.as_view(), name='dashboard'),
    path('profile/', views.CustomerProfileView.as_view(), name='profile'),
    
    # Advanced search endpoints
    path('search/advanced/', views.AdvancedSearchView.as_view(), name='advanced-search'),
    path('search/suggestions/', views.SearchSuggestionsView.as_view(), name='search-suggestions'),
    
    # Booking management endpoints
    path('bookings/quick-book/', views.QuickBookingView.as_view(), name='quick-book'),
    path('bookings/<int:booking_id>/cancel/', views.CancelBookingView.as_view(), name='cancel-booking'),
    path('bookings/<int:booking_id>/reschedule/', views.RescheduleBookingView.as_view(), name='reschedule-booking'),
    
    # Favorites management
    path('favorites/bulk-add/', views.BulkAddFavoritesView.as_view(), name='bulk-add-favorites'),
    path('favorites/bulk-remove/', views.BulkRemoveFavoritesView.as_view(), name='bulk-remove-favorites'),
    
    # Recommendations
    path('recommendations/', views.RecommendationsView.as_view(), name='recommendations'),
    path('recommendations/personalized/', views.PersonalizedRecommendationsView.as_view(), name='personalized-recommendations'),
    
    # Location-based endpoints
    path('nearby/services/', views.NearbyServicesView.as_view(), name='nearby-services'),
    path('nearby/providers/', views.NearbyProvidersView.as_view(), name='nearby-providers'),
    
    # Reviews and ratings
    path('reviews/', views.CustomerReviewsView.as_view(), name='reviews'),
    path('reviews/create/', views.CreateReviewView.as_view(), name='create-review'),
    
    # Notifications
    path('notifications/preferences/', views.NotificationPreferencesView.as_view(), name='notification-preferences'),
    
    # Analytics for customer (usage stats)
    path('analytics/usage/', views.CustomerUsageAnalyticsView.as_view(), name='usage-analytics'),
]
