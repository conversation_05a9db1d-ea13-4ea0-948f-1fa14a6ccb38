import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';
import { Service } from '../../services/api';

interface ServiceActionsProps {
  service: Service;
  onEdit?: () => void;
  onDuplicate?: () => void;
  onToggleStatus?: () => void;
  onDelete?: () => void;
  onViewAnalytics?: () => void;
  onViewBookings?: () => void;
  showAnalytics?: boolean;
  showBookings?: boolean;
  compact?: boolean;
  horizontal?: boolean;
}

export const ServiceActions: React.FC<ServiceActionsProps> = ({
  service,
  onEdit,
  onDuplicate,
  onToggleStatus,
  onDelete,
  onViewAnalytics,
  onViewBookings,
  showAnalytics = false,
  showBookings = false,
  compact = false,
  horizontal = true,
}) => {
  const handleToggleStatus = () => {
    const action = service.is_available ? 'disable' : 'enable';
    Alert.alert(
      `${action.charAt(0).toUpperCase() + action.slice(1)} Service`,
      `Are you sure you want to ${action} "${service.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: action.charAt(0).toUpperCase() + action.slice(1), onPress: onToggleStatus },
      ]
    );
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Service',
      `Are you sure you want to delete "${service.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: onDelete },
      ]
    );
  };

  const handleDuplicate = () => {
    Alert.alert(
      'Duplicate Service',
      `Create a copy of "${service.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Duplicate', onPress: onDuplicate },
      ]
    );
  };

  const actions = [
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit',
      color: colors.primary,
      onPress: onEdit,
      show: !!onEdit,
    },
    {
      id: 'duplicate',
      icon: 'content-copy',
      label: 'Duplicate',
      color: colors.secondary,
      onPress: handleDuplicate,
      show: !!onDuplicate,
    },
    {
      id: 'toggle',
      icon: service.is_available ? 'visibility-off' : 'visibility',
      label: service.is_available ? 'Disable' : 'Enable',
      color: service.is_available ? colors.warning : colors.success,
      onPress: handleToggleStatus,
      show: !!onToggleStatus,
    },
    {
      id: 'analytics',
      icon: 'analytics',
      label: 'Analytics',
      color: colors.accent,
      onPress: onViewAnalytics,
      show: showAnalytics && !!onViewAnalytics,
    },
    {
      id: 'bookings',
      icon: 'event',
      label: 'Bookings',
      color: colors.secondary,
      onPress: onViewBookings,
      show: showBookings && !!onViewBookings,
    },
    {
      id: 'delete',
      icon: 'delete',
      label: 'Delete',
      color: colors.error,
      onPress: handleDelete,
      show: !!onDelete,
    },
  ].filter(action => action.show);

  const renderAction = (action: typeof actions[0]) => (
    <TouchableOpacity
      key={action.id}
      style={[
        styles.actionButton,
        compact && styles.compactActionButton,
        !horizontal && styles.verticalActionButton,
      ]}
      onPress={action.onPress}
    >
      <Icon
        name={action.icon}
        size={compact ? 16 : 20}
        color={action.color}
      />
      {!compact && (
        <Text style={[
          styles.actionText,
          { color: action.color },
          !horizontal && styles.verticalActionText,
        ]}>
          {action.label}
        </Text>
      )}
    </TouchableOpacity>
  );

  if (actions.length === 0) {
    return null;
  }

  return (
    <View style={[
      styles.container,
      horizontal ? styles.horizontalContainer : styles.verticalContainer,
      compact && styles.compactContainer,
    ]}>
      {actions.map(renderAction)}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.md,
  },
  horizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  verticalContainer: {
    flexDirection: 'column',
  },
  compactContainer: {
    paddingTop: spacing.sm,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
  },
  compactActionButton: {
    paddingHorizontal: spacing.xs,
    paddingVertical: spacing.xs,
  },
  verticalActionButton: {
    justifyContent: 'flex-start',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    marginBottom: spacing.xs,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
  },
  actionText: {
    ...typography.caption,
    marginLeft: spacing.xs,
    fontWeight: '600',
  },
  verticalActionText: {
    ...typography.body,
    marginLeft: spacing.md,
  },
});
