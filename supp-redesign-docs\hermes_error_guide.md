### **Agent Directive: Hermes `ReferenceError` Triage and Resolution**

**Objective:** Diagnose and resolve the `[runtime not ready]: ReferenceError: Property 'Colors' doesn't exist, js engine: hermes` error in a React Native application using Expo, Django, and PostgreSQL.

-----

#### **1. Error Deconstruction: Initial Analysis**

The error indicates a failure during the application's initial JavaScript loading phase, before the React runtime is stable.

  * **`[runtime not ready]`**: Signals an error during the initial module loading and initialization, before the component tree mounts.[1, 2]
  * **`ReferenceError: Property 'Colors' doesn't exist`**: <PERSON> is attempting to access a property on a variable that is `undefined` or `null`.[3, 4] This is almost certainly a theme object that has not been initialized. A similar error, `TypeError: undefined is not an object`, can occur from library version mismatches affecting theme objects.[5]
  * **`js engine: hermes`**: Confirms the use of the Hermes engine, which uses Ahead-of-Time (AOT) compilation. This can influence module loading behavior compared to JavaScriptCore (JSC).[6, 7, 8]
  * **Stack Trace (`metroRequire`, `guardedLoadModule`):** The trace points to a failure within the Metro bundler's module loading system, not a component's render method. A required module (the theme) is evaluating to `undefined` when another module attempts to use it.

**Core Problem:** A piece of code (e.g., a `StyleSheet.create` call or a component-level constant) is executing *before* the global theme object containing the `Colors` property has been successfully initialized and provided.

-----

#### **2. Systematic Diagnostic and Resolution Protocol**

Execute the following steps in sequence. Proceed to the next step only if the previous one does not resolve the issue.

##### **Step 1: Purge All Caches**

Cache corruption is a frequent cause of module resolution failures.[9, 10]

1.  **Standard Cache Clear:**

    ```bash
    npx expo start --clear
    ```

    For Expo SDK 45 and below, use `expo r -c`.[9, 11]

2.  **Aggressive Cache Reset (If Step 1 Fails):**
    This process eliminates any stale artifacts from `node_modules`, native builds, or lock files.[2, 10]

    ```bash
    # 1. Stop the Metro server
    rm -rf node_modules
    rm -rf.expo
    rm package-lock.json # or yarn.lock
    npm install # or yarn install

    # 2. For iOS, reinstall Pods
    cd ios && pod install && cd..

    # 3. Restart with a clean cache
    npx expo start --clear
    ```

3.  **EAS Build Cache (If Applicable):**
    If the error occurs in an EAS build, clear the remote cache.[9]

    ```bash
    npx eas build --platform <android|ios> --clear-cache
    ```

##### **Step 2: Analyze Architectural Flaws**

**A. Asynchronous Theme Loading Race Condition**

  * **Symptom:** The theme is loaded asynchronously (e.g., from `AsyncStorage` or a Django API endpoint), but the UI attempts to render before the data arrives.[12, 13, 14, 15]
  * **Verification:** Check the root component (`App.js`) for `useEffect` hooks that fetch theme data.
  * **Resolution: Implement a "Loading Gate."** Prevent the main UI from rendering until the theme is loaded.
    ```javascript
    // In your root component (e.g., App.js)
    import React, { useState, useEffect } from 'react';
    import { PaperProvider } from 'react-native-paper';
    import { NavigationContainer } from '@react-navigation/native';
    import AsyncStorage from '@react-native-async-storage/async-storage';
    import { lightTheme, darkTheme } from './themes'; // Your theme definitions
    import SplashScreen from './screens/SplashScreen'; // A simple loading component

    export default function App() {
      const = useState(false);
      const = useState(lightTheme);

      useEffect(() => {
        const loadTheme = async () => {
          try {
            const savedTheme = await AsyncStorage.getItem('@theme');
            if (savedTheme) {
              setAppTheme(JSON.parse(savedTheme));
            }
            // Optional: Fetch from Django API as a fallback or primary source
          } catch (e) {
            // On error, fall back to a default theme to prevent a crash
            setAppTheme(lightTheme);
          } finally {
            setThemeReady(true);
          }
        };
        loadTheme();
      },);

      if (!isThemeReady) {
        return <SplashScreen />;
      }

      return (
        <PaperProvider theme={appTheme}>
          <NavigationContainer>
            {/* Your App's Navigators and Screens */}
          </NavigationContainer>
        </PaperProvider>
      );
    }
    ```

**B. Incorrect Theme Provider Scoping**

  * **Symptom:** A component that needs the theme is rendered outside the component tree of its `Provider` (e.g., `<PaperProvider>`, `<ThemeProvider>`).[16, 17, 18] The `useTheme()` hook returns `undefined` because it cannot find a provider in its ancestry.[19, 20]
  * **Verification:** Inspect `App.js`. The theme provider must wrap the `NavigationContainer`.
  * **Resolution: Ensure Correct Provider Nesting.**
      * **Correct:**
        ```javascript
        <PaperProvider theme={theme}>
          <NavigationContainer>
            {/* App content */}
          </NavigationContainer>
        </PaperProvider>
        ```
      * **Incorrect:**
        ```javascript
        <NavigationContainer>
          <PaperProvider theme={theme}>
            {/* App content */}
          </PaperProvider>
        </NavigationContainer>
        ```

##### **Step 3: Investigate Module Resolution Failures**

**A. Circular Dependencies**

  * **Symptom:** Module A imports Module B, and Module B (or a downstream dependency) imports Module A. Metro breaks the cycle by returning an empty, partially initialized `exports` object for one module, which results in `undefined`.[21]
  * **Detection: Use `madge`.**
    ```bash
    # Run from project root to find cycles in TypeScript/TSX files
    npx madge --circular --extensions ts,tsx.
    ```
  * **Resolution: Refactor Architecture.**
    1.  **Decompose:** Break down monolithic files (e.g., `utils.ts`, `theme.ts`) into smaller, single-responsibility modules (e.g., `theme/colors.ts`, `theme/spacing.ts`).[21]
    2.  **Invert Dependencies:** If two modules need a shared type, extract that type into a third, lower-level file that both can import from without importing each other.

**B. Faulty Module Imports/Exports**

  * **Symptom:** A mismatch between `export default` and a named import (`import { MyTheme } from...`), or vice-versa.
  * **Resolution: Verify Syntax.** Ensure the export statement in the source file matches the import statement in the consuming file. A linter can typically detect this.

**C. Expo SDK 53+ Metro Configuration**

  * **Symptom:** The error appears after upgrading to Expo SDK 53. This SDK enables `package.json:exports` resolution by default in Metro, which can be incompatible with some older third-party libraries.[22]
  * **Resolution: Disable `package.json:exports` Resolution.**
    1.  Create or edit `metro.config.js` in the project root.
    2.  Add the following configuration:
        ```javascript
        // metro.config.js
        const { getDefaultConfig } = require('expo/metro-config');
        const config = getDefaultConfig(__dirname);

        // Hotfix for libraries incompatible with package.json:exports
        config.resolver.unstable_enablePackageExports = false;

        module.exports = config;
        ```
    3.  Restart the bundler with a cleared cache: `npx expo start --clear`.

-----

#### **3. Advanced Debugging Workflow**

If the above steps do not resolve the issue, perform a deeper analysis.

1.  **Isolate with `console.log`:** Insert a log statement immediately before the failing line to confirm the variable is `undefined`.
    ```javascript
    console.log('Inspecting theme object:', myThemeObject);
    const color = myThemeObject.Colors.primary; // This line likely fails
    ```
2.  **Isolate with Commenting:** In the failing file, systematically comment out `import` statements one by one and reload the app. If commenting out an import resolves the `ReferenceError`, that module or one of its dependencies is the source of the problem.[10]
3.  **Use the Hermes Debugger:**
      * In the Metro terminal, press `j` to open the debugger in a browser.[23]
      * In your code, place a `debugger;` statement before the failing line.
      * Reload the app. Execution will pause.
      * Use the "Scope" panel in the DevTools to inspect all available variables and confirm which one is `undefined`.[23]

-----

#### **4. Prophylactic Design Patterns for Code Generation/Refactoring**

To prevent this class of error, adhere to the following architectural principles.

  * **Centralized, Strictly Typed Theme:** Define all theme constants in a central location and use TypeScript to enforce a strict `Theme` interface. This prevents typos and structural mismatches.[24]
    ```typescript
    // src/theme/types.ts
    export interface AppColors {
      primary: string;
      background: string;
      //...
    }
    export interface AppTheme {
      colors: AppColors;
      //...
    }
    ```
  * **Resilient Asynchronous Provider:** When loading themes asynchronously, create a custom provider that manages loading/error states and always provides a safe, non-null default theme during the loading phase to prevent the app from crashing.[25, 26, 27]
  * **Themed Base Components:** Abstract theme logic by creating reusable, themed base components (e.g., `<ThemedText>`, `<ThemedButton>`). This encapsulates styling logic and ensures consistency.[24, 28]
    ```javascript
    // src/components/ThemedText.tsx
    import React from 'react';
    import { Text } from 'react-native';
    import { useTheme } from 'react-native-paper';

    export const ThemedText = (props) => {
      const { colors } = useTheme();
      return <Text style={[{ color: colors.text }, props.style]} {...props} />;
    };
    ```