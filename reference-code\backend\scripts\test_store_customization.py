#!/usr/bin/env python
"""
Test Store Customization API Endpoints
Tests the new store customization functionality
"""
import os
import sys
import django
import json
from django.test import Client
from django.contrib.auth import get_user_model

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.catalog.models import ServiceProvider
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


def test_store_customization():
    """Test the store customization API endpoint"""
    print("=== TESTING STORE CUSTOMIZATION API ===\n")
    
    # Get a service provider
    try:
        provider = ServiceProvider.objects.first()
        if not provider:
            print("❌ No service providers found in database")
            return
        
        print(f"✅ Testing with provider: {provider.business_name}")
        print(f"   Provider ID: {provider.id}")
        
        # Get the provider's user for authentication
        user = provider.user
        if not user:
            print("❌ Provider has no associated user")
            return
        
        # Create JWT token for authentication
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)
        
        # Create test client
        client = Client()
        
        # Test data for store customization
        customization_data = {
            'store_theme_primary_color': '#8B5A3C',
            'store_theme_accent_color': '#F5E6D3',
            'store_theme_layout': 'grid',
            'display_show_prices': True,
            'display_show_duration': True,
            'display_show_ratings': True,
            'display_show_availability': False,
            'portfolio_type': 'instagram',
            'portfolio_instagram_max_posts': 12,
            'portfolio_show_instagram_captions': True,
            'portfolio_layout': 'masonry'
        }
        
        print(f"\n📝 Test customization data:")
        for key, value in customization_data.items():
            print(f"   {key}: {value}")
        
        # Test the store customization endpoint
        url = f'/api/catalog/providers/{provider.id}/store_customization/'
        response = client.patch(
            url,
            data=json.dumps(customization_data),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        print(f"\n🔧 API Request:")
        print(f"   URL: {url}")
        print(f"   Method: PATCH")
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Store customization updated successfully!")
            
            # Parse response data
            response_data = response.json()
            print(f"\n📋 Updated customization settings:")
            customization_fields = [
                'store_theme_primary_color', 'store_theme_accent_color', 'store_theme_layout',
                'display_show_prices', 'display_show_duration', 'display_show_ratings', 'display_show_availability',
                'portfolio_type', 'portfolio_instagram_max_posts', 'portfolio_show_instagram_captions', 'portfolio_layout'
            ]
            
            for field in customization_fields:
                if field in response_data:
                    print(f"   {field}: {response_data[field]}")
            
            # Verify the data was saved to database
            provider.refresh_from_db()
            print(f"\n🔍 Database verification:")
            print(f"   Primary Color: {provider.store_theme_primary_color}")
            print(f"   Layout: {provider.store_theme_layout}")
            print(f"   Instagram Max Posts: {provider.portfolio_instagram_max_posts}")
            print(f"   Portfolio Layout: {provider.portfolio_layout}")
            
        else:
            print(f"❌ Store customization failed!")
            print(f"   Response: {response.content.decode()}")
        
        # Test getting provider data with customization fields
        print(f"\n🔍 Testing provider retrieval with customization data...")
        get_response = client.get(
            f'/api/catalog/providers/{provider.id}/',
            HTTP_AUTHORIZATION=f'Bearer {access_token}'
        )
        
        if get_response.status_code == 200:
            print("✅ Provider data retrieved successfully!")
            get_data = get_response.json()
            print(f"   Theme Primary Color: {get_data.get('store_theme_primary_color', 'Not found')}")
            print(f"   Portfolio Type: {get_data.get('portfolio_type', 'Not found')}")
        else:
            print(f"❌ Failed to retrieve provider data: {get_response.status_code}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_store_customization()
