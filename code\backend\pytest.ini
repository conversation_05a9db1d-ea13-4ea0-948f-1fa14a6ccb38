[tool:pytest]
DJANGO_SETTINGS_MODULE = vierla_project.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test* *Tests
python_functions = test_*
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    auth: marks tests as authentication tests
testpaths = .
filterwarnings =
    ignore::django.utils.deprecation.RemovedInDjango50Warning
    ignore::DeprecationWarning
