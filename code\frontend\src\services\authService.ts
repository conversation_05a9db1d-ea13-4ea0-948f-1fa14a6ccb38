/**
 * Enhanced Authentication Service
 * Improved authentication service with better error handling and token management
 * Part of EPIC-05-CRITICAL: Authentication & UI Enhancement
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI, AuthResponse, LoginRequest } from './api/auth';
import { EnhancedError } from '../utils/errorHandler';

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthResponse['user'] | null;
  tokens: {
    access: string | null;
    refresh: string | null;
  };
  isLoading: boolean;
  error: string | null;
}

export interface LoginResult {
  success: boolean;
  user?: AuthResponse['user'];
  error?: string;
  errorCode?: string;
}

export interface TokenValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  error?: string;
}

class AuthService {
  private static instance: AuthService;
  private authState: AuthState = {
    isAuthenticated: false,
    user: null,
    tokens: {
      access: null,
      refresh: null,
    },
    isLoading: false,
    error: null,
  };

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Initialize authentication state from stored data
   */
  public async initialize(): Promise<AuthState> {
    try {
      this.authState.isLoading = true;
      
      const [accessToken, refreshToken, userData] = await Promise.all([
        AsyncStorage.getItem('access_token'),
        AsyncStorage.getItem('refresh_token'),
        AsyncStorage.getItem('user'),
      ]);

      if (accessToken && refreshToken && userData) {
        const user = JSON.parse(userData);
        
        // Validate token
        const tokenValidation = await this.validateToken(accessToken);
        
        if (tokenValidation.isValid) {
          this.authState = {
            isAuthenticated: true,
            user,
            tokens: {
              access: accessToken,
              refresh: refreshToken,
            },
            isLoading: false,
            error: null,
          };
        } else if (tokenValidation.needsRefresh) {
          // Try to refresh token
          const refreshResult = await this.refreshTokens(refreshToken);
          if (refreshResult.success) {
            this.authState = {
              isAuthenticated: true,
              user,
              tokens: refreshResult.tokens!,
              isLoading: false,
              error: null,
            };
          } else {
            await this.clearAuthData();
          }
        } else {
          await this.clearAuthData();
        }
      } else {
        this.authState.isLoading = false;
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      await this.clearAuthData();
    }

    return this.authState;
  }

  /**
   * Login with email and password
   */
  public async login(credentials: LoginRequest): Promise<LoginResult> {
    try {
      this.authState.isLoading = true;
      this.authState.error = null;

      const response = await authAPI.login(credentials);

      // Store tokens and user data
      await this.storeAuthData(response);

      this.authState = {
        isAuthenticated: true,
        user: response.user,
        tokens: {
          access: response.access,
          refresh: response.refresh,
        },
        isLoading: false,
        error: null,
      };

      return {
        success: true,
        user: response.user,
      };
    } catch (error) {
      const enhancedError = error as EnhancedError;
      const errorMessage = this.getLoginErrorMessage(enhancedError);
      
      this.authState.isLoading = false;
      this.authState.error = errorMessage;

      return {
        success: false,
        error: errorMessage,
        errorCode: enhancedError.errorCode,
      };
    }
  }

  /**
   * Logout user
   */
  public async logout(): Promise<void> {
    try {
      const refreshToken = this.authState.tokens.refresh;
      
      if (refreshToken) {
        try {
          await authAPI.logout(refreshToken);
        } catch (error) {
          // Ignore logout API errors, still clear local data
          console.warn('Logout API error:', error);
        }
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      await this.clearAuthData();
    }
  }

  /**
   * Refresh authentication tokens
   */
  public async refreshTokens(refreshToken: string): Promise<{
    success: boolean;
    tokens?: { access: string; refresh: string };
    error?: string;
  }> {
    try {
      // This would typically call a refresh endpoint
      // For now, we'll simulate the refresh logic
      const response = await fetch(`${process.env.API_BASE_URL || 'http://127.0.0.1:8000/api'}/auth/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        const tokens = {
          access: data.access,
          refresh: data.refresh || refreshToken, // Some APIs don't return new refresh token
        };

        await AsyncStorage.multiSet([
          ['access_token', tokens.access],
          ['refresh_token', tokens.refresh],
        ]);

        return { success: true, tokens };
      } else {
        return { success: false, error: 'Token refresh failed' };
      }
    } catch (error) {
      return { success: false, error: 'Network error during token refresh' };
    }
  }

  /**
   * Validate token
   */
  public async validateToken(token: string): Promise<TokenValidationResult> {
    try {
      // Check token format
      if (!this.isValidTokenFormat(token)) {
        return { isValid: false, needsRefresh: true };
      }

      // Check token expiration
      if (this.isTokenExpired(token)) {
        return { isValid: false, needsRefresh: true };
      }

      // Optionally verify with server
      try {
        await authAPI.checkAuthStatus();
        return { isValid: true, needsRefresh: false };
      } catch (error) {
        const enhancedError = error as EnhancedError;
        if (enhancedError.isAuthError) {
          return { isValid: false, needsRefresh: true };
        }
        // Network error, assume token is valid for now
        return { isValid: true, needsRefresh: false };
      }
    } catch (error) {
      return { isValid: false, needsRefresh: false, error: 'Token validation failed' };
    }
  }

  /**
   * Get current authentication state
   */
  public getAuthState(): AuthState {
    return { ...this.authState };
  }

  /**
   * Check if user has specific role
   */
  public hasRole(role: 'customer' | 'service_provider' | 'admin'): boolean {
    return this.authState.user?.role === role;
  }

  /**
   * Store authentication data
   */
  private async storeAuthData(authResponse: AuthResponse): Promise<void> {
    await AsyncStorage.multiSet([
      ['access_token', authResponse.access],
      ['refresh_token', authResponse.refresh],
      ['user', JSON.stringify(authResponse.user)],
    ]);
  }

  /**
   * Clear authentication data
   */
  private async clearAuthData(): Promise<void> {
    await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'user']);
    this.authState = {
      isAuthenticated: false,
      user: null,
      tokens: {
        access: null,
        refresh: null,
      },
      isLoading: false,
      error: null,
    };
  }

  /**
   * Check if token format is valid
   */
  private isValidTokenFormat(token: string): boolean {
    return token.split('.').length === 3;
  }

  /**
   * Check if token is expired
   */
  private isTokenExpired(token: string): boolean {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) return true;

      const payload = JSON.parse(atob(parts[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      return payload.exp < currentTime;
    } catch {
      return true;
    }
  }

  /**
   * Get user-friendly login error message
   */
  private getLoginErrorMessage(error: EnhancedError): string {
    if (error.isNetworkError) {
      return 'Unable to connect to the server. Please check your internet connection.';
    }

    if (error.isAccountLocked) {
      return 'Your account has been temporarily locked. Please try again later.';
    }

    if (error.isRateLimited) {
      return 'Too many login attempts. Please wait before trying again.';
    }

    if (error.response?.status === 400 || error.response?.status === 401) {
      return 'Invalid email or password. Please check your credentials.';
    }

    if (error.isServerError) {
      return 'Server error. Please try again in a few minutes.';
    }

    return 'Login failed. Please try again.';
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();
export default authService;
