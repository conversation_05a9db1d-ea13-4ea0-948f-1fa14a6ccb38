#!/usr/bin/env python
"""
Debug script to test authentication endpoints
"""

import os
import django
from django.conf import settings

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings')
os.environ.setdefault('USE_SQLITE', 'true')
django.setup()

from rest_framework.test import APIClient
from rest_framework import status
import json

def test_registration():
    """Test user registration endpoint"""
    client = APIClient()
    
    user_data = {
        'email': '<EMAIL>',
        'username': 'testuser',
        'first_name': 'Test',
        'last_name': 'User',
        'password': 'TestPassword123!',
        'password_confirm': 'TestPassword123!',
        'phone': '+1234567890'
    }
    
    response = client.post('/api/auth/register/', user_data, format='json')
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Data: {response.data}")
    
    if response.status_code != status.HTTP_201_CREATED:
        print("Registration failed!")
        return False
    
    print("Registration successful!")
    return True

if __name__ == '__main__':
    test_registration()
