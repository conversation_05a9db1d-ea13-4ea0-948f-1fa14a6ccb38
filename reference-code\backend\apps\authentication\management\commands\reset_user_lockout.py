"""
Django management command to reset user account lockouts and failed login attempts
Useful for debugging authentication issues during development
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
from apps.authentication.models import User


class Command(BaseCommand):
    help = 'Reset user account lockouts and clear rate limiting cache'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email of specific user to reset (optional)',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Reset all locked accounts',
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear rate limiting cache keys',
        )

    def handle(self, *args, **options):
        email = options.get('email')
        reset_all = options.get('all')
        clear_cache_flag = options.get('clear_cache')

        if clear_cache_flag:
            self.clear_rate_limiting_cache()

        if email:
            self.reset_user_by_email(email)
        elif reset_all:
            self.reset_all_locked_accounts()
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Please specify --email <email>, --all, or --clear-cache'
                )
            )

    def reset_user_by_email(self, email):
        """Reset specific user account lockout"""
        try:
            user = User.objects.get(email=email)
            
            self.stdout.write(f"User: {user.email}")
            self.stdout.write(f"Failed login attempts: {user.failed_login_attempts}")
            self.stdout.write(f"Account locked until: {user.account_locked_until}")
            self.stdout.write(f"Is account locked: {user.is_account_locked}")
            
            if user.is_account_locked or user.failed_login_attempts > 0:
                user.unlock_account()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully reset lockout for {email}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'Account {email} was not locked'
                    )
                )
                
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with email {email} not found')
            )

    def reset_all_locked_accounts(self):
        """Reset all locked user accounts"""
        locked_users = User.objects.filter(
            account_locked_until__isnull=False
        )
        
        failed_attempt_users = User.objects.filter(
            failed_login_attempts__gt=0
        )
        
        total_reset = 0
        
        for user in locked_users:
            user.unlock_account()
            total_reset += 1
            self.stdout.write(f"Reset lockout for: {user.email}")
            
        for user in failed_attempt_users:
            if user.failed_login_attempts > 0:
                user.reset_failed_login()
                total_reset += 1
                self.stdout.write(f"Reset failed attempts for: {user.email}")
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully reset {total_reset} user accounts'
            )
        )

    def clear_rate_limiting_cache(self):
        """Clear rate limiting cache keys"""
        cache_patterns = [
            'rate_limit:*',
            'endpoint_usage:*',
            'user_behavior_*',
            'customer_*',
            'provider_*',
            'burst_customer*',
            'adaptive_*'
        ]
        
        cleared_count = 0
        
        # Clear specific rate limiting patterns
        for pattern in cache_patterns:
            try:
                # Get all keys matching pattern (Redis-specific)
                if hasattr(cache, '_cache') and hasattr(cache._cache, 'get_client'):
                    client = cache._cache.get_client()
                    keys = client.keys(f"vierla:{pattern}")
                    if keys:
                        client.delete(*keys)
                        cleared_count += len(keys)
                        self.stdout.write(f"Cleared {len(keys)} keys for pattern: {pattern}")
            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"Could not clear pattern {pattern}: {e}")
                )
        
        # Clear general cache
        try:
            cache.clear()
            self.stdout.write("Cleared general cache")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Could not clear general cache: {e}")
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Cache clearing completed. Cleared {cleared_count} specific keys.'
            )
        )
