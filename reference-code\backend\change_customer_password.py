#!/usr/bin/env python
"""
Change Customer Password Script
Updates the <NAME_EMAIL> to 'Testpass123!'
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User


def change_customer_password():
    """Change <NAME_EMAIL> to 'Testpass123!'"""
    print("🔧 CHANGING CUSTOMER PASSWORD")
    print("=" * 50)
    
    try:
        # Get customer user
        customer = User.objects.get(email='<EMAIL>')
        print(f"✅ Found customer: {customer.email}")
        print(f"   Name: {customer.first_name} {customer.last_name}")
        print(f"   Role: {customer.role}")
        print(f"   Active: {customer.is_active}")
        print(f"   Verified: {customer.is_verified}")
        
        # Change password
        old_password_hash = customer.password
        customer.set_password('Testpass123!')
        customer.save()
        
        print(f"\n🔐 PASSWORD CHANGED SUCCESSFULLY!")
        print(f"   Email: {customer.email}")
        print(f"   New Password: Testpass123!")
        print(f"   Old Hash: {old_password_hash[:50]}...")
        print(f"   New Hash: {customer.password[:50]}...")
        
        # Verify the password change
        print(f"\n🧪 VERIFYING PASSWORD CHANGE:")
        test_passwords = ['testpass123', 'Testpass123!']
        
        for pwd in test_passwords:
            is_valid = customer.check_password(pwd)
            status = "✅ VALID" if is_valid else "❌ INVALID"
            print(f"   '{pwd}': {status}")
        
        return True
        
    except User.DoesNotExist:
        print("❌ Customer user not found")
        return False
    except Exception as e:
        print(f"❌ Error changing password: {e}")
        return False


if __name__ == '__main__':
    success = change_customer_password()
    if success:
        print(f"\n✅ SUCCESS: Password changed to 'Testpass123!' for <EMAIL>")
    else:
        print(f"\n❌ FAILED: Could not change password")
