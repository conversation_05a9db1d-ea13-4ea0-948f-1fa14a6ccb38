"""
Comprehensive test suite for Service Catalog models and APIs
Tests for ServiceCategory, ServiceProvider, and Service models
"""
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from decimal import Decimal
import uuid

from .models import ServiceCategory, ServiceProvider, Service

User = get_user_model()


class ServiceCategoryModelTest(TestCase):
    """Test cases for ServiceCategory model"""

    def setUp(self):
        """Set up test data"""
        self.category_data = {
            'name': 'Hair Services',
            'slug': 'hair-services',
            'description': 'Professional hair styling and treatment services',
            'icon': 'hair-icon',
            'color': '#FF5722',
        }

    def test_create_category(self):
        """Test creating a service category"""
        category = ServiceCategory.objects.create(**self.category_data)

        self.assertEqual(category.name, 'Hair Services')
        self.assertEqual(category.slug, 'hair-services')
        self.assertTrue(isinstance(category.id, uuid.UUID))
        self.assertTrue(category.is_active)
        self.assertFalse(category.is_popular)
        self.assertEqual(category.sort_order, 0)

    def test_category_str_representation(self):
        """Test string representation of category"""
        category = ServiceCategory.objects.create(**self.category_data)
        self.assertEqual(str(category), 'Hair Services')

    def test_category_unique_name(self):
        """Test that category names must be unique"""
        ServiceCategory.objects.create(**self.category_data)

        with self.assertRaises(IntegrityError):
            ServiceCategory.objects.create(**self.category_data)

    def test_category_unique_slug(self):
        """Test that category slugs must be unique"""
        ServiceCategory.objects.create(**self.category_data)

        duplicate_data = self.category_data.copy()
        duplicate_data['name'] = 'Different Name'

        with self.assertRaises(IntegrityError):
            ServiceCategory.objects.create(**duplicate_data)

    def test_hierarchical_categories(self):
        """Test parent-child category relationships"""
        parent = ServiceCategory.objects.create(**self.category_data)

        child_data = {
            'name': 'Hair Cutting',
            'slug': 'hair-cutting',
            'description': 'Hair cutting services',
            'icon': 'scissors-icon',
            'color': '#FF5722',
            'parent': parent
        }
        child = ServiceCategory.objects.create(**child_data)

        self.assertEqual(child.parent, parent)
        self.assertTrue(parent.has_subcategories)
        self.assertFalse(child.has_subcategories)
        self.assertIn(child, parent.subcategories.all())

    def test_color_validation(self):
        """Test color field validation"""
        # Valid hex color
        valid_data = self.category_data.copy()
        valid_data['color'] = '#123ABC'
        category = ServiceCategory(**valid_data)
        category.full_clean()  # Should not raise ValidationError

        # Invalid hex color
        invalid_data = self.category_data.copy()
        invalid_data['color'] = 'invalid-color'
        category = ServiceCategory(**invalid_data)

        with self.assertRaises(ValidationError):
            category.full_clean()

    def test_service_count_property(self):
        """Test service_count property"""
        category = ServiceCategory.objects.create(**self.category_data)
        self.assertEqual(category.service_count, 0)

        # This will be tested more thoroughly when we create services


class ServiceProviderModelTest(TestCase):
    """Test cases for ServiceProvider model"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='provider1',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            role='service_provider'
        )

        self.category = ServiceCategory.objects.create(
            name='Beauty Services',
            slug='beauty-services',
            description='Beauty and wellness services',
            icon='beauty-icon',
            color='#E91E63'
        )

        self.provider_data = {
            'user': self.user,
            'business_name': 'Beauty Studio Pro',
            'business_description': 'Professional beauty services',
            'business_phone': '+**********',
            'business_email': '<EMAIL>',
            'address': '123 Main Street',
            'city': 'Toronto',
            'state': 'Ontario',
            'zip_code': 'M5V 3A8',
            'country': 'Canada',
            'latitude': Decimal('43.6532'),
            'longitude': Decimal('-79.3832'),
        }

    def test_create_provider(self):
        """Test creating a service provider"""
        provider = ServiceProvider.objects.create(**self.provider_data)

        self.assertEqual(provider.business_name, 'Beauty Studio Pro')
        self.assertEqual(provider.user, self.user)
        self.assertTrue(isinstance(provider.id, uuid.UUID))
        self.assertTrue(provider.is_active)
        self.assertFalse(provider.is_verified)
        self.assertFalse(provider.is_featured)
        self.assertEqual(provider.rating, Decimal('0.00'))
        self.assertEqual(provider.review_count, 0)
        self.assertEqual(provider.total_bookings, 0)

    def test_provider_str_representation(self):
        """Test string representation of provider"""
        provider = ServiceProvider.objects.create(**self.provider_data)
        self.assertEqual(str(provider), 'Beauty Studio Pro')

    def test_one_to_one_user_relationship(self):
        """Test one-to-one relationship with User"""
        provider = ServiceProvider.objects.create(**self.provider_data)

        # Test reverse relationship
        self.assertEqual(self.user.service_provider, provider)

        # Test that creating another provider with same user fails
        with self.assertRaises(IntegrityError):
            ServiceProvider.objects.create(**self.provider_data)

    def test_phone_validation(self):
        """Test business phone validation"""
        # Valid phone numbers
        valid_phones = ['+**********', '**********', '+**********1234']

        for phone in valid_phones:
            data = self.provider_data.copy()
            data['business_phone'] = phone
            provider = ServiceProvider(**data)
            provider.full_clean()  # Should not raise ValidationError

        # Invalid phone numbers
        invalid_phones = ['123', 'abc123', '+**********12345678']

        for phone in invalid_phones:
            data = self.provider_data.copy()
            data['business_phone'] = phone
            provider = ServiceProvider(**data)

            with self.assertRaises(ValidationError):
                provider.full_clean()

    def test_rating_validation(self):
        """Test rating field validation"""
        provider = ServiceProvider.objects.create(**self.provider_data)

        # Valid ratings
        valid_ratings = [Decimal('0.00'), Decimal('2.50'), Decimal('5.00')]
        for rating in valid_ratings:
            provider.rating = rating
            provider.full_clean()  # Should not raise ValidationError

        # Invalid ratings
        invalid_ratings = [Decimal('-1.00'), Decimal('5.01'), Decimal('10.00')]
        for rating in invalid_ratings:
            provider.rating = rating
            with self.assertRaises(ValidationError):
                provider.full_clean()

    def test_full_address_property(self):
        """Test full_address property"""
        provider = ServiceProvider.objects.create(**self.provider_data)
        expected_address = "123 Main Street, Toronto, Ontario M5V 3A8, Canada"
        self.assertEqual(provider.full_address, expected_address)

    def test_has_location_property(self):
        """Test has_location property"""
        # Provider with location
        provider = ServiceProvider.objects.create(**self.provider_data)
        self.assertTrue(provider.has_location)

        # Provider without location
        data_no_location = self.provider_data.copy()
        data_no_location['latitude'] = None
        data_no_location['longitude'] = None
        user2 = User.objects.create_user(
            email='<EMAIL>',
            username='provider2',
            password='testpass123',
            role='service_provider'
        )
        data_no_location['user'] = user2

        provider_no_location = ServiceProvider.objects.create(**data_no_location)
        self.assertFalse(provider_no_location.has_location)

    def test_category_many_to_many(self):
        """Test many-to-many relationship with categories"""
        provider = ServiceProvider.objects.create(**self.provider_data)

        category2 = ServiceCategory.objects.create(
            name='Spa Services',
            slug='spa-services',
            description='Spa and relaxation services',
            icon='spa-icon',
            color='#4CAF50'
        )

        # Add categories
        provider.categories.add(self.category, category2)

        self.assertEqual(provider.categories.count(), 2)
        self.assertIn(self.category, provider.categories.all())
        self.assertIn(category2, provider.categories.all())

        # Test reverse relationship
        self.assertIn(provider, self.category.providers.all())


class ServiceModelTest(TestCase):
    """Test cases for Service model"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='provider_service',
            password='testpass123',
            first_name='Jane',
            last_name='Smith',
            role='service_provider'
        )

        self.category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='hair-icon',
            color='#FF5722'
        )

        self.provider = ServiceProvider.objects.create(
            user=self.user,
            business_name='Hair Studio Elite',
            business_description='Premium hair styling services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Style Avenue',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada'
        )

        self.service_data = {
            'provider': self.provider,
            'category': self.category,
            'name': 'Premium Haircut',
            'description': 'Professional haircut with styling consultation',
            'short_description': 'Premium haircut and styling',
            'base_price': Decimal('75.00'),
            'price_type': 'fixed',
            'duration': 60,
            'buffer_time': 15,
        }

    def test_create_service(self):
        """Test creating a service"""
        service = Service.objects.create(**self.service_data)

        self.assertEqual(service.name, 'Premium Haircut')
        self.assertEqual(service.provider, self.provider)
        self.assertEqual(service.category, self.category)
        self.assertTrue(isinstance(service.id, uuid.UUID))
        self.assertTrue(service.is_active)
        self.assertTrue(service.is_available)
        self.assertFalse(service.is_popular)
        self.assertEqual(service.booking_count, 0)
        self.assertEqual(service.price_type, 'fixed')

    def test_service_str_representation(self):
        """Test string representation of service"""
        service = Service.objects.create(**self.service_data)
        expected_str = "Premium Haircut - Hair Studio Elite"
        self.assertEqual(str(service), expected_str)

    def test_price_type_choices(self):
        """Test price type choices"""
        valid_price_types = ['fixed', 'hourly', 'range', 'consultation']

        for price_type in valid_price_types:
            data = self.service_data.copy()
            data['price_type'] = price_type
            service = Service(**data)
            service.full_clean()  # Should not raise ValidationError

    def test_base_price_validation(self):
        """Test base price validation"""
        # Valid prices
        valid_prices = [Decimal('0.00'), Decimal('50.00'), Decimal('999.99')]

        for price in valid_prices:
            data = self.service_data.copy()
            data['base_price'] = price
            service = Service(**data)
            service.full_clean()  # Should not raise ValidationError

        # Invalid prices
        invalid_prices = [Decimal('-1.00'), Decimal('-50.00')]

        for price in invalid_prices:
            data = self.service_data.copy()
            data['base_price'] = price
            service = Service(**data)

            with self.assertRaises(ValidationError):
                service.full_clean()

    def test_display_price_property(self):
        """Test display_price property for different price types"""
        # Fixed price
        service = Service.objects.create(**self.service_data)
        self.assertEqual(service.display_price, "$75.00")

        # Hourly price
        service.price_type = 'hourly'
        service.save()
        self.assertEqual(service.display_price, "$75.00/hour")

        # Range price
        service.price_type = 'range'
        service.max_price = Decimal('125.00')
        service.save()
        self.assertEqual(service.display_price, "$75.00 - $125.00")

        # Consultation price
        service.price_type = 'consultation'
        service.save()
        self.assertEqual(service.display_price, "Consultation Required")

    def test_display_duration_property(self):
        """Test display_duration property"""
        # Minutes only (less than 60)
        service = Service.objects.create(**self.service_data)
        service.duration = 45
        service.save()
        self.assertEqual(service.display_duration, "45m")

        # Exactly 60 minutes (should show as 1h)
        service.duration = 60
        service.save()
        self.assertEqual(service.display_duration, "1h")

        # Hours only
        service.duration = 120
        service.save()
        self.assertEqual(service.display_duration, "2h")

        # Hours and minutes
        service.duration = 90
        service.save()
        self.assertEqual(service.display_duration, "1h 30m")

    def test_total_duration_with_buffer_property(self):
        """Test total_duration_with_buffer property"""
        service = Service.objects.create(**self.service_data)
        expected_total = 60 + 15  # duration + buffer_time
        self.assertEqual(service.total_duration_with_buffer, expected_total)

    def test_foreign_key_relationships(self):
        """Test foreign key relationships"""
        service = Service.objects.create(**self.service_data)

        # Test provider relationship
        self.assertEqual(service.provider, self.provider)
        self.assertIn(service, self.provider.services.all())

        # Test category relationship
        self.assertEqual(service.category, self.category)
        self.assertIn(service, self.category.services.all())

    def test_service_count_in_category(self):
        """Test service count in category"""
        # Initially no services
        self.assertEqual(self.category.service_count, 0)

        # Create active service
        service1 = Service.objects.create(**self.service_data)
        self.assertEqual(self.category.service_count, 1)

        # Create inactive service
        inactive_data = self.service_data.copy()
        inactive_data['name'] = 'Inactive Service'
        inactive_data['is_active'] = False
        service2 = Service.objects.create(**inactive_data)

        # Should still be 1 (only active services counted)
        self.assertEqual(self.category.service_count, 1)

    def test_service_ordering(self):
        """Test default service ordering"""
        # Create services with different attributes
        service1 = Service.objects.create(**self.service_data)

        popular_data = self.service_data.copy()
        popular_data['name'] = 'Popular Service'
        popular_data['is_popular'] = True
        popular_data['base_price'] = Decimal('100.00')
        service2 = Service.objects.create(**popular_data)

        expensive_data = self.service_data.copy()
        expensive_data['name'] = 'Expensive Service'
        expensive_data['base_price'] = Decimal('200.00')
        service3 = Service.objects.create(**expensive_data)

        # Get ordered services
        services = list(Service.objects.all())

        # Popular service should be first
        self.assertEqual(services[0], service2)
        # Then ordered by price and name
        self.assertEqual(services[1], service1)  # Lower price
        self.assertEqual(services[2], service3)  # Higher price


class ServiceModelIntegrationTest(TransactionTestCase):
    """Integration tests for service models working together"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='integration_user',
            password='testpass123',
            role='service_provider'
        )

        # Create category hierarchy
        self.parent_category = ServiceCategory.objects.create(
            name='Beauty Services',
            slug='beauty-services',
            description='All beauty services',
            icon='beauty-icon',
            color='#E91E63'
        )

        self.child_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Hair styling services',
            icon='hair-icon',
            color='#FF5722',
            parent=self.parent_category
        )

        self.provider = ServiceProvider.objects.create(
            user=self.user,
            business_name='Full Service Beauty',
            business_description='Complete beauty services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='789 Beauty Boulevard',
            city='Montreal',
            state='Quebec',
            zip_code='H3A 0G4',
            country='Canada'
        )

    def test_complete_service_ecosystem(self):
        """Test complete service ecosystem functionality"""
        # Add categories to provider
        self.provider.categories.add(self.parent_category, self.child_category)

        # Create multiple services
        services_data = [
            {
                'name': 'Basic Haircut',
                'category': self.child_category,
                'base_price': Decimal('50.00'),
                'duration': 45,
                'is_popular': True
            },
            {
                'name': 'Premium Styling',
                'category': self.child_category,
                'base_price': Decimal('85.00'),
                'duration': 75,
                'is_popular': False
            },
            {
                'name': 'Hair Coloring',
                'category': self.child_category,
                'base_price': Decimal('120.00'),
                'duration': 120,
                'price_type': 'range',
                'max_price': Decimal('200.00')
            }
        ]

        created_services = []
        for service_data in services_data:
            service_data.update({
                'provider': self.provider,
                'description': f"Professional {service_data['name'].lower()}",
                'short_description': service_data['name']
            })
            service = Service.objects.create(**service_data)
            created_services.append(service)

        # Test relationships
        self.assertEqual(self.provider.services.count(), 3)
        self.assertEqual(self.child_category.services.count(), 3)
        self.assertEqual(self.child_category.service_count, 3)

        # Test category hierarchy
        self.assertTrue(self.parent_category.has_subcategories)
        self.assertFalse(self.child_category.has_subcategories)

        # Test provider categories
        self.assertEqual(self.provider.categories.count(), 2)

        # Test service ordering (popular first, then by price)
        ordered_services = list(self.provider.services.all())
        self.assertEqual(ordered_services[0].name, 'Basic Haircut')  # Popular
        self.assertEqual(ordered_services[1].name, 'Premium Styling')  # Lower price
        self.assertEqual(ordered_services[2].name, 'Hair Coloring')  # Higher price

        # Test service properties
        coloring_service = created_services[2]
        self.assertEqual(coloring_service.display_price, "$120.00 - $200.00")
        self.assertEqual(coloring_service.display_duration, "2h")
        self.assertEqual(coloring_service.total_duration_with_buffer, 135)  # 120 + 15

    def test_cascade_deletions(self):
        """Test cascade deletion behavior"""
        # Create service
        service = Service.objects.create(
            provider=self.provider,
            category=self.child_category,
            name='Test Service',
            description='Test service description',
            base_price=Decimal('60.00'),
            duration=60
        )

        service_id = service.id

        # Delete provider should delete service
        self.provider.delete()

        with self.assertRaises(Service.DoesNotExist):
            Service.objects.get(id=service_id)

        # Recreate for category deletion test
        new_user = User.objects.create_user(
            email='<EMAIL>',
            username='newprovider',
            password='testpass123',
            role='service_provider'
        )

        new_provider = ServiceProvider.objects.create(
            user=new_user,
            business_name='New Provider',
            business_description='New provider description',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 New Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada'
        )

        new_service = Service.objects.create(
            provider=new_provider,
            category=self.child_category,
            name='Another Test Service',
            description='Another test service description',
            base_price=Decimal('70.00'),
            duration=45
        )

        new_service_id = new_service.id

        # Delete category should delete service
        self.child_category.delete()

        with self.assertRaises(Service.DoesNotExist):
            Service.objects.get(id=new_service_id)


class ServiceRelationshipTest(TestCase):
    """Additional tests focused specifically on model relationships"""

    def setUp(self):
        """Set up test data for relationship testing"""
        # Create users
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='provider1',
            password='testpass123',
            role='service_provider'
        )

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='provider2',
            password='testpass123',
            role='service_provider'
        )

        # Create category hierarchy
        self.root_category = ServiceCategory.objects.create(
            name='Beauty & Wellness',
            slug='beauty-wellness',
            description='All beauty and wellness services',
            icon='beauty-icon',
            color='#E91E63'
        )

        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Hair styling and treatment',
            icon='hair-icon',
            color='#FF5722',
            parent=self.root_category
        )

        self.nail_category = ServiceCategory.objects.create(
            name='Nail Services',
            slug='nail-services',
            description='Nail care and art',
            icon='nail-icon',
            color='#9C27B0',
            parent=self.root_category
        )

        # Create providers
        self.provider1 = ServiceProvider.objects.create(
            user=self.user1,
            business_name='Hair & Nail Studio',
            business_description='Full service beauty salon',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Beauty Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada'
        )

        self.provider2 = ServiceProvider.objects.create(
            user=self.user2,
            business_name='Specialized Hair Care',
            business_description='Hair specialists only',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Hair Avenue',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada'
        )

    def test_category_hierarchy_relationships(self):
        """Test parent-child relationships in categories"""
        # Test parent-child relationship
        self.assertEqual(self.hair_category.parent, self.root_category)
        self.assertEqual(self.nail_category.parent, self.root_category)

        # Test reverse relationship
        subcategories = list(self.root_category.subcategories.all())
        self.assertIn(self.hair_category, subcategories)
        self.assertIn(self.nail_category, subcategories)
        self.assertEqual(len(subcategories), 2)

        # Test has_subcategories property
        self.assertTrue(self.root_category.has_subcategories)
        self.assertFalse(self.hair_category.has_subcategories)
        self.assertFalse(self.nail_category.has_subcategories)

    def test_provider_category_many_to_many(self):
        """Test many-to-many relationship between providers and categories"""
        # Initially no categories
        self.assertEqual(self.provider1.categories.count(), 0)
        self.assertEqual(self.provider2.categories.count(), 0)

        # Add categories to provider1 (both hair and nail)
        self.provider1.categories.add(self.hair_category, self.nail_category)
        self.assertEqual(self.provider1.categories.count(), 2)

        # Add only hair category to provider2
        self.provider2.categories.add(self.hair_category)
        self.assertEqual(self.provider2.categories.count(), 1)

        # Test reverse relationships
        hair_providers = list(self.hair_category.providers.all())
        self.assertIn(self.provider1, hair_providers)
        self.assertIn(self.provider2, hair_providers)
        self.assertEqual(len(hair_providers), 2)

        nail_providers = list(self.nail_category.providers.all())
        self.assertIn(self.provider1, nail_providers)
        self.assertNotIn(self.provider2, nail_providers)
        self.assertEqual(len(nail_providers), 1)

    def test_service_provider_relationship(self):
        """Test one-to-many relationship between provider and services"""
        # Create services for provider1
        service1 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Haircut',
            description='Professional haircut',
            base_price=Decimal('50.00'),
            duration=45
        )

        service2 = Service.objects.create(
            provider=self.provider1,
            category=self.nail_category,
            name='Manicure',
            description='Professional manicure',
            base_price=Decimal('35.00'),
            duration=30
        )

        # Create service for provider2
        service3 = Service.objects.create(
            provider=self.provider2,
            category=self.hair_category,
            name='Hair Styling',
            description='Professional hair styling',
            base_price=Decimal('75.00'),
            duration=60
        )

        # Test provider -> services relationship
        provider1_services = list(self.provider1.services.all())
        self.assertIn(service1, provider1_services)
        self.assertIn(service2, provider1_services)
        self.assertNotIn(service3, provider1_services)
        self.assertEqual(len(provider1_services), 2)

        provider2_services = list(self.provider2.services.all())
        self.assertIn(service3, provider2_services)
        self.assertNotIn(service1, provider2_services)
        self.assertNotIn(service2, provider2_services)
        self.assertEqual(len(provider2_services), 1)

        # Test service -> provider relationship
        self.assertEqual(service1.provider, self.provider1)
        self.assertEqual(service2.provider, self.provider1)
        self.assertEqual(service3.provider, self.provider2)

    def test_service_category_relationship(self):
        """Test one-to-many relationship between category and services"""
        # Create services in different categories
        hair_service1 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Haircut',
            description='Professional haircut',
            base_price=Decimal('50.00'),
            duration=45
        )

        hair_service2 = Service.objects.create(
            provider=self.provider2,
            category=self.hair_category,
            name='Hair Color',
            description='Professional hair coloring',
            base_price=Decimal('120.00'),
            duration=120
        )

        nail_service = Service.objects.create(
            provider=self.provider1,
            category=self.nail_category,
            name='Gel Nails',
            description='Gel nail application',
            base_price=Decimal('45.00'),
            duration=60
        )

        # Test category -> services relationship
        hair_services = list(self.hair_category.services.all())
        self.assertIn(hair_service1, hair_services)
        self.assertIn(hair_service2, hair_services)
        self.assertNotIn(nail_service, hair_services)
        self.assertEqual(len(hair_services), 2)

        nail_services = list(self.nail_category.services.all())
        self.assertIn(nail_service, nail_services)
        self.assertNotIn(hair_service1, nail_services)
        self.assertNotIn(hair_service2, nail_services)
        self.assertEqual(len(nail_services), 1)

        # Test service_count property
        self.assertEqual(self.hair_category.service_count, 2)
        self.assertEqual(self.nail_category.service_count, 1)
        self.assertEqual(self.root_category.service_count, 0)  # No direct services

    def test_user_provider_one_to_one(self):
        """Test one-to-one relationship between User and ServiceProvider"""
        # Test forward relationship
        self.assertEqual(self.provider1.user, self.user1)
        self.assertEqual(self.provider2.user, self.user2)

        # Test reverse relationship
        self.assertEqual(self.user1.service_provider, self.provider1)
        self.assertEqual(self.user2.service_provider, self.provider2)

        # Test that deleting user deletes provider
        user_id = self.user1.id
        provider_id = self.provider1.id

        self.user1.delete()

        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_id)

        with self.assertRaises(ServiceProvider.DoesNotExist):
            ServiceProvider.objects.get(id=provider_id)

    def test_complex_relationship_queries(self):
        """Test complex queries across relationships"""
        # Set up data
        self.provider1.categories.add(self.hair_category, self.nail_category)
        self.provider2.categories.add(self.hair_category)

        hair_service = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Premium Haircut',
            description='Premium haircut service',
            base_price=Decimal('85.00'),
            duration=60,
            is_popular=True
        )

        nail_service = Service.objects.create(
            provider=self.provider1,
            category=self.nail_category,
            name='Luxury Manicure',
            description='Luxury manicure service',
            base_price=Decimal('55.00'),
            duration=45
        )

        # Query services by provider categories
        provider1_category_services = Service.objects.filter(
            category__in=self.provider1.categories.all()
        )
        self.assertEqual(provider1_category_services.count(), 2)

        # Query providers by service category
        hair_category_providers = ServiceProvider.objects.filter(
            categories=self.hair_category
        )
        self.assertEqual(hair_category_providers.count(), 2)

        # Query popular services in specific category
        popular_hair_services = Service.objects.filter(
            category=self.hair_category,
            is_popular=True
        )
        self.assertEqual(popular_hair_services.count(), 1)
        self.assertEqual(popular_hair_services.first(), hair_service)

        # Query services by provider city
        toronto_services = Service.objects.filter(
            provider__city='Toronto'
        )
        self.assertEqual(toronto_services.count(), 2)

        # Query categories with active services
        categories_with_services = ServiceCategory.objects.filter(
            services__is_active=True
        ).distinct()
        self.assertEqual(categories_with_services.count(), 2)


class ServiceFilterTest(TestCase):
    """Test cases for ServiceFilter functionality"""

    def setUp(self):
        """Set up test data for filtering tests"""
        # Create users
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='provider1',
            password='testpass123',
            role='service_provider'
        )

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='provider2',
            password='testpass123',
            role='service_provider'
        )

        # Create categories
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='hair-icon',
            color='#FF5722'
        )

        self.spa_category = ServiceCategory.objects.create(
            name='Spa Services',
            slug='spa-services',
            description='Relaxing spa treatments',
            icon='spa-icon',
            color='#4CAF50'
        )

        # Create providers
        self.provider1 = ServiceProvider.objects.create(
            user=self.user1,
            business_name='Hair Studio Elite',
            business_description='Premium hair services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Hair Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            latitude=Decimal('43.6532'),
            longitude=Decimal('-79.3832'),
            is_verified=True,
            rating=Decimal('4.8')
        )

        self.provider2 = ServiceProvider.objects.create(
            user=self.user2,
            business_name='Spa Wellness Center',
            business_description='Relaxing spa treatments',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Spa Avenue',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=False,
            rating=Decimal('4.2')
        )

        # Create services
        self.service1 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Premium Haircut',
            description='Professional haircut with styling',
            base_price=Decimal('75.00'),
            duration=60,
            is_popular=True,
            is_available=True
        )

        self.service2 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Hair Coloring',
            description='Professional hair coloring service',
            base_price=Decimal('150.00'),
            price_type='range',
            max_price=Decimal('250.00'),
            duration=120,
            is_popular=False,
            is_available=True
        )

        self.service3 = Service.objects.create(
            provider=self.provider2,
            category=self.spa_category,
            name='Relaxing Massage',
            description='Full body relaxing massage',
            base_price=Decimal('120.00'),
            duration=90,
            is_popular=True,
            is_available=False
        )

    def test_search_filter(self):
        """Test search functionality across multiple fields"""
        from .filters import ServiceFilter

        # Search by service name
        filter_data = {'search': 'haircut'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

        # Search by provider name
        filter_data = {'search': 'spa'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service3, filtered.qs)
        self.assertNotIn(self.service1, filtered.qs)

        # Search by category
        filter_data = {'search': 'hair'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

    def test_price_filters(self):
        """Test price range filtering"""
        from .filters import ServiceFilter

        # Filter by minimum price
        filter_data = {'min_price': 100}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertNotIn(self.service1, filtered.qs)  # $75
        self.assertIn(self.service2, filtered.qs)     # $150
        self.assertIn(self.service3, filtered.qs)     # $120

        # Filter by maximum price
        filter_data = {'max_price': 100}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)     # $75
        self.assertNotIn(self.service2, filtered.qs) # $150
        self.assertNotIn(self.service3, filtered.qs) # $120

        # Filter by price range
        filter_data = {'min_price': 70, 'max_price': 130}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)     # $75
        self.assertNotIn(self.service2, filtered.qs) # $150
        self.assertIn(self.service3, filtered.qs)     # $120

    def test_duration_filters(self):
        """Test duration filtering"""
        from .filters import ServiceFilter

        # Filter by minimum duration
        filter_data = {'min_duration': 90}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertNotIn(self.service1, filtered.qs)  # 60 minutes
        self.assertIn(self.service2, filtered.qs)     # 120 minutes
        self.assertIn(self.service3, filtered.qs)     # 90 minutes

        # Filter by maximum duration
        filter_data = {'max_duration': 90}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)     # 60 minutes
        self.assertNotIn(self.service2, filtered.qs) # 120 minutes
        self.assertIn(self.service3, filtered.qs)     # 90 minutes

    def test_category_filters(self):
        """Test category filtering"""
        from .filters import ServiceFilter

        # Filter by single category
        filter_data = {'category': self.hair_category.id}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

        # Filter by multiple categories
        filter_data = {'categories': [self.hair_category.id, self.spa_category.id]}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertEqual(filtered.qs.count(), 3)  # All services

    def test_provider_filters(self):
        """Test provider-related filtering"""
        from .filters import ServiceFilter

        # Filter by provider verification
        filter_data = {'provider_verified': True}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

        # Filter by minimum rating
        filter_data = {'min_rating': 4.5}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)     # Provider rating 4.8
        self.assertIn(self.service2, filtered.qs)     # Provider rating 4.8
        self.assertNotIn(self.service3, filtered.qs) # Provider rating 4.2

        # Filter by provider city
        filter_data = {'provider_city': 'toronto'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

    def test_status_filters(self):
        """Test status-related filtering"""
        from .filters import ServiceFilter

        # Filter by popularity
        filter_data = {'is_popular': True}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertNotIn(self.service2, filtered.qs)
        self.assertIn(self.service3, filtered.qs)

        # Filter by availability
        filter_data = {'is_available': True}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)

        # Filter by location availability
        filter_data = {'has_location': True}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        self.assertIn(self.service1, filtered.qs)     # Provider1 has location
        self.assertIn(self.service2, filtered.qs)     # Provider1 has location
        self.assertNotIn(self.service3, filtered.qs) # Provider2 no location

    def test_sorting(self):
        """Test sorting functionality"""
        from .filters import ServiceFilter

        # Sort by price (low to high)
        filter_data = {'sort_by': 'price_low'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        services = list(filtered.qs)
        self.assertEqual(services[0], self.service1)  # $75
        self.assertEqual(services[1], self.service3)  # $120
        self.assertEqual(services[2], self.service2)  # $150

        # Sort by price (high to low)
        filter_data = {'sort_by': 'price_high'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        services = list(filtered.qs)
        self.assertEqual(services[0], self.service2)  # $150
        self.assertEqual(services[1], self.service3)  # $120
        self.assertEqual(services[2], self.service1)  # $75

        # Sort by rating
        filter_data = {'sort_by': 'rating'}
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        services = list(filtered.qs)
        # Services from provider1 (rating 4.8) should come first
        self.assertIn(services[0], [self.service1, self.service2])
        self.assertIn(services[1], [self.service1, self.service2])
        self.assertEqual(services[2], self.service3)  # Provider2 rating 4.2

    def test_combined_filters(self):
        """Test multiple filters applied together"""
        from .filters import ServiceFilter

        # Combine price, category, and availability filters
        filter_data = {
            'min_price': 50,
            'max_price': 200,
            'category': self.hair_category.id,
            'is_available': True
        }
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())

        # Should include both hair services that are available
        self.assertIn(self.service1, filtered.qs)
        self.assertIn(self.service2, filtered.qs)
        self.assertNotIn(self.service3, filtered.qs)  # Different category

        # Combine search with other filters
        filter_data = {
            'search': 'hair',
            'provider_verified': True,
            'sort_by': 'price_low'
        }
        filtered = ServiceFilter(filter_data, queryset=Service.objects.all())
        services = list(filtered.qs)

        # Should return hair services from verified provider, sorted by price
        self.assertEqual(len(services), 2)
        self.assertEqual(services[0], self.service1)  # Lower price first
        self.assertEqual(services[1], self.service2)


class ServiceAPIIntegrationTest(TestCase):
    """Integration tests for the complete service display flow"""

    def setUp(self):
        """Set up test data for integration tests"""
        # Create users
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            username='provider1',
            password='testpass123',
            role='service_provider'
        )

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            username='provider2',
            password='testpass123',
            role='service_provider'
        )

        # Create category hierarchy
        self.root_category = ServiceCategory.objects.create(
            name='Beauty Services',
            slug='beauty-services',
            description='All beauty services',
            icon='💄',
            color='#E91E63',
            is_popular=True
        )

        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='💇‍♀️',
            color='#FF5722',
            parent=self.root_category
        )

        self.spa_category = ServiceCategory.objects.create(
            name='Spa Services',
            slug='spa-services',
            description='Relaxing spa treatments',
            icon='🧖‍♀️',
            color='#4CAF50',
            parent=self.root_category
        )

        # Create providers with different characteristics
        self.provider1 = ServiceProvider.objects.create(
            user=self.user1,
            business_name='Elite Hair Studio',
            business_description='Premium hair styling and treatments',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Fashion Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            latitude=Decimal('43.6532'),
            longitude=Decimal('-79.3832'),
            is_verified=True,
            is_featured=True,
            rating=Decimal('4.9'),
            review_count=150,
            total_bookings=500,
            years_of_experience=8
        )

        self.provider2 = ServiceProvider.objects.create(
            user=self.user2,
            business_name='Zen Spa Retreat',
            business_description='Holistic wellness and spa treatments',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Wellness Avenue',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=True,
            is_featured=False,
            rating=Decimal('4.7'),
            review_count=89,
            total_bookings=200,
            years_of_experience=5
        )

        # Add categories to providers
        self.provider1.categories.add(self.hair_category)
        self.provider2.categories.add(self.spa_category)

        # Create diverse services
        self.service1 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Signature Haircut & Style',
            description='Premium haircut with personalized styling consultation',
            short_description='Premium haircut and styling',
            base_price=Decimal('85.00'),
            duration=75,
            is_popular=True,
            is_available=True,
            booking_count=45
        )

        self.service2 = Service.objects.create(
            provider=self.provider1,
            category=self.hair_category,
            name='Color Transformation',
            description='Complete hair color transformation with premium products',
            short_description='Professional hair coloring',
            base_price=Decimal('180.00'),
            price_type='range',
            max_price=Decimal('300.00'),
            duration=180,
            is_popular=False,
            is_available=True,
            booking_count=28
        )

        self.service3 = Service.objects.create(
            provider=self.provider2,
            category=self.spa_category,
            name='Deep Tissue Massage',
            description='Therapeutic deep tissue massage for muscle tension relief',
            short_description='Therapeutic massage therapy',
            base_price=Decimal('130.00'),
            duration=90,
            is_popular=True,
            is_available=True,
            booking_count=67
        )

        self.service4 = Service.objects.create(
            provider=self.provider2,
            category=self.spa_category,
            name='Facial Rejuvenation',
            description='Anti-aging facial treatment with organic products',
            short_description='Anti-aging facial treatment',
            base_price=Decimal('95.00'),
            duration=60,
            is_popular=False,
            is_available=False,  # Temporarily unavailable
            booking_count=12
        )

    def test_category_listing_flow(self):
        """Test the complete category browsing flow"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. Get all categories
        response = client.get('/api/catalog/categories/')
        self.assertEqual(response.status_code, 200)

        categories = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(categories), 3)  # Root + 2 subcategories

        # Verify category data structure
        root_cat = next(cat for cat in categories if cat['name'] == 'Beauty Services')
        self.assertTrue(root_cat['is_popular'])
        self.assertEqual(root_cat['service_count'], 0)  # No direct services

        # 2. Get services for a specific category
        hair_cat = next(cat for cat in categories if cat['name'] == 'Hair Services')
        response = client.get(f'/api/catalog/categories/{hair_cat["id"]}/services/')
        self.assertEqual(response.status_code, 200)

        services = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(services), 2)  # Both hair services

        # Verify service data includes provider info
        service = services[0]
        self.assertIn('provider_name', service)
        self.assertIn('provider_rating', service)
        self.assertIn('category_name', service)

    def test_service_search_flow(self):
        """Test the complete service search and filtering flow"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. Basic search
        response = client.get('/api/catalog/services/search/', {'search': 'hair'})
        self.assertEqual(response.status_code, 200)

        data = response.data
        self.assertIn('results', data)
        self.assertIn('search_metadata', data)

        results = data['results']
        self.assertEqual(len(results), 2)  # Both hair services

        # Verify search metadata
        metadata = data['search_metadata']
        self.assertEqual(metadata['total_results'], 2)
        self.assertIn('search', metadata['filters_applied'])

        # 2. Advanced filtering
        response = client.get('/api/catalog/services/search/', {
            'search': 'hair',
            'min_price': 80,
            'max_price': 200,
            'is_available': True,
            'sort_by': 'price_low'
        })
        self.assertEqual(response.status_code, 200)

        results = response.data['results']
        self.assertEqual(len(results), 2)

        # Verify sorting (lower price first)
        self.assertEqual(results[0]['name'], 'Signature Haircut & Style')  # $85
        self.assertEqual(results[1]['name'], 'Color Transformation')       # $180

        # 3. Category filtering
        response = client.get('/api/catalog/services/search/', {
            'category': str(self.spa_category.id),
            'is_available': True
        })
        self.assertEqual(response.status_code, 200)

        results = response.data['results']
        self.assertEqual(len(results), 1)  # Only available spa service
        self.assertEqual(results[0]['name'], 'Deep Tissue Massage')

    def test_service_detail_flow(self):
        """Test the service detail viewing flow"""
        from rest_framework.test import APIClient

        client = APIClient()

        # Get service detail
        response = client.get(f'/api/catalog/services/{self.service1.id}/')
        self.assertEqual(response.status_code, 200)

        service = response.data

        # Verify complete service information
        self.assertEqual(service['name'], 'Signature Haircut & Style')
        self.assertEqual(float(service['base_price']), 85.00)
        self.assertEqual(service['duration'], 75)
        self.assertTrue(service['is_popular'])
        self.assertTrue(service['is_available'])

        # Verify provider details are included
        self.assertIn('provider_detail', service)
        provider = service['provider_detail']
        self.assertEqual(provider['business_name'], 'Elite Hair Studio')
        self.assertEqual(float(provider['rating']), 4.9)
        self.assertTrue(provider['is_verified'])
        self.assertTrue(provider['is_featured'])

        # Verify category details are included
        self.assertIn('category_detail', service)
        category = service['category_detail']
        self.assertEqual(category['name'], 'Hair Services')
        self.assertEqual(category['icon'], '💇‍♀️')

    def test_provider_services_flow(self):
        """Test the provider-specific service browsing flow"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. Get provider details
        response = client.get(f'/api/catalog/providers/{self.provider1.id}/')
        self.assertEqual(response.status_code, 200)

        provider = response.data
        self.assertEqual(provider['business_name'], 'Elite Hair Studio')
        self.assertTrue(provider['is_featured'])
        self.assertIn('categories_detail', provider)

        # 2. Get provider's services
        response = client.get(f'/api/catalog/providers/{self.provider1.id}/services/')
        self.assertEqual(response.status_code, 200)

        services = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(services), 2)  # Both provider1 services

        # Verify services are from the correct provider
        for service in services:
            self.assertEqual(service['provider_name'], 'Elite Hair Studio')

        # 3. Filter provider's services
        response = client.get(f'/api/catalog/providers/{self.provider1.id}/services/', {
            'min_price': 100,
            'sort_by': 'price_high'
        })
        self.assertEqual(response.status_code, 200)

        services = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(services), 1)  # Only the expensive service
        self.assertEqual(services[0]['name'], 'Color Transformation')

    def test_popular_and_featured_flows(self):
        """Test popular services and featured provider flows"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. Get popular services
        response = client.get('/api/catalog/services/popular/')
        self.assertEqual(response.status_code, 200)

        services = response.data['results'] if 'results' in response.data else response.data
        popular_services = [s for s in services if s['is_popular']]
        self.assertEqual(len(popular_services), 2)  # service1 and service3

        # 2. Get featured provider services
        response = client.get('/api/catalog/services/featured/')
        self.assertEqual(response.status_code, 200)

        services = response.data['results'] if 'results' in response.data else response.data
        # All services should be from featured providers
        for service in services:
            # provider1 is featured, provider2 is not
            if service['provider_name'] == 'Elite Hair Studio':
                continue  # This is expected
            else:
                self.fail(f"Non-featured provider service found: {service['provider_name']}")

    def test_complete_user_journey(self):
        """Test a complete user journey from category to booking"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. User starts by browsing categories
        response = client.get('/api/catalog/categories/')
        categories = response.data['results'] if 'results' in response.data else response.data

        # 2. User selects hair services category
        hair_cat = next(cat for cat in categories if cat['name'] == 'Hair Services')
        response = client.get(f'/api/catalog/categories/{hair_cat["id"]}/services/')
        services = response.data['results'] if 'results' in response.data else response.data

        # 3. User searches for specific service
        response = client.get('/api/catalog/services/search/', {
            'search': 'haircut',
            'max_price': 100,
            'is_available': True
        })
        search_results = response.data['results']
        self.assertEqual(len(search_results), 1)  # Only the affordable haircut

        # 4. User views service details
        service_id = search_results[0]['id']
        response = client.get(f'/api/catalog/services/{service_id}/')
        service_detail = response.data

        # 5. User checks provider details
        provider_id = service_detail['provider']
        response = client.get(f'/api/catalog/providers/{provider_id}/')
        provider_detail = response.data

        # Verify the complete journey data integrity
        self.assertEqual(service_detail['name'], 'Signature Haircut & Style')
        self.assertEqual(provider_detail['business_name'], 'Elite Hair Studio')
        self.assertTrue(provider_detail['is_verified'])
        self.assertEqual(float(provider_detail['rating']), 4.9)

        # 6. User explores more services from this provider
        response = client.get(f'/api/catalog/providers/{provider_id}/services/')
        provider_services = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(provider_services), 2)

    def test_error_handling_and_edge_cases(self):
        """Test error handling and edge cases in the API"""
        from rest_framework.test import APIClient

        client = APIClient()

        # 1. Test invalid service ID
        response = client.get('/api/catalog/services/invalid-uuid/')
        self.assertEqual(response.status_code, 404)

        # 2. Test invalid category ID
        response = client.get('/api/catalog/categories/invalid-uuid/')
        self.assertEqual(response.status_code, 404)

        # 3. Test invalid provider ID
        response = client.get('/api/catalog/providers/invalid-uuid/')
        self.assertEqual(response.status_code, 404)

        # 4. Test search with invalid parameters
        response = client.get('/api/catalog/services/search/', {
            'min_price': 'invalid',
            'max_price': -10
        })
        self.assertEqual(response.status_code, 400)

        # 5. Test empty search results
        response = client.get('/api/catalog/services/search/', {
            'search': 'nonexistent service'
        })
        self.assertEqual(response.status_code, 200)
        results = response.data['results']
        self.assertEqual(len(results), 0)

        # 6. Test filtering that returns no results
        response = client.get('/api/catalog/services/search/', {
            'min_price': 1000,  # No services this expensive
            'is_available': True
        })
        self.assertEqual(response.status_code, 200)
        results = response.data['results']
        self.assertEqual(len(results), 0)


class ProviderServiceCRUDTest(TestCase):
    """Test cases for provider service CRUD operations"""

    def setUp(self):
        """Set up test data for provider service CRUD tests"""
        # Create users
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='provider',
            password='testpass123',
            role='service_provider'
        )

        self.other_provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='otherprovider',
            password='testpass123',
            role='service_provider'
        )

        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            username='customer',
            password='testpass123',
            role='customer'
        )

        # Create categories
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='💇‍♀️',
            color='#FF5722'
        )

        # Create providers
        self.provider = ServiceProvider.objects.create(
            user=self.provider_user,
            business_name='Test Hair Studio',
            business_description='Professional hair services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Test Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            is_verified=True,
            rating=Decimal('4.5')
        )

        self.other_provider = ServiceProvider.objects.create(
            user=self.other_provider_user,
            business_name='Other Hair Studio',
            business_description='Another hair studio',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Other Street',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=False,
            rating=Decimal('4.0')
        )

        # Create test service
        self.service = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Test Haircut',
            description='Professional haircut service',
            base_price=Decimal('75.00'),
            duration=60,
            is_available=True
        )

    def test_create_service_success(self):
        """Test successful service creation by provider"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        service_data = {
            'name': 'Premium Haircut',
            'description': 'High-end haircut with styling consultation',
            'category': self.hair_category.id,
            'base_price': '85.00',
            'duration': 75,
            'is_available': True
        }

        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify service was created
        service = Service.objects.get(name='Premium Haircut')
        self.assertEqual(service.provider, self.provider)
        self.assertEqual(service.base_price, Decimal('85.00'))
        self.assertEqual(service.duration, 75)

    def test_create_service_validation_errors(self):
        """Test service creation with validation errors"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test missing required fields
        response = client.post('/api/provider/services/', {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test invalid price
        service_data = {
            'name': 'Invalid Service',
            'description': 'Test description',
            'category': self.hair_category.id,
            'base_price': '-10.00',  # Invalid negative price
            'duration': 60
        }

        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test invalid duration
        service_data['base_price'] = '50.00'
        service_data['duration'] = 0  # Invalid zero duration

        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_service_unverified_provider_limit(self):
        """Test service creation limit for unverified providers"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.other_provider_user)

        # Create 3 services (limit for unverified providers)
        for i in range(3):
            service_data = {
                'name': f'Service {i+1}',
                'description': f'Test service {i+1}',
                'category': self.hair_category.id,
                'base_price': '50.00',
                'duration': 60
            }
            response = client.post('/api/provider/services/', service_data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Try to create 4th service (should fail)
        service_data = {
            'name': 'Service 4',
            'description': 'This should fail',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 60
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_list_provider_services(self):
        """Test listing services for authenticated provider"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Create additional service
        Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Hair Coloring',
            description='Professional hair coloring',
            base_price=Decimal('150.00'),
            duration=120
        )

        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should return only provider's services
        services = response.data['results'] if 'results' in response.data else response.data
        self.assertEqual(len(services), 2)

        # Verify all services belong to the provider
        for service in services:
            self.assertEqual(service['provider_name'], self.provider.business_name)

    def test_retrieve_service_details(self):
        """Test retrieving service details by provider"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        response = client.get(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        service_data = response.data
        self.assertEqual(service_data['name'], 'Test Haircut')
        self.assertEqual(float(service_data['base_price']), 75.00)
        self.assertEqual(service_data['duration'], 60)

    def test_update_service_success(self):
        """Test successful service update by owner"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        update_data = {
            'name': 'Updated Haircut',
            'description': 'Updated description',
            'base_price': '80.00',
            'duration': 90
        }

        response = client.patch(f'/api/provider/services/{self.service.id}/', update_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify service was updated
        self.service.refresh_from_db()
        self.assertEqual(self.service.name, 'Updated Haircut')
        self.assertEqual(self.service.base_price, Decimal('80.00'))
        self.assertEqual(self.service.duration, 90)

    def test_update_service_ownership_validation(self):
        """Test that providers can only update their own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.other_provider_user)

        update_data = {
            'name': 'Unauthorized Update',
            'base_price': '100.00'
        }

        response = client.patch(f'/api/provider/services/{self.service.id}/', update_data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify service was not updated
        self.service.refresh_from_db()
        self.assertEqual(self.service.name, 'Test Haircut')
        self.assertEqual(self.service.base_price, Decimal('75.00'))

    def test_delete_service_success(self):
        """Test successful service deletion by owner"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        response = client.delete(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Verify service was soft deleted
        self.service.refresh_from_db()
        self.assertFalse(self.service.is_active)

    def test_delete_service_ownership_validation(self):
        """Test that providers can only delete their own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.other_provider_user)

        response = client.delete(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify service was not deleted
        self.service.refresh_from_db()
        self.assertTrue(self.service.is_active)

    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access provider endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()

        # Test all CRUD operations without authentication
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = client.post('/api/provider/services/', {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = client.get(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = client.patch(f'/api/provider/services/{self.service.id}/', {})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        response = client.delete(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_customer_access_denied(self):
        """Test that customers cannot access provider endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.customer_user)

        # Test all CRUD operations as customer
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        response = client.post('/api/provider/services/', {})
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_service_filtering_and_sorting(self):
        """Test filtering and sorting of provider services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Create additional services with different attributes
        Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Expensive Service',
            description='High-end service',
            base_price=Decimal('200.00'),
            duration=120,
            is_available=False
        )

        Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Quick Service',
            description='Fast service',
            base_price=Decimal('30.00'),
            duration=30,
            is_available=True
        )

        # Test filtering by availability
        response = client.get('/api/provider/services/?is_available=true')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        services = response.data['results'] if 'results' in response.data else response.data
        available_services = [s for s in services if s['is_available']]
        self.assertEqual(len(available_services), 2)  # Original + Quick Service

        # Test sorting by price
        response = client.get('/api/provider/services/?ordering=base_price')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        services = response.data['results'] if 'results' in response.data else response.data
        prices = [float(s['base_price']) for s in services]
        self.assertEqual(prices, sorted(prices))  # Should be sorted ascending


class ProviderServiceAdvancedFeaturesTest(TestCase):
    """Test cases for advanced provider service management features"""

    def setUp(self):
        """Set up test data for advanced features tests"""
        # Create users
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='provider',
            password='testpass123',
            role='service_provider'
        )

        self.other_provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='otherprovider',
            password='testpass123',
            role='service_provider'
        )

        # Create category
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='💇‍♀️',
            color='#FF5722'
        )

        # Create provider
        self.provider = ServiceProvider.objects.create(
            user=self.provider_user,
            business_name='Test Hair Studio',
            business_description='Professional hair services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Test Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            is_verified=True,
            rating=Decimal('4.5')
        )

        self.other_provider = ServiceProvider.objects.create(
            user=self.other_provider_user,
            business_name='Other Hair Studio',
            business_description='Another hair studio',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Other Street',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=False,
            rating=Decimal('4.0')
        )

        # Create test services
        self.service1 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Haircut Service 1',
            description='Professional haircut service 1',
            base_price=Decimal('75.00'),
            duration=60,
            is_available=True,
            booking_count=15
        )

        self.service2 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Haircut Service 2',
            description='Professional haircut service 2',
            base_price=Decimal('85.00'),
            duration=75,
            is_available=False,
            booking_count=8
        )

        self.service3 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Haircut Service 3',
            description='Professional haircut service 3',
            base_price=Decimal('95.00'),
            duration=90,
            is_available=True,
            booking_count=22
        )

        # Create service for other provider
        self.other_service = Service.objects.create(
            provider=self.other_provider,
            category=self.hair_category,
            name='Other Provider Service',
            description='Service from other provider',
            base_price=Decimal('65.00'),
            duration=45,
            is_available=True
        )

    def test_toggle_service_status_success(self):
        """Test successful service status toggle by owner"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Toggle from available to unavailable
        initial_status = self.service1.is_available
        response = client.post(f'/api/provider/services/{self.service1.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify status was toggled
        self.service1.refresh_from_db()
        self.assertEqual(self.service1.is_available, not initial_status)

        # Toggle back
        response = client.post(f'/api/provider/services/{self.service1.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify status was toggled back
        self.service1.refresh_from_db()
        self.assertEqual(self.service1.is_available, initial_status)

    def test_toggle_service_status_ownership_validation(self):
        """Test that providers can only toggle their own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.other_provider_user)

        initial_status = self.service1.is_available
        response = client.post(f'/api/provider/services/{self.service1.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify status was not changed
        self.service1.refresh_from_db()
        self.assertEqual(self.service1.is_available, initial_status)

    def test_bulk_update_activate_services(self):
        """Test bulk activation of services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Set services to inactive
        self.service1.is_available = False
        self.service2.is_available = False
        self.service1.save()
        self.service2.save()

        bulk_data = {
            'service_ids': [str(self.service1.id), str(self.service2.id)],
            'action': 'activate'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['updated_count'], 2)

        # Verify services were activated
        self.service1.refresh_from_db()
        self.service2.refresh_from_db()
        self.assertTrue(self.service1.is_available)
        self.assertTrue(self.service2.is_available)

    def test_bulk_update_deactivate_services(self):
        """Test bulk deactivation of services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Set services to active
        self.service1.is_available = True
        self.service2.is_available = True
        self.service1.save()
        self.service2.save()

        bulk_data = {
            'service_ids': [str(self.service1.id), str(self.service2.id)],
            'action': 'deactivate'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['updated_count'], 2)

        # Verify services were deactivated
        self.service1.refresh_from_db()
        self.service2.refresh_from_db()
        self.assertFalse(self.service1.is_available)
        self.assertFalse(self.service2.is_available)

    def test_bulk_update_delete_services(self):
        """Test bulk deletion (soft delete) of services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Ensure services are active
        self.service1.is_active = True
        self.service2.is_active = True
        self.service1.save()
        self.service2.save()

        bulk_data = {
            'service_ids': [str(self.service1.id), str(self.service2.id)],
            'action': 'delete'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['updated_count'], 2)

        # Verify services were soft deleted
        self.service1.refresh_from_db()
        self.service2.refresh_from_db()
        self.assertFalse(self.service1.is_active)
        self.assertFalse(self.service2.is_active)

    def test_bulk_update_invalid_action(self):
        """Test bulk update with invalid action"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        bulk_data = {
            'service_ids': [str(self.service1.id)],
            'action': 'invalid_action'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid action type', response.data['error'])

    def test_bulk_update_missing_service_ids(self):
        """Test bulk update with missing service_ids"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        bulk_data = {
            'action': 'activate'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('service_ids is required', response.data['error'])

    def test_bulk_update_only_own_services(self):
        """Test that bulk update only affects provider's own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Try to bulk update including other provider's service
        bulk_data = {
            'service_ids': [str(self.service1.id), str(self.other_service.id)],
            'action': 'activate'
        }

        response = client.post('/api/provider/services/bulk_update/', bulk_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should only update 1 service (the provider's own service)
        self.assertEqual(response.data['updated_count'], 1)

    def test_service_analytics_success(self):
        """Test successful retrieval of service analytics"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        response = client.get(f'/api/provider/services/{self.service1.id}/analytics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        analytics_data = response.data
        self.assertIn('service_id', analytics_data)
        self.assertIn('total_bookings', analytics_data)
        self.assertIn('revenue', analytics_data)
        self.assertIn('average_rating', analytics_data)
        self.assertIn('conversion_rate', analytics_data)
        self.assertIn('popular_times', analytics_data)
        self.assertIn('customer_insights', analytics_data)

        # Verify service_id matches
        self.assertEqual(analytics_data['service_id'], self.service1.id)
        # Verify total_bookings matches the service's booking_count
        self.assertEqual(analytics_data['total_bookings'], self.service1.booking_count)

    def test_service_analytics_ownership_validation(self):
        """Test that providers can only access analytics for their own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.other_provider_user)

        response = client.get(f'/api/provider/services/{self.service1.id}/analytics/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_provider_summary_in_list_response(self):
        """Test that provider summary is included in list response"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if provider_summary is included
        if 'provider_summary' in response.data:
            summary = response.data['provider_summary']
            self.assertIn('total_services', summary)
            self.assertIn('active_services', summary)
            self.assertIn('inactive_services', summary)
            self.assertIn('is_verified', summary)
            self.assertIn('service_limit', summary)

            # Verify summary data
            self.assertEqual(summary['is_verified'], self.provider.is_verified)
            self.assertEqual(summary['total_services'], 3)  # service1, service2, service3

    def test_service_validation_price_range(self):
        """Test service validation for price range type"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test range pricing with valid max_price
        service_data = {
            'name': 'Range Price Service',
            'description': 'Service with price range',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'price_type': 'range',
            'max_price': '100.00',
            'duration': 60
        }

        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Test range pricing with invalid max_price (less than base_price)
        service_data['name'] = 'Invalid Range Service'
        service_data['max_price'] = '30.00'  # Less than base_price

        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_service_search_functionality(self):
        """Test service search functionality"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Search by service name
        response = client.get('/api/provider/services/?search=Service 1')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        services = response.data['results'] if 'results' in response.data else response.data

        # Should find service1 which contains "Service 1" in name
        found_service = any(s['name'] == 'Haircut Service 1' for s in services)
        self.assertTrue(found_service)

    def test_service_category_filtering(self):
        """Test filtering services by category"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Filter by category
        response = client.get(f'/api/provider/services/?category={self.hair_category.id}')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        services = response.data['results'] if 'results' in response.data else response.data

        # All returned services should belong to the hair category
        for service in services:
            if 'category' in service and service['category']:
                self.assertEqual(service['category']['id'], str(self.hair_category.id))


class ProviderServiceAuthenticationTest(TestCase):
    """Test cases for provider service authentication and authorization"""

    def setUp(self):
        """Set up test data for authentication tests"""
        # Create users with different roles
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='provider',
            password='testpass123',
            role='service_provider'
        )

        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            username='customer',
            password='testpass123',
            role='customer'
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='admin',
            password='testpass123',
            role='admin'
        )

        # Create category
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='💇‍♀️',
            color='#FF5722'
        )

        # Create provider profile
        self.provider = ServiceProvider.objects.create(
            user=self.provider_user,
            business_name='Test Hair Studio',
            business_description='Professional hair services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Test Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            is_verified=True,
            rating=Decimal('4.5')
        )

        # Create test service
        self.service = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Test Haircut',
            description='Professional haircut service',
            base_price=Decimal('75.00'),
            duration=60,
            is_available=True
        )

    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access provider endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()

        # Test list endpoint
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test create endpoint
        service_data = {
            'name': 'New Service',
            'description': 'Test service',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test detail endpoint
        response = client.get(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test update endpoint
        response = client.put(f'/api/provider/services/{self.service.id}/', service_data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        # Test delete endpoint
        response = client.delete(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_customer_access_denied(self):
        """Test that customers cannot access provider endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.customer_user)

        # Test list endpoint
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test create endpoint
        service_data = {
            'name': 'New Service',
            'description': 'Test service',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_admin_access_denied(self):
        """Test that admin users cannot access provider endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.admin_user)

        # Test list endpoint
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test create endpoint
        service_data = {
            'name': 'New Service',
            'description': 'Test service',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_provider_without_profile_access_denied(self):
        """Test that provider users without provider profile cannot access endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        # Create provider user without profile
        provider_no_profile = User.objects.create_user(
            email='<EMAIL>',
            username='noprofile',
            password='testpass123',
            role='service_provider'
        )

        client = APIClient()
        client.force_authenticate(user=provider_no_profile)

        # Test list endpoint
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test create endpoint
        service_data = {
            'name': 'New Service',
            'description': 'Test service',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_provider_access_allowed(self):
        """Test that authenticated providers can access their endpoints"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test list endpoint
        response = client.get('/api/provider/services/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test create endpoint
        service_data = {
            'name': 'New Service',
            'description': 'Test service',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Test detail endpoint
        response = client.get(f'/api/provider/services/{self.service.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test update endpoint
        update_data = {
            'name': 'Updated Service',
            'description': 'Updated description',
            'category': self.hair_category.id,
            'base_price': '80.00',
            'duration': 60
        }
        response = client.put(f'/api/provider/services/{self.service.id}/', update_data, format='json')
        if response.status_code != status.HTTP_200_OK:
            print(f"Update response status: {response.status_code}")
            print(f"Update response data: {response.data}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_service_ownership_validation(self):
        """Test that providers can only access their own services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        # Create another provider
        other_provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='other',
            password='testpass123',
            role='service_provider'
        )

        other_provider = ServiceProvider.objects.create(
            user=other_provider_user,
            business_name='Other Studio',
            business_description='Another studio',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Other Street',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=False,
            rating=Decimal('4.0')
        )

        # Create service for other provider
        other_service = Service.objects.create(
            provider=other_provider,
            category=self.hair_category,
            name='Other Service',
            description='Service from other provider',
            base_price=Decimal('65.00'),
            duration=45,
            is_available=True
        )

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Provider should not be able to access other provider's service
        response = client.get(f'/api/provider/services/{other_service.id}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Provider should not be able to update other provider's service
        update_data = {
            'name': 'Hacked Service',
            'description': 'Unauthorized update',
            'category': self.hair_category.id,
            'base_price': '1.00',
            'duration': 1
        }
        response = client.put(f'/api/provider/services/{other_service.id}/', update_data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Provider should not be able to delete other provider's service
        response = client.delete(f'/api/provider/services/{other_service.id}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Verify other service was not modified
        other_service.refresh_from_db()
        self.assertEqual(other_service.name, 'Other Service')
        self.assertTrue(other_service.is_active)

    def test_service_limit_enforcement_unverified_provider(self):
        """Test that unverified providers are limited to 3 active services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        # Create unverified provider
        unverified_user = User.objects.create_user(
            email='<EMAIL>',
            username='unverified',
            password='testpass123',
            role='service_provider'
        )

        unverified_provider = ServiceProvider.objects.create(
            user=unverified_user,
            business_name='Unverified Studio',
            business_description='Unverified provider',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='789 Unverified Street',
            city='Calgary',
            state='Alberta',
            zip_code='T2P 1J9',
            country='Canada',
            is_verified=False,
            rating=Decimal('3.5')
        )

        client = APIClient()
        client.force_authenticate(user=unverified_user)

        # Create 3 services (should succeed)
        for i in range(3):
            service_data = {
                'name': f'Service {i+1}',
                'description': f'Test service {i+1}',
                'category': self.hair_category.id,
                'base_price': '50.00',
                'duration': 45
            }
            response = client.post('/api/provider/services/', service_data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Try to create 4th service (should fail)
        service_data = {
            'name': 'Service 4',
            'description': 'This should fail',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_verified_provider_no_service_limit(self):
        """Test that verified providers have no service limit"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)  # This provider is verified

        # Create 5 services (should all succeed for verified provider)
        for i in range(5):
            service_data = {
                'name': f'Verified Service {i+1}',
                'description': f'Test service {i+1}',
                'category': self.hair_category.id,
                'base_price': '75.00',
                'duration': 60
            }
            response = client.post('/api/provider/services/', service_data)
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)


class ServiceValidationAndErrorHandlingTest(TestCase):
    """Test cases for service validation rules and error handling"""

    def setUp(self):
        """Set up test data for validation tests"""
        # Create test user and provider
        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            username='validation',
            password='testpass123',
            role='service_provider'
        )

        self.provider = ServiceProvider.objects.create(
            user=self.provider_user,
            business_name='Validation Test Studio',
            business_description='Test provider for validation',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Validation Street',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            country='Canada',
            is_verified=True,
            rating=Decimal('4.5')
        )

        # Create test categories
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='💇‍♀️',
            color='#FF5722'
        )

        self.nail_category = ServiceCategory.objects.create(
            name='Nail Services',
            slug='nail-services',
            description='Professional nail services',
            icon='💅',
            color='#E91E63'
        )

    def test_service_name_validation(self):
        """Test service name validation rules"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test empty name
        service_data = {
            'name': '',
            'description': 'Valid description for testing',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)

        # Test name too short
        service_data['name'] = 'Hi'
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test name too long (over 100 characters)
        service_data['name'] = 'A' * 101
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test valid name
        service_data['name'] = 'Valid Service Name'
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_service_description_validation(self):
        """Test service description validation rules"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test empty description
        service_data = {
            'name': 'Valid Service Name',
            'description': '',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('description', response.data)

        # Test description too short
        service_data['description'] = 'Short'
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test description too long (over 1000 characters)
        service_data['description'] = 'A' * 1001
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test valid description
        service_data['description'] = 'This is a valid service description with enough detail'
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_service_price_validation(self):
        """Test service price validation rules"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        base_service_data = {
            'name': 'Valid Service Name',
            'description': 'Valid service description for testing',
            'category': self.hair_category.id,
            'duration': 45
        }

        # Test negative price
        service_data = {**base_service_data, 'base_price': '-10.00'}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test zero price
        service_data = {**base_service_data, 'base_price': '0.00'}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test price too high (over $10,000)
        service_data = {**base_service_data, 'base_price': '10001.00'}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test invalid price format
        service_data = {**base_service_data, 'base_price': 'invalid'}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test valid price
        service_data = {**base_service_data, 'base_price': '75.50'}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_service_duration_validation(self):
        """Test service duration validation rules"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        base_service_data = {
            'name': 'Valid Service Name',
            'description': 'Valid service description for testing',
            'category': self.hair_category.id,
            'base_price': '50.00'
        }

        # Test negative duration
        service_data = {**base_service_data, 'duration': -10}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test zero duration
        service_data = {**base_service_data, 'duration': 0}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test duration too long (over 8 hours = 480 minutes)
        service_data = {**base_service_data, 'duration': 481}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test valid duration
        service_data = {**base_service_data, 'duration': 60}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_service_category_validation(self):
        """Test service category validation rules"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        base_service_data = {
            'name': 'Valid Service Name',
            'description': 'Valid service description for testing',
            'base_price': '50.00',
            'duration': 45
        }

        # Test missing category
        service_data = {**base_service_data}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('category', response.data)

        # Test invalid category ID
        service_data = {**base_service_data, 'category': 99999}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test valid category
        service_data = {**base_service_data, 'category': self.hair_category.id}
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_duplicate_service_name_validation(self):
        """Test that providers cannot create services with duplicate names"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Create first service
        service_data = {
            'name': 'Unique Service Name',
            'description': 'First service with this name',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Try to create second service with same name
        service_data['description'] = 'Second service with same name'
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)

    def test_service_update_validation(self):
        """Test validation rules when updating services"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Create a service first
        service = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Original Service',
            description='Original description',
            base_price=Decimal('50.00'),
            duration=45,
            is_available=True
        )

        # Test updating with invalid data
        update_data = {
            'name': '',  # Invalid empty name
            'description': 'Updated description',
            'category': self.hair_category.id,
            'base_price': '60.00',
            'duration': 50
        }
        response = client.put(f'/api/provider/services/{service.id}/', update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)

        # Test updating with valid data
        update_data['name'] = 'Updated Service Name'
        response = client.put(f'/api/provider/services/{service.id}/', update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_error_response_format(self):
        """Test that error responses have consistent format"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Submit invalid data
        service_data = {
            'name': '',  # Invalid
            'description': 'A',  # Too short
            'category': 99999,  # Invalid
            'base_price': '-10',  # Invalid
            'duration': 0  # Invalid
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Check error response structure
        self.assertIsInstance(response.data, dict)
        self.assertIn('name', response.data)
        self.assertIn('description', response.data)
        self.assertIn('category', response.data)
        self.assertIn('base_price', response.data)
        self.assertIn('duration', response.data)

        # Each field should have error messages
        for field in ['name', 'description', 'category', 'base_price', 'duration']:
            self.assertIsInstance(response.data[field], list)
            self.assertTrue(len(response.data[field]) > 0)

    def test_service_limit_validation_for_unverified_providers(self):
        """Test that unverified providers cannot exceed service limits"""
        from rest_framework.test import APIClient
        from rest_framework import status

        # Create unverified provider
        unverified_user = User.objects.create_user(
            email='<EMAIL>',
            username='unverified',
            password='testpass123',
            role='service_provider'
        )

        unverified_provider = ServiceProvider.objects.create(
            user=unverified_user,
            business_name='Unverified Studio',
            business_description='Unverified provider',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Test Street',
            city='Vancouver',
            state='British Columbia',
            zip_code='V6B 1A1',
            country='Canada',
            is_verified=False,  # Not verified
            rating=Decimal('4.0')
        )

        client = APIClient()
        client.force_authenticate(user=unverified_user)

        # Create 3 services (should succeed)
        for i in range(3):
            service_data = {
                'name': f'Service {i+1}',
                'description': f'Test service {i+1} description',
                'category': self.hair_category.id,
                'base_price': '50.00',
                'duration': 45
            }
            response = client.post('/api/provider/services/', service_data, format='json')
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Try to create 4th service (should fail)
        service_data = {
            'name': 'Service 4',
            'description': 'This should fail due to limit',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('limit', response.data['detail'].lower())

    def test_malformed_request_handling(self):
        """Test handling of malformed requests"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test with completely invalid JSON structure
        response = client.post('/api/provider/services/', 'invalid json', content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test with missing required fields
        response = client.post('/api/provider/services/', {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Test with extra unexpected fields
        service_data = {
            'name': 'Valid Service',
            'description': 'Valid description',
            'category': self.hair_category.id,
            'base_price': '50.00',
            'duration': 45,
            'unexpected_field': 'should be ignored',
            'another_field': 123
        }
        response = client.post('/api/provider/services/', service_data, format='json')
        # Should succeed but ignore extra fields
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_basic_validation_structure(self):
        """Test that validation errors are returned in proper format"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Test with missing required fields
        response = client.post('/api/provider/services/', {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsInstance(response.data, dict)

        # Should have validation errors for required fields
        required_fields = ['name', 'description', 'category', 'base_price', 'duration']
        for field in required_fields:
            if field in response.data:
                self.assertIsInstance(response.data[field], list)

    def test_service_status_transition_validation(self):
        """Test validation of service status transitions"""
        from rest_framework.test import APIClient
        from rest_framework import status

        client = APIClient()
        client.force_authenticate(user=self.provider_user)

        # Create a service
        service = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Status Test Service',
            description='Service for testing status transitions',
            base_price=Decimal('50.00'),
            duration=45,
            is_available=True,
            is_active=True
        )

        # Test toggling availability
        response = client.post(f'/api/provider/services/{service.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        service.refresh_from_db()
        self.assertFalse(service.is_available)

        # Test toggling back
        response = client.post(f'/api/provider/services/{service.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        service.refresh_from_db()
        self.assertTrue(service.is_available)

        # Test soft delete
        response = client.delete(f'/api/provider/services/{service.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        service.refresh_from_db()
        self.assertFalse(service.is_active)

        # Test that deleted service cannot be toggled
        response = client.post(f'/api/provider/services/{service.id}/toggle_status/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
