/**
 * Badge Component
 * shadcn/ui inspired badge component for status indicators and labels
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

import { Text } from './Text';
import { useTheme } from '../../contexts/ThemeContext';
import { createVariants, cn } from '../../lib/utils';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'success' | 'warning' | 'info' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
}

// Badge variants moved inside component



export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  style,
  textStyle,
  testID = 'badge',
}) => {
  const { colors, spacing, typography, borderRadius } = useTheme();

  // Create badge variants using theme
  const badgeVariants = createVariants({
    base: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: borderRadius.full,
      borderWidth: 1,
    },
    variants: {
      variant: {
        default: {
          backgroundColor: colors.primary,
          borderColor: colors.primary,
        },
        secondary: {
          backgroundColor: colors.background.secondary,
          borderColor: colors.primaryLight,
        },
        destructive: {
          backgroundColor: colors.error,
          borderColor: colors.error,
        },
        success: {
          backgroundColor: colors.success,
          borderColor: colors.success,
        },
        warning: {
          backgroundColor: colors.warning,
          borderColor: colors.warning,
        },
        info: {
          backgroundColor: colors.info,
          borderColor: colors.info,
        },
        outline: {
          backgroundColor: 'transparent',
          borderColor: colors.primaryLight,
        },
      },
      size: {
        sm: {
          paddingHorizontal: spacing.xs,
          paddingVertical: spacing.xs / 2,
          minHeight: 20,
        },
        md: {
          paddingHorizontal: spacing.sm,
          paddingVertical: spacing.xs,
          minHeight: 24,
        },
        lg: {
          paddingHorizontal: spacing.md,
          paddingVertical: spacing.sm,
          minHeight: 32,
        },
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  });

  // Create text variants using theme
  const textVariants = createVariants({
    base: {
      fontWeight: '500',
      textAlign: 'center',
    },
    variants: {
      variant: {
        default: {
          color: colors.white,
        },
        secondary: {
          color: colors.text.primary,
        },
        destructive: {
          color: colors.white,
        },
        success: {
          color: colors.white,
        },
        warning: {
          color: colors.white,
        },
        info: {
          color: colors.white,
        },
        outline: {
          color: colors.primaryLight,
        },
      },
      size: {
        sm: {
          fontSize: typography.fontSize.xs,
          lineHeight: typography.lineHeight.tight * typography.fontSize.xs,
        },
        md: {
          fontSize: typography.fontSize.sm,
          lineHeight: typography.lineHeight.normal * typography.fontSize.sm,
        },
        lg: {
          fontSize: typography.fontSize.base,
          lineHeight: typography.lineHeight.normal * typography.fontSize.base,
        },
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  });

  const badgeStyle = badgeVariants({ variant, size });
  const badgeTextStyle = textVariants({ variant, size });

  return (
    <View
      style={cn(badgeStyle, style)}
      testID={testID}
    >
      {typeof children === 'string' ? (
        <Text style={cn(badgeTextStyle, textStyle)}>
          {children}
        </Text>
      ) : (
        children
      )}
    </View>
  );
};

// Predefined badge variants for common use cases
export const StatusBadge: React.FC<Omit<BadgeProps, 'variant'> & { status: 'active' | 'inactive' | 'pending' | 'error' }> = ({
  status,
  ...props
}) => {
  const variantMap = {
    active: 'success' as const,
    inactive: 'secondary' as const,
    pending: 'warning' as const,
    error: 'destructive' as const,
  };

  return (
    <Badge variant={variantMap[status]} {...props} />
  );
};

export const PriorityBadge: React.FC<Omit<BadgeProps, 'variant'> & { priority: 'low' | 'medium' | 'high' | 'urgent' }> = ({
  priority,
  ...props
}) => {
  const variantMap = {
    low: 'info' as const,
    medium: 'default' as const,
    high: 'warning' as const,
    urgent: 'destructive' as const,
  };

  return (
    <Badge variant={variantMap[priority]} {...props} />
  );
};

export const CountBadge: React.FC<Omit<BadgeProps, 'children'> & { count: number; maxCount?: number }> = ({
  count,
  maxCount = 99,
  ...props
}) => {
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();
  
  return (
    <Badge size="sm" {...props}>
      {displayCount}
    </Badge>
  );
};
