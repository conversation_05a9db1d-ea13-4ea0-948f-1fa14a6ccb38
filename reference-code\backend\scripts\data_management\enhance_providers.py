#!/usr/bin/env python
"""
Script to enhance providers with location data and additional information
Following rules.md - mock data locations set to random addresses in Toronto and Ottawa
"""
import os
import sys
import django
from decimal import Decimal
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.authentication.models import User

def enhance_providers():
    """Enhance existing providers with location data and additional information"""
    
    print("🌍 Enhancing Providers with Location Data")
    print("=" * 50)
    
    # Toronto and Ottawa coordinates (following rules.md)
    toronto_locations = [
        {"address": "123 Queen St W", "city": "Toronto", "state": "ON", "zip_code": "M5H 2M9", "lat": 43.6532, "lon": -79.3832},
        {"address": "456 King St E", "city": "Toronto", "state": "ON", "zip_code": "M5A 1L6", "lat": 43.6514, "lon": -79.3598},
        {"address": "789 Bloor St W", "city": "Toronto", "state": "ON", "zip_code": "M6G 1K5", "lat": 43.6629, "lon": -79.4103},
        {"address": "321 Yonge St", "city": "Toronto", "state": "ON", "zip_code": "M5B 1R7", "lat": 43.6565, "lon": -79.3799},
        {"address": "654 Dundas St W", "city": "Toronto", "state": "ON", "zip_code": "M5T 1H3", "lat": 43.6532, "lon": -79.3957}
    ]
    
    ottawa_locations = [
        {"address": "100 Rideau St", "city": "Ottawa", "state": "ON", "zip_code": "K1N 9J7", "lat": 45.4215, "lon": -75.6972},
        {"address": "200 Bank St", "city": "Ottawa", "state": "ON", "zip_code": "K2P 1X4", "lat": 45.4112, "lon": -75.6934},
        {"address": "300 Somerset St W", "city": "Ottawa", "state": "ON", "zip_code": "K2P 0J9", "lat": 45.4089, "lon": -75.6903},
        {"address": "400 Sparks St", "city": "Ottawa", "state": "ON", "zip_code": "K1R 7S8", "lat": 45.4215, "lon": -75.6919},
        {"address": "500 Elgin St", "city": "Ottawa", "state": "ON", "zip_code": "K2P 1M5", "lat": 45.4165, "lon": -75.6890}
    ]
    
    all_locations = toronto_locations + ottawa_locations
    
    # Get existing provider
    try:
        provider = ServiceProvider.objects.first()
        if not provider:
            print("❌ No service provider found.")
            return
            
        print(f"📍 Enhancing provider: {provider.business_name}")
        
        # Select random location
        location = random.choice(all_locations)
        
        # Update provider with enhanced information
        provider.address = location["address"]
        provider.city = location["city"]
        provider.state = location["state"]
        provider.zip_code = location["zip_code"]
        provider.country = "Canada"
        provider.latitude = location["lat"]
        provider.longitude = location["lon"]
        
        # Add additional business information
        provider.business_description = "Professional beauty salon offering comprehensive hair, nail, and skincare services. Our experienced team provides personalized treatments in a relaxing, modern environment."
        provider.business_phone = "******-555-0123" if location["city"] == "Toronto" else "******-555-0123"
        provider.business_email = "<EMAIL>"
        provider.website = "https://testbeautysalon.com"
        provider.instagram_handle = "@testbeautysalon"
        provider.facebook_url = "https://facebook.com/testbeautysalon"
        
        # Set ratings and experience
        provider.rating = Decimal('4.7')
        provider.review_count = 127
        provider.total_bookings = 1543
        provider.years_of_experience = 8
        provider.is_verified = True
        provider.is_featured = True
        provider.mobile_optimized = True
        
        provider.save()
        
        print(f"✅ Enhanced provider with:")
        print(f"   📍 Location: {provider.address}, {provider.city}, {provider.state}")
        print(f"   🌐 Coordinates: {provider.latitude}, {provider.longitude}")
        print(f"   ⭐ Rating: {provider.rating} ({provider.review_count} reviews)")
        print(f"   📞 Phone: {provider.business_phone}")
        print(f"   🌐 Website: {provider.website}")
        print(f"   ✅ Verified: {provider.is_verified}")
        
    except Exception as e:
        print(f"❌ Error enhancing provider: {e}")
        return
    
    # Create additional categories for better testing
    print(f"\n📂 Creating additional service categories...")
    
    additional_categories = [
        {
            'name': 'Nail Services',
            'slug': 'nail-services',
            'description': 'Professional nail care and nail art services',
            'icon': 'nail-polish',
            'color': '#FF6B9D',
            'is_popular': True,
            'is_active': True,
            'sort_order': 2
        },
        {
            'name': 'Skincare & Facials',
            'slug': 'skincare-facials',
            'description': 'Rejuvenating skincare treatments and facial services',
            'icon': 'face-mask',
            'color': '#4ECDC4',
            'is_popular': True,
            'is_active': True,
            'sort_order': 3
        },
        {
            'name': 'Massage Therapy',
            'slug': 'massage-therapy',
            'description': 'Relaxing massage and wellness treatments',
            'icon': 'massage',
            'color': '#45B7D1',
            'is_popular': False,
            'is_active': True,
            'sort_order': 4
        }
    ]
    
    created_categories = []
    for cat_data in additional_categories:
        try:
            # Check if category already exists
            existing_cat = ServiceCategory.objects.filter(slug=cat_data['slug']).first()
            if existing_cat:
                print(f"⚠️  Category '{cat_data['name']}' already exists, skipping...")
                continue
                
            category = ServiceCategory.objects.create(**cat_data)
            created_categories.append(category)
            print(f"✅ Created category: {category.name}")
            
        except Exception as e:
            print(f"❌ Error creating category '{cat_data['name']}': {e}")
    
    print(f"\n🎉 Enhancement completed!")
    print(f"📊 Updated Service Catalog Summary:")
    print(f"   Categories: {ServiceCategory.objects.count()}")
    print(f"   Providers: {ServiceProvider.objects.count()}")
    print(f"   Services: {Service.objects.count()}")
    
    return provider, created_categories

if __name__ == '__main__':
    enhance_providers()
