# Authentication Testing Guide

**Version:** 1.0.0  
**Date:** August 6, 2025  
**Status:** Complete

## Overview

This document provides comprehensive information about the authentication testing suite for the Vierla application. The test suite covers login authentication fixes, error handling, and integration scenarios.

## Test Structure

### Test Files Organization

```
src/__tests__/
├── auth/
│   ├── apiClient.test.ts          # API client configuration tests
│   ├── authAPI.test.ts            # Authentication API service tests
│   ├── LoginScreen.test.tsx       # Login screen component tests
│   └── loginIntegration.test.ts   # End-to-end integration tests
├── error/
│   └── errorHandling.test.ts      # Error handling system tests
└── runAuthTests.js                # Test runner script
```

## Test Coverage

### 1. API Client Tests (`apiClient.test.ts`)

**Purpose:** Test API client configuration, interceptors, and token management

**Coverage:**
- ✅ Base URL configuration (************:8000)
- ✅ Request interceptor (authorization header injection)
- ✅ Response interceptor (token refresh on 401)
- ✅ Error handling for storage failures
- ✅ Timeout and header configuration

**Key Test Cases:**
```typescript
// Test authorization header injection
it('should add authorization header when token exists', async () => {
  // Verifies token is added to requests
});

// Test token refresh flow
it('should handle 401 errors with token refresh', async () => {
  // Verifies automatic token refresh on expiration
});
```

### 2. Authentication API Tests (`authAPI.test.ts`)

**Purpose:** Test all authentication API endpoints and data handling

**Coverage:**
- ✅ Login endpoint with correct data
- ✅ Registration endpoint
- ✅ Social authentication (Google/Apple)
- ✅ Profile management
- ✅ Password operations
- ✅ Email verification
- ✅ Error response handling

**Key Test Cases:**
```typescript
// Test login API call
it('should make POST request to login endpoint with correct data', async () => {
  // Verifies API call structure and response handling
});

// Test error handling
it('should handle login API errors', async () => {
  // Verifies error propagation and handling
});
```

### 3. Login Screen Tests (`LoginScreen.test.tsx`)

**Purpose:** Test login screen UI components and user interactions

**Coverage:**
- ✅ Component rendering
- ✅ Form validation (email, password)
- ✅ Login functionality
- ✅ Error handling and display
- ✅ Navigation flows
- ✅ Social authentication placeholders
- ✅ AsyncStorage integration

**Key Test Cases:**
```typescript
// Test form validation
it('should show error when email is invalid', async () => {
  // Verifies client-side validation
});

// Test successful login flow
it('should store tokens and user data on successful login', async () => {
  // Verifies complete login process
});
```

### 4. Integration Tests (`loginIntegration.test.ts`)

**Purpose:** Test end-to-end authentication flows

**Coverage:**
- ✅ Complete login flow with valid credentials
- ✅ Token storage and retrieval
- ✅ Error scenarios (400, 423, 500, network)
- ✅ Token refresh mechanism
- ✅ Backend connectivity verification

**Key Test Cases:**
```typescript
// Test complete login flow
it('should complete full login flow with valid credentials', async () => {
  // Verifies entire authentication process
});

// Test token refresh
it('should handle token refresh on 401 error', async () => {
  // Verifies automatic token refresh
});
```

### 5. Error Handling Tests (`errorHandling.test.ts`)

**Purpose:** Test standardized error handling system

**Coverage:**
- ✅ Error creation and classification
- ✅ Error message formatting
- ✅ Retry logic and exponential backoff
- ✅ Haptic feedback
- ✅ Error logging
- ✅ Error reporting debouncing

**Key Test Cases:**
```typescript
// Test error creation
it('should create AppError with correct properties', () => {
  // Verifies error object structure
});

// Test retry logic
it('should identify retryable errors', () => {
  // Verifies retry decision logic
});
```

## Running Tests

### Run All Authentication Tests

```bash
# From frontend directory
npm test

# Or run specific test suite
npm run test:auth
```

### Run Individual Test Files

```bash
# API Client tests
npx jest src/__tests__/auth/apiClient.test.ts

# Authentication API tests
npx jest src/__tests__/auth/authAPI.test.ts

# Login Screen tests
npx jest src/__tests__/auth/LoginScreen.test.tsx

# Integration tests
npx jest src/__tests__/auth/loginIntegration.test.ts

# Error handling tests
npx jest src/__tests__/error/errorHandling.test.ts
```

### Run Tests with Coverage

```bash
npx jest --coverage --collectCoverageFrom="src/**/*.{ts,tsx}"
```

### Use Test Runner Script

```bash
# Run all authentication tests
node src/__tests__/runAuthTests.js

# Run specific test
node src/__tests__/runAuthTests.js auth/LoginScreen.test.tsx
```

## Test Configuration

### Jest Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)'
  ],
  // ... other configuration
};
```

### Test Setup

The `setupTests.ts` file includes mocks for:
- React Native components
- AsyncStorage
- Navigation
- TanStack Query
- Expo Vector Icons
- Expo Haptics
- React Native Gesture Handler

## Test Data

### Valid Test Credentials

```typescript
const validCredentials = {
  email: '<EMAIL>',
  password: 'password123',
};

const customerCredentials = {
  email: '<EMAIL>',
  password: 'TestPass123!',
};

const demoCredentials = {
  email: '<EMAIL>',
  password: 'demo123',
};
```

### Mock Responses

```typescript
const mockAuthResponse = {
  access: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  refresh: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
  user: {
    id: '1',
    email: '<EMAIL>',
    username: 'testuser',
    first_name: 'Test',
    last_name: 'User',
    full_name: 'Test User',
    role: 'customer',
    is_verified: true,
    account_status: 'active',
    created_at: '2025-08-06T00:00:00Z',
  },
};
```

## Debugging Tests

### Common Issues

1. **Mock not working**: Ensure mocks are properly configured in `setupTests.ts`
2. **Async test failures**: Use `waitFor` for async operations
3. **Component not rendering**: Check if all dependencies are mocked
4. **Network test failures**: Verify fetch mocks are properly set up

### Debug Commands

```bash
# Run tests in debug mode
npx jest --detectOpenHandles --forceExit

# Run with verbose output
npx jest --verbose

# Run specific test with debugging
npx jest src/__tests__/auth/LoginScreen.test.tsx --verbose --no-cache
```

## Test Maintenance

### Adding New Tests

1. Create test file in appropriate directory
2. Follow existing naming conventions
3. Include comprehensive test cases
4. Update this documentation
5. Add to test runner script if needed

### Updating Tests

1. Keep tests in sync with component changes
2. Update mocks when dependencies change
3. Maintain test data consistency
4. Review coverage reports regularly

## Success Criteria

### Test Passing Requirements

- ✅ All API client tests pass
- ✅ All authentication API tests pass
- ✅ All login screen component tests pass
- ✅ All integration tests pass
- ✅ All error handling tests pass
- ✅ Code coverage > 80%
- ✅ No console errors during test execution

### Performance Requirements

- ✅ Test suite completes in < 30 seconds
- ✅ Individual test files complete in < 10 seconds
- ✅ No memory leaks in test execution

## Continuous Integration

### GitHub Actions Integration

```yaml
# .github/workflows/test.yml
name: Run Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run test:auth
```

## Next Steps

1. **Expand Test Coverage**: Add tests for additional authentication scenarios
2. **E2E Testing**: Implement Detox or similar for full app testing
3. **Performance Testing**: Add tests for authentication performance
4. **Security Testing**: Add tests for security vulnerabilities
5. **Accessibility Testing**: Add tests for screen reader compatibility

---

**Note**: This testing suite ensures the reliability and robustness of the authentication system fixes implemented for the Vierla application.
