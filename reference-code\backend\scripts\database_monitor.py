#!/usr/bin/env python
"""
Database Monitoring Script for Vierla Backend
Monitors database connections, sessions, and key metrics
"""
import os
import sys
import time
import django
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.db import connection


def get_database_stats():
    """Get current database statistics"""
    try:
        with connection.cursor() as cursor:
            stats = {}
            
            # Get session count
            cursor.execute("SELECT COUNT(*) FROM django_session")
            stats['sessions'] = cursor.fetchone()[0]
            
            # Get user count
            cursor.execute("SELECT COUNT(*) FROM users")
            stats['users'] = cursor.fetchone()[0]
            
            # Get service provider count
            cursor.execute("SELECT COUNT(*) FROM catalog_service_providers")
            stats['providers'] = cursor.fetchone()[0]
            
            # Get booking count
            cursor.execute("SELECT COUNT(*) FROM bookings_booking")
            stats['bookings'] = cursor.fetchone()[0]
            
            # Get service count
            cursor.execute("SELECT COUNT(*) FROM catalog_services")
            stats['services'] = cursor.fetchone()[0]
            
            # Get category count
            cursor.execute("SELECT COUNT(*) FROM catalog_service_categories")
            stats['categories'] = cursor.fetchone()[0]
            
            return stats
    except Exception as e:
        return {'error': str(e)}


def monitor_database():
    """Main monitoring loop"""
    print("=== VIERLA DATABASE MONITORING TERMINAL ===")
    print("Monitoring database metrics every 30 seconds...")
    print("Press Ctrl+C to stop\n")
    
    while True:
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            stats = get_database_stats()
            
            if 'error' in stats:
                print(f"[{timestamp}] Database error: {stats['error']}")
            else:
                print(f"[{timestamp}] Sessions: {stats['sessions']}, Users: {stats['users']}, "
                      f"Providers: {stats['providers']}, Bookings: {stats['bookings']}, "
                      f"Services: {stats['services']}, Categories: {stats['categories']}")
            
            time.sleep(30)
            
        except KeyboardInterrupt:
            print("\nDatabase monitoring stopped.")
            break
        except Exception as e:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] Monitoring error: {e}")
            time.sleep(30)


if __name__ == "__main__":
    monitor_database()
