#!/usr/bin/env python
"""
<PERSON>ript to create sample services for the Service Catalog system
"""
import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.authentication.models import User

def create_sample_services():
    """Create sample services for testing the Service Catalog system"""
    
    print("🎨 Creating Sample Services for Service Catalog")
    print("=" * 50)
    
    # Get existing provider and category
    try:
        provider = ServiceProvider.objects.first()
        category = ServiceCategory.objects.first()
        
        if not provider:
            print("❌ No service provider found. Please create a provider first.")
            return
            
        if not category:
            print("❌ No service category found. Please create a category first.")
            return
            
        print(f"📍 Using provider: {provider.business_name}")
        print(f"📂 Using category: {category.name}")
        
    except Exception as e:
        print(f"❌ Error getting provider/category: {e}")
        return
    
    # Define services data
    services_data = [
        {
            'name': 'Hair Cut & Style',
            'description': 'Professional haircut with styling. Includes consultation, wash, cut, and blow-dry.',
            'short_description': 'Professional haircut with styling',
            'mobile_description': 'Haircut + styling',
            'base_price': Decimal('45.00'),
            'max_price': Decimal('65.00'),
            'price_type': 'range',
            'duration': 60,
            'buffer_time': 15,
            'requirements': 'Please arrive with clean hair',
            'preparation_instructions': 'Wash hair before appointment',
            'is_popular': True,
            'is_available': True,
            'is_active': True
        },
        {
            'name': 'Hair Color & Highlights',
            'description': 'Full hair coloring service with highlights. Includes color consultation, application, and styling.',
            'short_description': 'Hair coloring with highlights',
            'mobile_description': 'Color + highlights',
            'base_price': Decimal('120.00'),
            'max_price': Decimal('180.00'),
            'price_type': 'range',
            'duration': 180,
            'buffer_time': 30,
            'requirements': 'Patch test required 48 hours before',
            'preparation_instructions': 'Do not wash hair 24 hours before appointment',
            'is_popular': True,
            'is_available': True,
            'is_active': True
        },
        {
            'name': 'Facial Treatment',
            'description': 'Relaxing facial treatment with deep cleansing, exfoliation, and moisturizing.',
            'short_description': 'Deep cleansing facial treatment',
            'mobile_description': 'Facial treatment',
            'base_price': Decimal('75.00'),
            'price_type': 'fixed',
            'duration': 90,
            'buffer_time': 15,
            'requirements': 'No makeup on arrival',
            'preparation_instructions': 'Remove all makeup before appointment',
            'is_popular': False,
            'is_available': True,
            'is_active': True
        },
        {
            'name': 'Manicure & Pedicure',
            'description': 'Complete nail care service including manicure and pedicure with polish.',
            'short_description': 'Full manicure and pedicure service',
            'mobile_description': 'Mani + Pedi',
            'base_price': Decimal('55.00'),
            'price_type': 'fixed',
            'duration': 75,
            'buffer_time': 15,
            'requirements': 'No nail polish on arrival',
            'preparation_instructions': 'Remove existing nail polish',
            'is_popular': True,
            'is_available': True,
            'is_active': True
        },
        {
            'name': 'Eyebrow Shaping',
            'description': 'Professional eyebrow shaping and grooming service.',
            'short_description': 'Eyebrow shaping and grooming',
            'mobile_description': 'Eyebrow shaping',
            'base_price': Decimal('25.00'),
            'price_type': 'fixed',
            'duration': 30,
            'buffer_time': 10,
            'requirements': 'No recent eyebrow treatments',
            'preparation_instructions': 'Avoid plucking for 2 weeks before',
            'is_popular': False,
            'is_available': True,
            'is_active': True
        }
    ]
    
    # Create services
    created_services = []
    print(f"\n🔨 Creating {len(services_data)} services...")
    
    for service_data in services_data:
        try:
            # Check if service already exists
            existing_service = Service.objects.filter(
                provider=provider,
                name=service_data['name']
            ).first()
            
            if existing_service:
                print(f"⚠️  Service '{service_data['name']}' already exists, skipping...")
                continue
            
            # Create the service
            service = Service.objects.create(
                provider=provider,
                category=category,
                **service_data
            )
            created_services.append(service)
            print(f"✅ Created: {service.name} - ${service.base_price}")
            
        except Exception as e:
            print(f"❌ Error creating service '{service_data['name']}': {e}")
    
    print(f"\n🎉 Successfully created {len(created_services)} services!")
    
    # Display summary
    print(f"\n📊 Service Catalog Summary:")
    print(f"   Categories: {ServiceCategory.objects.count()}")
    print(f"   Providers: {ServiceProvider.objects.count()}")
    print(f"   Services: {Service.objects.count()}")
    
    return created_services

if __name__ == '__main__':
    create_sample_services()
