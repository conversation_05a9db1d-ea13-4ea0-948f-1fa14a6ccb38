"""
API v1 URL Configuration - Enhanced based on Backend Agent feedback
Role-based API endpoints with proper versioning and organization
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import customer, provider, shared

# Create separate routers for different user roles
customer_router = DefaultRouter()
provider_router = DefaultRouter()
shared_router = DefaultRouter()

# Register customer-specific viewsets
customer_router.register(r'services', customer.views.CustomerServiceViewSet, basename='customer-services')
customer_router.register(r'bookings', customer.views.CustomerBookingViewSet, basename='customer-bookings')
customer_router.register(r'search', customer.views.CustomerSearchViewSet, basename='customer-search')
customer_router.register(r'favorites', customer.views.CustomerFavoriteViewSet, basename='customer-favorites')

# Register provider-specific viewsets
provider_router.register(r'dashboard', provider.views.ProviderDashboardViewSet, basename='provider-dashboard')
provider_router.register(r'store', provider.views.ProviderStoreViewSet, basename='provider-store')
provider_router.register(r'bookings', provider.views.ProviderBookingViewSet, basename='provider-bookings')
provider_router.register(r'analytics', provider.views.ProviderAnalyticsViewSet, basename='provider-analytics')

# Register shared viewsets (available to all authenticated users)
shared_router.register(r'auth', shared.views.AuthViewSet, basename='shared-auth')
shared_router.register(r'notifications', shared.views.NotificationViewSet, basename='shared-notifications')
shared_router.register(r'messaging', shared.views.MessagingViewSet, basename='shared-messaging')

app_name = 'api_v1'

urlpatterns = [
    # Customer endpoints - consolidated router and custom URLs
    path('customer/', include([
        *customer_router.urls,
        *[path('', include('api.v1.customer.urls'))],
    ])),

    # Provider endpoints - consolidated router and custom URLs
    path('provider/', include([
        *provider_router.urls,
        *[path('', include('api.v1.provider.urls'))],
    ])),

    # Shared endpoints - consolidated router and custom URLs
    path('shared/', include([
        *shared_router.urls,
        *[path('', include('api.v1.shared.urls'))],
    ])),
]
