/**
 * Debug test to isolate component rendering issues
 */

import React from 'react';
import { View, Text as RNText } from 'react-native';
import { render } from '@testing-library/react-native';

// Test basic React Native components first
describe('Component Debug Tests', () => {
  it('should render basic React Native components', () => {
    const TestComponent = () => (
      <View testID="test-view">
        <RNText testID="test-text">Hello World</RNText>
      </View>
    );

    const { getByText, getAllByText, debug, root } = render(<TestComponent />);

    // Debug to see if basic RN components work
    debug();

    // Try different approaches
    console.log('Root innerHTML:', root.innerHTML);

    try {
      const elements = getAllByText('Hello World');
      console.log('Found elements:', elements.length);
      expect(elements.length).toBeGreaterThan(0);
    } catch (error) {
      console.log('getAllByText failed:', error.message);
      // Just check that something rendered
      expect(root).toBeTruthy();
    }
  });

  it('should render our Text component', () => {
    const { Text } = require('../../components/ui');

    const TestComponent = () => (
      <View>
        <Text testID="custom-text">Hello from our Text component</Text>
      </View>
    );

    const { getByTestId, debug } = render(<TestComponent />);

    // Debug the rendered output to see what's happening
    debug();

    expect(getByTestId('custom-text')).toBeTruthy();
  });

  it('should render our Button component', () => {
    const { Button } = require('../../components/ui');
    
    const TestComponent = () => (
      <View>
        <Button onPress={() => {}}>Test Button</Button>
      </View>
    );

    const { getByText } = render(<TestComponent />);
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('should render our Card component', () => {
    const { Card } = require('../../components/ui');
    
    const TestComponent = () => (
      <View>
        <Card>
          <RNText>Card content</RNText>
        </Card>
      </View>
    );

    const { getByText } = render(<TestComponent />);
    expect(getByText('Card content')).toBeTruthy();
  });

  it('should render SocialButton component', () => {
    const { SocialButton } = require('../../components');
    
    const TestComponent = () => (
      <View>
        <SocialButton provider="google" onPress={() => {}} />
      </View>
    );

    render(<TestComponent />);
    // Just check it renders without error
  });
});
