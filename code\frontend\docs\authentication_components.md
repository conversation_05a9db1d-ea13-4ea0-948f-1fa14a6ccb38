# Frontend Authentication Components Documentation

## Overview

The Vierla React Native app provides a complete authentication system with modern UI components, navigation flow, and API integration.

## Architecture

### Navigation Structure

```
AppNavigator (Root)
├── AuthNavigator (Unauthenticated)
│   ├── LoginScreen
│   └── RegisterScreen
└── MainNavigator (Authenticated)
    ├── HomeScreen
    ├── ServicesScreen
    ├── BookingsScreen
    └── ProfileScreen
```

### State Management

- **Authentication State**: Managed via AsyncStorage and React Query
- **Navigation State**: Handled by React Navigation
- **API State**: Managed by TanStack Query (React Query)

## Core Components

### Button Component

Reusable button component with multiple variants and states.

**Props:**
```typescript
interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}
```

**Usage:**
```tsx
<Button
  title="Sign In"
  onPress={handleLogin}
  loading={isLoading}
  variant="primary"
/>
```

### Input Component

Text input component with validation, icons, and error states.

**Props:**
```typescript
interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  secureTextEntry?: boolean;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
}
```

**Usage:**
```tsx
<Input
  label="Email"
  value={email}
  onChangeText={setEmail}
  placeholder="Enter your email"
  keyboardType="email-address"
  leftIcon="mail"
  error={errors.email}
/>
```

### Text Component

Typography component with consistent styling and variants.

**Props:**
```typescript
interface TextProps extends RNTextProps {
  variant?: 'heading1' | 'heading2' | 'heading3' | 'body' | 'caption' | 'button';
  color?: 'primary' | 'secondary' | 'accent' | 'error' | 'success' | 'warning';
  align?: 'left' | 'center' | 'right';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
}
```

**Usage:**
```tsx
<Text variant="heading1" align="center">
  Welcome Back
</Text>
```

### SocialButton Component

Specialized button for social authentication providers.

**Props:**
```typescript
interface SocialButtonProps {
  provider: 'google' | 'apple';
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
}
```

**Usage:**
```tsx
<SocialButton
  provider="google"
  onPress={handleGoogleSignIn}
  loading={socialLoading.google}
/>
```

## Screens

### LoginScreen

User authentication screen with email/password and social login options.

**Features:**
- Email/password validation
- Social authentication buttons (Google, Apple)
- Loading states and error handling
- Navigation to registration and password reset
- Automatic token storage and navigation

**Key Functions:**
- `handleLogin()`: Email/password authentication
- `handleGoogleSignIn()`: Google OAuth flow
- `handleAppleSignIn()`: Apple Sign-In flow
- `validateForm()`: Client-side validation

### RegisterScreen

User registration screen with comprehensive form validation.

**Features:**
- Multi-field registration form
- Real-time validation
- Social registration options
- Password confirmation
- Automatic account creation and login

**Key Functions:**
- `handleRegister()`: Account creation
- `validateForm()`: Form validation
- `updateFormData()`: Form state management

### LoadingScreen

Displays while checking authentication status on app startup.

**Features:**
- Loading indicator
- Smooth transition to appropriate screen
- Error handling for auth state checks

## API Integration

### API Client Configuration

```typescript
// Base configuration
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:8000/api'
  : 'https://api.vierla.com/api';

// Automatic token injection
apiClient.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Automatic token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Attempt token refresh
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      // ... refresh logic
    }
    return Promise.reject(error);
  }
);
```

### Authentication API Service

```typescript
export const authAPI = {
  login: (data: LoginRequest) => Promise<AuthResponse>,
  register: (data: RegisterRequest) => Promise<AuthResponse>,
  socialAuth: (data: SocialAuthRequest) => Promise<AuthResponse>,
  logout: (refreshToken: string) => Promise<void>,
  getProfile: () => Promise<User>,
  updateProfile: (data: Partial<User>) => Promise<User>,
  // ... other methods
};
```

## Authentication Flow

### 1. App Startup
1. Check for stored access token
2. Validate token with backend
3. Navigate to appropriate screen (Auth/Main)

### 2. Login Process
1. User enters credentials
2. Client-side validation
3. API call to `/auth/login/`
4. Store tokens in AsyncStorage
5. Navigate to main app

### 3. Registration Process
1. User fills registration form
2. Real-time validation
3. API call to `/auth/register/`
4. Automatic login after registration
5. Navigate to main app

### 4. Social Authentication
1. User taps social button
2. Open provider's authentication flow
3. Receive identity token
4. Send token to `/auth/social/`
5. Store tokens and navigate

### 5. Token Management
1. Automatic token injection in requests
2. Token refresh on 401 responses
3. Logout clears all stored data

## Error Handling

### Validation Errors
- Real-time form validation
- Field-specific error messages
- Visual error indicators

### API Errors
- Network error handling
- Server error messages
- User-friendly error alerts

### Authentication Errors
- Invalid credentials
- Account lockout
- Token expiration

## Security Considerations

### Token Storage
- Secure storage using AsyncStorage
- Automatic cleanup on logout
- Token refresh mechanism

### Input Validation
- Client-side validation for UX
- Server-side validation for security
- Sanitization of user inputs

### Social Authentication
- Token verification on backend
- Secure provider integration
- User consent handling

## Testing

### Unit Tests
- Component rendering
- Form validation logic
- API service functions

### Integration Tests
- Authentication flow
- Navigation behavior
- API integration

### E2E Tests
- Complete user journeys
- Cross-platform compatibility
- Error scenarios

## Future Enhancements

### Planned Features
- Biometric authentication
- Multi-factor authentication
- Social provider expansion
- Enhanced security features

### Technical Improvements
- Offline authentication
- Enhanced error recovery
- Performance optimizations
- Accessibility improvements
