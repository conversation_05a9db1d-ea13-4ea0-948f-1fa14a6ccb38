#!/usr/bin/env python
"""
Test Categories API Integration
Tests the categories API endpoint to ensure it's working correctly
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

def test_categories_api():
    """Test the categories API endpoint"""
    print("🧪 Testing Categories API Integration")
    print("=" * 50)
    
    # Test the API endpoint
    api_url = "http://192.168.2.65:8000/api/catalog/categories/"
    
    try:
        print(f"📡 Making request to: {api_url}")
        response = requests.get(api_url, headers={
            'Accept': 'application/json',
            'User-Agent': 'Vierla-Frontend-Test/1.0'
        }, timeout=10)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response successful!")
            print(f"📈 Total categories: {data.get('count', 0)}")
            
            # Display first few categories
            results = data.get('results', [])
            if results:
                print(f"📋 First 5 categories:")
                for i, category in enumerate(results[:5]):
                    print(f"   {i+1}. {category.get('name')} ({category.get('icon', 'No icon')})")
                    print(f"      - Color: {category.get('color', 'No color')}")
                    print(f"      - Popular: {category.get('is_popular', False)}")
                    print(f"      - Service Count: {category.get('service_count', 0)}")
                    print()
            
            # Test frontend compatibility
            print("🔧 Testing Frontend Compatibility:")
            required_fields = ['id', 'name', 'slug', 'icon', 'color', 'is_popular', 'service_count']
            
            if results:
                first_category = results[0]
                missing_fields = []
                for field in required_fields:
                    if field not in first_category:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"❌ Missing required fields: {missing_fields}")
                else:
                    print(f"✅ All required fields present")
                
                # Check data types
                print("🔍 Field Type Validation:")
                print(f"   - id: {type(first_category.get('id')).__name__}")
                print(f"   - name: {type(first_category.get('name')).__name__}")
                print(f"   - is_popular: {type(first_category.get('is_popular')).__name__}")
                print(f"   - service_count: {type(first_category.get('service_count')).__name__}")
            
            return True
            
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_database_direct():
    """Test database access directly"""
    print("\n🗄️ Testing Database Direct Access")
    print("=" * 50)
    
    try:
        from apps.catalog.models import ServiceCategory
        
        categories = ServiceCategory.objects.all().order_by('sort_order')
        total_count = categories.count()
        
        print(f"📊 Total categories in database: {total_count}")
        
        if total_count > 0:
            print("📋 Categories in database:")
            for category in categories[:10]:  # Show first 10
                print(f"   - {category.name} ({category.icon}) - Popular: {category.is_popular}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Vierla Categories Integration Test")
    print("=" * 60)
    
    # Test database first
    db_success = test_database_direct()
    
    # Test API
    api_success = test_categories_api()
    
    print("\n📊 Test Summary")
    print("=" * 60)
    print(f"Database Access: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"API Endpoint: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if db_success and api_success:
        print("\n🎉 All tests passed! Categories integration is working correctly.")
        return True
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
