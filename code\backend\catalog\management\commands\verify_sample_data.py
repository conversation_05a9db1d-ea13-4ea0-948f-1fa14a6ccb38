"""
Django management command to verify sample data integrity
Checks data consistency, relationships, and business logic compliance
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db.models import Count, Avg, <PERSON>, Max, Q
from django.db import models

from catalog.models import ServiceCategory, ServiceProvider, Service

User = get_user_model()


class Command(BaseCommand):
    help = 'Verify sample data integrity and consistency'

    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed verification results',
        )
        parser.add_argument(
            '--fix-issues',
            action='store_true',
            help='Attempt to fix found issues automatically',
        )

    def handle(self, *args, **options):
        self.detailed = options['detailed']
        self.fix_issues = options['fix_issues']

        self.stdout.write(
            self.style.SUCCESS('🔍 Verifying sample data integrity...')
        )

        # Run verification checks
        issues = []
        issues.extend(self.verify_users())
        issues.extend(self.verify_categories())
        issues.extend(self.verify_providers())
        issues.extend(self.verify_services())
        issues.extend(self.verify_relationships())
        issues.extend(self.verify_business_logic())

        # Print results
        self.print_verification_results(issues)

        if issues and self.fix_issues:
            self.attempt_fixes(issues)

    def verify_users(self):
        """Verify user accounts"""
        self.stdout.write('\n👤 Verifying user accounts...')
        issues = []

        # Check test account flags
        test_users = User.objects.filter(is_test_account=True)
        non_test_users = User.objects.filter(is_test_account=False)
        
        if self.detailed:
            self.stdout.write(f'   📊 Test users: {test_users.count()}')
            self.stdout.write(f'   📊 Non-test users: {non_test_users.count()}')

        # Check for users without proper email verification
        unverified_test_users = test_users.filter(is_verified=False)
        if unverified_test_users.exists():
            issues.append({
                'type': 'user_verification',
                'severity': 'warning',
                'message': f'{unverified_test_users.count()} test users are not verified',
                'count': unverified_test_users.count()
            })

        # Check for duplicate emails
        duplicate_emails = User.objects.values('email').annotate(
            count=Count('email')
        ).filter(count__gt=1)
        
        if duplicate_emails.exists():
            issues.append({
                'type': 'duplicate_emails',
                'severity': 'error',
                'message': f'{duplicate_emails.count()} duplicate email addresses found',
                'count': duplicate_emails.count()
            })

        # Check role distribution
        customers = test_users.filter(role='customer').count()
        providers = test_users.filter(role='service_provider').count()
        
        if customers == 0:
            issues.append({
                'type': 'no_customers',
                'severity': 'error',
                'message': 'No customer accounts found'
            })
        
        if providers == 0:
            issues.append({
                'type': 'no_providers',
                'severity': 'error',
                'message': 'No provider accounts found'
            })

        if not issues:
            self.stdout.write('   ✅ User accounts verification passed')

        return issues

    def verify_categories(self):
        """Verify service categories"""
        self.stdout.write('\n📂 Verifying service categories...')
        issues = []

        categories = ServiceCategory.objects.all()
        active_categories = categories.filter(is_active=True)
        
        if self.detailed:
            self.stdout.write(f'   📊 Total categories: {categories.count()}')
            self.stdout.write(f'   📊 Active categories: {active_categories.count()}')

        if categories.count() < 3:
            issues.append({
                'type': 'insufficient_categories',
                'severity': 'warning',
                'message': f'Only {categories.count()} categories found (recommended: at least 3)'
            })

        if active_categories.count() == 0:
            issues.append({
                'type': 'no_active_categories',
                'severity': 'error',
                'message': 'No active categories found'
            })

        # Check for categories without services
        categories_without_services = active_categories.annotate(
            service_count=Count('services')
        ).filter(service_count=0)
        
        if categories_without_services.exists():
            issues.append({
                'type': 'empty_categories',
                'severity': 'warning',
                'message': f'{categories_without_services.count()} categories have no services',
                'count': categories_without_services.count()
            })

        if not issues:
            self.stdout.write('   ✅ Categories verification passed')

        return issues

    def verify_providers(self):
        """Verify service providers"""
        self.stdout.write('\n🏢 Verifying service providers...')
        issues = []

        providers = ServiceProvider.objects.all()
        active_providers = providers.filter(is_active=True)
        
        if self.detailed:
            self.stdout.write(f'   📊 Total providers: {providers.count()}')
            self.stdout.write(f'   📊 Active providers: {active_providers.count()}')

        if providers.count() == 0:
            issues.append({
                'type': 'no_providers',
                'severity': 'error',
                'message': 'No service providers found'
            })
            return issues

        # Check for providers without categories
        providers_without_categories = providers.annotate(
            category_count=Count('categories')
        ).filter(category_count=0)
        
        if providers_without_categories.exists():
            issues.append({
                'type': 'providers_without_categories',
                'severity': 'error',
                'message': f'{providers_without_categories.count()} providers have no categories',
                'count': providers_without_categories.count()
            })

        # Check for providers without services
        providers_without_services = providers.annotate(
            service_count=Count('services')
        ).filter(service_count=0)
        
        if providers_without_services.exists():
            issues.append({
                'type': 'providers_without_services',
                'severity': 'warning',
                'message': f'{providers_without_services.count()} providers have no services',
                'count': providers_without_services.count()
            })

        # Check rating ranges
        invalid_ratings = providers.filter(rating__lt=0).union(
            providers.filter(rating__gt=5)
        )
        
        if invalid_ratings.exists():
            issues.append({
                'type': 'invalid_ratings',
                'severity': 'error',
                'message': f'{invalid_ratings.count()} providers have invalid ratings',
                'count': invalid_ratings.count()
            })

        if not issues:
            self.stdout.write('   ✅ Providers verification passed')

        return issues

    def verify_services(self):
        """Verify services"""
        self.stdout.write('\n🛍️  Verifying services...')
        issues = []

        services = Service.objects.all()
        active_services = services.filter(is_active=True)
        
        if self.detailed:
            self.stdout.write(f'   📊 Total services: {services.count()}')
            self.stdout.write(f'   📊 Active services: {active_services.count()}')

        if services.count() == 0:
            issues.append({
                'type': 'no_services',
                'severity': 'error',
                'message': 'No services found'
            })
            return issues

        # Check for invalid prices
        invalid_prices = services.filter(base_price__lte=0)
        if invalid_prices.exists():
            issues.append({
                'type': 'invalid_prices',
                'severity': 'error',
                'message': f'{invalid_prices.count()} services have invalid prices',
                'count': invalid_prices.count()
            })

        # Check for invalid durations
        invalid_durations = services.filter(duration__lte=0)
        if invalid_durations.exists():
            issues.append({
                'type': 'invalid_durations',
                'severity': 'error',
                'message': f'{invalid_durations.count()} services have invalid durations',
                'count': invalid_durations.count()
            })

        # Check for services with range pricing but no max_price
        invalid_range_pricing = services.filter(
            price_type='range',
            max_price__isnull=True
        )
        if invalid_range_pricing.exists():
            issues.append({
                'type': 'invalid_range_pricing',
                'severity': 'error',
                'message': f'{invalid_range_pricing.count()} services have range pricing without max_price',
                'count': invalid_range_pricing.count()
            })

        if not issues:
            self.stdout.write('   ✅ Services verification passed')

        return issues

    def verify_relationships(self):
        """Verify data relationships"""
        self.stdout.write('\n🔗 Verifying data relationships...')
        issues = []

        # Check for orphaned services (services without providers)
        orphaned_services = Service.objects.filter(provider__isnull=True)
        if orphaned_services.exists():
            issues.append({
                'type': 'orphaned_services',
                'severity': 'error',
                'message': f'{orphaned_services.count()} services without providers',
                'count': orphaned_services.count()
            })

        # Check for services with inactive categories
        services_inactive_categories = Service.objects.filter(
            category__is_active=False
        )
        if services_inactive_categories.exists():
            issues.append({
                'type': 'services_inactive_categories',
                'severity': 'warning',
                'message': f'{services_inactive_categories.count()} services in inactive categories',
                'count': services_inactive_categories.count()
            })

        if not issues:
            self.stdout.write('   ✅ Relationships verification passed')

        return issues

    def verify_business_logic(self):
        """Verify business logic compliance"""
        self.stdout.write('\n💼 Verifying business logic...')
        issues = []

        # Check for unverified providers with too many services
        unverified_providers_many_services = ServiceProvider.objects.filter(
            is_verified=False
        ).annotate(
            service_count=Count('services', filter=models.Q(services__is_active=True))
        ).filter(service_count__gt=3)
        
        if unverified_providers_many_services.exists():
            issues.append({
                'type': 'unverified_provider_service_limit',
                'severity': 'error',
                'message': f'{unverified_providers_many_services.count()} unverified providers exceed service limit',
                'count': unverified_providers_many_services.count()
            })

        if not issues:
            self.stdout.write('   ✅ Business logic verification passed')

        return issues

    def print_verification_results(self, issues):
        """Print verification results"""
        self.stdout.write('\n' + '='*60)
        
        if not issues:
            self.stdout.write(
                self.style.SUCCESS('🎉 DATA VERIFICATION PASSED')
            )
            self.stdout.write('✅ All checks passed - data integrity confirmed')
        else:
            error_count = len([i for i in issues if i['severity'] == 'error'])
            warning_count = len([i for i in issues if i['severity'] == 'warning'])
            
            self.stdout.write(
                self.style.WARNING('⚠️  DATA VERIFICATION ISSUES FOUND')
            )
            self.stdout.write(f'❌ Errors: {error_count}')
            self.stdout.write(f'⚠️  Warnings: {warning_count}')
            
            self.stdout.write('\n📋 Issues Found:')
            for issue in issues:
                severity_icon = '❌' if issue['severity'] == 'error' else '⚠️ '
                self.stdout.write(f'   {severity_icon} {issue["message"]}')

        self.stdout.write('='*60)

    def attempt_fixes(self, issues):
        """Attempt to fix found issues automatically"""
        self.stdout.write('\n🔧 Attempting to fix issues...')
        
        fixed_count = 0
        for issue in issues:
            if issue['type'] == 'user_verification':
                # Fix unverified test users
                from django.utils import timezone
                User.objects.filter(is_test_account=True, is_verified=False).update(
                    is_verified=True,
                    email_verified_at=timezone.now()
                )
                fixed_count += 1
                self.stdout.write('   ✅ Fixed user verification issues')
            
            # Add more fix implementations as needed
        
        self.stdout.write(f'🔧 Fixed {fixed_count} issues automatically')
