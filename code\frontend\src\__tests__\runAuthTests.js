/**
 * Authentication Test Runner
 * Script to run all authentication-related tests
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Authentication Tests for Vierla Application');
console.log('=' .repeat(60));

const testFiles = [
  'auth/apiClient.test.ts',
  'auth/authAPI.test.ts', 
  'auth/LoginScreen.test.tsx',
  'auth/loginIntegration.test.ts',
  'error/errorHandling.test.ts',
];

const runTest = (testFile) => {
  console.log(`\n📋 Running: ${testFile}`);
  console.log('-'.repeat(40));
  
  try {
    const result = execSync(
      `npx jest src/__tests__/${testFile} --verbose --coverage --collectCoverageFrom="src/**/*.{ts,tsx}"`,
      { 
        cwd: path.resolve(__dirname, '../../..'),
        stdio: 'inherit',
        encoding: 'utf8'
      }
    );
    
    console.log(`✅ ${testFile} - PASSED`);
    return true;
  } catch (error) {
    console.log(`❌ ${testFile} - FAILED`);
    console.error(error.message);
    return false;
  }
};

const runAllTests = () => {
  console.log('\n🚀 Starting test execution...\n');
  
  let passedTests = 0;
  let failedTests = 0;
  
  testFiles.forEach(testFile => {
    if (runTest(testFile)) {
      passedTests++;
    } else {
      failedTests++;
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📁 Total: ${testFiles.length}`);
  
  if (failedTests === 0) {
    console.log('\n🎉 All authentication tests passed!');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please review the output above.');
    process.exit(1);
  }
};

// Run specific test if provided as argument
const specificTest = process.argv[2];
if (specificTest) {
  if (testFiles.includes(specificTest)) {
    console.log(`\n🎯 Running specific test: ${specificTest}\n`);
    runTest(specificTest);
  } else {
    console.log(`\n❌ Test file not found: ${specificTest}`);
    console.log('\nAvailable tests:');
    testFiles.forEach(file => console.log(`  - ${file}`));
    process.exit(1);
  }
} else {
  runAllTests();
}
