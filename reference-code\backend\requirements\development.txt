# <PERSON><PERSON><PERSON> - Development Requirements
-r base.txt

# Development Tools
django-debug-toolbar==4.2.0
django-extensions==3.2.3
ipython==8.18.1
ipdb==0.13.13

# Testing Framework
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
factory-boy==3.3.0
faker==20.1.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
django-stubs==4.2.7
bandit==1.7.5

# Performance Testing
locust==2.17.0

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Environment Management
python-dotenv==1.0.0
