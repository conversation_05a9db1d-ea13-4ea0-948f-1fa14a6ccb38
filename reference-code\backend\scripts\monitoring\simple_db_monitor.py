#!/usr/bin/env python
"""
Simple Database Monitoring Script for Vierla Backend
"""

import os
import sys
import time
import sqlite3
from datetime import datetime
from pathlib import Path


def get_db_stats():
    """Get basic database statistics"""
    # Get the correct path to the database file
    script_dir = Path(__file__).parent
    backend_dir = script_dir.parent.parent
    db_path = backend_dir / "db.sqlite3"

    if not db_path.exists():
        return {"error": "Database file not found"}

    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()

        stats = {}

        # Get table counts
        tables = [
            ('users', 'Users'),
            ('user_profiles', 'User Profiles'),
            ('catalog_service_categories', 'Service Categories'),
            ('catalog_service_providers', 'Service Providers'),
            ('catalog_services', 'Services'),
            ('bookings_booking', 'Bookings')
        ]

        for table_name, display_name in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                stats[display_name] = count
            except sqlite3.OperationalError:
                stats[display_name] = "N/A"

        # Get database size
        stats['Database Size (MB)'] = round(
            db_path.stat().st_size / (1024 * 1024), 2)

        conn.close()
        return stats

    except Exception as e:
        return {"error": str(e)}


def monitor_database():
    """Main monitoring loop"""
    print("🔍 Vierla Database Monitor Started")
    print("=" * 50)

    while True:
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            stats = get_db_stats()

            print(f"\n📊 Database Status - {timestamp}")
            print("-" * 40)

            if "error" in stats:
                print(f"❌ Error: {stats['error']}")
            else:
                for key, value in stats.items():
                    print(f"📋 {key}: {value}")

            time.sleep(30)  # Update every 30 seconds

        except KeyboardInterrupt:
            print("\n\n🛑 Database monitoring stopped")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            time.sleep(5)


if __name__ == "__main__":
    monitor_database()
