/**
 * Details Step Component
 * Third step of multi-step service creation form
 */

import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../../theme';
import { FormData, FormErrors } from '../MultiStepServiceForm';

interface DetailsStepProps {
  formData: FormData;
  errors: FormErrors;
  onUpdateFormData: (field: keyof FormData, value: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export const DetailsStep: React.FC<DetailsStepProps> = ({
  formData,
  errors,
  onUpdateFormData,
  onNext,
  onPrevious,
  isFirstStep,
}) => {
  const renderInput = (
    label: string,
    field: keyof FormData,
    placeholder: string,
    options?: {
      multiline?: boolean;
      maxLength?: number;
      keyboardType?: 'default' | 'numeric';
      required?: boolean;
    }
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>
        {label}
        {options?.required && <Text style={styles.required}> *</Text>}
      </Text>
      <TextInput
        style={[
          styles.input,
          options?.multiline && styles.textArea,
          errors[field] && styles.inputError,
        ]}
        value={formData[field] as string}
        onChangeText={(value) => onUpdateFormData(field, value)}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline={options?.multiline}
        numberOfLines={options?.multiline ? 6 : 1}
        maxLength={options?.maxLength}
        keyboardType={options?.keyboardType || 'default'}
        textAlignVertical={options?.multiline ? 'top' : 'center'}
      />
      {errors[field] && (
        <Text style={styles.errorText}>{errors[field]}</Text>
      )}
      {options?.maxLength && (
        <Text style={styles.characterCount}>
          {(formData[field] as string).length}/{options.maxLength}
        </Text>
      )}
    </View>
  );

  const renderDurationInput = () => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>
        Service Duration <Text style={styles.required}>*</Text>
      </Text>
      <View style={[styles.durationContainer, errors.duration && styles.inputError]}>
        <TextInput
          style={styles.durationInput}
          value={formData.duration}
          onChangeText={(value) => {
            // Only allow numbers
            const numericValue = value.replace(/[^0-9]/g, '');
            onUpdateFormData('duration', numericValue);
          }}
          placeholder="60"
          placeholderTextColor={colors.textSecondary}
          keyboardType="numeric"
        />
        <Text style={styles.durationUnit}>minutes</Text>
      </View>
      {errors.duration && (
        <Text style={styles.errorText}>{errors.duration}</Text>
      )}
      <Text style={styles.helpText}>
        How long does this service typically take?
      </Text>
    </View>
  );

  const renderBufferTimeInput = () => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>Buffer Time</Text>
      <View style={styles.durationContainer}>
        <TextInput
          style={styles.durationInput}
          value={formData.buffer_time}
          onChangeText={(value) => {
            const numericValue = value.replace(/[^0-9]/g, '');
            onUpdateFormData('buffer_time', numericValue);
          }}
          placeholder="15"
          placeholderTextColor={colors.textSecondary}
          keyboardType="numeric"
        />
        <Text style={styles.durationUnit}>minutes</Text>
      </View>
      <Text style={styles.helpText}>
        Extra time between bookings for preparation and travel
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Service Details</Text>
          <Text style={styles.subtitle}>
            Provide detailed information about your service
          </Text>
        </View>

        <View style={styles.form}>
          {renderInput(
            'Full Description',
            'description',
            'Describe your service in detail. What does it include? What can customers expect?',
            { 
              multiline: true, 
              maxLength: 1000,
              required: true
            }
          )}

          {renderDurationInput()}

          {renderBufferTimeInput()}

          {renderInput(
            'Requirements',
            'requirements',
            'What do customers need to prepare or provide? (e.g., materials, access, etc.)',
            { 
              multiline: true, 
              maxLength: 500
            }
          )}

          {renderInput(
            'Preparation Instructions',
            'preparation_instructions',
            'Any special instructions for customers before the service?',
            { 
              multiline: true, 
              maxLength: 500
            }
          )}
        </View>

        <View style={styles.helpSection}>
          <View style={styles.helpItem}>
            <Icon name="description" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              <Text style={styles.helpTextBold}>Description:</Text> Be specific about what's included and what customers can expect
            </Text>
          </View>
          
          <View style={styles.helpItem}>
            <Icon name="schedule" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              <Text style={styles.helpTextBold}>Duration:</Text> Include setup and cleanup time in your estimate
            </Text>
          </View>

          <View style={styles.helpItem}>
            <Icon name="checklist" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              <Text style={styles.helpTextBold}>Requirements:</Text> Clear requirements help avoid misunderstandings
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.buttonSecondary]}
          onPress={onPrevious}
          disabled={isFirstStep}
        >
          <Icon name="arrow-back" size={20} color={colors.primary} />
          <Text style={[styles.buttonText, styles.buttonTextSecondary]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.buttonPrimary]}
          onPress={onNext}
        >
          <Text style={[styles.buttonText, styles.buttonTextPrimary]}>
            Review
          </Text>
          <Icon name="arrow-forward" size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: spacing.lg,
  },
  title: {
    ...typography.h2,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  form: {
    paddingBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  required: {
    color: colors.error,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
    backgroundColor: colors.white,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...typography.caption,
    color: colors.error,
    marginTop: spacing.xs,
  },
  characterCount: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'right',
    marginTop: spacing.xs,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  durationInput: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
  },
  durationUnit: {
    ...typography.body,
    color: colors.textSecondary,
    paddingRight: spacing.md,
    fontWeight: '500',
  },
  helpText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    lineHeight: 16,
  },
  helpSection: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  helpTextBold: {
    fontWeight: '600',
    color: colors.textPrimary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    minWidth: 120,
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
  },
  buttonText: {
    ...typography.body,
    fontWeight: '600',
    marginHorizontal: spacing.xs,
  },
  buttonTextPrimary: {
    color: colors.white,
  },
  buttonTextSecondary: {
    color: colors.primary,
  },
});
