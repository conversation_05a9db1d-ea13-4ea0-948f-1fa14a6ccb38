/**
 * Authentication API Service
 * Handles all authentication-related API calls
 */

import { apiClient } from './client';
import { EnhancedError, formatErrorForLogging } from '../../utils/errorHandler';

// Types for API requests and responses
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface SocialAuthRequest {
  provider: 'google' | 'apple';
  identity_token: string;
  email?: string;
  first_name?: string;
  last_name?: string;
  user_id?: string;
}

export interface AuthResponse {
  access: string;
  refresh: string;
  user: {
    id: string;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    full_name: string;
    role: 'customer' | 'service_provider' | 'admin';
    is_verified: boolean;
    account_status: string;
    avatar?: string;
    phone?: string;
    created_at: string;
  };
  message?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirmRequest {
  token: string;
  password: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

// Authentication API functions
export const authAPI = {
  // Login with email and password
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    try {
      const response = await apiClient.post('/auth/login/', data);
      return response.data;
    } catch (error) {
      const enhancedError = error as EnhancedError;
      console.error(formatErrorForLogging(enhancedError, 'AUTH_LOGIN'));
      throw enhancedError;
    }
  },

  // Register new user
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/register/', data);
    return response.data;
  },

  // Social authentication (Google/Apple)
  socialAuth: async (data: SocialAuthRequest): Promise<AuthResponse> => {
    const response = await apiClient.post('/auth/social/', data);
    return response.data;
  },

  // Logout
  logout: async (refreshToken: string): Promise<void> => {
    await apiClient.post('/auth/logout/', { refresh: refreshToken });
  },

  // Get current user profile
  getProfile: async (): Promise<AuthResponse['user']> => {
    const response = await apiClient.get('/auth/profile/');
    return response.data;
  },

  // Update user profile
  updateProfile: async (data: Partial<AuthResponse['user']>): Promise<AuthResponse['user']> => {
    const response = await apiClient.patch('/auth/profile/update/', data);
    return response.data;
  },

  // Change password
  changePassword: async (data: ChangePasswordRequest): Promise<void> => {
    await apiClient.post('/auth/change-password/', data);
  },

  // Request password reset
  requestPasswordReset: async (data: PasswordResetRequest): Promise<void> => {
    await apiClient.post('/auth/password-reset/', data);
  },

  // Confirm password reset
  confirmPasswordReset: async (data: PasswordResetConfirmRequest): Promise<void> => {
    await apiClient.post('/auth/password-reset/confirm/', data);
  },

  // Verify email
  verifyEmail: async (token: string): Promise<void> => {
    await apiClient.post('/auth/verify-email/', { token });
  },

  // Resend verification email
  resendVerification: async (email: string): Promise<void> => {
    await apiClient.post('/auth/resend-verification/', { email });
  },

  // Check auth status
  checkAuthStatus: async (): Promise<AuthResponse['user']> => {
    const response = await apiClient.get('/auth/status/');
    return response.data;
  },
};
