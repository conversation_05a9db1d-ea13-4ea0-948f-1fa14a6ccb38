import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert, View, Text, TextInput, TouchableOpacity } from 'react-native';

// Mock the Alert module
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  setOptions: jest.fn(),
};

// Mock service data
const mockService = {
  id: 'service-1',
  name: 'Premium Haircut',
  description: 'Professional haircut with styling consultation',
  category: 'Hair Services',
  base_price: 75.00,
  duration: 60,
  is_active: true,
};

const mockCategories = [
  { id: 'cat-1', name: 'Hair Services', slug: 'hair-services' },
  { id: 'cat-2', name: 'Nail Services', slug: 'nail-services' },
  { id: 'cat-3', name: 'Lash Services', slug: 'lash-services' },
];

// Mock ProviderServiceForm component (since it doesn't exist yet, we'll test the interface)
interface ProviderServiceFormProps {
  service?: typeof mockService;
  categories: typeof mockCategories;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  testID?: string;
}

const ProviderServiceForm: React.FC<ProviderServiceFormProps> = ({
  service,
  categories,
  onSubmit,
  onCancel,
  isLoading = false,
  testID = 'provider-service-form',
}) => {
  const [formData, setFormData] = React.useState({
    name: service?.name || '',
    description: service?.description || '',
    category: service?.category || '',
    base_price: service?.base_price?.toString() || '',
    duration: service?.duration?.toString() || '',
  });

  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = React.useState<Record<string, boolean>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Service name must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    const price = parseFloat(formData.base_price);
    if (!formData.base_price.trim()) {
      newErrors.base_price = 'Price is required';
    } else if (isNaN(price) || price <= 0) {
      newErrors.base_price = 'Price must be a valid positive number';
    }

    const duration = parseInt(formData.duration);
    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    } else if (isNaN(duration) || duration <= 0) {
      newErrors.duration = 'Duration must be a valid positive number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    setHasInteracted({
      name: true,
      description: true,
      category: true,
      base_price: true,
      duration: true,
    });

    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit({
        ...formData,
        base_price: parseFloat(formData.base_price),
        duration: parseInt(formData.duration),
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to save service');
    }
  };

  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (hasInteracted[field]) {
      // Re-validate on change if user has already interacted
      setTimeout(() => validateForm(), 0);
    }
  };

  const handleFieldBlur = (field: string) => {
    setHasInteracted(prev => ({ ...prev, [field]: true }));
    validateForm();
  };

  return (
    <View testID={testID}>
      <View testID="form-container">
        <TextInput
          testID="service-name-input"
          placeholder="Service Name"
          value={formData.name}
          onChangeText={(value) => handleFieldChange('name', value)}
          onBlur={() => handleFieldBlur('name')}
        />
        {hasInteracted.name && errors.name && (
          <Text testID="name-error">{errors.name}</Text>
        )}

        <TextInput
          testID="service-description-input"
          placeholder="Service Description"
          value={formData.description}
          onChangeText={(value) => handleFieldChange('description', value)}
          onBlur={() => handleFieldBlur('description')}
          multiline
        />
        {hasInteracted.description && errors.description && (
          <Text testID="description-error">{errors.description}</Text>
        )}

        <TextInput
          testID="service-category-input"
          placeholder="Category (e.g., Hair Services)"
          value={formData.category}
          onChangeText={(value) => handleFieldChange('category', value)}
          onBlur={() => handleFieldBlur('category')}
        />
        {hasInteracted.category && errors.category && (
          <Text testID="category-error">{errors.category}</Text>
        )}

        <TextInput
          testID="service-price-input"
          placeholder="Price ($)"
          keyboardType="numeric"
          value={formData.base_price}
          onChangeText={(value) => handleFieldChange('base_price', value)}
          onBlur={() => handleFieldBlur('base_price')}
        />
        {hasInteracted.base_price && errors.base_price && (
          <Text testID="price-error">{errors.base_price}</Text>
        )}

        <TextInput
          testID="service-duration-input"
          placeholder="Duration (minutes)"
          keyboardType="numeric"
          value={formData.duration}
          onChangeText={(value) => handleFieldChange('duration', value)}
          onBlur={() => handleFieldBlur('duration')}
        />
        {hasInteracted.duration && errors.duration && (
          <Text testID="duration-error">{errors.duration}</Text>
        )}
      </View>

      <View testID="form-actions">
        <TouchableOpacity
          testID="cancel-button"
          onPress={onCancel}
          disabled={isLoading}
        >
          <Text>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity
          testID="submit-button"
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text>{isLoading ? 'Saving...' : service ? 'Update Service' : 'Create Service'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

describe('ProviderServiceForm', () => {
  const defaultProps = {
    categories: mockCategories,
    onSubmit: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders form fields correctly', () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      expect(getByTestId('provider-service-form')).toBeTruthy();
      expect(getByTestId('form-container')).toBeTruthy();
      expect(getByTestId('service-name-input')).toBeTruthy();
      expect(getByTestId('service-description-input')).toBeTruthy();
      expect(getByTestId('service-category-select')).toBeTruthy();
      expect(getByTestId('service-price-input')).toBeTruthy();
      expect(getByTestId('service-duration-input')).toBeTruthy();
      expect(getByTestId('form-actions')).toBeTruthy();
      expect(getByTestId('cancel-button')).toBeTruthy();
      expect(getByTestId('submit-button')).toBeTruthy();
    });

    it('renders category options correctly', () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);
      const categorySelect = getByTestId('service-category-select');
      
      expect(categorySelect).toBeTruthy();
      // In a real React Native test, we'd check the picker options
    });

    it('shows "Create Service" button text for new service', () => {
      const { getByTestId, getByText } = render(<ProviderServiceForm {...defaultProps} />);

      expect(getByText('Create Service')).toBeTruthy();
    });

    it('shows "Update Service" button text for existing service', () => {
      const props = {
        ...defaultProps,
        service: mockService,
      };
      const { getByText } = render(<ProviderServiceForm {...props} />);

      expect(getByText('Update Service')).toBeTruthy();
    });

    it('pre-fills form when editing existing service', () => {
      const props = {
        ...defaultProps,
        service: mockService,
      };
      const { getByTestId, getByDisplayValue } = render(<ProviderServiceForm {...props} />);

      expect(getByDisplayValue('Premium Haircut')).toBeTruthy();
      expect(getByDisplayValue('Professional haircut with styling consultation')).toBeTruthy();
      expect(getByDisplayValue('Hair Services')).toBeTruthy();
      expect(getByDisplayValue('75')).toBeTruthy();
      expect(getByDisplayValue('60')).toBeTruthy();
    });
  });

  describe('Form Validation', () => {
    it('shows validation errors for empty required fields', async () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      // Try to submit empty form
      fireEvent.press(getByTestId('submit-button'));

      await waitFor(() => {
        expect(getByTestId('name-error')).toBeTruthy();
        expect(getByTestId('description-error')).toBeTruthy();
        expect(getByTestId('category-error')).toBeTruthy();
        expect(getByTestId('price-error')).toBeTruthy();
        expect(getByTestId('duration-error')).toBeTruthy();
      });

      expect(defaultProps.onSubmit).not.toHaveBeenCalled();
    });

    it('validates minimum length for service name', async () => {
      const { getByTestId, getByText } = render(<ProviderServiceForm {...defaultProps} />);

      fireEvent.changeText(getByTestId('service-name-input'), 'Hi');
      fireEvent(getByTestId('service-name-input'), 'blur');

      await waitFor(() => {
        expect(getByText('Service name must be at least 3 characters')).toBeTruthy();
      });
    });

    it('validates minimum length for description', async () => {
      const { getByTestId, getByText } = render(<ProviderServiceForm {...defaultProps} />);

      fireEvent.changeText(getByTestId('service-description-input'), 'Short');
      fireEvent(getByTestId('service-description-input'), 'blur');

      await waitFor(() => {
        expect(getByText('Description must be at least 10 characters')).toBeTruthy();
      });
    });

    it('validates positive price values', async () => {
      const { getByTestId, getByText } = render(<ProviderServiceForm {...defaultProps} />);

      fireEvent.changeText(getByTestId('service-price-input'), '-10');
      fireEvent(getByTestId('service-price-input'), 'blur');

      await waitFor(() => {
        expect(getByText('Price must be a valid positive number')).toBeTruthy();
      });
    });

    it('validates positive duration values', async () => {
      const { getByTestId, getByText } = render(<ProviderServiceForm {...defaultProps} />);

      fireEvent.changeText(getByTestId('service-duration-input'), '0');
      fireEvent(getByTestId('service-duration-input'), 'blur');

      await waitFor(() => {
        expect(getByText('Duration must be a valid positive number')).toBeTruthy();
      });
    });
  });

  describe('Form Submission', () => {
    it('calls onSubmit with correct data for valid form', async () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      // Fill out valid form
      fireEvent.changeText(getByTestId('service-name-input'), 'Test Service');
      fireEvent.changeText(getByTestId('service-description-input'), 'This is a test service description');
      fireEvent.changeText(getByTestId('service-category-input'), 'Hair Services');
      fireEvent.changeText(getByTestId('service-price-input'), '50');
      fireEvent.changeText(getByTestId('service-duration-input'), '45');

      fireEvent.press(getByTestId('submit-button'));

      await waitFor(() => {
        expect(defaultProps.onSubmit).toHaveBeenCalledWith({
          name: 'Test Service',
          description: 'This is a test service description',
          category: 'Hair Services',
          base_price: 50,
          duration: 45,
        });
      });
    });

    it('handles submission errors gracefully', async () => {
      const onSubmitError = jest.fn().mockRejectedValue(new Error('API Error'));
      const props = {
        ...defaultProps,
        onSubmit: onSubmitError,
      };

      const { getByTestId } = render(<ProviderServiceForm {...props} />);

      // Fill out valid form
      fireEvent.change(getByTestId('service-name-input'), { target: { value: 'Test Service' } });
      fireEvent.change(getByTestId('service-description-input'), { target: { value: 'This is a test service description' } });
      fireEvent.change(getByTestId('service-category-select'), { target: { value: 'Hair Services' } });
      fireEvent.change(getByTestId('service-price-input'), { target: { value: '50' } });
      fireEvent.change(getByTestId('service-duration-input'), { target: { value: '45' } });

      fireEvent.click(getByTestId('submit-button'));

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to save service');
      });
    });

    it('calls onCancel when cancel button is pressed', () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      fireEvent.click(getByTestId('cancel-button'));

      expect(defaultProps.onCancel).toHaveBeenCalledTimes(1);
    });
  });

  describe('Loading State', () => {
    it('disables buttons when loading', () => {
      const props = {
        ...defaultProps,
        isLoading: true,
      };
      const { getByTestId } = render(<ProviderServiceForm {...props} />);

      expect((getByTestId('submit-button') as HTMLButtonElement).disabled).toBe(true);
      expect((getByTestId('cancel-button') as HTMLButtonElement).disabled).toBe(true);
    });

    it('shows loading text on submit button when loading', () => {
      const props = {
        ...defaultProps,
        isLoading: true,
      };
      const { getByTestId } = render(<ProviderServiceForm {...props} />);

      expect(getByTestId('submit-button').textContent).toBe('Saving...');
    });
  });

  describe('Real-time Validation', () => {
    it('validates fields in real-time after user interaction', async () => {
      const { getByTestId, queryByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      // Initially no error shown
      expect(queryByTestId('name-error')).toBeFalsy();

      // User interacts with field
      fireEvent.change(getByTestId('service-name-input'), { target: { value: 'Hi' } });
      fireEvent.blur(getByTestId('service-name-input'));

      // Error appears
      await waitFor(() => {
        expect(getByTestId('name-error')).toBeTruthy();
      });

      // User fixes the error
      fireEvent.change(getByTestId('service-name-input'), { target: { value: 'Valid Service Name' } });

      // Error disappears
      await waitFor(() => {
        expect(queryByTestId('name-error')).toBeFalsy();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper testIDs for accessibility testing', () => {
      const { getByTestId } = render(<ProviderServiceForm {...defaultProps} />);

      expect(getByTestId('provider-service-form')).toBeTruthy();
      expect(getByTestId('service-name-input')).toBeTruthy();
      expect(getByTestId('service-description-input')).toBeTruthy();
      expect(getByTestId('service-category-select')).toBeTruthy();
      expect(getByTestId('service-price-input')).toBeTruthy();
      expect(getByTestId('service-duration-input')).toBeTruthy();
      expect(getByTestId('submit-button')).toBeTruthy();
      expect(getByTestId('cancel-button')).toBeTruthy();
    });

    it('accepts custom testID', () => {
      const props = {
        ...defaultProps,
        testID: 'custom-form',
      };
      const { getByTestId } = render(<ProviderServiceForm {...props} />);

      expect(getByTestId('custom-form')).toBeTruthy();
    });
  });
});
