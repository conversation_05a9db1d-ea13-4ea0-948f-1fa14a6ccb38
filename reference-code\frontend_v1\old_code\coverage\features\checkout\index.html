
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for features/checkout</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> features/checkout</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">53.39% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>55/103</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.5% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>11/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.14% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.04% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>51/91</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="CheckoutScreen.tsx"><a href="CheckoutScreen.tsx.html">CheckoutScreen.tsx</a></td>
	<td data-value="65.95" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.95" class="pct medium">65.95%</td>
	<td data-value="47" class="abs medium">31/47</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="20" class="abs low">8/20</td>
	<td data-value="63.63" class="pct medium">63.63%</td>
	<td data-value="11" class="abs medium">7/11</td>
	<td data-value="70.73" class="pct medium">70.73%</td>
	<td data-value="41" class="abs medium">29/41</td>
	</tr>

<tr>
	<td class="file medium" data-value="PaymentScreen.tsx"><a href="PaymentScreen.tsx.html">PaymentScreen.tsx</a></td>
	<td data-value="55.81" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 55%"></div><div class="cover-empty" style="width: 45%"></div></div>
	</td>
	<td data-value="55.81" class="pct medium">55.81%</td>
	<td data-value="43" class="abs medium">24/43</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="18" class="abs low">3/18</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="12" class="abs medium">6/12</td>
	<td data-value="59.45" class="pct medium">59.45%</td>
	<td data-value="37" class="abs medium">22/37</td>
	</tr>

<tr>
	<td class="file low" data-value="PaymentSuccessScreen.tsx"><a href="PaymentSuccessScreen.tsx.html">PaymentSuccessScreen.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	</tr>

<tr>
	<td class="file empty" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="0" class="pic empty">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T23:26:10.033Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    