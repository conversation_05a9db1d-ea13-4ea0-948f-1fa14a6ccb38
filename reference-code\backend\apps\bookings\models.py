"""
Booking models for Vierla Beauty Services Marketplace
Comprehensive booking system with state machine, calendar integration, and notifications
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone as django_timezone
from decimal import Decimal
import uuid
from datetime import timedelta

from apps.core.models import BaseModel, TimestampedModel

User = get_user_model()


class Booking(BaseModel, TimestampedModel):
    """
    Comprehensive booking model with state machine and audit trail
    Supports mobile-first design with optimized queries
    """

    class Status(models.TextChoices):
        PENDING = 'pending', 'Pending Confirmation'
        CONFIRMED = 'confirmed', 'Confirmed'
        IN_PROGRESS = 'in_progress', 'In Progress'
        COMPLETED = 'completed', 'Completed'
        CANCELLED = 'cancelled', 'Cancelled'
        NO_SHOW = 'no_show', 'No Show'
        RESCHEDULED = 'rescheduled', 'Rescheduled'

    class PaymentStatus(models.TextChoices):
        PENDING = 'pending', 'Payment Pending'
        PAID = 'paid', 'Paid'
        PARTIALLY_PAID = 'partially_paid', 'Partially Paid'
        REFUNDED = 'refunded', 'Refunded'
        FAILED = 'failed', 'Payment Failed'

    class LocationType(models.TextChoices):
        SALON = 'salon', 'At Salon'
        MOBILE = 'mobile', 'Mobile Service'
        ONLINE = 'online', 'Online Service'

    # Core booking information
    booking_number = models.CharField(
        max_length=20,
        unique=True,
        editable=False,
        help_text="Human-readable booking number"
    )

    # Relationships
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='customer_bookings',
        help_text="Customer who made the booking"
    )
    provider = models.ForeignKey(
        'catalog.ServiceProvider',
        on_delete=models.CASCADE,
        related_name='provider_bookings',
        help_text="Service provider for this booking"
    )
    service = models.ForeignKey(
        'catalog.Service',
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text="Service being booked"
    )

    # Booking details
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        help_text="Current booking status"
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING,
        help_text="Payment status"
    )

    # Scheduling
    scheduled_datetime = models.DateTimeField(
        help_text="Scheduled date and time for the service"
    )
    duration_minutes = models.PositiveIntegerField(
        help_text="Duration of the service in minutes"
    )
    end_datetime = models.DateTimeField(
        blank=True,
        null=True,
        help_text="Calculated end time (scheduled_datetime + duration)"
    )

    # Location information
    location_type = models.CharField(
        max_length=10,
        choices=LocationType.choices,
        default=LocationType.SALON,
        help_text="Type of service location"
    )
    service_address = models.TextField(
        blank=True,
        help_text="Address where service will be performed"
    )
    service_latitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        blank=True,
        null=True,
        help_text="Latitude for mobile services"
    )
    service_longitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        blank=True,
        null=True,
        help_text="Longitude for mobile services"
    )

    # Pricing
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Base service price"
    )
    additional_charges = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Additional charges (travel, materials, etc.)"
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Discount applied"
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Tax amount"
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Total amount to be paid"
    )

    # Notes and communication
    customer_notes = models.TextField(
        blank=True,
        help_text="Special instructions from customer"
    )
    provider_notes = models.TextField(
        blank=True,
        help_text="Notes from service provider"
    )
    internal_notes = models.TextField(
        blank=True,
        help_text="Internal notes for admin use"
    )

    # Timestamps for state tracking
    confirmed_at = models.DateTimeField(blank=True, null=True)
    started_at = models.DateTimeField(blank=True, null=True)
    completed_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)

    # Cancellation information
    cancelled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='cancelled_bookings',
        help_text="User who cancelled the booking"
    )
    cancellation_reason = models.TextField(
        blank=True,
        help_text="Reason for cancellation"
    )

    # Mobile optimization fields
    is_mobile_optimized = models.BooleanField(
        default=True,
        help_text="Optimized for mobile display"
    )

    class Meta:
        db_table = 'bookings_booking'
        verbose_name = 'Booking'
        verbose_name_plural = 'Bookings'
        ordering = ['-scheduled_datetime', '-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['provider', 'scheduled_datetime']),
            models.Index(fields=['status', 'scheduled_datetime']),
            models.Index(fields=['scheduled_datetime', 'status']),
            models.Index(fields=['booking_number']),
            models.Index(fields=['payment_status']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(total_amount__gt=0),
                name='positive_total_amount'
            ),
            models.CheckConstraint(
                check=models.Q(duration_minutes__gt=0),
                name='positive_duration'
            ),
            models.CheckConstraint(
                check=models.Q(base_price__gte=0),
                name='non_negative_base_price'
            ),
        ]

    def save(self, *args, **kwargs):
        """Override save to generate booking number and calculate end time"""
        if not self.booking_number:
            self.booking_number = self.generate_booking_number()

        # Calculate end datetime
        if self.scheduled_datetime and self.duration_minutes:
            self.end_datetime = self.scheduled_datetime + \
                timedelta(minutes=self.duration_minutes)

        # Calculate total amount if not set
        if not self.total_amount:
            self.calculate_total()

        super().save(*args, **kwargs)

    def generate_booking_number(self):
        """Generate a human-readable booking number"""
        import random
        import string

        # Format: BK-YYYYMMDD-XXXX (e.g., BK-20250619-A1B2)
        date_str = django_timezone.now().strftime('%Y%m%d')
        random_str = ''.join(random.choices(
            string.ascii_uppercase + string.digits, k=4))
        return f"BK-{date_str}-{random_str}"

    def calculate_total(self):
        """Calculate total amount based on base price, charges, discounts, and tax"""
        subtotal = self.base_price + self.additional_charges - self.discount_amount
        self.total_amount = subtotal + self.tax_amount
        return self.total_amount

    def __str__(self):
        """String representation of booking"""
        return f"Booking {self.booking_number} - {self.customer.get_full_name()} with {self.provider.business_name}"

    # State machine methods
    def can_be_confirmed(self):
        """Check if booking can be confirmed"""
        return self.status == self.Status.PENDING

    def can_be_cancelled(self):
        """Check if booking can be cancelled"""
        cancellable_statuses = [self.Status.PENDING, self.Status.CONFIRMED]
        if self.status not in cancellable_statuses:
            return False

        # Check if it's within cancellation window
        now = django_timezone.now()
        cancellation_deadline = self.scheduled_datetime - \
            timedelta(hours=24)  # 24 hours before
        return now <= cancellation_deadline

    def can_be_started(self):
        """Check if booking can be started"""
        return self.status == self.Status.CONFIRMED

    def can_be_completed(self):
        """Check if booking can be completed"""
        return self.status == self.Status.IN_PROGRESS

    def can_be_rescheduled(self):
        """Check if booking can be rescheduled"""
        reschedulable_statuses = [self.Status.PENDING, self.Status.CONFIRMED]
        return self.status in reschedulable_statuses

    def confirm_booking(self, confirmed_by=None):
        """Confirm the booking"""
        if not self.can_be_confirmed():
            raise ValueError("Booking cannot be confirmed in current state")

        self.status = self.Status.CONFIRMED
        self.confirmed_at = django_timezone.now()
        self.save()

        # Create booking state change record
        BookingStateChange.objects.create(
            booking=self,
            from_status=self.Status.PENDING,
            to_status=self.Status.CONFIRMED,
            changed_by=confirmed_by,
            notes="Booking confirmed"
        )

    def start_booking(self, started_by=None):
        """Start the booking (service in progress)"""
        if not self.can_be_started():
            raise ValueError("Booking cannot be started in current state")

        self.status = self.Status.IN_PROGRESS
        self.started_at = django_timezone.now()
        self.save()

        BookingStateChange.objects.create(
            booking=self,
            from_status=self.Status.CONFIRMED,
            to_status=self.Status.IN_PROGRESS,
            changed_by=started_by,
            notes="Service started"
        )

    def complete_booking(self, completed_by=None):
        """Complete the booking"""
        if not self.can_be_completed():
            raise ValueError("Booking cannot be completed in current state")

        self.status = self.Status.COMPLETED
        self.completed_at = django_timezone.now()
        self.save()

        BookingStateChange.objects.create(
            booking=self,
            from_status=self.Status.IN_PROGRESS,
            to_status=self.Status.COMPLETED,
            changed_by=completed_by,
            notes="Service completed"
        )

    def cancel_booking(self, cancelled_by=None, reason=""):
        """Cancel the booking"""
        if not self.can_be_cancelled():
            raise ValueError(
                "Booking cannot be cancelled in current state or time")

        old_status = self.status
        self.status = self.Status.CANCELLED
        self.cancelled_at = django_timezone.now()
        self.cancelled_by = cancelled_by
        self.cancellation_reason = reason
        self.save()

        BookingStateChange.objects.create(
            booking=self,
            from_status=old_status,
            to_status=self.Status.CANCELLED,
            changed_by=cancelled_by,
            notes=f"Booking cancelled: {reason}"
        )

    def mark_no_show(self, marked_by=None):
        """Mark booking as no show"""
        if self.status != self.Status.CONFIRMED:
            raise ValueError(
                "Only confirmed bookings can be marked as no show")

        self.status = self.Status.NO_SHOW
        self.save()

        BookingStateChange.objects.create(
            booking=self,
            from_status=self.Status.CONFIRMED,
            to_status=self.Status.NO_SHOW,
            changed_by=marked_by,
            notes="Customer did not show up"
        )

    # Utility methods
    def get_cancellation_deadline(self):
        """Get the deadline for cancellation"""
        return self.scheduled_datetime - timedelta(hours=24)

    def is_upcoming(self):
        """Check if booking is upcoming"""
        return self.scheduled_datetime > django_timezone.now()

    def is_today(self):
        """Check if booking is today"""
        today = django_timezone.now().date()
        return self.scheduled_datetime.date() == today

    def get_time_until_booking(self):
        """Get time until booking starts"""
        if not self.is_upcoming():
            return None
        return self.scheduled_datetime - django_timezone.now()

    def get_duration_display(self):
        """Get human-readable duration"""
        hours = self.duration_minutes // 60
        minutes = self.duration_minutes % 60

        if hours > 0:
            return f"{hours}h {minutes}m" if minutes > 0 else f"{hours}h"
        return f"{minutes}m"


class BookingStateChange(BaseModel, TimestampedModel):
    """
    Track all state changes for bookings for audit trail
    """

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='state_changes',
        help_text="Booking that changed state"
    )
    from_status = models.CharField(
        max_length=20,
        choices=Booking.Status.choices,
        help_text="Previous status"
    )
    to_status = models.CharField(
        max_length=20,
        choices=Booking.Status.choices,
        help_text="New status"
    )
    changed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='booking_state_changes',
        help_text="User who made the change"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes about the state change"
    )

    class Meta:
        db_table = 'bookings_state_change'
        verbose_name = 'Booking State Change'
        verbose_name_plural = 'Booking State Changes'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['booking', 'created_at']),
            models.Index(fields=['to_status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.booking.booking_number}: {self.from_status} → {self.to_status}"


class TimeSlot(BaseModel, TimestampedModel):
    """
    Available time slots for booking services
    Supports calendar integration and availability management
    """

    provider = models.ForeignKey(
        'catalog.ServiceProvider',
        on_delete=models.CASCADE,
        related_name='time_slots',
        help_text="Service provider offering this time slot"
    )
    service = models.ForeignKey(
        'catalog.Service',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='time_slots',
        help_text="Specific service (optional, can be general availability)"
    )

    # Time slot details
    date = models.DateField(
        help_text="Date of the time slot"
    )
    start_time = models.TimeField(
        help_text="Start time of the slot"
    )
    end_time = models.TimeField(
        help_text="End time of the slot"
    )
    duration_minutes = models.PositiveIntegerField(
        help_text="Duration of the slot in minutes"
    )

    # Availability
    is_available = models.BooleanField(
        default=True,
        help_text="Whether this slot is available for booking"
    )
    max_bookings = models.PositiveIntegerField(
        default=1,
        help_text="Maximum number of bookings for this slot"
    )
    current_bookings = models.PositiveIntegerField(
        default=0,
        help_text="Current number of bookings for this slot"
    )

    # Pricing (can override service pricing)
    override_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Override price for this specific time slot"
    )

    # Special slot types
    is_break = models.BooleanField(
        default=False,
        help_text="Whether this is a break/unavailable slot"
    )
    is_recurring = models.BooleanField(
        default=False,
        help_text="Whether this slot repeats weekly"
    )

    # Notes
    notes = models.TextField(
        blank=True,
        help_text="Internal notes about this time slot"
    )

    class Meta:
        db_table = 'bookings_time_slot'
        verbose_name = 'Time Slot'
        verbose_name_plural = 'Time Slots'
        ordering = ['date', 'start_time']
        indexes = [
            models.Index(fields=['provider', 'date', 'is_available']),
            models.Index(fields=['date', 'start_time']),
            models.Index(fields=['service', 'date']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(end_time__gt=models.F('start_time')),
                name='end_time_after_start_time'
            ),
            models.CheckConstraint(
                check=models.Q(current_bookings__lte=models.F('max_bookings')),
                name='bookings_within_limit'
            ),
        ]

    def save(self, *args, **kwargs):
        """Calculate duration on save"""
        if self.start_time and self.end_time:
            start_datetime = django_timezone.datetime.combine(
                self.date, self.start_time)
            end_datetime = django_timezone.datetime.combine(
                self.date, self.end_time)
            self.duration_minutes = int(
                (end_datetime - start_datetime).total_seconds() / 60)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.provider.business_name} - {self.date} {self.start_time}-{self.end_time}"

    def is_fully_booked(self):
        """Check if this time slot is fully booked"""
        return self.current_bookings >= self.max_bookings

    def can_be_booked(self):
        """Check if this time slot can be booked"""
        return self.is_available and not self.is_fully_booked() and not self.is_break

    def get_available_spots(self):
        """Get number of available spots"""
        return max(0, self.max_bookings - self.current_bookings)

    def book_slot(self):
        """Book this time slot (increment current bookings)"""
        if not self.can_be_booked():
            raise ValueError("Time slot cannot be booked")

        self.current_bookings += 1
        self.save()

    def release_slot(self):
        """Release a booking from this time slot"""
        if self.current_bookings > 0:
            self.current_bookings -= 1
            self.save()


class BookingNotification(BaseModel, TimestampedModel):
    """
    Notifications related to bookings
    Supports push notifications, SMS, and email
    """

    class NotificationType(models.TextChoices):
        BOOKING_CREATED = 'booking_created', 'Booking Created'
        BOOKING_CONFIRMED = 'booking_confirmed', 'Booking Confirmed'
        BOOKING_CANCELLED = 'booking_cancelled', 'Booking Cancelled'
        BOOKING_REMINDER = 'booking_reminder', 'Booking Reminder'
        BOOKING_STARTED = 'booking_started', 'Booking Started'
        BOOKING_COMPLETED = 'booking_completed', 'Booking Completed'
        PAYMENT_REQUIRED = 'payment_required', 'Payment Required'
        PAYMENT_RECEIVED = 'payment_received', 'Payment Received'

    class NotificationChannel(models.TextChoices):
        PUSH = 'push', 'Push Notification'
        EMAIL = 'email', 'Email'
        SMS = 'sms', 'SMS'
        IN_APP = 'in_app', 'In-App Notification'

    class NotificationStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        SENT = 'sent', 'Sent'
        DELIVERED = 'delivered', 'Delivered'
        FAILED = 'failed', 'Failed'
        READ = 'read', 'Read'

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='notifications',
        help_text="Booking this notification is about"
    )
    recipient = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='booking_notifications',
        help_text="User who should receive this notification"
    )

    # Notification details
    notification_type = models.CharField(
        max_length=20,
        choices=NotificationType.choices,
        help_text="Type of notification"
    )
    channel = models.CharField(
        max_length=10,
        choices=NotificationChannel.choices,
        help_text="Channel to send notification through"
    )
    status = models.CharField(
        max_length=10,
        choices=NotificationStatus.choices,
        default=NotificationStatus.PENDING,
        help_text="Status of the notification"
    )

    # Content
    title = models.CharField(
        max_length=200,
        help_text="Notification title"
    )
    message = models.TextField(
        help_text="Notification message"
    )

    # Scheduling
    scheduled_for = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When to send this notification (for reminders)"
    )
    sent_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the notification was sent"
    )
    read_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the notification was read"
    )

    # Metadata
    external_id = models.CharField(
        max_length=100,
        blank=True,
        help_text="External service notification ID"
    )
    error_message = models.TextField(
        blank=True,
        help_text="Error message if sending failed"
    )

    class Meta:
        db_table = 'bookings_notification'
        verbose_name = 'Booking Notification'
        verbose_name_plural = 'Booking Notifications'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['booking', 'notification_type']),
            models.Index(fields=['recipient', 'status']),
            models.Index(fields=['scheduled_for', 'status']),
            models.Index(fields=['channel', 'status']),
        ]

    def __str__(self):
        return f"{self.notification_type} for {self.booking.booking_number} to {self.recipient.get_full_name()}"

    def mark_as_sent(self, external_id=None):
        """Mark notification as sent"""
        self.status = self.NotificationStatus.SENT
        self.sent_at = django_timezone.now()
        if external_id:
            self.external_id = external_id
        self.save()

    def mark_as_delivered(self):
        """Mark notification as delivered"""
        self.status = self.NotificationStatus.DELIVERED
        self.save()

    def mark_as_read(self):
        """Mark notification as read"""
        self.status = self.NotificationStatus.READ
        self.read_at = django_timezone.now()
        self.save()

    def mark_as_failed(self, error_message=""):
        """Mark notification as failed"""
        self.status = self.NotificationStatus.FAILED
        self.error_message = error_message
        self.save()
