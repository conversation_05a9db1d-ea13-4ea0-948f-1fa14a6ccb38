import React, { useCallback } from 'react';
import {
  FlatList,
  View,
  Text,
  StyleSheet,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import ServiceCard, { Service } from './ServiceCard';

const { width: screenWidth } = Dimensions.get('window');

export interface ServiceListProps {
  services: Service[];
  onServicePress: (service: Service) => void;
  onFavoriteToggle?: (serviceId: string) => void;
  favoriteServices?: string[];
  loading?: boolean;
  refreshing?: boolean;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
  variant?: 'grid' | 'list';
  testID?: string;
  emptyMessage?: string;
  emptySubtitle?: string;
}

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
};

export const ServiceList: React.FC<ServiceListProps> = ({
  services,
  onServicePress,
  onFavoriteToggle,
  favoriteServices = [],
  loading = false,
  refreshing = false,
  onRefresh,
  onLoadMore,
  hasMore = false,
  variant = 'list',
  testID,
  emptyMessage = 'No services found',
  emptySubtitle = 'Try adjusting your search or filters',
}) => {
  const renderServiceItem = useCallback(
    ({ item, index }: { item: Service; index: number }) => {
      const isFavorite = favoriteServices.includes(item.id);
      const cardVariant = item.is_popular ? 'featured' : 'default';

      return (
        <View
          style={[
            variant === 'grid' && styles.gridItem,
            variant === 'grid' && index % 2 === 1 && styles.gridItemRight,
          ]}
        >
          <ServiceCard
            service={item}
            onPress={() => onServicePress(item)}
            onFavorite={
              onFavoriteToggle ? () => onFavoriteToggle(item.id) : undefined
            }
            isFavorite={isFavorite}
            variant={cardVariant}
            testID={`${testID}-service-${item.id}`}
          />
        </View>
      );
    },
    [
      favoriteServices,
      onServicePress,
      onFavoriteToggle,
      variant,
      testID,
    ]
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer} testID={`${testID}-empty-state`}>
      <Text style={styles.emptyMessage}>{emptyMessage}</Text>
      <Text style={styles.emptySubtitle}>{emptySubtitle}</Text>
    </View>
  );

  const renderLoadingFooter = () => {
    if (!loading || services.length === 0) return null;

    return (
      <View style={styles.loadingFooter} testID={`${testID}-loading-footer`}>
        <ActivityIndicator size="small" color={Colors.primary.main} />
        <Text style={styles.loadingText}>Loading more services...</Text>
      </View>
    );
  };

  const renderInitialLoading = () => {
    if (!loading || services.length > 0) return null;

    return (
      <View style={styles.initialLoadingContainer} testID={`${testID}-initial-loading`}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>Loading services...</Text>
      </View>
    );
  };

  const handleEndReached = useCallback(() => {
    if (hasMore && !loading && onLoadMore) {
      onLoadMore();
    }
  }, [hasMore, loading, onLoadMore]);

  const getItemLayout = useCallback(
    (data: any, index: number) => {
      // Estimated item height for performance optimization
      const itemHeight = variant === 'grid' ? 280 : 320;
      return {
        length: itemHeight,
        offset: itemHeight * index,
        index,
      };
    },
    [variant]
  );

  const keyExtractor = useCallback((item: Service) => item.id, []);

  if (loading && services.length === 0) {
    return renderInitialLoading();
  }

  return (
    <View style={styles.container} testID={testID}>
      <FlatList
        data={services}
        renderItem={renderServiceItem}
        keyExtractor={keyExtractor}
        numColumns={variant === 'grid' ? 2 : 1}
        key={variant} // Force re-render when variant changes
        contentContainerStyle={[
          styles.listContainer,
          variant === 'grid' && styles.gridContainer,
          services.length === 0 && styles.emptyListContainer,
        ]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[Colors.primary.main]}
              tintColor={Colors.primary.main}
              testID={`${testID}-refresh-control`}
            />
          ) : undefined
        }
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={renderEmptyState}
        ListFooterComponent={renderLoadingFooter}
        getItemLayout={variant === 'list' ? getItemLayout : undefined}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={6}
        testID={`${testID}-flatlist`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: Spacing.medium,
    paddingBottom: Spacing.large,
  },
  gridContainer: {
    paddingHorizontal: Spacing.small,
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  gridItem: {
    flex: 1,
    marginHorizontal: Spacing.small,
  },
  gridItemRight: {
    marginLeft: Spacing.small,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.large * 2,
  },
  emptyMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Spacing.small,
  },
  emptySubtitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.large,
  },
  initialLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.large * 2,
  },
  loadingText: {
    marginLeft: Spacing.small,
    fontSize: 14,
    color: Colors.text.secondary,
  },
});

export default ServiceList;
