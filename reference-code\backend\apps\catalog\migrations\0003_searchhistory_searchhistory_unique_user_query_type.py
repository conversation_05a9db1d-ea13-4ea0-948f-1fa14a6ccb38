# Generated by Django 4.2.16 on 2025-06-22 20:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("catalog", "0002_serviceprovider_display_show_availability_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SearchHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "query",
                    models.CharField(help_text="Search query text", max_length=255),
                ),
                (
                    "search_type",
                    models.CharField(
                        choices=[
                            ("service", "Service Search"),
                            ("provider", "Provider Search"),
                            ("category", "Category Search"),
                            ("location", "Location Search"),
                        ],
                        default="service",
                        max_length=20,
                    ),
                ),
                (
                    "results_count",
                    models.IntegerField(
                        default=0, help_text="Number of results returned"
                    ),
                ),
                (
                    "clicked_result_id",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True,
                        help_text="ID of the result that was clicked",
                        max_length=50,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="search_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Search History",
                "verbose_name_plural": "Search Histories",
                "db_table": "catalog_search_history",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "-created_at"],
                        name="catalog_sea_user_id_264f5f_idx",
                    ),
                    models.Index(
                        fields=["query", "search_type"],
                        name="catalog_sea_query_264ecd_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="catalog_sea_created_d0863f_idx"
                    ),
                ],
            },
        ),
        migrations.AddConstraint(
            model_name="searchhistory",
            constraint=models.UniqueConstraint(
                fields=("user", "query", "search_type"), name="unique_user_query_type"
            ),
        ),
    ]
