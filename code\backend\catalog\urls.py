"""
URL configuration for catalog app
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ServiceCategoryViewSet, ServiceProviderViewSet, ServiceViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'categories', ServiceCategoryViewSet, basename='servicecategory')
router.register(r'providers', ServiceProviderViewSet, basename='serviceprovider')
router.register(r'services', ServiceViewSet, basename='service')

app_name = 'catalog'

urlpatterns = [
    path('', include(router.urls)),
]
