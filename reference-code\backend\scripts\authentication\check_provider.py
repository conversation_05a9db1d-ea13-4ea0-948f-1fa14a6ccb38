#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User
from apps.catalog.models import ServiceProvider

user = User.objects.get(email='<EMAIL>')
print(f'User ID: {user.id}')
print(f'User role: {user.role}')
print(f'Has provider_profile: {hasattr(user, "provider_profile")}')

if hasattr(user, 'provider_profile'):
    profile = user.provider_profile
    print(f'Provider profile ID: {profile.id}')
    print(f'Business name: {profile.business_name}')
    print(f'Is active: {profile.is_active}')
else:
    print('No provider profile found')
