/**
 * Enhanced Card Component
 * Based on shadcn/ui design patterns for React Native
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  ViewProps,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { createVariants, mergeStyles } from '../../lib/utils';

export interface CardProps extends ViewProps {
  variant?: 'default' | 'outline' | 'elevated' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardHeaderProps extends ViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardContentProps extends ViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardFooterProps extends ViewProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

const createCardVariants = (colors: any, spacing: any) => createVariants({
  base: {
    // Base styles
    borderRadius: 12,
    backgroundColor: colors.background.secondary,
  },
  variants: {
    variant: {
      default: {
        backgroundColor: colors.background.secondary,
        borderWidth: 1,
        borderColor: colors.text.tertiary,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: colors.text.tertiary,
      },
      elevated: {
        backgroundColor: colors.background.secondary,
        shadowColor: colors.black || '#000000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
      },
    },
    padding: {
      none: {
        padding: 0,
      },
      sm: {
        padding: spacing.sm,
      },
      md: {
        padding: spacing.md,
      },
      lg: {
        padding: spacing.lg,
      },
    },
  },
  defaultVariants: {
    variant: 'default',
    padding: 'md',
  }
});

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  children,
  style,
  ...props
}) => {
  const { colors, spacing } = useTheme();
  const cardVariants = createCardVariants(colors, spacing);
  const cardStyle = cardVariants({ variant, padding });
  const finalStyle = mergeStyles(cardStyle, style);

  return (
    <View style={finalStyle} {...props}>
      {children}
    </View>
  );
};

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  style,
  ...props
}) => {
  const { colors, spacing } = useTheme();
  const headerStyle = {
    paddingBottom: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.text.tertiary,
    marginBottom: spacing.md,
  };
  const finalStyle = mergeStyles(headerStyle, style);

  return (
    <View style={finalStyle} {...props}>
      {children}
    </View>
  );
};

export const CardContent: React.FC<CardContentProps> = ({
  children,
  style,
  ...props
}) => {
  const contentStyle = {
    flex: 1,
  };
  const finalStyle = mergeStyles(contentStyle, style);

  return (
    <View style={finalStyle} {...props}>
      {children}
    </View>
  );
};

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  style,
  ...props
}) => {
  const { colors, spacing } = useTheme();
  const footerStyle = {
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.text.tertiary,
    marginTop: spacing.md,
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
    alignItems: 'center' as const,
  };
  const finalStyle = mergeStyles(footerStyle, style);

  return (
    <View style={finalStyle} {...props}>
      {children}
    </View>
  );
};



export default Card;
