import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';

// Mock the Alert module
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

// Mock service data
const mockServices = [
  {
    id: 'service-1',
    name: 'Premium Haircut',
    description: 'Professional haircut with styling consultation',
    category: 'Hair Services',
    base_price: 75.00,
    duration: 60,
    is_active: true,
    is_available: true,
    booking_count: 15,
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'service-2',
    name: 'Hair Color',
    description: 'Full hair coloring service',
    category: 'Hair Services',
    base_price: 150.00,
    duration: 120,
    is_active: true,
    is_available: false,
    booking_count: 8,
    created_at: '2024-01-02T00:00:00Z',
  },
  {
    id: 'service-3',
    name: 'Hair Treatment',
    description: 'Deep conditioning treatment',
    category: 'Hair Services',
    base_price: 85.00,
    duration: 90,
    is_active: false,
    is_available: false,
    booking_count: 3,
    created_at: '2024-01-03T00:00:00Z',
  },
];

// Mock ProviderServiceList component
interface ProviderServiceListProps {
  services: typeof mockServices;
  onServicePress?: (service: any) => void;
  onEditService?: (service: any) => void;
  onDeleteService?: (service: any) => void;
  onToggleStatus?: (service: any) => void;
  onBulkAction?: (action: string, serviceIds: string[]) => void;
  isLoading?: boolean;
  testID?: string;
}

const ProviderServiceList: React.FC<ProviderServiceListProps> = ({
  services,
  onServicePress,
  onEditService,
  onDeleteService,
  onToggleStatus,
  onBulkAction,
  isLoading = false,
  testID = 'provider-service-list',
}) => {
  const [selectedServices, setSelectedServices] = React.useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = React.useState(false);

  const handleServiceSelect = (serviceId: string) => {
    setSelectedServices(prev => 
      prev.includes(serviceId) 
        ? prev.filter(id => id !== serviceId)
        : [...prev, serviceId]
    );
  };

  const handleSelectAll = () => {
    if (selectedServices.length === services.length) {
      setSelectedServices([]);
    } else {
      setSelectedServices(services.map(s => s.id));
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedServices.length === 0) {
      Alert.alert('Error', 'Please select services first');
      return;
    }
    onBulkAction?.(action, selectedServices);
    setSelectedServices([]);
    setIsSelectionMode(false);
  };

  const handleLongPress = (service: any) => {
    setIsSelectionMode(true);
    handleServiceSelect(service.id);
  };

  return (
    <div testID={testID}>
      {/* Header */}
      <div testID="list-header">
        <h2 testID="list-title">My Services ({services.length})</h2>
        <div testID="header-actions">
          {isSelectionMode ? (
            <>
              <button
                testID="select-all-button"
                onClick={handleSelectAll}
              >
                {selectedServices.length === services.length ? 'Deselect All' : 'Select All'}
              </button>
              <button
                testID="cancel-selection-button"
                onClick={() => {
                  setIsSelectionMode(false);
                  setSelectedServices([]);
                }}
              >
                Cancel
              </button>
            </>
          ) : (
            <button
              testID="selection-mode-button"
              onClick={() => setIsSelectionMode(true)}
            >
              Select
            </button>
          )}
        </div>
      </div>

      {/* Bulk Actions */}
      {isSelectionMode && selectedServices.length > 0 && (
        <div testID="bulk-actions">
          <span testID="selected-count">{selectedServices.length} selected</span>
          <button
            testID="bulk-activate-button"
            onClick={() => handleBulkAction('activate')}
          >
            Activate
          </button>
          <button
            testID="bulk-deactivate-button"
            onClick={() => handleBulkAction('deactivate')}
          >
            Deactivate
          </button>
          <button
            testID="bulk-delete-button"
            onClick={() => handleBulkAction('delete')}
          >
            Delete
          </button>
        </div>
      )}

      {/* Services List */}
      <div testID="services-container">
        {isLoading ? (
          <div testID="loading-indicator">Loading services...</div>
        ) : services.length === 0 ? (
          <div testID="empty-state">
            <span testID="empty-message">No services found</span>
            <span testID="empty-description">Create your first service to get started</span>
          </div>
        ) : (
          <div testID="services-list">
            {services.map(service => (
              <div
                key={service.id}
                testID={`service-item-${service.id}`}
                onClick={() => {
                  if (isSelectionMode) {
                    handleServiceSelect(service.id);
                  } else {
                    onServicePress?.(service);
                  }
                }}
                onLongPress={() => handleLongPress(service)}
                style={{
                  backgroundColor: selectedServices.includes(service.id) ? '#e3f2fd' : 'white',
                }}
              >
                {/* Selection Checkbox */}
                {isSelectionMode && (
                  <input
                    testID={`service-checkbox-${service.id}`}
                    type="checkbox"
                    checked={selectedServices.includes(service.id)}
                    onChange={() => handleServiceSelect(service.id)}
                  />
                )}

                {/* Service Info */}
                <div testID="service-info">
                  <h3 testID="service-name">{service.name}</h3>
                  <p testID="service-description">{service.description}</p>
                  <div testID="service-details">
                    <span testID="service-price">${service.base_price.toFixed(2)}</span>
                    <span testID="service-duration">{service.duration}min</span>
                    <span testID="service-bookings">{service.booking_count} bookings</span>
                  </div>
                </div>

                {/* Status Badges */}
                <div testID="service-status">
                  {!service.is_active && (
                    <span testID="inactive-badge">Inactive</span>
                  )}
                  {service.is_active && !service.is_available && (
                    <span testID="unavailable-badge">Unavailable</span>
                  )}
                  {service.is_active && service.is_available && (
                    <span testID="active-badge">Active</span>
                  )}
                </div>

                {/* Action Buttons */}
                {!isSelectionMode && (
                  <div testID="service-actions">
                    <button
                      testID={`edit-button-${service.id}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditService?.(service);
                      }}
                    >
                      Edit
                    </button>
                    <button
                      testID={`toggle-button-${service.id}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleStatus?.(service);
                      }}
                    >
                      {service.is_available ? 'Disable' : 'Enable'}
                    </button>
                    <button
                      testID={`delete-button-${service.id}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteService?.(service);
                      }}
                    >
                      Delete
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

describe('ProviderServiceList', () => {
  const defaultProps = {
    services: mockServices,
    onServicePress: jest.fn(),
    onEditService: jest.fn(),
    onDeleteService: jest.fn(),
    onToggleStatus: jest.fn(),
    onBulkAction: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders service list correctly', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      expect(getByTestId('provider-service-list')).toBeTruthy();
      expect(getByTestId('list-header')).toBeTruthy();
      expect(getByTestId('list-title')).toBeTruthy();
      expect(getByTestId('services-container')).toBeTruthy();
      expect(getByTestId('services-list')).toBeTruthy();
    });

    it('displays correct service count in title', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      expect(getByTestId('list-title').textContent).toBe('My Services (3)');
    });

    it('renders all services with correct information', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      // Check all services are rendered
      expect(getByTestId('service-item-service-1')).toBeTruthy();
      expect(getByTestId('service-item-service-2')).toBeTruthy();
      expect(getByTestId('service-item-service-3')).toBeTruthy();

      // Check first service details
      const firstService = getByTestId('service-item-service-1');
      expect(firstService.textContent).toContain('Premium Haircut');
      expect(firstService.textContent).toContain('Professional haircut with styling consultation');
      expect(firstService.textContent).toContain('$75.00');
      expect(firstService.textContent).toContain('60min');
      expect(firstService.textContent).toContain('15 bookings');
    });

    it('displays correct status badges', () => {
      const { getByTestId, queryByTestId } = render(<ProviderServiceList {...defaultProps} />);

      // Active service should show active badge
      const activeService = getByTestId('service-item-service-1');
      expect(activeService.querySelector('[data-testid="active-badge"]')).toBeTruthy();

      // Unavailable service should show unavailable badge
      const unavailableService = getByTestId('service-item-service-2');
      expect(unavailableService.querySelector('[data-testid="unavailable-badge"]')).toBeTruthy();

      // Inactive service should show inactive badge
      const inactiveService = getByTestId('service-item-service-3');
      expect(inactiveService.querySelector('[data-testid="inactive-badge"]')).toBeTruthy();
    });
  });

  describe('Service Interactions', () => {
    it('calls onServicePress when service is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('service-item-service-1'));

      expect(defaultProps.onServicePress).toHaveBeenCalledWith(mockServices[0]);
    });

    it('calls onEditService when edit button is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('edit-button-service-1'));

      expect(defaultProps.onEditService).toHaveBeenCalledWith(mockServices[0]);
    });

    it('calls onToggleStatus when toggle button is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('toggle-button-service-1'));

      expect(defaultProps.onToggleStatus).toHaveBeenCalledWith(mockServices[0]);
    });

    it('calls onDeleteService when delete button is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('delete-button-service-1'));

      expect(defaultProps.onDeleteService).toHaveBeenCalledWith(mockServices[0]);
    });

    it('shows correct toggle button text based on service status', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      // Available service should show "Disable"
      expect(getByTestId('toggle-button-service-1').textContent).toBe('Disable');

      // Unavailable service should show "Enable"
      expect(getByTestId('toggle-button-service-2').textContent).toBe('Enable');
    });
  });

  describe('Selection Mode', () => {
    it('enters selection mode when selection button is clicked', () => {
      const { getByTestId, queryByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));

      expect(getByTestId('select-all-button')).toBeTruthy();
      expect(getByTestId('cancel-selection-button')).toBeTruthy();
      expect(queryByTestId('selection-mode-button')).toBeFalsy();
    });

    it('shows checkboxes in selection mode', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));

      expect(getByTestId('service-checkbox-service-1')).toBeTruthy();
      expect(getByTestId('service-checkbox-service-2')).toBeTruthy();
      expect(getByTestId('service-checkbox-service-3')).toBeTruthy();
    });

    it('selects services when checkboxes are clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('service-checkbox-service-1'));

      expect((getByTestId('service-checkbox-service-1') as HTMLInputElement).checked).toBe(true);
      expect(getByTestId('bulk-actions')).toBeTruthy();
      expect(getByTestId('selected-count').textContent).toBe('1 selected');
    });

    it('selects all services when select all is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('select-all-button'));

      expect((getByTestId('service-checkbox-service-1') as HTMLInputElement).checked).toBe(true);
      expect((getByTestId('service-checkbox-service-2') as HTMLInputElement).checked).toBe(true);
      expect((getByTestId('service-checkbox-service-3') as HTMLInputElement).checked).toBe(true);
      expect(getByTestId('selected-count').textContent).toBe('3 selected');
    });

    it('deselects all services when deselect all is clicked', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('select-all-button')); // Select all first
      fireEvent.click(getByTestId('select-all-button')); // Then deselect all

      expect((getByTestId('service-checkbox-service-1') as HTMLInputElement).checked).toBe(false);
      expect((getByTestId('service-checkbox-service-2') as HTMLInputElement).checked).toBe(false);
      expect((getByTestId('service-checkbox-service-3') as HTMLInputElement).checked).toBe(false);
    });

    it('exits selection mode when cancel is clicked', () => {
      const { getByTestId, queryByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('service-checkbox-service-1'));
      fireEvent.click(getByTestId('cancel-selection-button'));

      expect(queryByTestId('bulk-actions')).toBeFalsy();
      expect(queryByTestId('service-checkbox-service-1')).toBeFalsy();
      expect(getByTestId('selection-mode-button')).toBeTruthy();
    });
  });

  describe('Bulk Actions', () => {
    it('shows bulk actions when services are selected', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('service-checkbox-service-1'));

      expect(getByTestId('bulk-actions')).toBeTruthy();
      expect(getByTestId('bulk-activate-button')).toBeTruthy();
      expect(getByTestId('bulk-deactivate-button')).toBeTruthy();
      expect(getByTestId('bulk-delete-button')).toBeTruthy();
    });

    it('calls onBulkAction with correct parameters for activate', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('service-checkbox-service-1'));
      fireEvent.click(getByTestId('bulk-activate-button'));

      expect(defaultProps.onBulkAction).toHaveBeenCalledWith('activate', ['service-1']);
    });

    it('calls onBulkAction with correct parameters for deactivate', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('service-checkbox-service-1'));
      fireEvent.click(getByTestId('service-checkbox-service-2'));
      fireEvent.click(getByTestId('bulk-deactivate-button'));

      expect(defaultProps.onBulkAction).toHaveBeenCalledWith('deactivate', ['service-1', 'service-2']);
    });

    it('shows error when trying bulk action with no selection', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      fireEvent.click(getByTestId('selection-mode-button'));
      fireEvent.click(getByTestId('bulk-activate-button'));

      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Please select services first');
      expect(defaultProps.onBulkAction).not.toHaveBeenCalled();
    });
  });

  describe('Loading and Empty States', () => {
    it('shows loading indicator when loading', () => {
      const props = {
        ...defaultProps,
        isLoading: true,
      };
      const { getByTestId, queryByTestId } = render(<ProviderServiceList {...props} />);

      expect(getByTestId('loading-indicator')).toBeTruthy();
      expect(queryByTestId('services-list')).toBeFalsy();
    });

    it('shows empty state when no services', () => {
      const props = {
        ...defaultProps,
        services: [],
      };
      const { getByTestId, queryByTestId } = render(<ProviderServiceList {...props} />);

      expect(getByTestId('empty-state')).toBeTruthy();
      expect(getByTestId('empty-message')).toBeTruthy();
      expect(getByTestId('empty-description')).toBeTruthy();
      expect(queryByTestId('services-list')).toBeFalsy();
    });
  });

  describe('Accessibility', () => {
    it('has proper testIDs for accessibility testing', () => {
      const { getByTestId } = render(<ProviderServiceList {...defaultProps} />);

      expect(getByTestId('provider-service-list')).toBeTruthy();
      expect(getByTestId('list-header')).toBeTruthy();
      expect(getByTestId('services-container')).toBeTruthy();
      expect(getByTestId('service-item-service-1')).toBeTruthy();
    });

    it('accepts custom testID', () => {
      const props = {
        ...defaultProps,
        testID: 'custom-service-list',
      };
      const { getByTestId } = render(<ProviderServiceList {...props} />);

      expect(getByTestId('custom-service-list')).toBeTruthy();
    });
  });
});
