# Shared API Serializers
from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()


class AuthSerializer(serializers.Serializer):
    """Serializer for user authentication"""
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    device_info = serializers.JSONField(required=False, default=dict)
    remember_me = serializers.BooleanField(default=False)


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile information"""
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'phone', 'role', 'is_active', 'date_joined'
        ]
        read_only_fields = ['id', 'date_joined', 'is_active']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}".strip()


class NotificationSerializer(serializers.Serializer):
    """Serializer for notifications - delegates to BookingNotification"""
    id = serializers.IntegerField(read_only=True)
    title = serializers.CharField(max_length=255)
    message = serializers.CharField()
    notification_type = serializers.CharField(max_length=50)
    channel = serializers.CharField(max_length=10)
    status = serializers.CharField(max_length=10)
    is_read = serializers.SerializerMethodField()
    booking_id = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(read_only=True)
    read_at = serializers.DateTimeField(read_only=True)

    def get_is_read(self, obj):
        """Check if notification is read"""
        if hasattr(obj, 'status'):
            from apps.bookings.models import BookingNotification
            return obj.status == BookingNotification.NotificationStatus.READ
        return False

    def get_booking_id(self, obj):
        """Get booking ID if available"""
        if hasattr(obj, 'booking') and obj.booking:
            return obj.booking.id
        return None


class MessagingSerializer(serializers.Serializer):
    """Serializer for messaging - delegates to actual messaging serializers"""
    id = serializers.IntegerField(read_only=True)
    content = serializers.CharField()
    sender_id = serializers.IntegerField()
    conversation_id = serializers.IntegerField()
    message_type = serializers.CharField(default='text')
    created_at = serializers.DateTimeField(read_only=True)
