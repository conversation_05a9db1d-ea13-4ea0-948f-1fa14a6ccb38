/**
 * Toast System - Standardized Toast Notifications for Vierla Application
 * 
 * This component provides a comprehensive toast notification system with
 * support for different types, animations, and user interactions.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import {
  ToastConfig,
  TOAST_COLORS,
} from '../../utils/errorTypes';

const { width: screenWidth } = Dimensions.get('window');

interface ToastSystemProps {
  position?: 'top' | 'bottom';
  maxToasts?: number;
  defaultDuration?: number;
}

interface ToastItemProps {
  toast: ToastConfig;
  onDismiss: (id: string) => void;
  position: 'top' | 'bottom';
}

/**
 * Individual Toast Item Component
 */
const ToastItem: React.FC<ToastItemProps> = ({ toast, onDismiss, position }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(position === 'top' ? -100 : 100)).current;

  useEffect(() => {
    // Animate in
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto dismiss if not persistent
    if (!toast.persistent && toast.duration) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, toast.duration);

      return () => clearTimeout(timer);
    }
  }, []);

  const handleDismiss = useCallback(() => {
    // Animate out
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: position === 'top' ? -100 : 100,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss(toast.id!);
      toast.onDismiss?.();
    });
  }, [toast.id, onDismiss, toast.onDismiss]);

  const getToastIcon = (type: ToastConfig['type']) => {
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'close-circle';
      case 'warning':
        return 'warning';
      case 'info':
        return 'information-circle';
      default:
        return 'information-circle';
    }
  };

  const toastColor = TOAST_COLORS[toast.type];

  return (
    <Animated.View
      style={[
        styles.toastContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
          backgroundColor: `${toastColor}15`, // 15% opacity
          borderLeftColor: toastColor,
        },
      ]}
      testID={toast.testID}
    >
      <View style={styles.toastContent}>
        <Ionicons
          name={getToastIcon(toast.type) as any}
          size={24}
          color={toastColor}
          style={styles.toastIcon}
        />
        <View style={styles.toastText}>
          <Text style={[styles.toastTitle, { color: toastColor }]}>
            {toast.title}
          </Text>
          {toast.message && (
            <Text style={styles.toastMessage}>
              {toast.message}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={styles.dismissButton}
          onPress={handleDismiss}
          accessibilityLabel="Dismiss notification"
          accessibilityRole="button"
        >
          <Ionicons name="close" size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>
      {toast.action && (
        <TouchableOpacity
          style={[styles.actionButton, { borderTopColor: `${toastColor}20` }]}
          onPress={toast.action.onPress}
          accessibilityRole="button"
        >
          <Text style={[styles.actionText, { color: toastColor }]}>
            {toast.action.label}
          </Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

/**
 * Toast System Component
 */
export const ToastSystem: React.FC<ToastSystemProps> = ({
  position = 'top',
  maxToasts = 3,
  defaultDuration = 4000,
}) => {
  const [toasts, setToasts] = useState<ToastConfig[]>([]);

  const showToast = useCallback(
    (config: Omit<ToastConfig, 'id'>) => {
      const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const toast: ToastConfig = {
        ...config,
        id,
        duration: config.duration ?? defaultDuration,
      };

      setToasts(prev => {
        const newToasts = position === 'top' 
          ? [toast, ...prev] 
          : [...prev, toast];
        return newToasts.slice(0, maxToasts);
      });

      // Haptic feedback
      if (config.enableHaptics !== false) {
        switch (config.type) {
          case 'success':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'error':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          case 'warning':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          default:
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }
    },
    [defaultDuration, maxToasts, position]
  );

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const dismissAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Expose methods to parent components
  React.useImperativeHandle(
    React.useRef(),
    () => ({
      showToast,
      dismissAllToasts,
    }),
    [showToast, dismissAllToasts]
  );

  if (toasts.length === 0) return null;

  return (
    <SafeAreaView
      style={[
        styles.container,
        position === 'top' ? styles.topContainer : styles.bottomContainer,
      ]}
      pointerEvents="box-none"
    >
      {toasts.map(toast => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onDismiss={dismissToast}
          position={position}
        />
      ))}
    </SafeAreaView>
  );
};

/**
 * Toast Provider Component
 */
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <>
      {children}
      <ToastSystem />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 9999,
    paddingHorizontal: 16,
  },
  topContainer: {
    top: 0,
  },
  bottomContainer: {
    bottom: 0,
  },
  toastContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderLeftWidth: 4,
    marginVertical: 4,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    maxWidth: screenWidth - 32,
  },
  toastContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
  },
  toastIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  toastText: {
    flex: 1,
  },
  toastTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  toastMessage: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
});
