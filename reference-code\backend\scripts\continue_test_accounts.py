#!/usr/bin/env python3
"""
Continue Test Account Creation - Hair Services Providers 3-5
"""

import os
import sys
import django
from decimal import Decimal

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User
from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from django.contrib.auth.hashers import make_password

def create_remaining_hair_providers():
    """Create remaining Hair Services providers (3-5)"""
    print("Creating remaining Hair Services providers (3-5)...")
    
    # Hair Services Providers 3-5
    hair_providers = [
        {
            'email': '<EMAIL>',
            'username': 'hair_provider_3',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'business_name': 'Trendy Cuts Studio',
            'business_description': 'Creative hair designs and color specialists in Ottawa',
            'phone': '+***********',
            'address': '789 Somerset St, Ottawa, ON K1R 6P1',
            'city': 'Ottawa',
            'zip_code': 'K1R 6P1',
            'latitude': Decimal('45.4111'),
            'longitude': Decimal('-75.6903'),
            'years_experience': 6,
            'rating': Decimal('4.9')
        },
        {
            'email': '<EMAIL>',
            'username': 'hair_provider_4',
            'first_name': 'David',
            'last_name': 'Thompson',
            'business_name': 'Classic Hair Co',
            'business_description': 'Traditional and contemporary hair services in Toronto',
            'phone': '+***********',
            'address': '321 King St W, Toronto, ON M5V 1J5',
            'city': 'Toronto',
            'zip_code': 'M5V 1J5',
            'latitude': Decimal('43.6426'),
            'longitude': Decimal('-79.3871'),
            'years_experience': 12,
            'rating': Decimal('4.6')
        },
        {
            'email': '<EMAIL>',
            'username': 'hair_provider_5',
            'first_name': 'Lisa',
            'last_name': 'Wang',
            'business_name': 'Luxe Hair Boutique',
            'business_description': 'Premium hair care and styling services in Ottawa',
            'phone': '+***********',
            'address': '654 Bank St, Ottawa, ON K1S 3T4',
            'city': 'Ottawa',
            'zip_code': 'K1S 3T4',
            'latitude': Decimal('45.4036'),
            'longitude': Decimal('-75.6890'),
            'years_experience': 10,
            'rating': Decimal('4.8')
        }
    ]
    
    # Get Hair Services category
    hair_cat = ServiceCategory.objects.get(slug='hair-services')
    
    # Create remaining Hair Services providers
    for provider_data in hair_providers:
        user, user_created = User.objects.get_or_create(
            email=provider_data['email'],
            defaults={
                'username': provider_data['username'],
                'first_name': provider_data['first_name'],
                'last_name': provider_data['last_name'],
                'role': User.UserRole.SERVICE_PROVIDER,
                'password': make_password('TestPass123!'),
                'is_active': True,
                'is_verified': True
            }
        )
        print(f"User: {'Created' if user_created else 'Exists'} - {user.email}")
        
        provider, provider_created = ServiceProvider.objects.get_or_create(
            user=user,
            defaults={
                'business_name': provider_data['business_name'],
                'business_description': provider_data['business_description'],
                'business_phone': provider_data['phone'],
                'business_email': user.email,
                'address': provider_data['address'],
                'city': provider_data['city'],
                'state': 'ON',
                'zip_code': provider_data['zip_code'],
                'country': 'Canada',
                'latitude': provider_data['latitude'],
                'longitude': provider_data['longitude'],
                'years_of_experience': provider_data['years_experience'],
                'is_active': True,
                'is_verified': True,
                'is_featured': True,
                'rating': provider_data['rating'],
                'review_count': 20
            }
        )
        print(f"Provider: {'Created' if provider_created else 'Exists'} - {provider.business_name}")
        provider.categories.add(hair_cat)
        print(f"Added Hair Services category to {provider.business_name}")
    
    print("Hair Services providers completed!")
    return True

if __name__ == "__main__":
    create_remaining_hair_providers()
