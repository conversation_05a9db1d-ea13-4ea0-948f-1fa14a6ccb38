import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Types
export interface FilterOption {
  id: string;
  label: string;
  value: any;
  count?: number;
}

export interface FilterSection {
  id: string;
  title: string;
  type: 'single' | 'multiple' | 'range' | 'toggle';
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

export interface FilterValues {
  [key: string]: any;
}

export interface FilterPanelProps {
  sections: FilterSection[];
  values: FilterValues;
  onValuesChange: (values: FilterValues) => void;
  onApply?: (values: FilterValues) => void;
  onReset?: () => void;
  visible: boolean;
  onClose: () => void;
  testID?: string;
}

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  border: '#E0E0E0',
  overlay: 'rgba(0, 0, 0, 0.5)',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
};

export const FilterPanel: React.FC<FilterPanelProps> = ({
  sections,
  values,
  onValuesChange,
  onApply,
  onReset,
  visible,
  onClose,
  testID = 'filter-panel',
}) => {
  const [localValues, setLocalValues] = useState<FilterValues>(values);

  const handleValueChange = useCallback((sectionId: string, value: any) => {
    const newValues = { ...localValues, [sectionId]: value };
    setLocalValues(newValues);
    onValuesChange(newValues);
  }, [localValues, onValuesChange]);

  const handleApply = useCallback(() => {
    onApply?.(localValues);
    onClose();
  }, [localValues, onApply, onClose]);

  const handleReset = useCallback(() => {
    const resetValues: FilterValues = {};
    setLocalValues(resetValues);
    onValuesChange(resetValues);
    onReset?.();
  }, [onValuesChange, onReset]);

  const renderSingleSelect = (section: FilterSection) => (
    <View key={section.id} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.optionsContainer}>
        {section.options?.map((option) => {
          const isSelected = localValues[section.id] === option.value;
          return (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.optionButton,
                isSelected && styles.optionButtonSelected
              ]}
              onPress={() => handleValueChange(section.id, option.value)}
              testID={`${testID}-${section.id}-${option.id}`}
            >
              <Text style={[
                styles.optionText,
                isSelected && styles.optionTextSelected
              ]}>
                {option.label}
                {option.count !== undefined && ` (${option.count})`}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );

  const renderMultipleSelect = (section: FilterSection) => (
    <View key={section.id} style={styles.section}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <View style={styles.optionsContainer}>
        {section.options?.map((option) => {
          const selectedValues = localValues[section.id] || [];
          const isSelected = selectedValues.includes(option.value);
          return (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.optionButton,
                isSelected && styles.optionButtonSelected
              ]}
              onPress={() => {
                const currentValues = localValues[section.id] || [];
                let newValues;
                if (isSelected) {
                  newValues = currentValues.filter((v: any) => v !== option.value);
                } else {
                  newValues = [...currentValues, option.value];
                }
                handleValueChange(section.id, newValues);
              }}
              testID={`${testID}-${section.id}-${option.id}`}
            >
              <Text style={[
                styles.optionText,
                isSelected && styles.optionTextSelected
              ]}>
                {option.label}
                {option.count !== undefined && ` (${option.count})`}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );

  const renderToggle = (section: FilterSection) => (
    <View key={section.id} style={styles.section}>
      <TouchableOpacity
        style={styles.toggleRow}
        onPress={() => handleValueChange(section.id, !localValues[section.id])}
        testID={`${testID}-${section.id}-toggle`}
      >
        <Text style={styles.sectionTitle}>{section.title}</Text>
        <View style={[
          styles.toggleSwitch,
          localValues[section.id] && styles.toggleSwitchActive
        ]}>
          <View style={[
            styles.toggleThumb,
            localValues[section.id] && styles.toggleThumbActive
          ]} />
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderRange = (section: FilterSection) => {
    const currentValue = localValues[section.id] || { min: section.min, max: section.max };
    
    return (
      <View key={section.id} style={styles.section}>
        <Text style={styles.sectionTitle}>{section.title}</Text>
        <View style={styles.rangeContainer}>
          <Text style={styles.rangeLabel}>
            {currentValue.min}{section.unit} - {currentValue.max}{section.unit}
          </Text>
          {/* TODO: Implement actual range slider */}
          <View style={styles.rangePlaceholder}>
            <Text style={styles.rangePlaceholderText}>Range slider coming soon</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderSection = (section: FilterSection) => {
    switch (section.type) {
      case 'single':
        return renderSingleSelect(section);
      case 'multiple':
        return renderMultipleSelect(section);
      case 'toggle':
        return renderToggle(section);
      case 'range':
        return renderRange(section);
      default:
        return null;
    }
  };

  const getActiveFiltersCount = () => {
    return Object.keys(localValues).filter(key => {
      const value = localValues[key];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value !== undefined && value !== null && value !== '';
    }).length;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container} testID={testID}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            testID={`${testID}-close-button`}
          >
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Filters</Text>
          
          <TouchableOpacity
            style={styles.resetButton}
            onPress={handleReset}
            testID={`${testID}-reset-button`}
          >
            <Text style={styles.resetButtonText}>Reset</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          testID={`${testID}-scroll`}
        >
          {sections.map(renderSection)}
        </ScrollView>
        
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.applyButton}
            onPress={handleApply}
            testID={`${testID}-apply-button`}
          >
            <Text style={styles.applyButtonText}>
              Apply Filters {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.background.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  closeButton: {
    padding: Spacing.small,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  resetButton: {
    padding: Spacing.small,
  },
  resetButtonText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    marginBottom: Spacing.small,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: Spacing.medium,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -Spacing.micro,
  },
  optionButton: {
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.small,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background.surface,
    margin: Spacing.micro,
  },
  optionButtonSelected: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  optionText: {
    fontSize: 14,
    color: Colors.text.primary,
  },
  optionTextSelected: {
    color: Colors.primary.white,
    fontWeight: '500',
  },
  toggleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  toggleSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.border,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: Colors.primary.main,
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: Colors.background.surface,
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  rangeContainer: {
    alignItems: 'center',
  },
  rangeLabel: {
    fontSize: 16,
    color: Colors.text.primary,
    fontWeight: '500',
    marginBottom: Spacing.medium,
  },
  rangePlaceholder: {
    width: '100%',
    height: 40,
    backgroundColor: Colors.background.light,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  rangePlaceholderText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  footer: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  applyButton: {
    backgroundColor: Colors.primary.main,
    paddingVertical: Spacing.medium,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    color: Colors.primary.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FilterPanel;
