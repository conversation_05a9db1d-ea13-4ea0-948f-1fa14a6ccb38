"""
Django management command to generate comprehensive sample data
Creates realistic test data using factory classes for development and testing
"""
import random
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings

from catalog.models import ServiceCategory, ServiceProvider, Service
from catalog.factories import CustomerFactory, ProviderFactory, ServiceFactory


class Command(BaseCommand):
    help = 'Generate comprehensive sample data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--customers',
            type=int,
            default=20,
            help='Number of customer accounts to create (default: 20)',
        )
        parser.add_argument(
            '--providers',
            type=int,
            default=15,
            help='Number of provider accounts to create (default: 15)',
        )
        parser.add_argument(
            '--services-per-provider',
            type=int,
            default=4,
            help='Average number of services per provider (default: 4)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing data',
        )

    def handle(self, *args, **options):
        # Security check - only allow in development/testing
        if not self.is_development_environment():
            raise CommandError(
                'Sample data generation is only allowed in development/testing environments. '
                'Current environment: {}'.format(getattr(settings, 'ENVIRONMENT', 'unknown'))
            )

        self.customers_count = options['customers']
        self.providers_count = options['providers']
        self.services_per_provider = options['services_per_provider']
        self.force = options['force']

        self.stdout.write(
            self.style.SUCCESS('🎨 Generating comprehensive sample data...')
        )

        try:
            self.generate_sample_data()
            self.print_summary()
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to generate sample data: {e}')
            )
            raise CommandError(f'Sample data generation failed: {e}')

    def is_development_environment(self):
        """Check if we're in a development environment"""
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        
        # Allow in development, testing, or when DEBUG is True
        return environment in ['development', 'testing'] or debug_mode

    def generate_sample_data(self):
        """Generate all sample data"""
        # Ensure categories exist
        self.ensure_categories()
        
        # Generate customers
        customers = self.generate_customers()
        
        # Generate providers
        providers = self.generate_providers()
        
        # Generate services
        services = self.generate_services(providers)
        
        return {
            'customers': customers,
            'providers': providers,
            'services': services
        }

    def ensure_categories(self):
        """Ensure all necessary categories exist"""
        categories = [
            {
                'name': 'Hair & Beauty',
                'slug': 'hair-beauty',
                'description': 'Hair styling, cuts, coloring, and beauty treatments',
                'is_active': True,
                'is_popular': True,
                'sort_order': 1
            },
            {
                'name': 'Wellness & Spa',
                'slug': 'wellness-spa',
                'description': 'Massage therapy, spa treatments, and wellness services',
                'is_active': True,
                'is_popular': True,
                'sort_order': 2
            },
            {
                'name': 'Fitness & Training',
                'slug': 'fitness-training',
                'description': 'Personal training, group fitness, and specialized workouts',
                'is_active': True,
                'is_popular': True,
                'sort_order': 3
            },
            {
                'name': 'Skincare & Aesthetics',
                'slug': 'skincare-aesthetics',
                'description': 'Facial treatments, skincare, and aesthetic services',
                'is_active': True,
                'is_popular': True,
                'sort_order': 4
            },
            {
                'name': 'Nail Care',
                'slug': 'nail-care',
                'description': 'Manicures, pedicures, and nail art services',
                'is_active': True,
                'is_popular': False,
                'sort_order': 5
            },
            {
                'name': 'Massage Therapy',
                'slug': 'massage-therapy',
                'description': 'Therapeutic and relaxation massage services',
                'is_active': True,
                'is_popular': False,
                'sort_order': 6
            }
        ]

        created_count = 0
        for category_data in categories:
            category, created = ServiceCategory.objects.get_or_create(
                slug=category_data['slug'],
                defaults=category_data
            )
            if created:
                created_count += 1
                self.stdout.write(f'✅ Created category: {category.name}')

        if created_count > 0:
            self.stdout.write(f'📊 Categories created: {created_count}')

    def generate_customers(self):
        """Generate customer accounts"""
        self.stdout.write(f'\n👤 Generating {self.customers_count} customer accounts...')
        
        customers = []
        for i in range(self.customers_count):
            try:
                customer = CustomerFactory.create_customer()
                customers.append(customer)
                
                if (i + 1) % 5 == 0:  # Progress update every 5 customers
                    self.stdout.write(f'   ✅ Created {i + 1} customers...')
                    
            except Exception as e:
                self.stdout.write(f'   ⚠️  Failed to create customer {i + 1}: {e}')
                continue

        self.stdout.write(f'📊 Total customers created: {len(customers)}')
        return customers

    def generate_providers(self):
        """Generate service provider accounts"""
        self.stdout.write(f'\n🏢 Generating {self.providers_count} provider accounts...')
        
        # Get available categories
        categories = list(ServiceCategory.objects.filter(is_active=True))
        if not categories:
            raise CommandError('No active service categories found. Create categories first.')
        
        providers = []
        for i in range(self.providers_count):
            try:
                # Distribute providers across categories
                category = categories[i % len(categories)]
                provider = ProviderFactory.create_provider(category_slug=category.slug)
                providers.append(provider)
                
                if (i + 1) % 3 == 0:  # Progress update every 3 providers
                    self.stdout.write(f'   ✅ Created {i + 1} providers...')
                    
            except Exception as e:
                self.stdout.write(f'   ⚠️  Failed to create provider {i + 1}: {e}')
                continue

        self.stdout.write(f'📊 Total providers created: {len(providers)}')
        return providers

    def generate_services(self, providers):
        """Generate services for providers"""
        if not providers:
            self.stdout.write('⚠️  No providers available for service generation')
            return []

        self.stdout.write(f'\n🛍️  Generating services for providers...')
        
        services = []
        total_services = 0
        
        for provider in providers:
            # Generate 2-6 services per provider (average 4)
            num_services = random.randint(2, 6)
            provider_services = []
            
            for i in range(num_services):
                try:
                    service = ServiceFactory.create_service(provider)
                    services.append(service)
                    provider_services.append(service)
                    total_services += 1
                    
                except Exception as e:
                    self.stdout.write(f'   ⚠️  Failed to create service for {provider.business_name}: {e}')
                    continue
            
            self.stdout.write(
                f'   ✅ Created {len(provider_services)} services for {provider.business_name}'
            )

        self.stdout.write(f'📊 Total services created: {total_services}')
        return services

    def print_summary(self):
        """Print generation summary"""
        # Get final counts
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        test_users = User.objects.filter(is_test_account=True).count()
        customers = User.objects.filter(is_test_account=True, role='customer').count()
        providers = User.objects.filter(is_test_account=True, role='service_provider').count()
        categories = ServiceCategory.objects.count()
        services = Service.objects.count()
        provider_profiles = ServiceProvider.objects.count()

        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🎉 SAMPLE DATA GENERATION COMPLETE')
        )
        self.stdout.write('='*60)

        self.stdout.write(f'\n📊 Generated Data Summary:')
        self.stdout.write(f'   👥 Total Test Users: {test_users}')
        self.stdout.write(f'   👤 Customers: {customers}')
        self.stdout.write(f'   🏢 Providers: {providers}')
        self.stdout.write(f'   🏪 Provider Profiles: {provider_profiles}')
        self.stdout.write(f'   📂 Categories: {categories}')
        self.stdout.write(f'   🛍️  Services: {services}')

        # Calculate averages
        if provider_profiles > 0:
            avg_services = round(services / provider_profiles, 1)
            self.stdout.write(f'   📈 Average services per provider: {avg_services}')

        self.stdout.write(f'\n🔑 Test Credentials:')
        self.stdout.write(f'   Password for all accounts: TestPass123!')

        self.stdout.write(f'\n📱 Quick Access:')
        self.stdout.write(f'   🌐 API Base: http://localhost:8000/api/')
        self.stdout.write(f'   📚 API Docs: http://localhost:8000/api/docs/')
        self.stdout.write(f'   🔧 Admin Panel: http://localhost:8000/admin/')

        self.stdout.write(f'\n💡 Management Commands:')
        self.stdout.write(f'   🧹 Cleanup: python manage.py cleanup_test_accounts')
        self.stdout.write(f'   🔄 Regenerate: python manage.py generate_sample_data --force')
        self.stdout.write(f'   📊 Custom size: python manage.py generate_sample_data --customers 50 --providers 30')

        self.stdout.write(f'\n🚀 Sample data ready for development!')
        self.stdout.write(f'📱 Frontend can now connect to realistic test environment')
        self.stdout.write(f'🧪 Comprehensive data available for testing scenarios')
