/**
 * Simple Authentication API Service Test
 * Basic tests for authentication API without complex mocking
 */

import { authAPI } from '../auth';

// Mock the API client
jest.mock('../client', () => ({
  apiClient: {
    post: jest.fn(),
    get: jest.fn(),
    patch: jest.fn(),
  },
}));

import { apiClient } from '../client';
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('Authentication API Service - Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should make POST request to login endpoint', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '1',
            email: '<EMAIL>',
            username: 'testuser',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.login(loginData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/login/', loginData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('register', () => {
    it('should make POST request to register endpoint', async () => {
      const registerData = {
        email: '<EMAIL>',
        username: 'newuser',
        password: 'password123',
        password_confirm: 'password123',
        first_name: 'New',
        last_name: 'User',
      };

      const mockResponse = {
        data: {
          access: 'mock-access-token',
          refresh: 'mock-refresh-token',
          user: {
            id: '2',
            email: '<EMAIL>',
            username: 'newuser',
          },
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await authAPI.register(registerData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/register/', registerData);
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getProfile', () => {
    it('should make GET request to profile endpoint', async () => {
      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await authAPI.getProfile();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/auth/profile/');
      expect(result).toEqual(mockResponse.data);
    });
  });
});
