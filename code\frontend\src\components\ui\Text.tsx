/**
 * Enhanced Text Component
 * Based on shadcn/ui design patterns for React Native
 */

import React from 'react';
import {
  Text as RNText,
  StyleSheet,
  TextStyle,
  TextProps as RNTextProps,
} from 'react-native';

import { useTheme } from '../../contexts/ThemeContext';
import { createVariants, mergeStyles } from '../../lib/utils';

export interface TextProps extends RNTextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'caption' | 'button' | 'overline';
  color?: 'primary' | 'secondary' | 'muted' | 'accent' | 'destructive' | 'success' | 'warning';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  align?: 'left' | 'center' | 'right' | 'justify';
  children: React.ReactNode;
  style?: TextStyle;
}

export const Text: React.FC<TextProps> = ({
  variant = 'body',
  color = 'primary',
  weight = 'normal',
  align = 'left',
  children,
  style,
  testID,
  ...props
}) => {
  const { colors, typography } = useTheme();

  // Create text variants using theme
  const textVariants = createVariants({
    base: {
      // Base styles
      fontSize: typography.fontSize.base,
      fontWeight: '400' as const,
      color: colors.text.primary,
    },
    variants: {
      variant: {
        h1: {
          fontSize: typography.fontSize['4xl'],
          fontWeight: '700' as const,
        },
        h2: {
          fontSize: typography.fontSize['3xl'],
          fontWeight: '600' as const,
        },
        h3: {
          fontSize: typography.fontSize['2xl'],
          fontWeight: '600' as const,
        },
        h4: {
          fontSize: typography.fontSize.xl,
          fontWeight: '600' as const,
        },
        body: {
          fontSize: typography.fontSize.base,
          fontWeight: '400' as const,
        },
        caption: {
          fontSize: typography.fontSize.sm,
          fontWeight: '400' as const,
        },
        button: {
          fontSize: typography.fontSize.base,
          fontWeight: '600' as const,
        },
        overline: {
          fontSize: typography.fontSize.sm,
          fontWeight: '500' as const,
          textTransform: 'uppercase' as const,
          letterSpacing: 0.5,
        },
      },
      color: {
        primary: {
          color: colors.text.primary,
        },
        secondary: {
          color: colors.text.secondary,
        },
        muted: {
          color: colors.text.tertiary,
        },
        accent: {
          color: colors.primary,
        },
        destructive: {
          color: colors.error,
        },
        success: {
          color: colors.success,
        },
        warning: {
          color: colors.warning,
        },
      },
      weight: {
        normal: {
          fontWeight: '400' as const,
        },
        medium: {
          fontWeight: '500' as const,
        },
        semibold: {
          fontWeight: '600' as const,
        },
        bold: {
          fontWeight: '700' as const,
        },
      },
      align: {
        left: {
          textAlign: 'left' as const,
        },
        center: {
          textAlign: 'center' as const,
        },
        right: {
          textAlign: 'right' as const,
        },
        justify: {
          textAlign: 'justify' as const,
        },
      },
    },
    defaultVariants: {
      variant: 'body',
      color: 'primary',
      weight: 'normal',
      align: 'left',
    }
  });

  const textStyle = textVariants({ variant, color, weight, align });
  const finalStyle = mergeStyles(textStyle, style);

  return (
    <RNText style={finalStyle} testID={testID} {...props}>
      {children}
    </RNText>
  );
};

export default Text;
