/**
 * End-to-End Booking Flow Tests - Complete user journey testing
 *
 * Test Coverage:
 * - User authentication and onboarding
 * - Service discovery and search functionality
 * - Complete booking process from search to confirmation
 * - Payment processing and receipt generation
 * - Booking management and modifications
 * - Cross-platform compatibility testing
 *
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

describe('Booking Flow E2E Tests', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('User Authentication', () => {
    it('should allow user to login successfully', async () => {
      // Wait for app to load
      await waitFor(element(by.id('welcome-screen')))
        .toBeVisible()
        .withTimeout(10000);

      // Navigate to login
      await element(by.id('login-button')).tap();

      // Wait for login screen
      await waitFor(element(by.id('login-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Enter credentials
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');

      // Submit login
      await element(by.id('submit-login-button')).tap();

      // Wait for home screen
      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);

      // Verify user is logged in
      await expect(element(by.text('Welcome back, John'))).toBeVisible();
    });

    it('should handle login errors gracefully', async () => {
      await element(by.id('login-button')).tap();

      // Enter invalid credentials
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('wrongpassword');

      await element(by.id('submit-login-button')).tap();

      // Verify error message is displayed
      await waitFor(element(by.text('Invalid credentials')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Service Discovery', () => {
    beforeEach(async () => {
      // Login first
      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should allow user to search for services', async () => {
      // Navigate to search
      await element(by.id('search-tab')).tap();

      // Wait for search screen
      await waitFor(element(by.id('search-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Perform search
      await element(by.id('search-input')).typeText('haircut');
      await element(by.id('search-button')).tap();

      // Wait for search results
      await waitFor(element(by.id('search-results')))
        .toBeVisible()
        .withTimeout(10000);

      // Verify results are displayed
      await expect(element(by.text('Professional Haircut'))).toBeVisible();
      await expect(element(by.text('Elite Hair Studio'))).toBeVisible();
    });

    it('should display service details when tapped', async () => {
      await element(by.id('search-tab')).tap();
      await element(by.id('search-input')).typeText('haircut');
      await element(by.id('search-button')).tap();

      await waitFor(element(by.id('search-results')))
        .toBeVisible()
        .withTimeout(10000);

      // Tap on first service
      await element(by.id('service-card-0')).tap();

      // Wait for service details screen
      await waitFor(element(by.id('service-details-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Verify service details are displayed
      await expect(element(by.text('Professional Haircut'))).toBeVisible();
      await expect(element(by.text('$65.00'))).toBeVisible();
      await expect(element(by.text('60 minutes'))).toBeVisible();
    });

    it('should filter search results', async () => {
      await element(by.id('search-tab')).tap();
      await element(by.id('search-input')).typeText('beauty');
      await element(by.id('search-button')).tap();

      await waitFor(element(by.id('search-results')))
        .toBeVisible()
        .withTimeout(10000);

      // Open filters
      await element(by.id('filter-button')).tap();

      // Apply price filter
      await element(by.id('price-filter-50-100')).tap();
      await element(by.id('apply-filters-button')).tap();

      // Verify filtered results
      await waitFor(element(by.id('search-results')))
        .toBeVisible()
        .withTimeout(5000);

      // Results should be filtered by price range
      await expect(element(by.text('2 services found'))).toBeVisible();
    });
  });

  describe('Booking Process', () => {
    beforeEach(async () => {
      // Login and navigate to service
      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);

      await element(by.id('search-tab')).tap();
      await element(by.id('search-input')).typeText('haircut');
      await element(by.id('search-button')).tap();

      await waitFor(element(by.id('search-results')))
        .toBeVisible()
        .withTimeout(10000);

      await element(by.id('service-card-0')).tap();

      await waitFor(element(by.id('service-details-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });

    it('should complete booking flow successfully', async () => {
      // Start booking process
      await element(by.id('book-now-button')).tap();

      // Wait for booking screen
      await waitFor(element(by.id('booking-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Select date
      await element(by.id('date-picker')).tap();
      await element(by.text('15')).tap(); // Select 15th of current month
      await element(by.id('confirm-date-button')).tap();

      // Select time slot
      await waitFor(element(by.id('time-slots')))
        .toBeVisible()
        .withTimeout(5000);

      await element(by.id('time-slot-09:00')).tap();

      // Add notes
      await element(by.id('notes-input')).typeText('First time customer');

      // Proceed to payment
      await element(by.id('proceed-to-payment-button')).tap();

      // Wait for payment screen
      await waitFor(element(by.id('payment-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Select payment method
      await element(by.id('payment-method-card-0')).tap();

      // Confirm payment
      await element(by.id('confirm-payment-button')).tap();

      // Wait for confirmation screen
      await waitFor(element(by.id('booking-confirmation-screen')))
        .toBeVisible()
        .withTimeout(15000);

      // Verify booking confirmation
      await expect(element(by.text('Booking Confirmed!'))).toBeVisible();
      await expect(element(by.text('VB-2024-'))).toBeVisible(); // Booking number
      await expect(element(by.text('Professional Haircut'))).toBeVisible();
    });

    it('should handle unavailable time slots', async () => {
      await element(by.id('book-now-button')).tap();

      await waitFor(element(by.id('booking-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Select date
      await element(by.id('date-picker')).tap();
      await element(by.text('20')).tap(); // Select a date with no availability
      await element(by.id('confirm-date-button')).tap();

      // Verify no available slots message
      await waitFor(element(by.text('No available time slots for this date')))
        .toBeVisible()
        .withTimeout(5000);

      // Suggest alternative dates
      await expect(
        element(by.text('Try these available dates:')),
      ).toBeVisible();
    });

    it('should allow booking modification', async () => {
      // Complete a booking first (abbreviated)
      await element(by.id('book-now-button')).tap();
      await element(by.id('date-picker')).tap();
      await element(by.text('15')).tap();
      await element(by.id('confirm-date-button')).tap();
      await element(by.id('time-slot-09:00')).tap();
      await element(by.id('proceed-to-payment-button')).tap();
      await element(by.id('payment-method-card-0')).tap();
      await element(by.id('confirm-payment-button')).tap();

      await waitFor(element(by.id('booking-confirmation-screen')))
        .toBeVisible()
        .withTimeout(15000);

      // Navigate to bookings
      await element(by.id('view-booking-button')).tap();

      await waitFor(element(by.id('booking-details-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Reschedule booking
      await element(by.id('reschedule-button')).tap();

      await waitFor(element(by.id('reschedule-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Select new date and time
      await element(by.id('date-picker')).tap();
      await element(by.text('16')).tap();
      await element(by.id('confirm-date-button')).tap();
      await element(by.id('time-slot-10:00')).tap();
      await element(by.id('confirm-reschedule-button')).tap();

      // Verify reschedule confirmation
      await waitFor(element(by.text('Booking rescheduled successfully')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Payment Processing', () => {
    beforeEach(async () => {
      // Login and navigate to payment
      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should add new payment method', async () => {
      // Navigate to payment management
      await element(by.id('profile-tab')).tap();
      await element(by.id('payment-methods-button')).tap();

      await waitFor(element(by.id('payment-management-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Add new payment method
      await element(by.id('add-payment-method-button')).tap();

      await waitFor(element(by.id('add-payment-method-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Fill payment details
      await element(by.id('card-number-input')).typeText('****************');
      await element(by.id('expiry-input')).typeText('12/25');
      await element(by.id('cvc-input')).typeText('123');
      await element(by.id('cardholder-name-input')).typeText('John Doe');

      // Save payment method
      await element(by.id('save-payment-method-button')).tap();

      // Verify payment method was added
      await waitFor(element(by.text('Payment method added successfully')))
        .toBeVisible()
        .withTimeout(5000);

      await expect(element(by.text('VISA •••• 4242'))).toBeVisible();
    });

    it('should handle payment failures gracefully', async () => {
      // Navigate to service and start booking with invalid payment
      await element(by.id('search-tab')).tap();
      await element(by.id('search-input')).typeText('haircut');
      await element(by.id('search-button')).tap();
      await element(by.id('service-card-0')).tap();
      await element(by.id('book-now-button')).tap();

      // Complete booking details
      await element(by.id('date-picker')).tap();
      await element(by.text('15')).tap();
      await element(by.id('confirm-date-button')).tap();
      await element(by.id('time-slot-09:00')).tap();
      await element(by.id('proceed-to-payment-button')).tap();

      // Use a card that will be declined
      await element(by.id('add-payment-method-button')).tap();
      await element(by.id('card-number-input')).typeText('****************'); // Declined card
      await element(by.id('expiry-input')).typeText('12/25');
      await element(by.id('cvc-input')).typeText('123');
      await element(by.id('cardholder-name-input')).typeText('John Doe');
      await element(by.id('save-payment-method-button')).tap();

      // Attempt payment
      await element(by.id('confirm-payment-button')).tap();

      // Verify error handling
      await waitFor(element(by.text('Payment failed')))
        .toBeVisible()
        .withTimeout(10000);

      await expect(
        element(by.text('Please try a different payment method')),
      ).toBeVisible();
    });
  });

  describe('Booking Management', () => {
    beforeEach(async () => {
      // Login
      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should display user bookings', async () => {
      // Navigate to bookings
      await element(by.id('bookings-tab')).tap();

      await waitFor(element(by.id('booking-management-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Verify bookings are displayed
      await expect(element(by.text('My Bookings'))).toBeVisible();

      // Check for upcoming bookings
      await element(by.text('Upcoming')).tap();
      await expect(element(by.id('booking-card-0'))).toBeVisible();
    });

    it('should allow booking cancellation', async () => {
      await element(by.id('bookings-tab')).tap();

      await waitFor(element(by.id('booking-management-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Tap on a booking
      await element(by.id('booking-card-0')).tap();

      await waitFor(element(by.id('booking-details-screen')))
        .toBeVisible()
        .withTimeout(5000);

      // Cancel booking
      await element(by.id('cancel-booking-button')).tap();

      // Confirm cancellation
      await waitFor(element(by.text('Cancel Booking')))
        .toBeVisible()
        .withTimeout(3000);

      await element(by.text('Yes, Cancel')).tap();

      // Select cancellation reason
      await element(by.text('Schedule conflict')).tap();

      // Verify cancellation
      await waitFor(element(by.text('Booking cancelled successfully')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle network connectivity issues', async () => {
      // Simulate network disconnection
      await device.setURLBlacklist(['.*']);

      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      // Verify network error handling
      await waitFor(element(by.text('Network error')))
        .toBeVisible()
        .withTimeout(10000);

      await expect(
        element(by.text('Please check your internet connection')),
      ).toBeVisible();

      // Re-enable network
      await device.setURLBlacklist([]);
    });

    it('should handle app backgrounding during booking', async () => {
      // Login and start booking
      await element(by.id('login-button')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('submit-login-button')).tap();

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);

      await element(by.id('search-tab')).tap();
      await element(by.id('search-input')).typeText('haircut');
      await element(by.id('search-button')).tap();
      await element(by.id('service-card-0')).tap();
      await element(by.id('book-now-button')).tap();

      // Background the app
      await device.sendToHome();
      await device.launchApp();

      // Verify booking state is preserved
      await waitFor(element(by.id('booking-screen')))
        .toBeVisible()
        .withTimeout(5000);

      await expect(element(by.text('Professional Haircut'))).toBeVisible();
    });
  });

  describe('Accessibility', () => {
    it('should be accessible to screen readers', async () => {
      // Enable accessibility
      await device.enableSynchronization();

      await element(by.id('login-button')).tap();

      // Verify accessibility labels are present
      await expect(element(by.label('Email input field'))).toBeVisible();
      await expect(element(by.label('Password input field'))).toBeVisible();
      await expect(element(by.label('Login button'))).toBeVisible();
    });

    it('should support keyboard navigation', async () => {
      await element(by.id('login-button')).tap();

      // Navigate using keyboard
      await element(by.id('email-input')).tap();
      await element(by.id('email-input')).typeText('<EMAIL>');

      // Tab to next field
      await device.pressKey('tab');
      await element(by.id('password-input')).typeText('password123');

      // Submit with enter
      await device.pressKey('enter');

      await waitFor(element(by.id('home-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });
});
