"""
Booking URLs for Vierla Beauty Services Marketplace
Comprehensive URL routing for booking management
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .views import BookingViewSet, TimeSlotViewSet, BookingNotificationViewSet

app_name = 'bookings'

# Create router and register viewsets
router = DefaultRouter()
router.register(r'bookings', BookingViewSet, basename='booking')
router.register(r'time-slots', TimeSlotViewSet, basename='timeslot')
router.register(r'notifications', BookingNotificationViewSet,
                basename='notification')

urlpatterns = [
    path('', include(router.urls)),
]

# URL patterns will be:
# /api/bookings/bookings/ - List/Create bookings
# /api/bookings/bookings/{id}/ - Retrieve/Update/Delete booking
# /api/bookings/bookings/{id}/confirm/ - Confirm booking
# /api/bookings/bookings/{id}/start/ - Start booking
# /api/bookings/bookings/{id}/complete/ - Complete booking
# /api/bookings/bookings/{id}/cancel/ - Cancel booking
# /api/bookings/bookings/{id}/mark_no_show/ - Mark as no show
# /api/bookings/bookings/upcoming/ - Get upcoming bookings
# /api/bookings/bookings/today/ - Get today's bookings
# /api/bookings/bookings/history/ - Get booking history
# /api/bookings/bookings/stats/ - Get booking statistics
#
# /api/bookings/time-slots/ - List/Create time slots
# /api/bookings/time-slots/{id}/ - Retrieve/Update/Delete time slot
# /api/bookings/time-slots/available/ - Get available time slots
#
# /api/bookings/notifications/ - List notifications
# /api/bookings/notifications/{id}/ - Retrieve notification
# /api/bookings/notifications/{id}/mark_read/ - Mark notification as read
# /api/bookings/notifications/mark_all_read/ - Mark all notifications as read
