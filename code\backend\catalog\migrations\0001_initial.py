# Generated by Django 5.2.4 on 2025-08-05 04:17

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ServiceCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the service category",
                        max_length=100,
                        unique=True,
                        verbose_name="category name",
                    ),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="URL-friendly version of the name",
                        max_length=120,
                        unique=True,
                        verbose_name="slug",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the category",
                        verbose_name="description",
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        help_text="Icon identifier (emoji or icon name)",
                        max_length=50,
                        verbose_name="icon",
                    ),
                ),
                (
                    "color",
                    models.CharField(
                        default="#8FBC8F",
                        help_text="Hex color code for the category",
                        max_length=7,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Color must be a valid hex color code",
                                regex="^#[0-9A-Fa-f]{6}$",
                            )
                        ],
                        verbose_name="color",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Category banner image",
                        null=True,
                        upload_to="categories/%Y/%m/",
                        verbose_name="category image",
                    ),
                ),
                (
                    "is_popular",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as popular category for featured display",
                        verbose_name="is popular",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this category is active and visible",
                        verbose_name="is active",
                    ),
                ),
                (
                    "sort_order",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Order for displaying categories",
                        verbose_name="sort order",
                    ),
                ),
                (
                    "mobile_icon",
                    models.CharField(
                        blank=True,
                        help_text="Mobile-specific icon identifier",
                        max_length=50,
                        verbose_name="mobile icon",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent category for hierarchical organization",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="catalog.servicecategory",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Category",
                "verbose_name_plural": "Service Categories",
                "ordering": ["sort_order", "name"],
            },
        ),
        migrations.CreateModel(
            name="ServiceProvider",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        help_text="Name of the business or service provider",
                        max_length=200,
                        verbose_name="business name",
                    ),
                ),
                (
                    "business_description",
                    models.TextField(
                        help_text="Detailed description of the business and services offered",
                        verbose_name="business description",
                    ),
                ),
                (
                    "business_phone",
                    models.CharField(
                        help_text="Business contact phone number",
                        max_length=20,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be in valid format",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                        verbose_name="business phone",
                    ),
                ),
                (
                    "business_email",
                    models.EmailField(
                        help_text="Business contact email address",
                        max_length=254,
                        verbose_name="business email",
                    ),
                ),
                (
                    "address",
                    models.TextField(
                        help_text="Full business address",
                        verbose_name="business address",
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        help_text="Business city", max_length=100, verbose_name="city"
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        help_text="State or province",
                        max_length=100,
                        verbose_name="state/province",
                    ),
                ),
                (
                    "zip_code",
                    models.CharField(
                        help_text="Zip or postal code",
                        max_length=20,
                        verbose_name="zip/postal code",
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        default="Canada",
                        help_text="Country",
                        max_length=100,
                        verbose_name="country",
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        help_text="Latitude coordinate for location-based services",
                        max_digits=10,
                        null=True,
                        verbose_name="latitude",
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True,
                        decimal_places=8,
                        help_text="Longitude coordinate for location-based services",
                        max_digits=11,
                        null=True,
                        verbose_name="longitude",
                    ),
                ),
                (
                    "website",
                    models.URLField(
                        blank=True,
                        help_text="Business website URL",
                        verbose_name="website",
                    ),
                ),
                (
                    "instagram_handle",
                    models.CharField(
                        blank=True,
                        help_text="Instagram username without @",
                        max_length=100,
                        verbose_name="Instagram handle",
                    ),
                ),
                (
                    "facebook_url",
                    models.URLField(
                        blank=True,
                        help_text="Facebook business page URL",
                        verbose_name="Facebook page",
                    ),
                ),
                (
                    "profile_image",
                    models.ImageField(
                        blank=True,
                        help_text="Business profile picture",
                        null=True,
                        upload_to="providers/profiles/%Y/%m/",
                        verbose_name="profile image",
                    ),
                ),
                (
                    "cover_image",
                    models.ImageField(
                        blank=True,
                        help_text="Business cover/banner image",
                        null=True,
                        upload_to="providers/covers/%Y/%m/",
                        verbose_name="cover image",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the business is verified by admin",
                        verbose_name="is verified",
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False,
                        help_text="Whether to feature this provider prominently",
                        verbose_name="is featured",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the provider is active and accepting bookings",
                        verbose_name="is active",
                    ),
                ),
                (
                    "rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Average rating from customer reviews",
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("5.00")),
                        ],
                        verbose_name="average rating",
                    ),
                ),
                (
                    "review_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of reviews received",
                        verbose_name="review count",
                    ),
                ),
                (
                    "total_bookings",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of completed bookings",
                        verbose_name="total bookings",
                    ),
                ),
                (
                    "years_of_experience",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Years of professional experience",
                        null=True,
                        verbose_name="years of experience",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "categories",
                    models.ManyToManyField(
                        help_text="Service categories offered by this provider",
                        related_name="providers",
                        to="catalog.servicecategory",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="User account associated with this provider",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="service_provider",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Service Provider",
                "verbose_name_plural": "Service Providers",
                "ordering": ["-is_featured", "-rating", "business_name"],
            },
        ),
        migrations.CreateModel(
            name="Service",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the service",
                        max_length=200,
                        verbose_name="service name",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed description of the service",
                        verbose_name="service description",
                    ),
                ),
                (
                    "short_description",
                    models.CharField(
                        blank=True,
                        help_text="Brief description for mobile display",
                        max_length=255,
                        verbose_name="short description",
                    ),
                ),
                (
                    "mobile_description",
                    models.TextField(
                        blank=True,
                        help_text="Mobile-optimized description",
                        verbose_name="mobile description",
                    ),
                ),
                (
                    "base_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Base price for the service",
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="base price",
                    ),
                ),
                (
                    "price_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Price"),
                            ("hourly", "Hourly Rate"),
                            ("range", "Price Range"),
                            ("consultation", "Consultation Required"),
                        ],
                        default="fixed",
                        help_text="Type of pricing structure",
                        max_length=20,
                        verbose_name="price type",
                    ),
                ),
                (
                    "max_price",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Maximum price for range pricing",
                        max_digits=10,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                        verbose_name="maximum price",
                    ),
                ),
                (
                    "duration",
                    models.PositiveIntegerField(
                        help_text="Service duration in minutes",
                        verbose_name="duration (minutes)",
                    ),
                ),
                (
                    "buffer_time",
                    models.PositiveIntegerField(
                        default=15,
                        help_text="Buffer time between appointments",
                        verbose_name="buffer time (minutes)",
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        help_text="Main service image",
                        null=True,
                        upload_to="services/%Y/%m/",
                        verbose_name="service image",
                    ),
                ),
                (
                    "is_popular",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as popular service for featured display",
                        verbose_name="is popular",
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the service is currently available for booking",
                        verbose_name="is available",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the service is active and visible",
                        verbose_name="is active",
                    ),
                ),
                (
                    "requirements",
                    models.TextField(
                        blank=True,
                        help_text="Special requirements or prerequisites for the service",
                        verbose_name="requirements",
                    ),
                ),
                (
                    "preparation_instructions",
                    models.TextField(
                        blank=True,
                        help_text="Instructions for client preparation before the service",
                        verbose_name="preparation instructions",
                    ),
                ),
                (
                    "booking_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of bookings for this service",
                        verbose_name="booking count",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "category",
                    models.ForeignKey(
                        help_text="Category this service belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="services",
                        to="catalog.servicecategory",
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider offering this service",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="services",
                        to="catalog.serviceprovider",
                    ),
                ),
            ],
            options={
                "verbose_name": "Service",
                "verbose_name_plural": "Services",
                "ordering": ["-is_popular", "base_price", "name"],
            },
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(fields=["name"], name="catalog_ser_name_bdc703_idx"),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(fields=["slug"], name="catalog_ser_slug_b96bd1_idx"),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["is_active"], name="catalog_ser_is_acti_0ecd64_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["is_popular"], name="catalog_ser_is_popu_3f01f9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="servicecategory",
            index=models.Index(
                fields=["sort_order"], name="catalog_ser_sort_or_251bcf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["business_name"], name="catalog_ser_busines_46b31d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(fields=["city"], name="catalog_ser_city_19a79f_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["is_active"], name="catalog_ser_is_acti_3f700a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["is_verified"], name="catalog_ser_is_veri_6e87b7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(fields=["rating"], name="catalog_ser_rating_f2fa09_idx"),
        ),
        migrations.AddIndex(
            model_name="serviceprovider",
            index=models.Index(
                fields=["is_featured"], name="catalog_ser_is_feat_efa540_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(fields=["name"], name="catalog_ser_name_e9ac3e_idx"),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["base_price"], name="catalog_ser_base_pr_b00282_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["is_active"], name="catalog_ser_is_acti_43a295_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["is_available"], name="catalog_ser_is_avai_d94635_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["is_popular"], name="catalog_ser_is_popu_92f93f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["created_at"], name="catalog_ser_created_6c841f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["provider", "is_active"], name="catalog_ser_provide_b925d8_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="service",
            index=models.Index(
                fields=["category", "is_active"], name="catalog_ser_categor_657b62_idx"
            ),
        ),
    ]
