"""
Simple test to verify ALLOWED_HOSTS configuration
This test checks if the required IP addresses are in ALLOWED_HOSTS
"""
import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'vierla_project.settings')

import django
django.setup()

from django.conf import settings

def test_allowed_hosts():
    """Test that required IPs are in ALLOWED_HOSTS"""
    print("Current ALLOWED_HOSTS:", settings.ALLOWED_HOSTS)
    
    required_hosts = ['localhost', '127.0.0.1', '************', '********']
    missing_hosts = []
    
    for host in required_hosts:
        if host not in settings.ALLOWED_HOSTS:
            missing_hosts.append(host)
    
    if missing_hosts:
        print(f"FAIL: Missing hosts in ALLOWED_HOSTS: {missing_hosts}")
        return False
    else:
        print("PASS: All required hosts are in ALLOWED_HOSTS")
        return True

if __name__ == '__main__':
    success = test_allowed_hosts()
    sys.exit(0 if success else 1)
