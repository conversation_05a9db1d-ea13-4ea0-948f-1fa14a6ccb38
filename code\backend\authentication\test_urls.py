"""
URL Configuration Tests for Authentication App
Tests that all authentication URLs are properly configured and accessible
"""

from django.test import TestCase
from django.urls import reverse, resolve
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase

User = get_user_model()


class AuthenticationURLTests(TestCase):
    """Test authentication URL patterns"""
    
    def test_login_url_resolves(self):
        """Test that login URL resolves correctly"""
        url = reverse('authentication:login')
        self.assertEqual(url, '/api/auth/login/')
    
    def test_register_url_resolves(self):
        """Test that register URL resolves correctly"""
        url = reverse('authentication:register')
        self.assertEqual(url, '/api/auth/register/')
    
    def test_logout_url_resolves(self):
        """Test that logout URL resolves correctly"""
        url = reverse('authentication:logout')
        self.assertEqual(url, '/api/auth/logout/')
    
    def test_token_refresh_url_resolves(self):
        """Test that token refresh URL resolves correctly"""
        url = reverse('authentication:token_refresh')
        self.assertEqual(url, '/api/auth/token/refresh/')
    
    def test_profile_url_resolves(self):
        """Test that profile URL resolves correctly"""
        url = reverse('authentication:profile')
        self.assertEqual(url, '/api/auth/profile/')
    
    def test_social_auth_url_resolves(self):
        """Test that social auth URL resolves correctly"""
        url = reverse('authentication:social_auth')
        self.assertEqual(url, '/api/auth/social/')
    
    def test_verify_email_url_resolves(self):
        """Test that verify email URL resolves correctly"""
        url = reverse('authentication:verify_email')
        self.assertEqual(url, '/api/auth/verify-email/')
    
    def test_password_reset_request_url_resolves(self):
        """Test that password reset request URL resolves correctly"""
        url = reverse('authentication:password_reset_request')
        self.assertEqual(url, '/api/auth/password/reset/')
    
    def test_password_reset_confirm_url_resolves(self):
        """Test that password reset confirm URL resolves correctly"""
        url = reverse('authentication:password_reset_confirm')
        self.assertEqual(url, '/api/auth/password/reset/confirm/')


class AuthenticationURLAccessTests(APITestCase):
    """Test authentication URL access and responses"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='urltest',
            password='URLTest123!'
        )
    
    def test_login_url_accessible(self):
        """Test that login URL is accessible"""
        url = reverse('authentication:login')
        response = self.client.post(url, {
            'email': '<EMAIL>',
            'password': 'password'
        })
        # Should return 400 for invalid credentials, not 404
        self.assertNotEqual(response.status_code, 404)
    
    def test_register_url_accessible(self):
        """Test that register URL is accessible"""
        url = reverse('authentication:register')
        response = self.client.post(url, {
            'email': '<EMAIL>',
            'username': 'newuser',
            'password': 'NewPassword123!'
        })
        # Should not return 404
        self.assertNotEqual(response.status_code, 404)
    
    def test_profile_url_requires_authentication(self):
        """Test that profile URL requires authentication"""
        url = reverse('authentication:profile')
        response = self.client.get(url)
        # Should return 401 unauthorized, not 404
        self.assertEqual(response.status_code, 401)
