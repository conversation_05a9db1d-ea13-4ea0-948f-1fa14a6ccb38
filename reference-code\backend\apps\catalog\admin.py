"""
Django Admin Configuration for Service Catalog
Enhanced admin interface with mobile-first considerations and comprehensive management features
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Q
from django.contrib.admin import SimpleListFilter

from .models import (
    ServiceCategory, ServiceProvider, Service, OperatingHours,
    ServiceAvailability, ServiceGallery, ServiceLocation
)


class ServiceCategoryFilter(SimpleListFilter):
    """Custom filter for service categories"""
    title = 'Category Level'
    parameter_name = 'category_level'

    def lookups(self, request, model_admin):
        return (
            ('parent', 'Parent Categories'),
            ('child', 'Child Categories'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'parent':
            return queryset.filter(parent__isnull=True)
        if self.value() == 'child':
            return queryset.filter(parent__isnull=False)
        return queryset


class ProviderStatusFilter(SimpleListFilter):
    """Custom filter for provider status"""
    title = 'Provider Status'
    parameter_name = 'provider_status'

    def lookups(self, request, model_admin):
        return (
            ('verified_active', 'Verified & Active'),
            ('featured', 'Featured'),
            ('new', 'New (Unverified)'),
            ('inactive', 'Inactive'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'verified_active':
            return queryset.filter(is_verified=True, is_active=True)
        if self.value() == 'featured':
            return queryset.filter(is_featured=True)
        if self.value() == 'new':
            return queryset.filter(is_verified=False, is_active=True)
        if self.value() == 'inactive':
            return queryset.filter(is_active=False)
        return queryset


class ServiceStatusFilter(SimpleListFilter):
    """Custom filter for service status"""
    title = 'Service Status'
    parameter_name = 'service_status'

    def lookups(self, request, model_admin):
        return (
            ('active_available', 'Active & Available'),
            ('popular', 'Popular'),
            ('unavailable', 'Unavailable'),
            ('inactive', 'Inactive'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'active_available':
            return queryset.filter(is_active=True, is_available=True)
        if self.value() == 'popular':
            return queryset.filter(is_popular=True)
        if self.value() == 'unavailable':
            return queryset.filter(is_active=True, is_available=False)
        if self.value() == 'inactive':
            return queryset.filter(is_active=False)
        return queryset


class SubcategoryInline(admin.TabularInline):
    """Inline admin for subcategories"""
    model = ServiceCategory
    fk_name = 'parent'
    extra = 0
    fields = ('name', 'slug', 'icon', 'is_active', 'is_popular', 'sort_order')
    readonly_fields = ('slug',)


class ServiceInline(admin.TabularInline):
    """Inline admin for services"""
    model = Service
    extra = 0
    fields = ('name', 'base_price', 'duration',
              'is_active', 'is_available', 'is_popular')
    readonly_fields = ('booking_count',)


class OperatingHoursInline(admin.TabularInline):
    """Inline admin for operating hours"""
    model = OperatingHours
    extra = 0
    fields = ('day', 'is_open', 'open_time', 'close_time',
              'break_start', 'break_end', 'notes')


class ServiceGalleryInline(admin.TabularInline):
    """Inline admin for service gallery"""
    model = ServiceGallery
    extra = 0
    fields = ('image', 'image_type', 'caption',
              'is_featured', 'is_cover', 'sort_order')
    readonly_fields = ('image_preview',)

    def image_preview(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 50px; max-width: 100px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Preview"


@admin.register(ServiceCategory)
class ServiceCategoryAdmin(admin.ModelAdmin):
    """Admin interface for Service Categories"""

    list_display = [
        'name', 'parent', 'level_indicator', 'icon_display', 'is_active',
        'is_popular', 'service_count', 'sort_order', 'created_at'
    ]
    list_filter = [ServiceCategoryFilter,
                   'is_active', 'is_popular', 'created_at']
    search_fields = ['name', 'description', 'slug']
    prepopulated_fields = {'slug': ('name',)}
    ordering = ['sort_order', 'name']
    list_editable = ['is_active', 'is_popular', 'sort_order']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'parent')
        }),
        ('Display Settings', {
            'fields': ('icon', 'mobile_icon', 'color', 'image')
        }),
        ('Status & Ordering', {
            'fields': ('is_active', 'is_popular', 'sort_order')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    inlines = [SubcategoryInline]

    def level_indicator(self, obj):
        """Visual indicator of category level"""
        indent = "—" * obj.level
        return format_html(f"{indent} {obj.name}")
    level_indicator.short_description = "Hierarchy"

    def icon_display(self, obj):
        """Display category icon"""
        if obj.icon:
            return format_html(f'<span style="font-size: 20px;">{obj.icon}</span>')
        return "—"
    icon_display.short_description = "Icon"

    def service_count(self, obj):
        """Count of services in this category"""
        count = obj.services.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:catalog_service_changelist') + \
                f'?category__id__exact={obj.id}'
            return format_html('<a href="{}">{} services</a>', url, count)
        return "0 services"
    service_count.short_description = "Services"

    def get_queryset(self, request):
        """Optimize queryset with prefetch"""
        return super().get_queryset(request).select_related('parent').prefetch_related('services')


@admin.register(ServiceProvider)
class ServiceProviderAdmin(admin.ModelAdmin):
    """Admin interface for Service Providers"""

    list_display = [
        'business_name', 'user_email', 'city_state', 'rating_display',
        'status_badges', 'service_count', 'total_bookings', 'is_verified',
        'is_featured', 'is_active', 'created_at'
    ]
    list_filter = [
        ProviderStatusFilter, 'is_verified', 'is_featured', 'is_active',
        'city', 'state', 'mobile_optimized', 'created_at'
    ]
    search_fields = [
        'business_name', 'business_description', 'user__email',
        'user__first_name', 'user__last_name', 'city', 'business_phone'
    ]
    ordering = ['-created_at']
    list_editable = ['is_verified', 'is_featured', 'is_active']

    fieldsets = (
        ('Business Information', {
            'fields': ('user', 'business_name', 'business_description', 'business_phone', 'business_email')
        }),
        ('Location', {
            'fields': ('address', 'city', 'state', 'zip_code', 'country', 'latitude', 'longitude')
        }),
        ('Online Presence', {
            'fields': ('website', 'instagram_handle', 'facebook_url')
        }),
        ('Media', {
            'fields': ('profile_image', 'cover_image')
        }),
        ('Status & Verification', {
            'fields': ('is_active', 'is_verified', 'is_featured', 'mobile_optimized')
        }),
        ('Categories', {
            'fields': ('categories',)
        }),
        ('Statistics', {
            'fields': ('rating', 'review_count', 'total_bookings', 'years_of_experience'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at', 'rating',
                       'review_count', 'total_bookings')
    filter_horizontal = ('categories',)

    inlines = [ServiceInline, OperatingHoursInline, ServiceGalleryInline]

    def user_email(self, obj):
        """Display user email with link"""
        if obj.user:
            url = reverse('admin:authentication_user_change',
                          args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.email)
        return "—"
    user_email.short_description = "User Email"

    def city_state(self, obj):
        """Display city and state"""
        return f"{obj.city}, {obj.state}" if obj.city and obj.state else "—"
    city_state.short_description = "Location"

    def rating_display(self, obj):
        """Display rating with stars"""
        if obj.review_count > 0:
            stars = "★" * int(obj.rating) + "☆" * (5 - int(obj.rating))
            return format_html(
                f'<span title="{obj.rating:.1f} ({obj.review_count} reviews)">{stars}</span>'
            )
        return "No reviews"
    rating_display.short_description = "Rating"

    def status_badges(self, obj):
        """Display status badges"""
        badges = []
        if obj.is_verified:
            badges.append(
                '<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">VERIFIED</span>')
        if obj.is_featured:
            badges.append(
                '<span style="background: #ffc107; color: black; padding: 2px 6px; border-radius: 3px; font-size: 11px;">FEATURED</span>')
        if not obj.is_active:
            badges.append(
                '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">INACTIVE</span>')
        return format_html(' '.join(badges)) if badges else "—"
    status_badges.short_description = "Status"

    def service_count(self, obj):
        """Count of active services"""
        count = obj.services.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:catalog_service_changelist') + \
                f'?provider__id__exact={obj.id}'
            return format_html('<a href="{}">{}</a>', url, count)
        return "0"
    service_count.short_description = "Services"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('user').prefetch_related('categories', 'services')

    actions = ['verify_providers', 'feature_providers',
               'activate_providers', 'deactivate_providers']

    def verify_providers(self, request, queryset):
        """Bulk verify providers"""
        updated = queryset.update(is_verified=True)
        self.message_user(
            request, f'{updated} providers verified successfully.')
    verify_providers.short_description = "Verify selected providers"

    def feature_providers(self, request, queryset):
        """Bulk feature providers"""
        updated = queryset.update(is_featured=True)
        self.message_user(
            request, f'{updated} providers featured successfully.')
    feature_providers.short_description = "Feature selected providers"

    def activate_providers(self, request, queryset):
        """Bulk activate providers"""
        updated = queryset.update(is_active=True)
        self.message_user(
            request, f'{updated} providers activated successfully.')
    activate_providers.short_description = "Activate selected providers"

    def deactivate_providers(self, request, queryset):
        """Bulk deactivate providers"""
        updated = queryset.update(is_active=False)
        self.message_user(
            request, f'{updated} providers deactivated successfully.')
    deactivate_providers.short_description = "Deactivate selected providers"


class ServiceLocationInline(admin.StackedInline):
    """Inline admin for service location"""
    model = ServiceLocation
    extra = 0
    fields = ('location_type', 'travel_radius', 'travel_fee',
              'service_areas', 'virtual_platform', 'location_notes')


class ServiceAvailabilityInline(admin.StackedInline):
    """Inline admin for service availability"""
    model = ServiceAvailability
    extra = 0
    fields = (
        'availability_type', 'min_advance_booking', 'max_advance_booking',
        'max_bookings_per_day', 'max_bookings_per_slot', 'cancellation_hours',
        'weekend_available', 'holiday_available', 'instant_booking'
    )


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    """Admin interface for Services"""

    list_display = [
        'name', 'provider_name', 'category', 'price_display', 'duration_display',
        'status_badges', 'booking_count', 'is_popular', 'is_active', 'is_available', 'created_at'
    ]
    list_filter = [
        ServiceStatusFilter, 'category', 'provider', 'price_type',
        'is_active', 'is_available', 'is_popular', 'created_at'
    ]
    search_fields = [
        'name', 'description', 'short_description', 'mobile_description',
        'provider__business_name', 'category__name'
    ]
    ordering = ['-created_at']
    list_editable = ['is_active', 'is_available', 'is_popular']

    fieldsets = (
        ('Basic Information', {
            'fields': ('provider', 'category', 'name', 'description', 'short_description', 'mobile_description')
        }),
        ('Pricing', {
            'fields': ('base_price', 'price_type', 'max_price')
        }),
        ('Duration & Scheduling', {
            'fields': ('duration', 'buffer_time')
        }),
        ('Service Details', {
            'fields': ('requirements', 'preparation_instructions')
        }),
        ('Media', {
            'fields': ('image',)
        }),
        ('Status & Visibility', {
            'fields': ('is_active', 'is_available', 'is_popular')
        }),
        ('Statistics', {
            'fields': ('booking_count',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at', 'booking_count')

    inlines = [ServiceLocationInline, ServiceAvailabilityInline]

    def provider_name(self, obj):
        """Display provider name with link"""
        if obj.provider:
            url = reverse('admin:catalog_serviceprovider_change',
                          args=[obj.provider.id])
            return format_html('<a href="{}">{}</a>', url, obj.provider.business_name)
        return "—"
    provider_name.short_description = "Provider"

    def price_display(self, obj):
        """Display formatted price"""
        return obj.display_price
    price_display.short_description = "Price"

    def duration_display(self, obj):
        """Display formatted duration"""
        return obj.display_duration
    duration_display.short_description = "Duration"

    def status_badges(self, obj):
        """Display status badges"""
        badges = []
        if obj.is_popular:
            badges.append(
                '<span style="background: #17a2b8; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">POPULAR</span>')
        if not obj.is_available:
            badges.append(
                '<span style="background: #ffc107; color: black; padding: 2px 6px; border-radius: 3px; font-size: 11px;">UNAVAILABLE</span>')
        if not obj.is_active:
            badges.append(
                '<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">INACTIVE</span>')
        return format_html(' '.join(badges)) if badges else "—"
    status_badges.short_description = "Status"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('provider', 'category')

    actions = ['make_popular', 'remove_popular', 'make_available',
               'make_unavailable', 'activate_services', 'deactivate_services']

    def make_popular(self, request, queryset):
        """Mark services as popular"""
        updated = queryset.update(is_popular=True)
        self.message_user(request, f'{updated} services marked as popular.')
    make_popular.short_description = "Mark as popular"

    def remove_popular(self, request, queryset):
        """Remove popular status"""
        updated = queryset.update(is_popular=False)
        self.message_user(request, f'{updated} services removed from popular.')
    remove_popular.short_description = "Remove popular status"

    def make_available(self, request, queryset):
        """Make services available"""
        updated = queryset.update(is_available=True)
        self.message_user(request, f'{updated} services made available.')
    make_available.short_description = "Make available"

    def make_unavailable(self, request, queryset):
        """Make services unavailable"""
        updated = queryset.update(is_available=False)
        self.message_user(request, f'{updated} services made unavailable.')
    make_unavailable.short_description = "Make unavailable"

    def activate_services(self, request, queryset):
        """Activate services"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} services activated.')
    activate_services.short_description = "Activate services"

    def deactivate_services(self, request, queryset):
        """Deactivate services"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} services deactivated.')
    deactivate_services.short_description = "Deactivate services"


@admin.register(OperatingHours)
class OperatingHoursAdmin(admin.ModelAdmin):
    """Admin interface for Operating Hours"""

    list_display = ['provider_name', 'day', 'is_open',
                    'hours_display', 'break_display', 'notes']
    list_filter = ['day', 'is_open', 'provider__city', 'provider__state']
    search_fields = ['provider__business_name', 'notes']
    ordering = ['provider', 'day']
    list_editable = ['is_open']

    fieldsets = (
        ('Provider & Day', {
            'fields': ('provider', 'day', 'is_open')
        }),
        ('Operating Hours', {
            'fields': ('open_time', 'close_time')
        }),
        ('Break Time', {
            'fields': ('break_start', 'break_end'),
            'classes': ('collapse',)
        }),
        ('Notes', {
            'fields': ('notes',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    def provider_name(self, obj):
        """Display provider name with link"""
        if obj.provider:
            url = reverse('admin:catalog_serviceprovider_change',
                          args=[obj.provider.id])
            return format_html('<a href="{}">{}</a>', url, obj.provider.business_name)
        return "—"
    provider_name.short_description = "Provider"

    def hours_display(self, obj):
        """Display operating hours"""
        if obj.is_open:
            return f"{obj.open_time} - {obj.close_time}"
        return "Closed"
    hours_display.short_description = "Hours"

    def break_display(self, obj):
        """Display break hours"""
        if obj.break_start and obj.break_end:
            return f"{obj.break_start} - {obj.break_end}"
        return "No break"
    break_display.short_description = "Break"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('provider')


@admin.register(ServiceAvailability)
class ServiceAvailabilityAdmin(admin.ModelAdmin):
    """Admin interface for Service Availability"""

    list_display = [
        'service_name', 'availability_type', 'advance_booking_display',
        'booking_limits_display', 'instant_booking', 'weekend_available'
    ]
    list_filter = [
        'availability_type', 'instant_booking', 'weekend_available',
        'holiday_available', 'service__category'
    ]
    search_fields = ['service__name', 'service__provider__business_name']
    ordering = ['service']

    fieldsets = (
        ('Service', {
            'fields': ('service',)
        }),
        ('Availability Type', {
            'fields': ('availability_type',)
        }),
        ('Booking Windows', {
            'fields': ('min_advance_booking', 'max_advance_booking')
        }),
        ('Booking Limits', {
            'fields': ('max_bookings_per_day', 'max_bookings_per_slot')
        }),
        ('Cancellation Policy', {
            'fields': ('cancellation_hours',)
        }),
        ('Special Availability', {
            'fields': ('weekend_available', 'holiday_available')
        }),
        ('Mobile Features', {
            'fields': ('instant_booking',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    def service_name(self, obj):
        """Display service name with link"""
        if obj.service:
            url = reverse('admin:catalog_service_change',
                          args=[obj.service.id])
            return format_html('<a href="{}">{}</a>', url, obj.service.name)
        return "—"
    service_name.short_description = "Service"

    def advance_booking_display(self, obj):
        """Display advance booking window"""
        return f"{obj.min_advance_booking}h - {obj.max_advance_booking}d"
    advance_booking_display.short_description = "Advance Booking"

    def booking_limits_display(self, obj):
        """Display booking limits"""
        daily = obj.max_bookings_per_day or "∞"
        slot = obj.max_bookings_per_slot
        return f"{daily}/day, {slot}/slot"
    booking_limits_display.short_description = "Limits"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('service', 'service__provider')


@admin.register(ServiceGallery)
class ServiceGalleryAdmin(admin.ModelAdmin):
    """Admin interface for Service Gallery"""

    list_display = [
        'image_preview', 'provider_name', 'service_name', 'image_type',
        'is_featured', 'is_cover', 'sort_order', 'created_at'
    ]
    list_filter = ['image_type', 'is_featured',
                   'is_cover', 'mobile_optimized', 'created_at']
    search_fields = ['caption', 'alt_text',
                     'provider__business_name', 'service__name']
    ordering = ['provider', 'service', 'sort_order']
    list_editable = ['is_featured', 'is_cover', 'sort_order']

    fieldsets = (
        ('Image', {
            'fields': ('image', 'image_preview')
        }),
        ('Associated Content', {
            'fields': ('provider', 'service')
        }),
        ('Image Details', {
            'fields': ('image_type', 'caption', 'alt_text')
        }),
        ('Display Settings', {
            'fields': ('is_featured', 'is_cover', 'sort_order', 'mobile_optimized')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at', 'image_preview')

    def image_preview(self, obj):
        """Display image preview"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 150px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Preview"

    def provider_name(self, obj):
        """Display provider name with link"""
        if obj.provider:
            url = reverse('admin:catalog_serviceprovider_change',
                          args=[obj.provider.id])
            return format_html('<a href="{}">{}</a>', url, obj.provider.business_name)
        return "—"
    provider_name.short_description = "Provider"

    def service_name(self, obj):
        """Display service name with link"""
        if obj.service:
            url = reverse('admin:catalog_service_change',
                          args=[obj.service.id])
            return format_html('<a href="{}">{}</a>', url, obj.service.name)
        return "—"
    service_name.short_description = "Service"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('provider', 'service')


@admin.register(ServiceLocation)
class ServiceLocationAdmin(admin.ModelAdmin):
    """Admin interface for Service Location"""

    list_display = [
        'service_name', 'location_type', 'travel_radius_display',
        'travel_fee', 'virtual_platform', 'created_at'
    ]
    list_filter = ['location_type',
                   'service__category', 'service__provider__city']
    search_fields = [
        'service__name', 'service__provider__business_name',
        'virtual_platform', 'location_notes'
    ]
    ordering = ['service']

    fieldsets = (
        ('Service', {
            'fields': ('service',)
        }),
        ('Location Type', {
            'fields': ('location_type',)
        }),
        ('Travel Settings', {
            'fields': ('travel_radius', 'travel_fee', 'service_areas'),
            'classes': ('collapse',)
        }),
        ('Virtual Settings', {
            'fields': ('virtual_platform',),
            'classes': ('collapse',)
        }),
        ('Additional Information', {
            'fields': ('location_notes',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ('created_at', 'updated_at')

    def service_name(self, obj):
        """Display service name with link"""
        if obj.service:
            url = reverse('admin:catalog_service_change',
                          args=[obj.service.id])
            return format_html('<a href="{}">{}</a>', url, obj.service.name)
        return "—"
    service_name.short_description = "Service"

    def travel_radius_display(self, obj):
        """Display travel radius"""
        if obj.travel_radius:
            return f"{obj.travel_radius} km"
        return "—"
    travel_radius_display.short_description = "Travel Radius"

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('service', 'service__provider')


# Custom admin site configuration
admin.site.site_header = "Vierla Service Catalog Administration"
admin.site.site_title = "Vierla Admin"
admin.site.index_title = "Service Catalog Management"

# Add custom CSS for better mobile admin experience
admin.site.enable_nav_sidebar = True
