/**
 * Test Accounts Panel
 * Development component for quick login with test accounts
 * Part of EPIC-05-CRITICAL: Authentication & UI Enhancement
 */

import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Text } from '../ui/Text';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { TEST_ACCOUNTS, TestAccount, getTestAccountsByRole } from '../../config/testAccounts';

interface TestAccountsPanelProps {
  onClose?: () => void;
  visible?: boolean;
}

export const TestAccountsPanel: React.FC<TestAccountsPanelProps> = ({
  onClose,
  visible = true,
}) => {
  const { colors, spacing } = useTheme();
  const { login, isLoading } = useAuth();
  const [selectedRole, setSelectedRole] = useState<'all' | 'customer' | 'service_provider' | 'admin'>('all');

  if (!visible || !__DEV__) {
    return null;
  }

  const handleQuickLogin = async (account: TestAccount) => {
    try {
      const success = await login(account.email, account.password);
      if (success) {
        Alert.alert(
          'Login Successful',
          `Logged in as ${account.name} (${account.role})`,
          [{ text: 'OK', onPress: onClose }]
        );
      } else {
        Alert.alert(
          'Login Failed',
          'Failed to login with test account. Please check backend connection.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Login Error',
        `Error logging in: ${error}`,
        [{ text: 'OK' }]
      );
    }
  };

  const getFilteredAccounts = (): TestAccount[] => {
    if (selectedRole === 'all') {
      return Object.values(TEST_ACCOUNTS);
    }
    return getTestAccountsByRole(selectedRole);
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'customer':
        return 'success' as const;
      case 'service_provider':
        return 'info' as const;
      case 'admin':
        return 'destructive' as const;
      default:
        return 'default' as const;
    }
  };

  const styles = StyleSheet.create({
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    panel: {
      backgroundColor: colors.background.primary,
      borderRadius: 12,
      padding: spacing.lg,
      margin: spacing.md,
      maxHeight: '80%',
      width: '90%',
      maxWidth: 400,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.md,
    },
    title: {
      color: colors.text.primary,
    },
    closeButton: {
      minWidth: 60,
    },
    filterContainer: {
      flexDirection: 'row',
      marginBottom: spacing.md,
      gap: spacing.xs,
    },
    filterButton: {
      flex: 1,
      paddingVertical: spacing.xs,
    },
    accountsContainer: {
      maxHeight: 400,
    },
    accountCard: {
      marginBottom: spacing.sm,
      padding: spacing.md,
    },
    accountHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: spacing.xs,
    },
    accountInfo: {
      flex: 1,
    },
    accountName: {
      marginBottom: spacing.xs / 2,
    },
    accountEmail: {
      color: colors.text.secondary,
    },
    accountDescription: {
      marginVertical: spacing.xs,
      color: colors.text.secondary,
    },
    featuresContainer: {
      marginTop: spacing.xs,
    },
    featuresTitle: {
      marginBottom: spacing.xs / 2,
      color: colors.text.secondary,
    },
    feature: {
      color: colors.text.tertiary,
      marginLeft: spacing.sm,
    },
    loginButton: {
      marginTop: spacing.sm,
    },
    warning: {
      backgroundColor: colors.warning + '20',
      padding: spacing.sm,
      borderRadius: 8,
      marginBottom: spacing.md,
    },
    warningText: {
      color: colors.warning,
      textAlign: 'center',
    },
  });

  return (
    <View style={styles.overlay}>
      <Card style={styles.panel}>
        <View style={styles.header}>
          <Text variant="h3" style={styles.title}>
            Test Accounts
          </Text>
          <Button
            variant="outline"
            size="sm"
            onPress={onClose}
            style={styles.closeButton}
          >
            Close
          </Button>
        </View>

        <View style={styles.warning}>
          <Text style={styles.warningText}>
            Development Only - Test accounts for quick login
          </Text>
        </View>

        <View style={styles.filterContainer}>
          <Button
            variant={selectedRole === 'all' ? 'default' : 'outline'}
            size="sm"
            onPress={() => setSelectedRole('all')}
            style={styles.filterButton}
          >
            All
          </Button>
          <Button
            variant={selectedRole === 'customer' ? 'default' : 'outline'}
            size="sm"
            onPress={() => setSelectedRole('customer')}
            style={styles.filterButton}
          >
            Customer
          </Button>
          <Button
            variant={selectedRole === 'service_provider' ? 'default' : 'outline'}
            size="sm"
            onPress={() => setSelectedRole('service_provider')}
            style={styles.filterButton}
          >
            Provider
          </Button>
          <Button
            variant={selectedRole === 'admin' ? 'default' : 'outline'}
            size="sm"
            onPress={() => setSelectedRole('admin')}
            style={styles.filterButton}
          >
            Admin
          </Button>
        </View>

        <ScrollView style={styles.accountsContainer} showsVerticalScrollIndicator={false}>
          {getFilteredAccounts().map((account, index) => (
            <Card key={index} style={styles.accountCard}>
              <View style={styles.accountHeader}>
                <View style={styles.accountInfo}>
                  <Text variant="h4" style={styles.accountName}>
                    {account.name}
                  </Text>
                  <Text variant="caption" style={styles.accountEmail}>
                    {account.email}
                  </Text>
                </View>
                <Badge variant={getRoleBadgeVariant(account.role)}>
                  {account.role.replace('_', ' ')}
                </Badge>
              </View>

              <Text variant="caption" style={styles.accountDescription}>
                {account.description}
              </Text>

              <View style={styles.featuresContainer}>
                <Text variant="caption" style={styles.featuresTitle}>
                  Features:
                </Text>
                {account.features.slice(0, 3).map((feature, featureIndex) => (
                  <Text key={featureIndex} variant="caption" style={styles.feature}>
                    • {feature}
                  </Text>
                ))}
                {account.features.length > 3 && (
                  <Text variant="caption" style={styles.feature}>
                    • +{account.features.length - 3} more...
                  </Text>
                )}
              </View>

              <Button
                variant="outline"
                size="sm"
                onPress={() => handleQuickLogin(account)}
                disabled={isLoading}
                loading={isLoading}
                style={styles.loginButton}
              >
                {isLoading ? 'Logging in...' : 'Quick Login'}
              </Button>
            </Card>
          ))}
        </ScrollView>
      </Card>
    </View>
  );
};

export default TestAccountsPanel;
