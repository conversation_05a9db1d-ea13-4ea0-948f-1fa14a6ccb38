# Service Catalog System Documentation

## Overview

The Service Catalog system is a comprehensive Django application that manages service providers, categories, and services for the Vierla platform. It provides a robust foundation for mobile-first service discovery and booking.

## Architecture

### Models

#### ServiceCategory
Hierarchical category system supporting parent-child relationships.

**Key Features:**
- Hierarchical structure with unlimited nesting
- Mobile-optimized icons and colors
- SEO-friendly slugs
- Popularity tracking
- Sort ordering

**Fields:**
- `name`: Category name (max 100 chars)
- `slug`: URL-friendly identifier
- `description`: Detailed description
- `parent`: Self-referencing foreign key for hierarchy
- `icon`: Unicode icon for mobile display
- `mobile_icon`: Alternative mobile icon
- `color`: Hex color code for theming
- `image`: Category image
- `is_active`: Visibility flag
- `is_popular`: Featured category flag
- `sort_order`: Display ordering

#### ServiceProvider
Business entities offering services on the platform.

**Key Features:**
- User account integration
- Location-based services
- Rating and review system
- Verification status
- Featured provider support
- Social media integration

**Fields:**
- `user`: OneToOne relationship with User model
- `business_name`: Official business name
- `business_description`: Business overview
- `business_phone`: Contact number
- `business_email`: Business email
- `address`: Physical address
- `city`, `state`, `zip_code`, `country`: Location details
- `latitude`, `longitude`: GPS coordinates
- `website`: Business website
- `instagram_handle`: Instagram username
- `facebook_url`: Facebook page URL
- `profile_image`: Business profile picture
- `cover_image`: Cover photo
- `categories`: Many-to-many relationship with ServiceCategory
- `is_active`: Account status
- `is_verified`: Verification status
- `is_featured`: Featured provider flag
- `mobile_optimized`: Mobile optimization flag
- `rating`: Average rating (calculated)
- `review_count`: Total reviews (calculated)
- `total_bookings`: Booking count (calculated)
- `years_of_experience`: Experience years

#### Service
Individual services offered by providers.

**Key Features:**
- Flexible pricing models
- Duration management
- Availability tracking
- Mobile-optimized descriptions
- Booking statistics

**Fields:**
- `provider`: Foreign key to ServiceProvider
- `category`: Foreign key to ServiceCategory
- `name`: Service name
- `description`: Full description
- `short_description`: Brief overview
- `mobile_description`: Mobile-optimized description
- `base_price`: Starting price
- `price_type`: Pricing model (fixed/hourly/custom)
- `max_price`: Maximum price for ranges
- `duration`: Service duration in minutes
- `buffer_time`: Time between bookings
- `requirements`: Service requirements
- `preparation_instructions`: Client preparation notes
- `image`: Service image
- `is_active`: Availability flag
- `is_available`: Current availability
- `is_popular`: Popular service flag
- `booking_count`: Total bookings

#### OperatingHours
Provider business hours management.

**Key Features:**
- Day-specific hours
- Break time support
- Holiday handling
- Notes for special conditions

**Fields:**
- `provider`: Foreign key to ServiceProvider
- `day`: Day of week (0=Monday, 6=Sunday)
- `is_open`: Open/closed flag
- `open_time`: Opening time
- `close_time`: Closing time
- `break_start`: Break start time
- `break_end`: Break end time
- `notes`: Special notes

#### ServiceAvailability
Advanced availability configuration for services.

**Key Features:**
- Booking window management
- Capacity limits
- Cancellation policies
- Weekend/holiday availability
- Instant booking support

**Fields:**
- `service`: OneToOne relationship with Service
- `availability_type`: Availability model
- `min_advance_booking`: Minimum advance booking hours
- `max_advance_booking`: Maximum advance booking days
- `max_bookings_per_day`: Daily booking limit
- `max_bookings_per_slot`: Slot booking limit
- `cancellation_hours`: Cancellation deadline
- `weekend_available`: Weekend availability
- `holiday_available`: Holiday availability
- `instant_booking`: Instant booking enabled

#### ServiceGallery
Media management for providers and services.

**Key Features:**
- Image type categorization
- Featured image support
- Mobile optimization
- Sort ordering
- SEO-friendly alt text

**Fields:**
- `provider`: Foreign key to ServiceProvider
- `service`: Foreign key to Service (optional)
- `image`: Image file
- `image_type`: Image category
- `caption`: Image caption
- `alt_text`: SEO alt text
- `is_featured`: Featured image flag
- `is_cover`: Cover image flag
- `mobile_optimized`: Mobile optimization flag
- `sort_order`: Display order

#### ServiceLocation
Location and service delivery configuration.

**Key Features:**
- Multiple location types
- Travel radius management
- Virtual service support
- Service area definitions

**Fields:**
- `service`: OneToOne relationship with Service
- `location_type`: Service delivery type
- `travel_radius`: Travel distance in km
- `travel_fee`: Travel charge
- `service_areas`: Covered areas (JSON)
- `virtual_platform`: Virtual meeting platform
- `location_notes`: Additional notes

### Utilities

#### Location Utils (`utils.py`)
Geographic calculation utilities for location-based services.

**Functions:**
- `calculate_distance(lat1, lon1, lat2, lon2)`: Haversine distance calculation
- `get_nearby_providers(latitude, longitude, radius_km=10)`: Find providers within radius
- `validate_coordinates(latitude, longitude)`: Coordinate validation

### Admin Interface

Comprehensive Django admin interface with:

#### ServiceCategoryAdmin
- Hierarchical display with visual indentation
- Icon preview
- Service count with links
- Bulk actions for status management
- Custom filters for category levels

#### ServiceProviderAdmin
- User integration with email links
- Rating display with stars
- Status badges (verified, featured, inactive)
- Location display (city, state)
- Service count with links
- Bulk verification and featuring actions
- Advanced filtering by status and location

#### ServiceAdmin
- Provider and category links
- Price and duration formatting
- Status badges
- Booking count display
- Bulk availability management
- Service location and availability inlines

#### Additional Admin Classes
- `OperatingHoursAdmin`: Business hours management
- `ServiceAvailabilityAdmin`: Availability configuration
- `ServiceGalleryAdmin`: Media management with previews
- `ServiceLocationAdmin`: Location settings

### API Integration

The catalog system integrates with Django REST Framework for API endpoints:

#### Serializers
- Category serialization with hierarchy support
- Provider serialization with location data
- Service serialization with availability info
- Nested serialization for related data

#### ViewSets
- Category CRUD operations
- Provider management
- Service discovery and filtering
- Location-based search

#### Filtering
- Category-based filtering
- Location radius filtering
- Price range filtering
- Availability filtering
- Rating-based filtering

### Mobile Optimization

#### Design Considerations
- Mobile-first responsive design
- Touch-friendly interface elements
- Optimized image loading
- Reduced data usage
- Offline capability support

#### Performance Features
- Database query optimization
- Caching strategies
- Image compression
- Lazy loading
- Pagination

### Testing

Comprehensive test suite covering:

#### Model Tests
- Field validation
- Method functionality
- Relationship integrity
- Business logic

#### Utility Tests
- Distance calculations
- Coordinate validation
- Provider search functionality

#### Admin Tests
- Interface functionality
- Bulk actions
- Filtering and search

#### API Tests
- Endpoint functionality
- Authentication
- Data serialization
- Error handling

### Security

#### Data Protection
- Input validation
- SQL injection prevention
- XSS protection
- CSRF protection

#### Access Control
- User authentication
- Permission-based access
- Provider ownership validation
- Admin-only operations

### Performance

#### Database Optimization
- Proper indexing
- Query optimization
- Relationship prefetching
- Connection pooling

#### Caching Strategy
- Model-level caching
- Query result caching
- Template fragment caching
- Static file caching

### Deployment

#### Requirements
- Django 4.2.16+
- PostgreSQL with PostGIS
- Redis for caching
- Celery for background tasks

#### Configuration
- Environment-specific settings
- Database configuration
- Media file handling
- Static file serving

### Monitoring

#### Logging
- Structured logging
- Error tracking
- Performance monitoring
- User activity logging

#### Metrics
- Service usage statistics
- Provider performance metrics
- System health monitoring
- Business intelligence data

## Usage Examples

### Creating a Service Category

```python
from apps.catalog.models import ServiceCategory

# Create parent category
beauty = ServiceCategory.objects.create(
    name="Beauty & Wellness",
    slug="beauty-wellness",
    description="Beauty and wellness services",
    icon="💄",
    color="#FF69B4",
    is_active=True,
    is_popular=True,
    sort_order=1
)

# Create subcategory
hair = ServiceCategory.objects.create(
    name="Hair Services",
    slug="hair-services",
    parent=beauty,
    description="Professional hair care services",
    icon="💇",
    is_active=True,
    sort_order=1
)
```

### Registering a Service Provider

```python
from apps.catalog.models import ServiceProvider
from apps.authentication.models import User

# Create user account
user = User.objects.create_user(
    email="<EMAIL>",
    password="secure_password",
    first_name="Beauty",
    last_name="Salon"
)

# Create provider profile
provider = ServiceProvider.objects.create(
    user=user,
    business_name="Elite Beauty Salon",
    business_description="Premium beauty services",
    business_phone="+**********",
    address="123 Main St",
    city="New York",
    state="NY",
    zip_code="10001",
    country="USA",
    latitude=40.7128,
    longitude=-74.0060,
    is_active=True,
    is_verified=True
)

# Add categories
provider.categories.add(beauty, hair)
```

### Adding a Service

```python
from apps.catalog.models import Service, ServiceAvailability

# Create service
haircut = Service.objects.create(
    provider=provider,
    category=hair,
    name="Professional Haircut",
    description="Expert haircut with styling",
    short_description="Professional haircut",
    mobile_description="Expert cut & style",
    base_price=50.00,
    price_type="fixed",
    duration=60,
    buffer_time=15,
    is_active=True,
    is_available=True
)

# Configure availability
ServiceAvailability.objects.create(
    service=haircut,
    availability_type="scheduled",
    min_advance_booking=2,
    max_advance_booking=30,
    max_bookings_per_day=8,
    max_bookings_per_slot=1,
    cancellation_hours=24,
    weekend_available=True,
    instant_booking=False
)
```

### Finding Nearby Providers

```python
from apps.catalog.utils import get_nearby_providers

# Find providers within 10km
providers = get_nearby_providers(
    latitude=40.7128,
    longitude=-74.0060,
    radius_km=10
)

for provider in providers:
    print(f"{provider.business_name} - {provider.distance_km:.1f}km away")
```

## Best Practices

### Model Design
- Use appropriate field types and constraints
- Implement proper validation
- Add helpful string representations
- Include metadata for admin interface

### Performance
- Use select_related() and prefetch_related()
- Implement proper indexing
- Cache frequently accessed data
- Optimize database queries

### Security
- Validate all user inputs
- Use Django's built-in security features
- Implement proper authentication
- Follow OWASP guidelines

### Testing
- Write comprehensive test coverage
- Test edge cases and error conditions
- Use factories for test data
- Mock external dependencies

### Documentation
- Document all public APIs
- Include usage examples
- Maintain up-to-date documentation
- Use clear and concise language

## Troubleshooting

### Common Issues

#### Migration Problems
```bash
# Reset migrations if needed
python manage.py migrate catalog zero
python manage.py makemigrations catalog
python manage.py migrate catalog
```

#### Performance Issues
- Check database indexes
- Review query patterns
- Monitor cache hit rates
- Analyze slow queries

#### Data Integrity
- Validate foreign key relationships
- Check for orphaned records
- Verify calculated fields
- Run data consistency checks

### Support

For technical support or questions:
- Check the Django documentation
- Review the codebase comments
- Consult the test suite for examples
- Contact the development team

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies
3. Run migrations
4. Create test data
5. Start development server

### Code Standards
- Follow PEP 8 style guidelines
- Write comprehensive tests
- Document new features
- Use meaningful commit messages

### Pull Request Process
1. Create feature branch
2. Implement changes
3. Add tests
4. Update documentation
5. Submit pull request

## License

This project is proprietary software developed for Vierla platform.
