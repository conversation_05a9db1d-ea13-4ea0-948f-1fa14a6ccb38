import React, { useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Types
export interface SortOption {
  id: string;
  label: string;
  value: string;
  description?: string;
  icon?: string;
}

export interface SortSelectorProps {
  options: SortOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  visible: boolean;
  onClose: () => void;
  title?: string;
  testID?: string;
}

// Default sort options for services
export const defaultSortOptions: SortOption[] = [
  {
    id: 'relevance',
    label: 'Relevance',
    value: 'relevance',
    description: 'Best match for your search',
    icon: 'star',
  },
  {
    id: 'price_low',
    label: 'Price: Low to High',
    value: 'price_low',
    description: 'Lowest price first',
    icon: 'arrow-up',
  },
  {
    id: 'price_high',
    label: 'Price: High to Low',
    value: 'price_high',
    description: 'Highest price first',
    icon: 'arrow-down',
  },
  {
    id: 'rating',
    label: 'Highest Rated',
    value: 'rating',
    description: 'Best rated providers first',
    icon: 'star',
  },
  {
    id: 'popularity',
    label: 'Most Popular',
    value: 'popularity',
    description: 'Most booked services first',
    icon: 'trending-up',
  },
  {
    id: 'duration',
    label: 'Duration',
    value: 'duration',
    description: 'Shortest duration first',
    icon: 'time',
  },
  {
    id: 'newest',
    label: 'Newest',
    value: 'newest',
    description: 'Recently added services',
    icon: 'calendar',
  },
  {
    id: 'name',
    label: 'Name A-Z',
    value: 'name',
    description: 'Alphabetical order',
    icon: 'text',
  },
];

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  border: '#E0E0E0',
  overlay: 'rgba(0, 0, 0, 0.5)',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
};

export const SortSelector: React.FC<SortSelectorProps> = ({
  options = defaultSortOptions,
  selectedValue = 'relevance',
  onSelect,
  visible,
  onClose,
  title = 'Sort by',
  testID = 'sort-selector',
}) => {
  const handleSelect = useCallback((value: string) => {
    onSelect(value);
    onClose();
  }, [onSelect, onClose]);

  const getSelectedOption = () => {
    return options.find(option => option.value === selectedValue);
  };

  const renderOption = (option: SortOption) => {
    const isSelected = option.value === selectedValue;
    
    return (
      <TouchableOpacity
        key={option.id}
        style={[
          styles.optionItem,
          isSelected && styles.optionItemSelected
        ]}
        onPress={() => handleSelect(option.value)}
        testID={`${testID}-option-${option.id}`}
      >
        <View style={styles.optionContent}>
          {option.icon && (
            <Ionicons
              name={option.icon as any}
              size={20}
              color={isSelected ? Colors.primary.main : Colors.text.secondary}
              style={styles.optionIcon}
            />
          )}
          
          <View style={styles.optionText}>
            <Text style={[
              styles.optionLabel,
              isSelected && styles.optionLabelSelected
            ]}>
              {option.label}
            </Text>
            
            {option.description && (
              <Text style={[
                styles.optionDescription,
                isSelected && styles.optionDescriptionSelected
              ]}>
                {option.description}
              </Text>
            )}
          </View>
        </View>
        
        {isSelected && (
          <Ionicons
            name="checkmark"
            size={20}
            color={Colors.primary.main}
            testID={`${testID}-selected-${option.id}`}
          />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
      testID={testID}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            testID={`${testID}-close-button`}
          >
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>{title}</Text>
          
          <View style={styles.headerSpacer} />
        </View>
        
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          testID={`${testID}-scroll`}
        >
          {options.map(renderOption)}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

// Compact sort selector for inline use
export interface CompactSortSelectorProps {
  options: SortOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  testID?: string;
}

export const CompactSortSelector: React.FC<CompactSortSelectorProps> = ({
  options = defaultSortOptions,
  selectedValue = 'relevance',
  onSelect,
  testID = 'compact-sort-selector',
}) => {
  const [modalVisible, setModalVisible] = React.useState(false);
  
  const selectedOption = options.find(option => option.value === selectedValue);
  
  const handleSelect = useCallback((value: string) => {
    onSelect(value);
    setModalVisible(false);
  }, [onSelect]);

  return (
    <>
      <TouchableOpacity
        style={styles.compactButton}
        onPress={() => setModalVisible(true)}
        testID={testID}
      >
        <Ionicons
          name="swap-vertical"
          size={16}
          color={Colors.text.primary}
          style={styles.compactIcon}
        />
        <Text style={styles.compactText}>
          {selectedOption?.label || 'Sort'}
        </Text>
        <Ionicons
          name="chevron-down"
          size={16}
          color={Colors.text.secondary}
        />
      </TouchableOpacity>
      
      <SortSelector
        options={options}
        selectedValue={selectedValue}
        onSelect={handleSelect}
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        testID={`${testID}-modal`}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.background.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  closeButton: {
    padding: Spacing.small,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  headerSpacer: {
    width: 40, // Same width as close button for centering
  },
  content: {
    flex: 1,
  },
  optionItem: {
    backgroundColor: Colors.background.surface,
    paddingHorizontal: Spacing.large,
    paddingVertical: Spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionItemSelected: {
    backgroundColor: Colors.primary.light + '20', // 20% opacity
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    marginRight: Spacing.medium,
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    color: Colors.text.primary,
    fontWeight: '500',
  },
  optionLabelSelected: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  optionDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: Spacing.micro,
  },
  optionDescriptionSelected: {
    color: Colors.primary.main + 'CC', // 80% opacity
  },
  // Compact selector styles
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.small,
    backgroundColor: Colors.background.surface,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  compactIcon: {
    marginRight: Spacing.small,
  },
  compactText: {
    fontSize: 14,
    color: Colors.text.primary,
    marginRight: Spacing.small,
  },
});

export default SortSelector;
