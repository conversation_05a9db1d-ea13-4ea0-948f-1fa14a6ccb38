#!/usr/bin/env python3
"""
Add remaining service providers to ensure each category has at least 3 providers
"""

import os
import sys
import django
from decimal import Decimal
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from apps.catalog.models import ServiceCategory, ServiceProvider, Service

User = get_user_model()

# Provider templates for missing categories
PROVIDER_TEMPLATES = {
    'Makeup Services': [
        {'name': 'Glam Makeup Studio', 'description': 'Professional makeup services for all occasions'},
        {'name': 'Beauty Bar', 'description': 'Full-service makeup studio with bridal and event specialization'},
    ],
    'Hair & Beauty': [
        {'name': 'Elite Hair Studio', 'description': 'Premium hair styling and coloring services with expert stylists'},
        {'name': 'Modern Cuts Salon', 'description': 'Contemporary hair salon specializing in trendy cuts and styles'},
    ],
    'Skincare & Facials': [
        {'name': 'Glow Skincare Studio', 'description': 'Professional facial treatments and skincare consultations'},
        {'name': 'Radiant Skin Spa', 'description': 'Luxury spa offering advanced facial treatments and skincare'},
        {'name': 'Clear Skin Clinic', 'description': 'Medical-grade skincare treatments and acne solutions'},
    ],
    'Massage Therapy': [
        {'name': 'Zen Massage Studio', 'description': 'Therapeutic massage services for relaxation and wellness'},
        {'name': 'Healing Touch Spa', 'description': 'Professional massage therapy for pain relief and stress reduction'},
        {'name': 'Muscle Relief Clinic', 'description': 'Sports massage and deep tissue therapy specialists'},
    ]
}

# Ottawa locations for variety
OTTAWA_LOCATIONS = [
    {
        'address': '234 Sparks Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1P 5B5',
        'latitude': Decimal('45.4215'),
        'longitude': Decimal('-75.6972')
    },
    {
        'address': '567 Bank Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1S 3T4',
        'latitude': Decimal('45.4042'),
        'longitude': Decimal('-75.6903')
    },
    {
        'address': '890 Rideau Street',
        'city': 'Ottawa',
        'state': 'Ontario',
        'zip_code': 'K1N 5Y4',
        'latitude': Decimal('45.4274'),
        'longitude': Decimal('-75.6812')
    }
]

def create_provider_user(provider_data, location, category_name):
    """Create a user account for the provider"""
    username = provider_data['name'].lower().replace(' ', '_').replace('&', 'and')
    email = f"{username}@example.com"
    
    # Check if user already exists
    if User.objects.filter(email=email).exists():
        return None
    
    user = User.objects.create_user(
        username=username,
        email=email,
        password='password123',
        first_name=provider_data['name'].split()[0],
        last_name=provider_data['name'].split()[-1] if len(provider_data['name'].split()) > 1 else 'Studio',
        role='service_provider',
        is_active=True
    )
    return user

def create_service_provider(provider_data, location, category):
    """Create a service provider"""
    user = create_provider_user(provider_data, location, category.name)
    if not user:
        return None
    
    provider = ServiceProvider.objects.create(
        user=user,
        business_name=provider_data['name'],
        business_description=provider_data['description'],
        business_phone=f"+1{random.randint(**********, **********)}",  # Ottawa area code
        business_email=user.email,
        address=location['address'],
        city=location['city'],
        state=location['state'],
        zip_code=location['zip_code'],
        country='Canada',
        latitude=location['latitude'],
        longitude=location['longitude'],
        rating=Decimal(str(round(random.uniform(4.0, 5.0), 1))),
        review_count=random.randint(15, 150),
        total_bookings=random.randint(50, 500),
        years_of_experience=random.randint(2, 15),
        is_verified=True,
        is_featured=random.choice([True, False]),
        is_active=True,
        mobile_optimized=True
    )
    
    # Add to category
    provider.categories.add(category)
    
    return provider

def main():
    print("🚀 ADDING REMAINING SERVICE PROVIDERS")
    print("=" * 60)
    
    categories = ServiceCategory.objects.all()
    total_created = 0
    
    for category in categories:
        current_count = ServiceProvider.objects.filter(categories=category).count()
        needed = max(0, 3 - current_count)
        
        if needed == 0:
            continue
        
        print(f"\n🏪 Category: {category.name}")
        print(f"   Current providers: {current_count}")
        print(f"   Need to add: {needed}")
        
        # Get provider templates for this category
        templates = PROVIDER_TEMPLATES.get(category.name, [])
        if not templates:
            print(f"   ⚠️ No templates for {category.name}")
            continue
        
        # Create providers
        for i in range(min(needed, len(templates))):
            template = templates[i]
            location = OTTAWA_LOCATIONS[i % len(OTTAWA_LOCATIONS)]
            
            provider = create_service_provider(template, location, category)
            if provider:
                total_created += 1
                print(f"   ✅ Created: {provider.business_name} in {provider.city}")
            else:
                print(f"   ❌ Failed to create: {template['name']}")
    
    print(f"\n✅ Created {total_created} new service providers")
    
    # Print final statistics
    print(f"\n📈 FINAL TOTALS:")
    for category in categories:
        count = ServiceProvider.objects.filter(categories=category).count()
        print(f"   {category.name}: {count} providers")

if __name__ == '__main__':
    main()
