
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for features/authentication</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> features/authentication</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">16.91% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>34/201</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.16% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>36/149</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.54% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/53</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.11% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>32/187</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="LoginScreen.tsx"><a href="LoginScreen.tsx.html">LoginScreen.tsx</a></td>
	<td data-value="34" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34" class="pct low">34%</td>
	<td data-value="50" class="abs low">17/50</td>
	<td data-value="34.28" class="pct low">34.28%</td>
	<td data-value="35" class="abs low">12/35</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="14" class="abs low">2/14</td>
	<td data-value="34.78" class="pct low">34.78%</td>
	<td data-value="46" class="abs low">16/46</td>
	</tr>

<tr>
	<td class="file low" data-value="ModernLoginScreen.tsx"><a href="ModernLoginScreen.tsx.html">ModernLoginScreen.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="74" class="abs low">0/74</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="44" class="abs low">0/44</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="71" class="abs low">0/71</td>
	</tr>

<tr>
	<td class="file low" data-value="RegisterScreen.tsx"><a href="RegisterScreen.tsx.html">RegisterScreen.tsx</a></td>
	<td data-value="22.07" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.07" class="pct low">22.07%</td>
	<td data-value="77" class="abs low">17/77</td>
	<td data-value="34.28" class="pct low">34.28%</td>
	<td data-value="70" class="abs low">24/70</td>
	<td data-value="8.69" class="pct low">8.69%</td>
	<td data-value="23" class="abs low">2/23</td>
	<td data-value="22.85" class="pct low">22.85%</td>
	<td data-value="70" class="abs low">16/70</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-15T23:26:10.033Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    