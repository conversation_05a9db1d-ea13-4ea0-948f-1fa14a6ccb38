/**
 * Basic Info Step Component
 * First step of multi-step service creation form
 */

import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../../theme';
import { FormData, FormErrors } from '../MultiStepServiceForm';
import { ServiceCategory } from '../../../services/api';

interface BasicInfoStepProps {
  formData: FormData;
  errors: FormErrors;
  categories: ServiceCategory[];
  onUpdateFormData: (field: keyof FormData, value: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  formData,
  errors,
  categories,
  onUpdateFormData,
  onNext,
  onPrevious,
  isFirstStep,
}) => {
  const renderInput = (
    label: string,
    field: keyof FormData,
    placeholder: string,
    options?: {
      multiline?: boolean;
      maxLength?: number;
      keyboardType?: 'default' | 'numeric' | 'email-address';
      autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
    }
  ) => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          options?.multiline && styles.textArea,
          errors[field] && styles.inputError,
        ]}
        value={formData[field] as string}
        onChangeText={(value) => onUpdateFormData(field, value)}
        placeholder={placeholder}
        placeholderTextColor={colors.textSecondary}
        multiline={options?.multiline}
        numberOfLines={options?.multiline ? 4 : 1}
        maxLength={options?.maxLength}
        keyboardType={options?.keyboardType || 'default'}
        autoCapitalize={options?.autoCapitalize || 'sentences'}
      />
      {errors[field] && (
        <Text style={styles.errorText}>{errors[field]}</Text>
      )}
      {options?.maxLength && (
        <Text style={styles.characterCount}>
          {(formData[field] as string).length}/{options.maxLength}
        </Text>
      )}
    </View>
  );

  const renderCategoryPicker = () => (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>Category *</Text>
      <View style={[styles.pickerContainer, errors.category && styles.inputError]}>
        <Picker
          selectedValue={formData.category}
          onValueChange={(value) => onUpdateFormData('category', value)}
          style={styles.picker}
        >
          <Picker.Item label="Select a category..." value="" />
          {categories.map((category) => (
            <Picker.Item
              key={category.id}
              label={category.name}
              value={category.id}
            />
          ))}
        </Picker>
      </View>
      {errors.category && (
        <Text style={styles.errorText}>{errors.category}</Text>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Basic Information</Text>
          <Text style={styles.subtitle}>
            Let's start with the basics about your service
          </Text>
        </View>

        <View style={styles.form}>
          {renderInput(
            'Service Name *',
            'name',
            'Enter your service name',
            { maxLength: 100, autoCapitalize: 'words' }
          )}

          {renderCategoryPicker()}

          {renderInput(
            'Short Description',
            'short_description',
            'Brief description for service listings (optional)',
            { 
              multiline: true, 
              maxLength: 150,
              autoCapitalize: 'sentences'
            }
          )}
        </View>

        <View style={styles.helpSection}>
          <View style={styles.helpItem}>
            <Icon name="lightbulb-outline" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              Choose a clear, descriptive name that customers will easily understand
            </Text>
          </View>
          
          <View style={styles.helpItem}>
            <Icon name="category" size={20} color={colors.primary} />
            <Text style={styles.helpText}>
              Select the category that best matches your service for better discoverability
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.button, styles.buttonSecondary]}
          onPress={onPrevious}
          disabled={isFirstStep}
        >
          <Icon name="arrow-back" size={20} color={isFirstStep ? colors.textSecondary : colors.primary} />
          <Text style={[
            styles.buttonText, 
            styles.buttonTextSecondary,
            isFirstStep && styles.buttonTextDisabled
          ]}>
            Previous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.buttonPrimary]}
          onPress={onNext}
        >
          <Text style={[styles.buttonText, styles.buttonTextPrimary]}>
            Next
          </Text>
          <Icon name="arrow-forward" size={20} color={colors.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: spacing.lg,
  },
  title: {
    ...typography.h2,
    color: colors.textPrimary,
    marginBottom: spacing.sm,
  },
  subtitle: {
    ...typography.body,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  form: {
    paddingBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  label: {
    ...typography.body,
    color: colors.textPrimary,
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    ...typography.body,
    color: colors.textPrimary,
    backgroundColor: colors.white,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  errorText: {
    ...typography.caption,
    color: colors.error,
    marginTop: spacing.xs,
  },
  characterCount: {
    ...typography.caption,
    color: colors.textSecondary,
    textAlign: 'right',
    marginTop: spacing.xs,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  picker: {
    height: 50,
    color: colors.textPrimary,
  },
  helpSection: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.xl,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  helpText: {
    ...typography.caption,
    color: colors.textSecondary,
    marginLeft: spacing.sm,
    flex: 1,
    lineHeight: 18,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
    minWidth: 120,
    justifyContent: 'center',
  },
  buttonPrimary: {
    backgroundColor: colors.primary,
  },
  buttonSecondary: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
  },
  buttonText: {
    ...typography.body,
    fontWeight: '600',
    marginHorizontal: spacing.xs,
  },
  buttonTextPrimary: {
    color: colors.white,
  },
  buttonTextSecondary: {
    color: colors.primary,
  },
  buttonTextDisabled: {
    color: colors.textSecondary,
  },
});
