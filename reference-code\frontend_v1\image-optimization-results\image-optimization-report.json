{"timestamp": "2025-08-01T20:31:23.695Z", "summary": {"totalImages": 7, "totalSizeMB": 2.22, "largeImages": 2, "optimizationSuggestions": 15}, "optimizations": [{"type": "size", "severity": "warning", "file": "assets\\app-logo.png", "issue": "Image is 1649KB (exceeds 500KB limit)", "suggestion": "Compress image or use WebP format"}, {"type": "size", "severity": "warning", "file": "assets\\main-app-logo.png", "issue": "Image is 564KB (exceeds 500KB limit)", "suggestion": "Compress image or use WebP format"}, {"type": "format", "severity": "info", "file": "assets\\app-logo.png", "issue": "Suboptimal format: .png", "suggestion": "Consider converting to WebP or JPEG for better compression"}, {"type": "format", "severity": "info", "file": "assets\\main-app-logo.png", "issue": "Suboptimal format: .png", "suggestion": "Consider converting to WebP or JPEG for better compression"}, {"type": "variants", "severity": "warning", "file": "assets\\adaptive-icon.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\app-logo.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\favicon.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\icon.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\main-app-logo.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\placeholder-logo.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "variants", "severity": "warning", "file": "assets\\splash-icon.png", "issue": "Missing variants: @2x, @3x", "suggestion": "Create high-resolution variants for better display on different devices"}, {"type": "unused", "severity": "info", "file": "assets\\adaptive-icon.png", "issue": "Image appears to be unused", "suggestion": "Remove if not needed to reduce bundle size"}, {"type": "unused", "severity": "info", "file": "assets\\favicon.png", "issue": "Image appears to be unused", "suggestion": "Remove if not needed to reduce bundle size"}, {"type": "unused", "severity": "info", "file": "assets\\placeholder-logo.png", "issue": "Image appears to be unused", "suggestion": "Remove if not needed to reduce bundle size"}, {"type": "unused", "severity": "info", "file": "assets\\splash-icon.png", "issue": "Image appears to be unused", "suggestion": "Remove if not needed to reduce bundle size"}], "imageAnalysis": [{"path": "assets\\adaptive-icon.png", "size": 17547, "sizeKB": 17, "extension": ".png", "basename": "adaptive-icon", "isLarge": false, "isOptimalFormat": true}, {"path": "assets\\app-logo.png", "size": 1688878, "sizeKB": 1649, "extension": ".png", "basename": "app-logo", "isLarge": true, "isOptimalFormat": false}, {"path": "assets\\favicon.png", "size": 1466, "sizeKB": 1, "extension": ".png", "basename": "favicon", "isLarge": false, "isOptimalFormat": true}, {"path": "assets\\icon.png", "size": 22380, "sizeKB": 22, "extension": ".png", "basename": "icon", "isLarge": false, "isOptimalFormat": true}, {"path": "assets\\main-app-logo.png", "size": 577754, "sizeKB": 564, "extension": ".png", "basename": "main-app-logo", "isLarge": true, "isOptimalFormat": false}, {"path": "assets\\placeholder-logo.png", "size": 958, "sizeKB": 1, "extension": ".png", "basename": "placeholder-logo", "isLarge": false, "isOptimalFormat": true}, {"path": "assets\\splash-icon.png", "size": 17547, "sizeKB": 17, "extension": ".png", "basename": "splash-icon", "isLarge": false, "isOptimalFormat": true}], "variantAnalysis": [{"baseImage": "assets\\adaptive-icon.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\app-logo.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\favicon.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\icon.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\main-app-logo.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\placeholder-logo.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}, {"baseImage": "assets\\splash-icon.png", "variants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}], "missingVariants": [{"variant": "@2x", "path": "assets\\<EMAIL>", "exists": false}, {"variant": "@3x", "path": "assets\\<EMAIL>", "exists": false}]}], "recommendations": ["Use WebP format for better compression while maintaining quality", "Implement lazy loading for images not immediately visible", "Create @2x and @3x variants for all images used in the app", "Consider using vector graphics (SVG) for icons and simple graphics", "Implement image caching strategy for better performance", "Use appropriate image dimensions - avoid scaling large images down", "Remove unused images to reduce bundle size", "Consider using progressive JPEG for large photos"]}