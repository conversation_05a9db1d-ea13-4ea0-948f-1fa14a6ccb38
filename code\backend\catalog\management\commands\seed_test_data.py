"""
Management command to seed comprehensive test data for Vierla platform.

This command creates test accounts and associated data according to the
TEST-ACCOUNTS-SPECIFICATION.md document.

Usage:
    python manage.py seed_test_data
    python manage.py seed_test_data --quick
    python manage.py seed_test_data --force
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Seed comprehensive test data for the Vierla platform'

    def add_arguments(self, parser):
        parser.add_argument(
            '--quick',
            action='store_true',
            help='Create minimal test data set (faster execution)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing test accounts'
        )
        parser.add_argument(
            '--customers-only',
            action='store_true',
            help='Create only customer test accounts'
        )
        parser.add_argument(
            '--providers-only',
            action='store_true',
            help='Create only provider test accounts'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🌱 SEEDING TEST DATA FOR VIERLA PLATFORM')
        )
        self.stdout.write('='*60)

        # Security check
        if not self.is_development_environment():
            self.stdout.write(
                self.style.ERROR(
                    '❌ This command can only be run in development environment!'
                )
            )
            return

        # Prepare command arguments
        cmd_args = []
        if options['quick']:
            cmd_args.append('--quick')
        if options['force']:
            cmd_args.append('--force')
        if options['customers_only']:
            cmd_args.append('--customers-only')
        if options['providers_only']:
            cmd_args.append('--providers-only')

        try:
            # Step 1: Create test accounts
            self.stdout.write('\n🔧 Step 1: Creating test accounts...')
            call_command('create_test_accounts', *cmd_args)

            # Step 2: Create additional sample data if not quick mode
            if not options['quick']:
                self.stdout.write('\n🔧 Step 2: Generating additional sample data...')
                call_command('generate_sample_data', '--test-only')

            # Step 3: Verify data integrity
            self.stdout.write('\n🔧 Step 3: Verifying data integrity...')
            call_command('verify_sample_data', '--test-accounts-only')

            # Success message
            self.stdout.write('\n' + '='*60)
            self.stdout.write(
                self.style.SUCCESS('✅ TEST DATA SEEDING COMPLETED SUCCESSFULLY!')
            )
            self.stdout.write('='*60)
            
            self.print_usage_info()

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during test data seeding: {str(e)}')
            )
            raise

    def is_development_environment(self):
        """Check if running in development environment"""
        return (
            settings.DEBUG and 
            'test' in settings.DATABASES['default']['NAME'].lower() or
            'dev' in settings.DATABASES['default']['NAME'].lower() or
            os.environ.get('DJANGO_ENV') == 'development'
        )

    def print_usage_info(self):
        """Print information about test accounts"""
        self.stdout.write('\n📋 Test Account Information:')
        self.stdout.write('   🔐 All test accounts use password: VierlaTest123!')
        self.stdout.write('   📧 Admin accounts:')
        self.stdout.write('      • <EMAIL> (Super Admin)')
        self.stdout.write('      • <EMAIL> (Support Staff)')
        self.stdout.write('   👤 Customer accounts:')
        self.stdout.write('      • <EMAIL> (Emma Thompson)')
        self.stdout.write('      • <EMAIL> (Sarah Johnson)')
        self.stdout.write('      • <EMAIL> (Michael Chen)')
        self.stdout.write('      • <EMAIL> (Priya Patel)')
        self.stdout.write('   🏢 Provider accounts:')
        self.stdout.write('      • <EMAIL> (Trendy Cuts Salon)')
        self.stdout.write('      • <EMAIL> (Elite Cuts Barbershop)')
        self.stdout.write('      • <EMAIL> (Luxe Nail Lounge)')
        self.stdout.write('      • <EMAIL> (Lash Studio Elite)')
        self.stdout.write('      • <EMAIL> (Wellness Massage Therapy)')
        self.stdout.write('      • <EMAIL> (New Beauty Studio)')
        self.stdout.write('\n💡 Use these accounts for testing different user scenarios!')
        self.stdout.write('   Run "python manage.py reset_test_data --force" to clean up.')
