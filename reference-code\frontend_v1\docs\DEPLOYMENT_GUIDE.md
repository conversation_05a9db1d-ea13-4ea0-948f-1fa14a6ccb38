# Vierla Frontend Deployment Guide

## Overview

This guide covers the deployment process for the Vierla frontend application across different environments and platforms.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [Build Process](#build-process)
3. [iOS Deployment](#ios-deployment)
4. [Android Deployment](#android-deployment)
5. [Web Deployment](#web-deployment)
6. [CI/CD Pipeline](#cicd-pipeline)
7. [Environment Variables](#environment-variables)
8. [Monitoring and Analytics](#monitoring-and-analytics)
9. [Troubleshooting](#troubleshooting)

## Environment Setup

### Development Environment

```bash
# Install dependencies
npm install

# Start development server
npm start

# Run on specific platform
npm run ios
npm run android
npm run web
```

### Staging Environment

```bash
# Build for staging
npm run build:staging

# Deploy to staging
npm run deploy:staging
```

### Production Environment

```bash
# Build for production
npm run build:production

# Deploy to production
npm run deploy:production
```

## Build Process

### Pre-build Checklist

- [ ] All tests passing
- [ ] Code review completed
- [ ] Version number updated
- [ ] Environment variables configured
- [ ] Assets optimized
- [ ] Documentation updated

### Build Commands

```bash
# Clean build
npm run clean

# Build for development
npm run build:dev

# Build for staging
npm run build:staging

# Build for production
npm run build:prod
```

### Build Configuration

```javascript
// app.config.js
export default {
  expo: {
    name: "Vierla",
    slug: "vierla",
    version: "1.0.0",
    platforms: ["ios", "android", "web"],
    // Environment-specific configuration
  }
};
```

## iOS Deployment

### Prerequisites

- Apple Developer Account
- Xcode (latest version)
- iOS certificates and provisioning profiles
- App Store Connect access

### Build Process

1. **Configure iOS project**
   ```bash
   cd ios
   pod install
   ```

2. **Build for iOS**
   ```bash
   # Development build
   expo build:ios --type simulator

   # Production build
   expo build:ios --type archive
   ```

3. **Upload to App Store Connect**
   ```bash
   # Using Expo
   expo upload:ios

   # Using Xcode
   # Archive and upload through Xcode
   ```

### iOS Configuration

```javascript
// app.config.js - iOS specific
{
  ios: {
    bundleIdentifier: "com.vierla.app",
    buildNumber: "1.0.0",
    supportsTablet: true,
    infoPlist: {
      NSLocationWhenInUseUsageDescription: "Location access for nearby services",
      NSCameraUsageDescription: "Camera access for profile photos"
    }
  }
}
```

### App Store Submission

1. **Prepare app metadata**
   - App description
   - Keywords
   - Screenshots
   - App icon

2. **Submit for review**
   - Upload build to App Store Connect
   - Fill out app information
   - Submit for Apple review

## Android Deployment

### Prerequisites

- Google Play Console account
- Android keystore
- Google Play signing key

### Build Process

1. **Generate keystore**
   ```bash
   keytool -genkey -v -keystore vierla-release-key.keystore -alias vierla -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **Build for Android**
   ```bash
   # Development build
   expo build:android --type apk

   # Production build
   expo build:android --type app-bundle
   ```

3. **Upload to Google Play**
   ```bash
   # Using Expo
   expo upload:android

   # Manual upload through Google Play Console
   ```

### Android Configuration

```javascript
// app.config.js - Android specific
{
  android: {
    package: "com.vierla.app",
    versionCode: 1,
    permissions: [
      "ACCESS_FINE_LOCATION",
      "CAMERA",
      "READ_EXTERNAL_STORAGE"
    ],
    adaptiveIcon: {
      foregroundImage: "./assets/adaptive-icon.png",
      backgroundColor: "#FFFFFF"
    }
  }
}
```

### Google Play Submission

1. **Prepare app listing**
   - App description
   - Screenshots
   - Feature graphic
   - App icon

2. **Release management**
   - Internal testing
   - Closed testing
   - Open testing
   - Production release

## Web Deployment

### Build for Web

```bash
# Build web version
expo build:web

# Serve locally
npx serve web-build
```

### Deployment Options

#### 1. Netlify Deployment

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy to Netlify
netlify deploy --prod --dir=web-build
```

#### 2. Vercel Deployment

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy to Vercel
vercel --prod
```

#### 3. AWS S3 + CloudFront

```bash
# Build for web
npm run build:web

# Upload to S3
aws s3 sync web-build/ s3://your-bucket-name

# Invalidate CloudFront
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

## CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test

  build-ios:
    needs: test
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v2
      - uses: expo/expo-github-action@v7
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - run: expo build:ios

  build-android:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: expo/expo-github-action@v7
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      - run: expo build:android
```

### Deployment Stages

1. **Development**
   - Automatic deployment on feature branch push
   - Development environment variables
   - Debug builds

2. **Staging**
   - Deployment on main branch merge
   - Staging environment variables
   - Release candidate builds

3. **Production**
   - Manual deployment trigger
   - Production environment variables
   - Optimized builds

## Environment Variables

### Configuration Management

```javascript
// config/environment.js
const config = {
  development: {
    API_URL: 'http://localhost:3000',
    DEBUG: true,
  },
  staging: {
    API_URL: 'https://staging-api.vierla.com',
    DEBUG: false,
  },
  production: {
    API_URL: 'https://api.vierla.com',
    DEBUG: false,
  }
};

export default config[process.env.NODE_ENV || 'development'];
```

### Secure Variables

```bash
# .env.example
API_URL=https://api.vierla.com
STRIPE_PUBLISHABLE_KEY=pk_test_...
GOOGLE_MAPS_API_KEY=AIza...
SENTRY_DSN=https://...
```

### Expo Secrets

```bash
# Set Expo secrets
expo secrets:set API_URL https://api.vierla.com
expo secrets:set STRIPE_KEY pk_live_...
```

## Monitoring and Analytics

### Error Tracking

```javascript
// services/errorTracking.js
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: process.env.NODE_ENV,
});
```

### Performance Monitoring

```javascript
// services/analytics.js
import { Analytics } from '@segment/analytics-react-native';

const analytics = new Analytics({
  writeKey: 'YOUR_SEGMENT_WRITE_KEY',
});
```

### Health Checks

```javascript
// services/healthCheck.js
export const performHealthCheck = async () => {
  try {
    const response = await fetch('/api/health');
    return response.ok;
  } catch (error) {
    return false;
  }
};
```

## Troubleshooting

### Common Issues

#### 1. Build Failures

```bash
# Clear cache
expo r -c

# Clear node modules
rm -rf node_modules
npm install

# Clear iOS build
cd ios && xcodebuild clean
```

#### 2. Certificate Issues (iOS)

```bash
# Download certificates
expo fetch:ios:certs

# Update provisioning profiles
expo credentials:manager
```

#### 3. Keystore Issues (Android)

```bash
# Generate new keystore
expo credentials:manager

# Upload existing keystore
expo upload:android:keystore
```

### Debugging Deployment

1. **Check build logs**
   ```bash
   expo build:status
   ```

2. **Validate configuration**
   ```bash
   expo config --type public
   ```

3. **Test locally**
   ```bash
   expo start --no-dev --minify
   ```

## Rollback Procedures

### iOS Rollback

1. **App Store Connect**
   - Remove current version from sale
   - Promote previous version

2. **TestFlight**
   - Distribute previous build
   - Notify testers

### Android Rollback

1. **Google Play Console**
   - Halt current rollout
   - Rollback to previous version

2. **Staged Rollout**
   - Reduce rollout percentage
   - Monitor crash reports

### Web Rollback

```bash
# Revert to previous deployment
git revert HEAD
git push origin main

# Or rollback specific deployment
netlify rollback
```

## Security Considerations

### Code Signing

- Use proper certificates for iOS
- Sign Android APKs with release keystore
- Validate signatures before deployment

### API Security

- Use HTTPS for all API calls
- Implement proper authentication
- Validate SSL certificates

### Data Protection

- Encrypt sensitive data
- Use secure storage for tokens
- Implement proper session management

## Performance Optimization

### Bundle Size

```bash
# Analyze bundle size
npx react-native-bundle-visualizer

# Optimize images
npx expo optimize

# Remove unused dependencies
npm prune
```

### Caching Strategy

- Implement proper HTTP caching
- Use CDN for static assets
- Cache API responses appropriately

---

This deployment guide should be updated as the deployment process evolves and new requirements are identified.
