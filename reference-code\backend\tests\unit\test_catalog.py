"""
Unit tests for Catalog app
"""
import pytest
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from apps.catalog.models import ServiceCategory, ServiceProvider, Service
from apps.catalog.serializers import (
    ServiceCategorySerializer, ServiceProviderSerializer, ServiceSerializer
)


@pytest.mark.unit
@pytest.mark.catalog
class TestServiceCategoryModel:
    """Test ServiceCategory model functionality"""

    def test_create_service_category_success(self, db):
        """Test creating a service category"""
        category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services',
            icon='✂️',
            color='#4CAF50',
            is_active=True,
            sort_order=1
        )
        
        assert category.name == 'Hair Services'
        assert category.slug == 'hair-services'
        assert category.description == 'Professional hair services'
        assert category.icon == '✂️'
        assert category.color == '#4CAF50'
        assert category.is_active is True
        assert category.sort_order == 1

    @pytest.mark.django_db
    def test_service_category_string_representation(self, service_category):
        """Test category string representation"""
        assert str(service_category) == 'Hair Services'

    def test_service_category_slug_unique(self, db):
        """Test that category slug must be unique"""
        ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair services'
        )
        
        with pytest.raises(IntegrityError):
            ServiceCategory.objects.create(
                name='Hair Care',
                slug='hair-services',  # Duplicate slug
                description='Hair care services'
            )

    def test_service_category_ordering(self, db):
        """Test category ordering by sort_order"""
        # Clear existing categories to ensure clean test
        ServiceCategory.objects.all().delete()

        cat1 = ServiceCategory.objects.create(
            name='Category 1', slug='cat-1', sort_order=2
        )
        cat2 = ServiceCategory.objects.create(
            name='Category 2', slug='cat-2', sort_order=1
        )
        cat3 = ServiceCategory.objects.create(
            name='Category 3', slug='cat-3', sort_order=3
        )

        categories = list(ServiceCategory.objects.all())
        assert categories[0] == cat2  # sort_order=1
        assert categories[1] == cat1  # sort_order=2
        assert categories[2] == cat3  # sort_order=3


@pytest.mark.unit
@pytest.mark.catalog
class TestServiceProviderModel:
    """Test ServiceProvider model functionality"""

    @pytest.mark.django_db
    def test_create_service_provider_success(self, provider_user, service_category):
        """Test creating a service provider"""
        provider = ServiceProvider.objects.create(
            user=provider_user,
            business_name='Test Beauty Salon',
            business_description='Professional beauty services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Test Street',
            city='Toronto',
            state='ON',
            zip_code='M5V 3A8',
            country='Canada',
            latitude=Decimal('43.6532'),
            longitude=Decimal('-79.3832'),
            is_active=True,
            is_verified=True
        )
        provider.categories.add(service_category)
        
        assert provider.user == provider_user
        assert provider.business_name == 'Test Beauty Salon'
        assert provider.business_description == 'Professional beauty services'
        assert provider.business_phone == '+**********'
        assert provider.business_email == '<EMAIL>'
        assert provider.address == '123 Test Street'
        assert provider.city == 'Toronto'
        assert provider.state == 'ON'
        assert provider.zip_code == 'M5V 3A8'
        assert provider.country == 'Canada'
        assert provider.latitude == Decimal('43.6532')
        assert provider.longitude == Decimal('-79.3832')
        assert provider.is_active is True
        assert provider.is_verified is True
        assert service_category in provider.categories.all()

    @pytest.mark.django_db
    def test_service_provider_string_representation(self, service_provider):
        """Test provider string representation"""
        assert str(service_provider) == 'Test Beauty Salon'

    @pytest.mark.django_db
    def test_service_provider_user_unique(self, provider_user, service_category):
        """Test that each user can only have one provider profile"""
        ServiceProvider.objects.create(
            user=provider_user,
            business_name='First Salon'
        )
        
        with pytest.raises(IntegrityError):
            ServiceProvider.objects.create(
                user=provider_user,
                business_name='Second Salon'
            )

    @pytest.mark.django_db
    def test_service_provider_location_validation(self, provider_user):
        """Test location coordinate validation"""
        # Valid coordinates
        provider = ServiceProvider.objects.create(
            user=provider_user,
            business_name='Test Salon',
            latitude=Decimal('43.6532'),
            longitude=Decimal('-79.3832')
        )
        assert provider.latitude == Decimal('43.6532')
        assert provider.longitude == Decimal('-79.3832')


@pytest.mark.unit
@pytest.mark.catalog
class TestServiceModel:
    """Test Service model functionality"""

    @pytest.mark.django_db
    def test_create_service_success(self, service_provider, service_category):
        """Test creating a service"""
        service = Service.objects.create(
            provider=service_provider,
            category=service_category,
            name='Haircut',
            description='Professional haircut service',
            base_price=Decimal('50.00'),
            duration=60,
            is_active=True,
            is_available=True
        )
        
        assert service.provider == service_provider
        assert service.category == service_category
        assert service.name == 'Haircut'
        assert service.description == 'Professional haircut service'
        assert service.base_price == Decimal('50.00')
        assert service.duration == 60
        assert service.is_active is True
        assert service.is_available is True

    @pytest.mark.django_db
    def test_service_string_representation(self, service):
        """Test service string representation"""
        expected = f"{service.provider.business_name} - {service.name}"
        assert str(service) == expected

    @pytest.mark.django_db
    def test_service_price_validation(self, service_provider, service_category):
        """Test service price validation"""
        # Valid price
        service = Service.objects.create(
            provider=service_provider,
            category=service_category,
            name='Test Service',
            base_price=Decimal('25.50'),
            duration=30
        )
        assert service.base_price == Decimal('25.50')

    @pytest.mark.django_db
    def test_service_duration_validation(self, service_provider, service_category):
        """Test service duration validation"""
        # Valid duration
        service = Service.objects.create(
            provider=service_provider,
            category=service_category,
            name='Test Service',
            base_price=Decimal('50.00'),
            duration=90
        )
        assert service.duration == 90


@pytest.mark.unit
@pytest.mark.catalog
class TestCatalogSerializers:
    """Test catalog serializers"""

    @pytest.mark.django_db
    def test_service_category_serializer(self, service_category):
        """Test service category serializer"""
        serializer = ServiceCategorySerializer(service_category)
        data = serializer.data
        
        assert data['name'] == service_category.name
        assert data['slug'] == service_category.slug
        assert data['description'] == service_category.description
        assert data['icon'] == service_category.icon
        assert data['color'] == service_category.color
        assert data['is_active'] == service_category.is_active
        assert data['sort_order'] == service_category.sort_order

    @pytest.mark.django_db
    def test_service_provider_serializer(self, service_provider):
        """Test service provider serializer"""
        serializer = ServiceProviderSerializer(service_provider)
        data = serializer.data
        
        assert data['business_name'] == service_provider.business_name
        assert data['business_description'] == service_provider.business_description
        assert data['business_phone'] == service_provider.business_phone
        assert data['business_email'] == service_provider.business_email
        assert data['address'] == service_provider.address
        assert data['city'] == service_provider.city
        assert data['state'] == service_provider.state
        assert data['zip_code'] == service_provider.zip_code
        assert data['country'] == service_provider.country
        assert data['is_active'] == service_provider.is_active
        assert data['is_verified'] == service_provider.is_verified

    @pytest.mark.django_db
    def test_service_serializer(self, service):
        """Test service serializer"""
        serializer = ServiceSerializer(service)
        data = serializer.data
        
        assert data['name'] == service.name
        assert data['description'] == service.description
        assert Decimal(data['base_price']) == service.base_price
        assert data['duration'] == service.duration
        assert data['is_active'] == service.is_active
        assert data['is_available'] == service.is_available
        assert data['provider_name'] == service.provider.business_name
        assert data['category_name'] == service.category.name

    def test_service_category_serializer_create(self, db):
        """Test creating category via serializer"""
        data = {
            'name': 'New Category',
            'slug': 'new-category',
            'description': 'A new service category',
            'icon': '🎨',
            'color': '#FF5722',
            'is_active': True,
            'sort_order': 5
        }
        
        serializer = ServiceCategorySerializer(data=data)
        assert serializer.is_valid()
        
        category = serializer.save()
        assert category.name == 'New Category'
        assert category.slug == 'new-category'
        assert category.description == 'A new service category'
        assert category.icon == '🎨'
        assert category.color == '#FF5722'
        assert category.is_active is True
        assert category.sort_order == 5
