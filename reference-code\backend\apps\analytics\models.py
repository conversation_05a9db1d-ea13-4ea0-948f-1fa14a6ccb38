from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class PerformanceMetric(models.Model):
    """
    Core performance metrics tracking - Food delivery app analogous metrics
    Tracks latency, RPS, queueing delays, read/write ratios
    """
    METRIC_TYPES = [
        ('api_latency', 'API Response Latency'),
        ('rps', 'Requests Per Second'),
        ('queue_delay', 'Queue Processing Delay'),
        ('db_read_time', 'Database Read Time'),
        ('db_write_time', 'Database Write Time'),
        ('cache_hit_rate', 'Cache Hit Rate'),
        ('error_rate', 'Error Rate'),
        ('booking_completion_time', 'Booking Completion Time'),
        ('search_response_time', 'Search Response Time'),
        ('payment_processing_time', 'Payment Processing Time'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    metric_type = models.Char<PERSON>ield(max_length=50, choices=METRIC_TYPES)
    value = models.DecimalField(max_digits=10, decimal_places=4)
    unit = models.CharField(max_length=20)  # ms, seconds, percentage, count
    endpoint = models.CharField(max_length=200, blank=True, null=True)
    # GET, POST, etc.
    method = models.CharField(max_length=10, blank=True, null=True)
    status_code = models.IntegerField(blank=True, null=True)
    user_id = models.UUIDField(blank=True, null=True)
    session_id = models.CharField(max_length=100, blank=True, null=True)
    timestamp = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'analytics_performance_metrics'
        indexes = [
            models.Index(fields=['metric_type', 'timestamp']),
            models.Index(fields=['endpoint', 'timestamp']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['user_id', 'timestamp']),
        ]
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.metric_type}: {self.value}{self.unit} at {self.timestamp}"


class SystemHealthMetric(models.Model):
    """
    System-wide health metrics for monitoring overall performance
    """
    HEALTH_TYPES = [
        ('cpu_usage', 'CPU Usage Percentage'),
        ('memory_usage', 'Memory Usage Percentage'),
        ('disk_usage', 'Disk Usage Percentage'),
        ('active_connections', 'Active Database Connections'),
        ('queue_size', 'Task Queue Size'),
        ('cache_memory', 'Cache Memory Usage'),
        ('response_time_avg', 'Average Response Time'),
        ('throughput', 'Requests Throughput'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    health_type = models.CharField(max_length=50, choices=HEALTH_TYPES)
    value = models.DecimalField(max_digits=10, decimal_places=4)
    unit = models.CharField(max_length=20)
    server_instance = models.CharField(max_length=100, default='default')
    timestamp = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'analytics_system_health'
        indexes = [
            models.Index(fields=['health_type', 'timestamp']),
            models.Index(fields=['server_instance', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.health_type}: {self.value}{self.unit} at {self.timestamp}"


class BusinessMetric(models.Model):
    """
    Business-specific metrics for tracking app performance from business perspective
    """
    BUSINESS_TYPES = [
        ('booking_conversion_rate', 'Booking Conversion Rate'),
        ('search_to_booking_time', 'Search to Booking Time'),
        ('provider_response_time', 'Provider Response Time'),
        ('customer_satisfaction', 'Customer Satisfaction Score'),
        ('revenue_per_hour', 'Revenue Per Hour'),
        ('active_users', 'Active Users Count'),
        ('booking_cancellation_rate', 'Booking Cancellation Rate'),
        ('provider_utilization', 'Provider Utilization Rate'),
        ('peak_hour_performance', 'Peak Hour Performance'),
        ('geographic_performance', 'Geographic Performance'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    business_type = models.CharField(max_length=50, choices=BUSINESS_TYPES)
    value = models.DecimalField(max_digits=12, decimal_places=4)
    unit = models.CharField(max_length=20)
    region = models.CharField(max_length=100, blank=True, null=True)
    category = models.CharField(max_length=100, blank=True, null=True)
    timestamp = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'analytics_business_metrics'
        indexes = [
            models.Index(fields=['business_type', 'timestamp']),
            models.Index(fields=['region', 'timestamp']),
            models.Index(fields=['category', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.business_type}: {self.value}{self.unit} at {self.timestamp}"


class RealTimeMetric(models.Model):
    """
    Real-time metrics for live dashboard monitoring
    """
    REALTIME_TYPES = [
        ('current_rps', 'Current Requests Per Second'),
        ('active_users_now', 'Active Users Right Now'),
        ('queue_length', 'Current Queue Length'),
        ('avg_response_time_1min', '1-Minute Average Response Time'),
        ('error_rate_1min', '1-Minute Error Rate'),
        ('booking_rate_1min', '1-Minute Booking Rate'),
        ('cache_hit_rate_1min', '1-Minute Cache Hit Rate'),
        ('db_connections_active', 'Active Database Connections'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    metric_type = models.CharField(max_length=50, choices=REALTIME_TYPES)
    value = models.DecimalField(max_digits=10, decimal_places=4)
    unit = models.CharField(max_length=20)
    timestamp = models.DateTimeField(default=timezone.now)
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'analytics_realtime_metrics'
        indexes = [
            models.Index(fields=['metric_type', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.metric_type}: {self.value}{self.unit} at {self.timestamp}"


class PerformanceAlert(models.Model):
    """
    Performance alerts and threshold monitoring
    """
    ALERT_TYPES = [
        ('high_latency', 'High API Latency'),
        ('high_error_rate', 'High Error Rate'),
        ('low_cache_hit', 'Low Cache Hit Rate'),
        ('high_queue_delay', 'High Queue Delay'),
        ('system_overload', 'System Overload'),
        ('database_slow', 'Database Performance Issue'),
        ('booking_failure_spike', 'Booking Failure Spike'),
        ('payment_processing_delay', 'Payment Processing Delay'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('false_positive', 'False Positive'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPES)
    severity = models.CharField(max_length=20, choices=SEVERITY_LEVELS)
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='active')
    threshold_value = models.DecimalField(max_digits=10, decimal_places=4)
    actual_value = models.DecimalField(max_digits=10, decimal_places=4)
    unit = models.CharField(max_length=20)
    message = models.TextField()
    endpoint = models.CharField(max_length=200, blank=True, null=True)
    triggered_at = models.DateTimeField(default=timezone.now)
    acknowledged_at = models.DateTimeField(blank=True, null=True)
    resolved_at = models.DateTimeField(blank=True, null=True)
    acknowledged_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, blank=True, null=True, related_name='acknowledged_alerts')
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'analytics_performance_alerts'
        indexes = [
            models.Index(fields=['alert_type', 'status']),
            models.Index(fields=['severity', 'status']),
            models.Index(fields=['triggered_at']),
            models.Index(fields=['status']),
        ]
        ordering = ['-triggered_at']

    def __str__(self):
        return f"{self.alert_type} ({self.severity}) - {self.status}"


class PerformanceDashboard(models.Model):
    """
    Dashboard configuration for performance monitoring
    """
    DASHBOARD_TYPES = [
        ('system_overview', 'System Overview Dashboard'),
        ('api_performance', 'API Performance Dashboard'),
        ('business_metrics', 'Business Metrics Dashboard'),
        ('real_time_monitoring', 'Real-time Monitoring Dashboard'),
        ('error_tracking', 'Error Tracking Dashboard'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    dashboard_type = models.CharField(max_length=50, choices=DASHBOARD_TYPES)
    config = models.JSONField(default=dict)  # Dashboard configuration
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name='created_dashboards')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'analytics_performance_dashboards'
        indexes = [
            models.Index(fields=['dashboard_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.dashboard_type})"
