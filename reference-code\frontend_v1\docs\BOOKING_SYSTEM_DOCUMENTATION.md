# Booking System Documentation

## Overview

The Vierla booking system provides a comprehensive, real-time booking experience for customers to book services from providers. The system includes service discovery, booking flow management, real-time tracking, and payment processing.

## Architecture

### Core Components

#### 1. BookingFlowScreen
**Location**: `src/screens/BookingFlowScreen.tsx`

The main booking flow orchestrator that manages the step-by-step booking process.

**Features**:
- Multi-step booking wizard
- Service selection
- Time slot booking
- Customer information collection
- Payment processing
- Booking confirmation

**Usage**:
```typescript
import { BookingFlowScreen } from '../screens/BookingFlowScreen';

// Navigate to booking flow
navigation.navigate('BookingFlow', {
  providerId: 'provider_123',
  serviceId: 'service_456', // Optional - if not provided, shows service selection
  serviceName: 'Haircut',
  servicePrice: 30.00,
  serviceDuration: 45,
});
```

#### 2. ServiceSelectionStep
**Location**: `src/components/booking/ServiceSelectionStep.tsx`

Handles service selection within the booking flow.

**Features**:
- Service listing with search and filtering
- Category-based filtering
- Service details display
- Price and duration information

**Props**:
```typescript
interface ServiceSelectionStepProps {
  providerId: string;
  selectedServiceId?: string;
  onServiceSelect: (data: {
    serviceId: string;
    serviceName: string;
    servicePrice: number;
    serviceDuration: number;
  }) => void;
}
```

#### 3. RealTimeBookingTracker
**Location**: `src/components/booking/RealTimeBookingTracker.tsx`

Provides real-time tracking of booking status and provider location.

**Features**:
- Live booking status updates
- Provider location tracking
- Estimated arrival times
- Direct communication with provider
- Progress visualization

**Usage**:
```typescript
import { RealTimeBookingTracker } from '../components/booking/RealTimeBookingTracker';

<RealTimeBookingTracker
  bookingId="booking_123"
  onStatusChange={(status) => console.log('Status changed:', status)}
  onComplete={() => navigation.navigate('BookingComplete')}
/>
```

### Services

#### 1. Real-time Notification Service
**Location**: `src/services/realTimeNotificationService.ts`

Manages real-time notifications and WebSocket connections.

**Features**:
- WebSocket connection management
- Booking status updates
- Message notifications
- Provider location updates
- System alerts

**API**:
```typescript
import { realTimeNotificationService } from '../services/realTimeNotificationService';

// Initialize service
await realTimeNotificationService.initialize();

// Listen for booking updates
realTimeNotificationService.on('booking_update', (data) => {
  console.log('Booking updated:', data);
});

// Send typing indicator
realTimeNotificationService.sendTypingIndicator(conversationId, true);
```

#### 2. Error Handling Service
**Location**: `src/services/errorHandlingService.ts`

Centralized error handling and user feedback management.

**Features**:
- Error categorization and severity assessment
- User-friendly error messages
- Recovery action suggestions
- Error reporting and analytics
- Offline error storage

**API**:
```typescript
import { errorHandlingService } from '../services/errorHandlingService';

// Handle an error
try {
  await someApiCall();
} catch (error) {
  const errorReport = await errorHandlingService.handleError(
    error,
    { component: 'BookingFlow', action: 'createBooking' },
    'Failed to create your booking. Please try again.'
  );
}

// Listen for errors
errorHandlingService.addErrorListener((errorReport) => {
  console.log('Error occurred:', errorReport);
});
```

## Booking Flow States

### Step 1: Service Selection
- **Condition**: No serviceId provided in navigation params
- **Purpose**: Allow user to select from available services
- **Next**: Time Slot Selection

### Step 2: Time Slot Selection
- **Purpose**: Select date and time for the service
- **Data Required**: serviceId, serviceDuration
- **Next**: Customer Information

### Step 3: Customer Information
- **Purpose**: Collect customer details for the booking
- **Pre-filled**: User profile data if logged in
- **Validation**: Required fields validation
- **Next**: Payment

### Step 4: Payment
- **Purpose**: Select payment method and process payment
- **Options**: Saved payment methods, new payment method
- **Next**: Summary

### Step 5: Summary & Confirmation
- **Purpose**: Review booking details and confirm
- **Actions**: Create booking, process payment
- **Success**: Navigate to BookingConfirmation
- **Error**: Show error message with retry options

## Real-time Features

### Booking Status Updates
The system provides real-time updates for booking status changes:

1. **Confirmed**: Booking is confirmed and provider notified
2. **Provider En Route**: Provider is traveling to customer location
3. **Provider Arrived**: Provider has arrived at the location
4. **In Progress**: Service is being performed
5. **Completed**: Service has been completed
6. **Cancelled**: Booking has been cancelled

### Provider Location Tracking
- Real-time provider location updates during service
- Estimated arrival time calculations
- Route visualization on map

### Live Messaging
- Real-time messaging between customer and provider
- Typing indicators
- Message status (sent, delivered, read)

## Error Handling

### Error Types
1. **Network Errors**: Connection issues, API failures
2. **Validation Errors**: Invalid input data
3. **Authentication Errors**: Session expired, unauthorized
4. **Permission Errors**: Access denied
5. **System Errors**: Application crashes, unexpected errors

### Recovery Strategies
- **Retry Mechanisms**: Automatic and manual retry options
- **Offline Support**: Queue operations for when connection is restored
- **Fallback UI**: Graceful degradation when features are unavailable
- **User Guidance**: Clear instructions for resolving issues

## Performance Optimization

### Caching Strategy
- Service data caching with TTL
- Provider information caching
- Image optimization and lazy loading
- API response caching

### Memory Management
- Component unmounting cleanup
- WebSocket connection management
- Cache size limits and cleanup
- Memory leak prevention

### Bundle Optimization
- Code splitting for booking components
- Lazy loading of non-critical features
- Image compression and optimization
- Efficient re-rendering strategies

## Testing

### Test Coverage
- Unit tests for all components and services
- Integration tests for booking flow
- Performance tests for critical paths
- Accessibility compliance tests
- Error handling scenario tests

### Test Utilities
**Location**: `src/utils/integrationTestUtils.tsx`

Comprehensive testing utilities for booking system testing:

```typescript
import { testBookingFlow, performanceTestUtils } from '../utils/integrationTestUtils';

// Test complete booking flow
await testBookingFlow.selectService(getByTestId, 'service_123');
await testBookingFlow.selectTimeSlot(getByTestId, '10:00');
await testBookingFlow.fillCustomerInfo(getByTestId, customerData);
await testBookingFlow.confirmBooking(getByTestId);

// Performance testing
const renderTime = await performanceTestUtils.measureRenderTime(component);
performanceTestUtils.expectPerformanceWithinBounds(renderTime, 1000);
```

## API Integration

### Booking Service
**Endpoints**:
- `POST /api/bookings/` - Create new booking
- `GET /api/bookings/{id}/` - Get booking details
- `PUT /api/bookings/{id}/` - Update booking
- `DELETE /api/bookings/{id}/` - Cancel booking
- `GET /api/providers/{id}/availability/` - Get provider availability

### Payment Service
**Endpoints**:
- `POST /api/payments/` - Process payment
- `GET /api/payment-methods/` - Get saved payment methods
- `POST /api/payment-methods/` - Add new payment method

### WebSocket Events
**Connection**: `ws://backend-url/ws/notifications/`

**Events**:
- `booking_update` - Booking status changes
- `new_message` - New chat messages
- `provider_location` - Provider location updates
- `payment_status` - Payment processing updates

## Configuration

### Environment Variables
```env
# WebSocket Configuration
WEBSOCKET_URL=ws://localhost:8000/ws/notifications/

# API Configuration
API_BASE_URL=http://localhost:8000/api/

# Performance Configuration
CACHE_TTL=300000  # 5 minutes
MAX_CACHE_SIZE=50  # 50MB
PERFORMANCE_MONITORING=true

# Error Handling Configuration
ERROR_REPORTING=true
OFFLINE_STORAGE=true
MAX_RETRIES=3
RETRY_DELAY=1000
```

### Feature Flags
```typescript
const FEATURE_FLAGS = {
  REAL_TIME_TRACKING: true,
  PAYMENT_PROCESSING: true,
  OFFLINE_SUPPORT: true,
  PERFORMANCE_MONITORING: true,
  ERROR_REPORTING: true,
};
```

## Troubleshooting

### Common Issues

#### 1. WebSocket Connection Failures
**Symptoms**: Real-time updates not working
**Solutions**:
- Check network connectivity
- Verify WebSocket URL configuration
- Check authentication token validity
- Review server WebSocket implementation

#### 2. Payment Processing Errors
**Symptoms**: Payment fails during booking
**Solutions**:
- Verify payment method validity
- Check payment service configuration
- Review payment gateway integration
- Validate payment amount and currency

#### 3. Booking Creation Failures
**Symptoms**: Booking fails to create
**Solutions**:
- Validate all required fields
- Check provider availability
- Verify service configuration
- Review API endpoint accessibility

### Debug Mode
Enable debug logging for detailed troubleshooting:

```typescript
// Enable debug mode
window.__VIERLA_DEBUG__ = true;

// Check logs in console
console.log('Booking debug info:', debugInfo);
```

## Security Considerations

### Data Protection
- Sensitive data encryption in transit and at rest
- Secure payment processing with PCI compliance
- User authentication and authorization
- Input validation and sanitization

### API Security
- JWT token authentication
- Rate limiting and throttling
- CORS configuration
- Request validation and filtering

### WebSocket Security
- Authenticated connections only
- Message validation and filtering
- Connection rate limiting
- Secure WebSocket (WSS) in production

## Deployment

### Build Configuration
```bash
# Production build
npm run build:production

# Development build
npm run build:development

# Testing build
npm run build:testing
```

### Environment Setup
1. Configure environment variables
2. Set up WebSocket server
3. Configure payment gateway
4. Set up error reporting service
5. Configure performance monitoring

### Monitoring
- Real-time performance metrics
- Error rate monitoring
- User experience analytics
- System health checks
- WebSocket connection monitoring

## Future Enhancements

### Planned Features
1. **Advanced Scheduling**: Recurring bookings, bulk scheduling
2. **Enhanced Tracking**: Detailed service progress tracking
3. **AI Integration**: Smart scheduling recommendations
4. **Multi-language Support**: Internationalization
5. **Advanced Analytics**: Detailed booking analytics

### Technical Improvements
1. **Performance**: Further optimization and caching
2. **Offline Support**: Enhanced offline capabilities
3. **Testing**: Increased test coverage and automation
4. **Documentation**: Interactive API documentation
5. **Monitoring**: Advanced performance monitoring
