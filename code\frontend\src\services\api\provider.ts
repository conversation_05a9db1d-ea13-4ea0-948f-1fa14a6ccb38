import { apiClient } from './client';

export interface Service {
  id: string;
  name: string;
  description: string;
  short_description?: string;
  mobile_description?: string;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  base_price: number;
  price_type: 'fixed' | 'range';
  max_price?: number;
  duration: number;
  buffer_time: number;
  requirements?: string;
  preparation_instructions?: string;
  is_available: boolean;
  is_active: boolean;
  is_popular: boolean;
  booking_count: number;
  created_at: string;
  updated_at: string;
}

export interface ServiceCreateData {
  name: string;
  description: string;
  short_description?: string;
  mobile_description?: string;
  category: string;
  base_price: number;
  price_type?: 'fixed' | 'range';
  max_price?: number;
  duration: number;
  buffer_time?: number;
  requirements?: string;
  preparation_instructions?: string;
}

export interface ServiceUpdateData extends Partial<ServiceCreateData> {
  is_available?: boolean;
  is_popular?: boolean;
}

export interface ProviderSummary {
  total_services: number;
  active_services: number;
  inactive_services: number;
  is_verified: boolean;
  service_limit: number | null;
}

export interface ServicesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Service[];
  provider_summary: ProviderSummary;
}

export interface DashboardData {
  provider_info: {
    business_name: string;
    is_verified: boolean;
    rating: number;
    total_bookings: number;
  };
  service_stats: {
    total_services: number;
    active_services: number;
    inactive_services: number;
    popular_services: number;
    service_limit: number | null;
    services_remaining: number | null;
  };
  revenue_stats: {
    total_revenue: number;
    average_service_price: number;
  };
  recent_services: Array<{
    id: string;
    name: string;
    base_price: number;
    is_available: boolean;
    booking_count: number;
  }>;
  top_services: Array<{
    id: string;
    name: string;
    base_price: number;
    booking_count: number;
  }>;
}

export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  is_active: boolean;
}

export interface BulkUpdateResponse {
  updated_count: number;
  message: string;
}

export const providerServiceAPI = {
  // Get all services for the authenticated provider
  getServices: (params?: {
    page?: number;
    search?: string;
    category?: string;
    is_available?: boolean;
    ordering?: string;
  }) => {
    return apiClient.get<ServicesResponse>('/provider/services/', { params });
  },

  // Get a specific service
  getService: (serviceId: string) => {
    return apiClient.get<Service>(`/provider/services/${serviceId}/`);
  },

  // Create a new service
  createService: (data: ServiceCreateData) => {
    return apiClient.post<Service>('/provider/services/', data);
  },

  // Update an existing service
  updateService: (serviceId: string, data: ServiceUpdateData) => {
    return apiClient.put<Service>(`/provider/services/${serviceId}/`, data);
  },

  // Partially update a service
  patchService: (serviceId: string, data: Partial<ServiceUpdateData>) => {
    return apiClient.patch<Service>(`/provider/services/${serviceId}/`, data);
  },

  // Delete a service (soft delete)
  deleteService: (serviceId: string) => {
    return apiClient.delete(`/provider/services/${serviceId}/`);
  },

  // Toggle service availability status
  toggleServiceStatus: (serviceId: string) => {
    return apiClient.post<{ is_available: boolean; message: string }>(
      `/provider/services/${serviceId}/toggle_status/`
    );
  },

  // Get service analytics
  getServiceAnalytics: (serviceId: string, params?: {
    period?: 'week' | 'month' | 'quarter' | 'year';
    start_date?: string;
    end_date?: string;
  }) => {
    return apiClient.get(`/provider/services/${serviceId}/analytics/`, { params });
  },

  // Bulk update services
  bulkUpdateServices: (serviceIds: string[], action: 'activate' | 'deactivate' | 'delete') => {
    return apiClient.post<BulkUpdateResponse>('/provider/services/bulk_update/', {
      service_ids: serviceIds,
      action,
    });
  },

  // Get dashboard summary
  getDashboardSummary: () => {
    return apiClient.get<DashboardData>('/provider/services/dashboard_summary/');
  },

  // Get services grouped by category
  getCategoriesSummary: () => {
    return apiClient.get('/provider/services/categories_summary/');
  },

  // Duplicate a service
  duplicateService: (serviceId: string) => {
    return apiClient.post<Service>(`/provider/services/${serviceId}/duplicate/`);
  },

  // Get available service categories
  getCategories: () => {
    return apiClient.get<ServiceCategory[]>('/categories/');
  },

  // Upload service images (if implemented)
  uploadServiceImage: (serviceId: string, imageData: FormData) => {
    return apiClient.post(`/provider/services/${serviceId}/upload_image/`, imageData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get service booking history
  getServiceBookings: (serviceId: string, params?: {
    page?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    return apiClient.get(`/provider/services/${serviceId}/bookings/`, { params });
  },

  // Export services data
  exportServices: (format: 'csv' | 'excel' = 'csv') => {
    return apiClient.get(`/provider/services/export/`, {
      params: { format },
      responseType: 'blob',
    });
  },
};
