# Generated by Django 4.2.16 on 2025-06-19 05:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemHealthMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "health_type",
                    models.CharField(
                        choices=[
                            ("cpu_usage", "CPU Usage Percentage"),
                            ("memory_usage", "Memory Usage Percentage"),
                            ("disk_usage", "Disk Usage Percentage"),
                            ("active_connections", "Active Database Connections"),
                            ("queue_size", "Task Queue Size"),
                            ("cache_memory", "Cache Memory Usage"),
                            ("response_time_avg", "Average Response Time"),
                            ("throughput", "Requests Throughput"),
                        ],
                        max_length=50,
                    ),
                ),
                ("value", models.DecimalField(decimal_places=4, max_digits=10)),
                ("unit", models.CharField(max_length=20)),
                (
                    "server_instance",
                    models.CharField(default="default", max_length=100),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "db_table": "analytics_system_health",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["health_type", "timestamp"],
                        name="analytics_s_health__422b76_idx",
                    ),
                    models.Index(
                        fields=["server_instance", "timestamp"],
                        name="analytics_s_server__439612_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_s_timesta_7e02f0_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RealTimeMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("current_rps", "Current Requests Per Second"),
                            ("active_users_now", "Active Users Right Now"),
                            ("queue_length", "Current Queue Length"),
                            (
                                "avg_response_time_1min",
                                "1-Minute Average Response Time",
                            ),
                            ("error_rate_1min", "1-Minute Error Rate"),
                            ("booking_rate_1min", "1-Minute Booking Rate"),
                            ("cache_hit_rate_1min", "1-Minute Cache Hit Rate"),
                            ("db_connections_active", "Active Database Connections"),
                        ],
                        max_length=50,
                    ),
                ),
                ("value", models.DecimalField(decimal_places=4, max_digits=10)),
                ("unit", models.CharField(max_length=20)),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "db_table": "analytics_realtime_metrics",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["metric_type", "timestamp"],
                        name="analytics_r_metric__3e7b19_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_r_timesta_6140d1_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PerformanceMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "metric_type",
                    models.CharField(
                        choices=[
                            ("api_latency", "API Response Latency"),
                            ("rps", "Requests Per Second"),
                            ("queue_delay", "Queue Processing Delay"),
                            ("db_read_time", "Database Read Time"),
                            ("db_write_time", "Database Write Time"),
                            ("cache_hit_rate", "Cache Hit Rate"),
                            ("error_rate", "Error Rate"),
                            ("booking_completion_time", "Booking Completion Time"),
                            ("search_response_time", "Search Response Time"),
                            ("payment_processing_time", "Payment Processing Time"),
                        ],
                        max_length=50,
                    ),
                ),
                ("value", models.DecimalField(decimal_places=4, max_digits=10)),
                ("unit", models.CharField(max_length=20)),
                ("endpoint", models.CharField(blank=True, max_length=200, null=True)),
                ("method", models.CharField(blank=True, max_length=10, null=True)),
                ("status_code", models.IntegerField(blank=True, null=True)),
                ("user_id", models.UUIDField(blank=True, null=True)),
                ("session_id", models.CharField(blank=True, max_length=100, null=True)),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "db_table": "analytics_performance_metrics",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["metric_type", "timestamp"],
                        name="analytics_p_metric__c5d7c4_idx",
                    ),
                    models.Index(
                        fields=["endpoint", "timestamp"],
                        name="analytics_p_endpoin_3a453f_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_p_timesta_cc4f72_idx"
                    ),
                    models.Index(
                        fields=["user_id", "timestamp"],
                        name="analytics_p_user_id_237de3_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BusinessMetric",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "business_type",
                    models.CharField(
                        choices=[
                            ("booking_conversion_rate", "Booking Conversion Rate"),
                            ("search_to_booking_time", "Search to Booking Time"),
                            ("provider_response_time", "Provider Response Time"),
                            ("customer_satisfaction", "Customer Satisfaction Score"),
                            ("revenue_per_hour", "Revenue Per Hour"),
                            ("active_users", "Active Users Count"),
                            ("booking_cancellation_rate", "Booking Cancellation Rate"),
                            ("provider_utilization", "Provider Utilization Rate"),
                            ("peak_hour_performance", "Peak Hour Performance"),
                            ("geographic_performance", "Geographic Performance"),
                        ],
                        max_length=50,
                    ),
                ),
                ("value", models.DecimalField(decimal_places=4, max_digits=12)),
                ("unit", models.CharField(max_length=20)),
                ("region", models.CharField(blank=True, max_length=100, null=True)),
                ("category", models.CharField(blank=True, max_length=100, null=True)),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "db_table": "analytics_business_metrics",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["business_type", "timestamp"],
                        name="analytics_b_busines_35a873_idx",
                    ),
                    models.Index(
                        fields=["region", "timestamp"],
                        name="analytics_b_region_6b6d2e_idx",
                    ),
                    models.Index(
                        fields=["category", "timestamp"],
                        name="analytics_b_categor_ff7a88_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_b_timesta_4ade96_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PerformanceDashboard",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "dashboard_type",
                    models.CharField(
                        choices=[
                            ("system_overview", "System Overview Dashboard"),
                            ("api_performance", "API Performance Dashboard"),
                            ("business_metrics", "Business Metrics Dashboard"),
                            ("real_time_monitoring", "Real-time Monitoring Dashboard"),
                            ("error_tracking", "Error Tracking Dashboard"),
                        ],
                        max_length=50,
                    ),
                ),
                ("config", models.JSONField(default=dict)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_dashboards",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_performance_dashboards",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["dashboard_type"], name="analytics_p_dashboa_07c83c_idx"
                    ),
                    models.Index(
                        fields=["is_active"], name="analytics_p_is_acti_d644cf_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="analytics_p_created_54eea2_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PerformanceAlert",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("high_latency", "High API Latency"),
                            ("high_error_rate", "High Error Rate"),
                            ("low_cache_hit", "Low Cache Hit Rate"),
                            ("high_queue_delay", "High Queue Delay"),
                            ("system_overload", "System Overload"),
                            ("database_slow", "Database Performance Issue"),
                            ("booking_failure_spike", "Booking Failure Spike"),
                            ("payment_processing_delay", "Payment Processing Delay"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("false_positive", "False Positive"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "threshold_value",
                    models.DecimalField(decimal_places=4, max_digits=10),
                ),
                ("actual_value", models.DecimalField(decimal_places=4, max_digits=10)),
                ("unit", models.CharField(max_length=20)),
                ("message", models.TextField()),
                ("endpoint", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "triggered_at",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("acknowledged_at", models.DateTimeField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "acknowledged_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="acknowledged_alerts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_performance_alerts",
                "ordering": ["-triggered_at"],
                "indexes": [
                    models.Index(
                        fields=["alert_type", "status"],
                        name="analytics_p_alert_t_a9b93a_idx",
                    ),
                    models.Index(
                        fields=["severity", "status"],
                        name="analytics_p_severit_c25c87_idx",
                    ),
                    models.Index(
                        fields=["triggered_at"], name="analytics_p_trigger_e002e9_idx"
                    ),
                    models.Index(
                        fields=["status"], name="analytics_p_status_9305f5_idx"
                    ),
                ],
            },
        ),
    ]
