"""
Comprehensive Authentication Tests for Vierla Application
Following TDD protocol - these tests will initially fail until implementation is complete

Test Coverage:
- User model functionality
- Authentication endpoints
- JWT token management
- Email verification
- Password reset
- Social authentication
- Account security features
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.core import mail
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from unittest.mock import patch, Mock
import json
from datetime import timedelta, datetime
from decimal import Decimal

User = get_user_model()


class UserModelTests(TestCase):
    """Test cases for the custom User model"""

    def setUp(self):
        """Set up test data"""
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'TestPassword123!',
            'phone': '+**********',
            'role': 'customer'
        }

    def test_create_user_with_email(self):
        """Test creating a user with email"""
        user = User.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password']
        )
        self.assertEqual(user.email, self.user_data['email'])
        self.assertTrue(user.check_password(self.user_data['password']))
        self.assertEqual(user.role, 'customer')  # Default role
        self.assertFalse(user.is_verified)  # Default verification status
        self.assertEqual(user.account_status, 'pending_verification')

    def test_create_user_without_email_raises_error(self):
        """Test that creating user without email raises ValueError"""
        with self.assertRaises(ValueError):
            User.objects.create_user(
                email='',
                username='testuser',
                password='password123'
            )

    def test_create_superuser(self):
        """Test creating a superuser"""
        admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.assertTrue(admin_user.is_superuser)
        self.assertTrue(admin_user.is_staff)
        self.assertEqual(admin_user.role, 'admin')

    def test_user_role_choices(self):
        """Test user role choices are properly set"""
        # Test customer role
        customer = User.objects.create_user(
            email='<EMAIL>',
            username='customer',
            password='pass123',
            role='customer'
        )
        self.assertEqual(customer.role, 'customer')

        # Test service provider role
        provider = User.objects.create_user(
            email='<EMAIL>',
            username='provider',
            password='pass123',
            role='service_provider'
        )
        self.assertEqual(provider.role, 'service_provider')

    def test_phone_number_validation(self):
        """Test phone number validation"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='phoneuser',
            password='pass123',
            phone='+**********'
        )
        self.assertEqual(user.phone, '+**********')

    def test_user_full_name_property(self):
        """Test full name property"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='nameuser',
            password='pass123',
            first_name='John',
            last_name='Doe'
        )
        self.assertEqual(user.full_name, 'John Doe')

    def test_verify_email_method(self):
        """Test email verification method"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='verifyuser',
            password='pass123'
        )
        self.assertFalse(user.is_verified)
        self.assertIsNone(user.email_verified_at)

        user.verify_email()
        self.assertTrue(user.is_verified)
        self.assertIsNotNone(user.email_verified_at)
        self.assertEqual(user.account_status, 'active')

    def test_account_lockout_mechanism(self):
        """Test account lockout after failed login attempts"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='lockoutuser',
            password='pass123'
        )

        # Test increment failed login
        for i in range(4):
            user.increment_failed_login()
            self.assertEqual(user.failed_login_attempts, i + 1)
            self.assertIsNone(user.account_locked_until)

        # 5th attempt should lock account
        user.increment_failed_login()
        self.assertEqual(user.failed_login_attempts, 5)
        self.assertIsNotNone(user.account_locked_until)
        self.assertTrue(user.is_account_locked)

    def test_reset_failed_login_attempts(self):
        """Test resetting failed login attempts"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='resetuser',
            password='pass123'
        )

        user.failed_login_attempts = 3
        user.save()

        user.reset_failed_login()
        self.assertEqual(user.failed_login_attempts, 0)


class AuthenticationAPITests(APITestCase):
    """Test cases for authentication API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.register_url = reverse('authentication:register')
        self.login_url = reverse('authentication:login')
        self.logout_url = reverse('authentication:logout')
        self.profile_url = reverse('authentication:profile')
        self.token_refresh_url = reverse('authentication:token_refresh')

        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'password': 'TestPassword123!',
            'password_confirm': 'TestPassword123!',
            'phone': '+**********'
        }

        # Create a test user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='existing',
            password='ExistingPass123!',
            role='customer'
        )
        self.user.verify_email()  # Verify the test user

    def test_user_registration_success(self):
        """Test successful user registration"""
        response = self.client.post(self.register_url, self.user_data, format='json')

        # Debug: print response if it fails
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Registration failed with status {response.status_code}")
            print(f"Response data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('user', response.data)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

        # Verify user was created
        user = User.objects.get(email=self.user_data['email'])
        self.assertEqual(user.email, self.user_data['email'])
        self.assertFalse(user.is_verified)  # Should require email verification

    def test_user_registration_duplicate_email(self):
        """Test registration with duplicate email fails"""
        # Use existing user's email
        self.user_data['email'] = self.user.email

        response = self.client.post(self.register_url, self.user_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)

    def test_user_registration_invalid_password(self):
        """Test registration with weak password fails"""
        self.user_data['password'] = '123'  # Too weak

        response = self.client.post(self.register_url, self.user_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_user_login_success(self):
        """Test successful user login"""
        login_data = {
            'email': self.user.email,
            'password': 'ExistingPass123!'
        }

        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)

        # Verify user data in response
        user_data = response.data['user']
        self.assertEqual(user_data['email'], self.user.email)
        self.assertEqual(user_data['role'], self.user.role)

    def test_user_login_invalid_credentials(self):
        """Test login with invalid credentials fails"""
        login_data = {
            'email': self.user.email,
            'password': 'WrongPassword123!'
        }

        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('detail', response.data)

    def test_user_login_unverified_account(self):
        """Test login with unverified account"""
        unverified_user = User.objects.create_user(
            email='<EMAIL>',
            username='unverified',
            password='UnverifiedPass123!'
        )

        login_data = {
            'email': unverified_user.email,
            'password': 'UnverifiedPass123!'
        }

        response = self.client.post(self.login_url, login_data, format='json')

        # Should still allow login but indicate unverified status
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['user']['is_verified'])

    def test_account_lockout_after_failed_attempts(self):
        """Test account lockout after multiple failed login attempts"""
        login_data = {
            'email': self.user.email,
            'password': 'WrongPassword123!'
        }

        # Make 5 failed login attempts
        for i in range(5):
            response = self.client.post(self.login_url, login_data, format='json')
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # 6th attempt should return account locked error
        response = self.client.post(self.login_url, login_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_423_LOCKED)

    def test_token_refresh_success(self):
        """Test successful token refresh"""
        # First login to get tokens
        login_data = {
            'email': self.user.email,
            'password': 'ExistingPass123!'
        }
        login_response = self.client.post(self.login_url, login_data, format='json')
        refresh_token = login_response.data['refresh']

        # Use refresh token to get new access token
        refresh_data = {'refresh': refresh_token}
        response = self.client.post(self.token_refresh_url, refresh_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)

    def test_token_refresh_invalid_token(self):
        """Test token refresh with invalid token fails"""
        refresh_data = {'refresh': 'invalid_token'}
        response = self.client.post(self.token_refresh_url, refresh_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_protected_endpoint_requires_authentication(self):
        """Test that protected endpoints require authentication"""
        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_protected_endpoint_with_valid_token(self):
        """Test accessing protected endpoint with valid token"""
        # Get access token
        refresh = RefreshToken.for_user(self.user)
        access_token = str(refresh.access_token)

        # Set authorization header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')

        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)


class EmailVerificationTests(APITestCase):
    """Test cases for email verification functionality"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.verify_email_url = reverse('authentication:verify_email')
        self.resend_verification_url = reverse('authentication:resend_verification')

        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='verifyuser',
            password='VerifyPass123!'
        )

    def test_email_verification_token_created_on_registration(self):
        """Test that email verification token is created when user registers"""
        # This will be tested when we implement the EmailVerificationToken model
        pass

    def test_verify_email_with_valid_token(self):
        """Test email verification with valid token"""
        # Create verification token (will be implemented)
        token = 'valid_verification_token'

        response = self.client.post(self.verify_email_url, {'token': token}, format='json')

        # Should succeed when implemented
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_verify_email_with_invalid_token(self):
        """Test email verification with invalid token"""
        response = self.client.post(self.verify_email_url, {'token': 'invalid_token'}, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_verify_email_with_expired_token(self):
        """Test email verification with expired token"""
        # Will be implemented with token expiration logic
        pass

    def test_resend_verification_email(self):
        """Test resending verification email"""
        data = {'email': self.user.email}
        response = self.client.post(self.resend_verification_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)


class PasswordResetTests(APITestCase):
    """Test cases for password reset functionality"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.password_reset_request_url = reverse('authentication:password_reset_request')
        self.password_reset_confirm_url = reverse('authentication:password_reset_confirm')

        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='resetuser',
            password='ResetPass123!'
        )
        self.user.verify_email()

    def test_password_reset_request_valid_email(self):
        """Test password reset request with valid email"""
        data = {'email': self.user.email}
        response = self.client.post(self.password_reset_request_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should send email (will be tested when email backend is configured)

    def test_password_reset_request_invalid_email(self):
        """Test password reset request with invalid email"""
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.password_reset_request_url, data, format='json')

        # Should still return 200 for security (don't reveal if email exists)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_password_reset_confirm_valid_token(self):
        """Test password reset confirmation with valid token"""
        # Create reset token (will be implemented)
        token = 'valid_reset_token'
        new_password = 'NewPassword123!'

        data = {
            'token': token,
            'password': new_password,
            'password_confirm': new_password
        }

        response = self.client.post(self.password_reset_confirm_url, data, format='json')

        # Should succeed when implemented
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_password_reset_confirm_invalid_token(self):
        """Test password reset confirmation with invalid token"""
        data = {
            'token': 'invalid_token',
            'password': 'NewPassword123!',
            'password_confirm': 'NewPassword123!'
        }

        response = self.client.post(self.password_reset_confirm_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_password_reset_confirm_mismatched_passwords(self):
        """Test password reset with mismatched password confirmation"""
        data = {
            'token': 'valid_token',
            'password': 'NewPassword123!',
            'password_confirm': 'DifferentPassword123!'
        }

        response = self.client.post(self.password_reset_confirm_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class SocialAuthenticationTests(APITestCase):
    """Test cases for social authentication (Google, Apple)"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.social_auth_url = reverse('authentication:social_auth')

    @patch('authentication.views.verify_google_token')
    def test_google_authentication_success(self, mock_verify):
        """Test successful Google authentication"""
        # Mock Google token verification
        mock_verify.return_value = {
            'email': '<EMAIL>',
            'first_name': 'Google',
            'last_name': 'User',
            'user_id': 'google_user_id'
        }

        data = {
            'provider': 'google',
            'identity_token': 'valid_google_token',
            'email': '<EMAIL>',
            'first_name': 'Google',
            'last_name': 'User'
        }

        response = self.client.post(self.social_auth_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)

    @patch('authentication.views.verify_apple_token')
    def test_apple_authentication_success(self, mock_verify):
        """Test successful Apple authentication"""
        # Mock Apple token verification
        mock_verify.return_value = {
            'email': '<EMAIL>',
            'first_name': 'Apple',
            'last_name': 'User',
            'user_id': 'apple_user_id'
        }

        data = {
            'provider': 'apple',
            'identity_token': 'valid_apple_token',
            'email': '<EMAIL>',
            'first_name': 'Apple',
            'last_name': 'User'
        }

        response = self.client.post(self.social_auth_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
        self.assertIn('user', response.data)

    def test_social_authentication_invalid_provider(self):
        """Test social authentication with invalid provider"""
        data = {
            'provider': 'invalid_provider',
            'identity_token': 'some_token'
        }

        response = self.client.post(self.social_auth_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_social_authentication_invalid_token(self):
        """Test social authentication with invalid token"""
        data = {
            'provider': 'google',
            'identity_token': 'invalid_token'
        }

        response = self.client.post(self.social_auth_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class ProfileManagementTests(APITestCase):
    """Test cases for user profile management"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.profile_url = reverse('authentication:profile')
        self.profile_update_url = reverse('authentication:profile_update')

        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='profileuser',
            password='ProfilePass123!',
            first_name='Profile',
            last_name='User'
        )
        self.user.verify_email()

        # Authenticate user
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

    def test_get_user_profile(self):
        """Test retrieving user profile"""
        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], self.user.email)
        self.assertEqual(response.data['first_name'], self.user.first_name)
        self.assertEqual(response.data['last_name'], self.user.last_name)

    def test_update_user_profile(self):
        """Test updating user profile"""
        update_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'bio': 'Updated bio',
            'phone': '+1987654321'
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify changes were saved
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, 'Updated')
        self.assertEqual(self.user.last_name, 'Name')
        self.assertEqual(self.user.bio, 'Updated bio')
        self.assertEqual(self.user.phone, '+1987654321')

    def test_update_profile_invalid_phone(self):
        """Test updating profile with invalid phone number"""
        update_data = {
            'phone': 'invalid_phone'
        }

        response = self.client.patch(self.profile_update_url, update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('phone', response.data)

    def test_change_password(self):
        """Test changing user password"""
        change_password_url = reverse('authentication:change_password')

        data = {
            'old_password': 'ProfilePass123!',
            'new_password': 'NewProfilePass123!',
            'new_password_confirm': 'NewProfilePass123!'
        }

        response = self.client.post(change_password_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewProfilePass123!'))

    def test_change_password_wrong_old_password(self):
        """Test changing password with wrong old password"""
        change_password_url = reverse('authentication:change_password')

        data = {
            'old_password': 'WrongPassword123!',
            'new_password': 'NewProfilePass123!',
            'new_password_confirm': 'NewProfilePass123!'
        }

        response = self.client.post(change_password_url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_upload_avatar(self):
        """Test uploading user avatar"""
        # This test will be implemented when file upload is configured
        pass


class TokenModelTests(TestCase):
    """Test cases for token models (EmailVerificationToken, PasswordResetToken)"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='tokenuser',
            password='TokenPass123!'
        )

    def test_email_verification_token_creation(self):
        """Test creating email verification token"""
        # Will be implemented when EmailVerificationToken model is created
        pass

    def test_email_verification_token_expiration(self):
        """Test email verification token expiration"""
        # Will be implemented when EmailVerificationToken model is created
        pass

    def test_password_reset_token_creation(self):
        """Test creating password reset token"""
        # Will be implemented when PasswordResetToken model is created
        pass

    def test_password_reset_token_expiration(self):
        """Test password reset token expiration"""
        # Will be implemented when PasswordResetToken model is created
        pass


class SecurityTests(APITestCase):
    """Test cases for security features"""

    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.login_url = reverse('authentication:login')

        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='securityuser',
            password='SecurityPass123!'
        )
        self.user.verify_email()

    def test_rate_limiting_on_login_endpoint(self):
        """Test rate limiting on login endpoint"""
        # This will be implemented when rate limiting is configured
        pass

    def test_jwt_token_contains_user_info(self):
        """Test that JWT token contains necessary user information"""
        login_data = {
            'email': self.user.email,
            'password': 'SecurityPass123!'
        }

        response = self.client.post(self.login_url, login_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Decode token and verify claims (will be implemented)
        access_token = response.data['access']
        # TODO: Decode JWT and verify it contains role, is_verified, etc.

    def test_device_token_storage(self):
        """Test storing device token for push notifications"""
        # Will be implemented when device token functionality is added
        pass

    def test_last_activity_tracking(self):
        """Test that user last activity is tracked"""
        # Will be implemented when activity tracking is added
        pass
