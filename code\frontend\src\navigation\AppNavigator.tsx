/**
 * App Navigator
 * Main navigation container that handles auth state and routing
 */

import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';
import { OnboardingNavigator } from './OnboardingNavigator';
import { LoadingScreen } from '../screens/LoadingScreen';
import { AuthProvider } from '../contexts/AuthContext';
import { NavigationProvider } from '../contexts/NavigationContext';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Onboarding: undefined;
  Loading: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

export const AppNavigator: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [onboardingCompleted, setOnboardingCompleted] = useState(false);

  useEffect(() => {
    checkAppStatus();
  }, []);

  const checkAppStatus = async () => {
    try {
      // Check authentication status
      const accessToken = await AsyncStorage.getItem('access_token');
      const user = await AsyncStorage.getItem('user');

      // Check onboarding completion
      const onboardingStatus = await AsyncStorage.getItem('onboarding_completed');

      setIsAuthenticated(!!(accessToken && user));
      setOnboardingCompleted(onboardingStatus === 'true');
    } catch (error) {
      console.error('Error checking app status:', error);
      setIsAuthenticated(false);
      setOnboardingCompleted(false);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationProvider onStateRefresh={checkAppStatus}>
          <NavigationContainer>
            <Stack.Navigator
              screenOptions={{
                headerShown: false,
                cardStyle: { backgroundColor: '#FFFFFF' },
              }}
            >
              {isAuthenticated ? (
                <Stack.Screen name="Main" component={MainNavigator} />
              ) : onboardingCompleted ? (
                <Stack.Screen name="Auth" component={AuthNavigator} />
              ) : (
                <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
              )}
            </Stack.Navigator>
          </NavigationContainer>
        </NavigationProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};
