/**
 * Progress Indicator Component
 * Shows progress through multi-step form with step navigation
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { colors, typography, spacing } from '../../theme';

interface Step {
  id: string;
  title: string;
  description: string;
}

interface ProgressIndicatorProps {
  steps: Step[];
  currentStep: number;
  onStepPress?: (stepIndex: number) => void;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
  onStepPress,
}) => {
  const renderStep = (step: Step, index: number) => {
    const isActive = index === currentStep;
    const isCompleted = index < currentStep;
    const isClickable = onStepPress && index < currentStep;

    const stepContent = (
      <View style={styles.stepContainer}>
        <View style={styles.stepHeader}>
          <View style={[
            styles.stepCircle,
            isCompleted && styles.stepCircleCompleted,
            isActive && styles.stepCircleActive,
          ]}>
            {isCompleted ? (
              <Icon name="check" size={16} color={colors.white} />
            ) : (
              <Text style={[
                styles.stepNumber,
                isActive && styles.stepNumberActive,
              ]}>
                {index + 1}
              </Text>
            )}
          </View>
          
          {index < steps.length - 1 && (
            <View style={[
              styles.stepConnector,
              isCompleted && styles.stepConnectorCompleted,
            ]} />
          )}
        </View>
        
        <View style={styles.stepContent}>
          <Text style={[
            styles.stepTitle,
            isActive && styles.stepTitleActive,
            isCompleted && styles.stepTitleCompleted,
          ]}>
            {step.title}
          </Text>
          
          <Text style={[
            styles.stepDescription,
            isActive && styles.stepDescriptionActive,
          ]}>
            {step.description}
          </Text>
        </View>
      </View>
    );

    if (isClickable) {
      return (
        <TouchableOpacity
          key={step.id}
          style={styles.stepTouchable}
          onPress={() => onStepPress(index)}
          activeOpacity={0.7}
        >
          {stepContent}
        </TouchableOpacity>
      );
    }

    return (
      <View key={step.id} style={styles.stepTouchable}>
        {stepContent}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill,
            { width: `${(currentStep / (steps.length - 1)) * 100}%` }
          ]} 
        />
      </View>
      
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => renderStep(step, index))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 2,
    marginBottom: spacing.lg,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  stepTouchable: {
    flex: 1,
  },
  stepContainer: {
    alignItems: 'center',
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.backgroundSecondary,
    borderWidth: 2,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  stepCircleActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  stepCircleCompleted: {
    backgroundColor: colors.success,
    borderColor: colors.success,
  },
  stepNumber: {
    ...typography.caption,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  stepNumberActive: {
    color: colors.white,
  },
  stepConnector: {
    position: 'absolute',
    left: '50%',
    right: '-50%',
    height: 2,
    backgroundColor: colors.border,
    zIndex: 1,
  },
  stepConnectorCompleted: {
    backgroundColor: colors.success,
  },
  stepContent: {
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
  },
  stepTitle: {
    ...typography.caption,
    color: colors.textSecondary,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  stepTitleActive: {
    color: colors.primary,
  },
  stepTitleCompleted: {
    color: colors.success,
  },
  stepDescription: {
    ...typography.caption,
    fontSize: 10,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 12,
  },
  stepDescriptionActive: {
    color: colors.primary,
  },
});
