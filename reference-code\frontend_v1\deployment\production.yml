# Vierla Frontend v2 - Production Deployment Configuration
# This configuration deploys the enhanced Vierla frontend application to production

apiVersion: v1
kind: ConfigMap
metadata:
  name: vierla-frontend-config
  namespace: vierla-production
data:
  EXPO_PUBLIC_API_URL: "https://api.vierla.com"
  EXPO_PUBLIC_WS_URL: "wss://api.vierla.com"
  EXPO_PUBLIC_ENVIRONMENT: "production"
  EXPO_PUBLIC_APP_VERSION: "2.0.0"
  EXPO_PUBLIC_SENTRY_DSN: "https://<EMAIL>/project"
  EXPO_PUBLIC_ANALYTICS_ID: "GA-XXXXXXXXX"
  EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: "pk_live_xxxxxxxxx"
  EXPO_PUBLIC_GOOGLE_MAPS_API_KEY: "your-google-maps-api-key"
  EXPO_PUBLIC_FIREBASE_CONFIG: |
    {
      "apiKey": "your-firebase-api-key",
      "authDomain": "vierla-prod.firebaseapp.com",
      "projectId": "vierla-prod",
      "storageBucket": "vierla-prod.appspot.com",
      "messagingSenderId": "123456789",
      "appId": "1:123456789:web:abcdef123456"
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vierla-frontend
  namespace: vierla-production
  labels:
    app: vierla-frontend
    version: v2.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: vierla-frontend
  template:
    metadata:
      labels:
        app: vierla-frontend
        version: v2.0.0
    spec:
      containers:
      - name: vierla-frontend
        image: vierla/frontend:v2.0.0
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        envFrom:
        - configMapRef:
            name: vierla-frontend-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /app/config
          readOnly: true
        - name: ssl-certs
          mountPath: /etc/ssl/certs
          readOnly: true
      volumes:
      - name: app-config
        configMap:
          name: vierla-frontend-config
      - name: ssl-certs
        secret:
          secretName: vierla-ssl-certs
      imagePullSecrets:
      - name: vierla-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: vierla-frontend-service
  namespace: vierla-production
  labels:
    app: vierla-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: vierla-frontend

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: vierla-frontend-ingress
  namespace: vierla-production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://vierla.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
spec:
  tls:
  - hosts:
    - app.vierla.com
    secretName: vierla-frontend-tls
  rules:
  - host: app.vierla.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: vierla-frontend-service
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: vierla-frontend-hpa
  namespace: vierla-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: vierla-frontend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: vierla-frontend-pdb
  namespace: vierla-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: vierla-frontend

---
apiVersion: v1
kind: Secret
metadata:
  name: vierla-registry-secret
  namespace: vierla-production
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: # Base64 encoded Docker registry credentials

---
apiVersion: v1
kind: Secret
metadata:
  name: vierla-ssl-certs
  namespace: vierla-production
type: kubernetes.io/tls
data:
  tls.crt: # Base64 encoded SSL certificate
  tls.key: # Base64 encoded SSL private key

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: vierla-frontend-monitor
  namespace: vierla-production
  labels:
    app: vierla-frontend
spec:
  selector:
    matchLabels:
      app: vierla-frontend
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: vierla-frontend-network-policy
  namespace: vierla-production
spec:
  podSelector:
    matchLabels:
      app: vierla-frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: nginx-ingress
    ports:
    - protocol: TCP
      port: 3000
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: vierla-backend
    ports:
    - protocol: TCP
      port: 8000
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
    - protocol: UDP
      port: 53

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vierla-frontend-sa
  namespace: vierla-production
automountServiceAccountToken: false

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: vierla-frontend-role
  namespace: vierla-production
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: vierla-frontend-rolebinding
  namespace: vierla-production
subjects:
- kind: ServiceAccount
  name: vierla-frontend-sa
  namespace: vierla-production
roleRef:
  kind: Role
  name: vierla-frontend-role
  apiGroup: rbac.authorization.k8s.io
