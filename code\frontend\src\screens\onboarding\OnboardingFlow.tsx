/**
 * OnboardingFlow Component
 * Centralized state management for the complete onboarding process
 * Provides unified flow control, step progression, and completion handling
 */

import React, { useState, useCallback, useEffect } from 'react';
import { View, StyleSheet, BackHandler } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import WelcomeScreen from './WelcomeScreen';
import InitializationScreen from './InitializationScreen';
import RoleSelectionScreen from './RoleSelectionScreen';
import CustomerOnboardingCarousel from './CustomerOnboardingCarousel';
import ProviderOnboardingCarousel from './ProviderOnboardingCarousel';
import { formatErrorForLogging } from '../../utils/errorHandler';

// Types
export type OnboardingStep = 
  | 'welcome' 
  | 'initialization' 
  | 'role-selection' 
  | 'customer-onboarding' 
  | 'provider-onboarding' 
  | 'complete';

export type UserRole = 'customer' | 'service_provider';

interface OnboardingFlowProps {
  onComplete: (role: UserRole) => void;
  onSignIn: () => void;
}

/**
 * OnboardingFlow Component
 * Manages the complete onboarding process with centralized state
 */
const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  onComplete,
  onSignIn,
}) => {
  const navigation = useNavigation();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Handle back button on Android
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [currentStep]);

  /**
   * Handle hardware back button press
   */
  const handleBackPress = useCallback((): boolean => {
    handleGoBack();
    return true; // Prevent default back behavior
  }, [currentStep]);

  /**
   * Navigate to the next step in the onboarding flow
   */
  const handleNextStep = useCallback((step: OnboardingStep) => {
    setCurrentStep(step);
  }, []);

  /**
   * Handle back navigation between steps
   */
  const handleGoBack = useCallback(() => {
    switch (currentStep) {
      case 'welcome':
        // Can't go back from welcome, exit onboarding
        navigation.goBack();
        break;
      case 'initialization':
        setCurrentStep('welcome');
        break;
      case 'role-selection':
        setCurrentStep('initialization');
        break;
      case 'customer-onboarding':
      case 'provider-onboarding':
        setCurrentStep('role-selection');
        setSelectedRole(null); // Reset role selection
        break;
      case 'complete':
        // Shouldn't be able to go back from complete
        break;
      default:
        setCurrentStep('welcome');
    }
  }, [currentStep, navigation]);

  /**
   * Handle getting started from welcome screen
   */
  const handleGetStarted = useCallback(() => {
    setCurrentStep('initialization');
  }, []);

  /**
   * Handle initialization completion
   */
  const handleInitializationComplete = useCallback(() => {
    setCurrentStep('role-selection');
  }, []);

  /**
   * Handle role selection
   */
  const handleRoleSelected = useCallback((role: UserRole) => {
    setSelectedRole(role);
    
    if (role === 'customer') {
      setCurrentStep('customer-onboarding');
    } else {
      setCurrentStep('provider-onboarding');
    }
  }, []);

  /**
   * Handle onboarding completion
   */
  const handleOnboardingComplete = useCallback(async () => {
    if (!selectedRole) {
      console.error('No role selected during onboarding completion');
      return;
    }

    setIsLoading(true);

    try {
      // Store onboarding completion status
      await AsyncStorage.setItem('onboarding_completed', 'true');
      await AsyncStorage.setItem('user_role', selectedRole);
      
      // Mark as complete
      setCurrentStep('complete');
      
      // Call completion callback
      onComplete(selectedRole);
    } catch (error: any) {
      console.error(formatErrorForLogging(error, 'ONBOARDING_COMPLETION'));
      // Handle error - could show error message or retry
      // For now, just log and continue
    } finally {
      setIsLoading(false);
    }
  }, [selectedRole, onComplete]);

  /**
   * Handle sign in navigation
   */
  const handleSignIn = useCallback(() => {
    onSignIn();
  }, [onSignIn]);

  /**
   * Render the current step component
   */
  const renderCurrentStep = () => {
    const commonProps = {
      onBack: currentStep !== 'welcome' ? handleGoBack : undefined,
      isLoading,
    };

    switch (currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            onGetStarted={handleGetStarted}
            onSignIn={handleSignIn}
            {...commonProps}
          />
        );

      case 'initialization':
        return (
          <InitializationScreen
            onComplete={handleInitializationComplete}
            {...commonProps}
          />
        );

      case 'role-selection':
        return (
          <RoleSelectionScreen
            onRoleSelected={handleRoleSelected}
            selectedRole={selectedRole}
            {...commonProps}
          />
        );

      case 'customer-onboarding':
        return (
          <CustomerOnboardingCarousel
            onComplete={handleOnboardingComplete}
            {...commonProps}
          />
        );

      case 'provider-onboarding':
        return (
          <ProviderOnboardingCarousel
            onComplete={handleOnboardingComplete}
            {...commonProps}
          />
        );

      case 'complete':
        // This state is handled by the parent component
        return null;

      default:
        // Fallback to welcome screen for unknown states
        console.warn(`Unknown onboarding step: ${currentStep}`);
        return (
          <WelcomeScreen
            onGetStarted={handleGetStarted}
            onSignIn={handleSignIn}
            {...commonProps}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      {renderCurrentStep()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});

export default OnboardingFlow;
