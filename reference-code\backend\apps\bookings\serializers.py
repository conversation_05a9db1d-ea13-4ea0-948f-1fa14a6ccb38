"""
Booking serializers for Vierla Beauty Services Marketplace
Comprehensive serializers with mobile-first design and validation
"""
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone as django_timezone
from decimal import Decimal
from datetime import timed<PERSON><PERSON>

from .models import Booking, BookingStateChange, TimeSlot, BookingNotification
from apps.catalog.models import ServiceProvider, Service
from apps.catalog.serializers import ServiceListSerializer, ServiceProviderListSerializer

User = get_user_model()


class BookingStateChangeSerializer(serializers.ModelSerializer):
    """Serializer for booking state changes (audit trail)"""
    
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)
    
    class Meta:
        model = BookingStateChange
        fields = [
            'id', 'from_status', 'to_status', 'changed_by', 'changed_by_name',
            'notes', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class BookingListSerializer(serializers.ModelSerializer):
    """Lightweight serializer for booking lists (mobile optimized)"""
    
    customer_name = serializers.CharField(source='customer.get_full_name', read_only=True)
    provider_name = serializers.CharField(source='provider.business_name', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    service_mobile_description = serializers.CharField(source='service.mobile_description', read_only=True)
    
    # Computed fields for mobile display
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    location_type_display = serializers.CharField(source='get_location_type_display', read_only=True)
    duration_display = serializers.CharField(source='get_duration_display', read_only=True)
    
    # Booking state checks
    can_be_cancelled = serializers.SerializerMethodField()
    can_be_confirmed = serializers.SerializerMethodField()
    can_be_started = serializers.SerializerMethodField()
    can_be_completed = serializers.SerializerMethodField()
    
    # Time-related fields
    is_upcoming = serializers.SerializerMethodField()
    is_today = serializers.SerializerMethodField()
    cancellation_deadline = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = [
            'id', 'booking_number', 'customer_name', 'provider_name', 
            'service_name', 'service_mobile_description', 'status', 'status_display',
            'payment_status', 'payment_status_display', 'location_type', 'location_type_display',
            'scheduled_datetime', 'duration_minutes', 'duration_display', 'total_amount',
            'can_be_cancelled', 'can_be_confirmed', 'can_be_started', 'can_be_completed',
            'is_upcoming', 'is_today', 'cancellation_deadline', 'created_at'
        ]
        read_only_fields = ['id', 'booking_number', 'created_at']
    
    def get_can_be_cancelled(self, obj):
        return obj.can_be_cancelled()
    
    def get_can_be_confirmed(self, obj):
        return obj.can_be_confirmed()
    
    def get_can_be_started(self, obj):
        return obj.can_be_started()
    
    def get_can_be_completed(self, obj):
        return obj.can_be_completed()
    
    def get_is_upcoming(self, obj):
        return obj.is_upcoming()
    
    def get_is_today(self, obj):
        return obj.is_today()
    
    def get_cancellation_deadline(self, obj):
        return obj.get_cancellation_deadline()


class BookingDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for individual booking views"""
    
    # Related object details
    customer = serializers.StringRelatedField(read_only=True)
    provider = ServiceProviderListSerializer(read_only=True)
    service = ServiceListSerializer(read_only=True)
    cancelled_by = serializers.StringRelatedField(read_only=True)
    
    # State changes (audit trail)
    state_changes = BookingStateChangeSerializer(many=True, read_only=True)
    
    # Computed fields
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    location_type_display = serializers.CharField(source='get_location_type_display', read_only=True)
    duration_display = serializers.CharField(source='get_duration_display', read_only=True)
    
    # Booking state checks
    can_be_cancelled = serializers.SerializerMethodField()
    can_be_confirmed = serializers.SerializerMethodField()
    can_be_started = serializers.SerializerMethodField()
    can_be_completed = serializers.SerializerMethodField()
    can_be_rescheduled = serializers.SerializerMethodField()
    
    # Time-related fields
    is_upcoming = serializers.SerializerMethodField()
    is_today = serializers.SerializerMethodField()
    time_until_booking = serializers.SerializerMethodField()
    cancellation_deadline = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = [
            'id', 'booking_number', 'customer', 'provider', 'service',
            'status', 'status_display', 'payment_status', 'payment_status_display',
            'scheduled_datetime', 'duration_minutes', 'duration_display', 'end_datetime',
            'location_type', 'location_type_display', 'service_address',
            'service_latitude', 'service_longitude',
            'base_price', 'additional_charges', 'discount_amount', 'tax_amount', 'total_amount',
            'customer_notes', 'provider_notes', 'internal_notes',
            'confirmed_at', 'started_at', 'completed_at', 'cancelled_at',
            'cancelled_by', 'cancellation_reason',
            'can_be_cancelled', 'can_be_confirmed', 'can_be_started', 
            'can_be_completed', 'can_be_rescheduled',
            'is_upcoming', 'is_today', 'time_until_booking', 'cancellation_deadline',
            'state_changes', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'booking_number', 'end_datetime', 'confirmed_at', 'started_at',
            'completed_at', 'cancelled_at', 'cancelled_by', 'created_at', 'updated_at'
        ]
    
    def get_can_be_cancelled(self, obj):
        return obj.can_be_cancelled()
    
    def get_can_be_confirmed(self, obj):
        return obj.can_be_confirmed()
    
    def get_can_be_started(self, obj):
        return obj.can_be_started()
    
    def get_can_be_completed(self, obj):
        return obj.can_be_completed()
    
    def get_can_be_rescheduled(self, obj):
        return obj.can_be_rescheduled()
    
    def get_is_upcoming(self, obj):
        return obj.is_upcoming()
    
    def get_is_today(self, obj):
        return obj.is_today()
    
    def get_time_until_booking(self, obj):
        time_delta = obj.get_time_until_booking()
        if time_delta:
            total_seconds = int(time_delta.total_seconds())
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            return {'hours': hours, 'minutes': minutes, 'total_seconds': total_seconds}
        return None
    
    def get_cancellation_deadline(self, obj):
        return obj.get_cancellation_deadline()


class BookingCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new bookings"""
    
    # Required fields for booking creation
    provider_id = serializers.UUIDField(write_only=True)
    service_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = Booking
        fields = [
            'provider_id', 'service_id', 'scheduled_datetime', 'location_type',
            'service_address', 'service_latitude', 'service_longitude',
            'customer_notes'
        ]
    
    def validate_scheduled_datetime(self, value):
        """Validate that booking is not in the past"""
        if value <= django_timezone.now():
            raise serializers.ValidationError("Booking cannot be scheduled in the past")
        
        # Check if it's too far in the future (e.g., 6 months)
        max_future = django_timezone.now() + timedelta(days=180)
        if value > max_future:
            raise serializers.ValidationError("Booking cannot be scheduled more than 6 months in advance")
        
        return value
    
    def validate(self, data):
        """Cross-field validation"""
        provider_id = data.get('provider_id')
        service_id = data.get('service_id')
        scheduled_datetime = data.get('scheduled_datetime')
        location_type = data.get('location_type')
        
        # Validate provider exists
        try:
            provider = ServiceProvider.objects.get(id=provider_id)
        except ServiceProvider.DoesNotExist:
            raise serializers.ValidationError("Provider not found")
        
        # Validate service exists and belongs to provider
        try:
            service = Service.objects.get(id=service_id, provider=provider)
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found or does not belong to this provider")
        
        # Validate service is available
        if not service.is_available or not service.is_active:
            raise serializers.ValidationError("Service is not available for booking")
        
        # Validate location type for mobile services
        if location_type == Booking.LocationType.MOBILE:
            if not data.get('service_address'):
                raise serializers.ValidationError("Service address is required for mobile services")
        
        # Store validated objects for use in create method
        data['provider'] = provider
        data['service'] = service
        
        return data
    
    def create(self, validated_data):
        """Create booking with proper initialization"""
        # Remove write-only fields
        provider = validated_data.pop('provider')
        service = validated_data.pop('service')
        validated_data.pop('provider_id')
        validated_data.pop('service_id')
        
        # Set customer from request
        customer = self.context['request'].user
        
        # Create booking
        booking = Booking.objects.create(
            customer=customer,
            provider=provider,
            service=service,
            duration_minutes=service.duration,
            base_price=service.base_price,
            total_amount=service.base_price,  # Will be recalculated in save()
            **validated_data
        )
        
        return booking


class TimeSlotSerializer(serializers.ModelSerializer):
    """Serializer for time slots"""
    
    provider_name = serializers.CharField(source='provider.business_name', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    
    # Availability checks
    is_fully_booked = serializers.SerializerMethodField()
    can_be_booked = serializers.SerializerMethodField()
    available_spots = serializers.SerializerMethodField()
    
    class Meta:
        model = TimeSlot
        fields = [
            'id', 'provider', 'provider_name', 'service', 'service_name',
            'date', 'start_time', 'end_time', 'duration_minutes',
            'is_available', 'max_bookings', 'current_bookings',
            'override_price', 'is_break', 'is_recurring',
            'is_fully_booked', 'can_be_booked', 'available_spots',
            'notes', 'created_at'
        ]
        read_only_fields = ['id', 'duration_minutes', 'created_at']
    
    def get_is_fully_booked(self, obj):
        return obj.is_fully_booked()
    
    def get_can_be_booked(self, obj):
        return obj.can_be_booked()
    
    def get_available_spots(self, obj):
        return obj.get_available_spots()


class BookingNotificationSerializer(serializers.ModelSerializer):
    """Serializer for booking notifications"""
    
    booking_number = serializers.CharField(source='booking.booking_number', read_only=True)
    recipient_name = serializers.CharField(source='recipient.get_full_name', read_only=True)
    
    # Display fields
    notification_type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    channel_display = serializers.CharField(source='get_channel_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = BookingNotification
        fields = [
            'id', 'booking', 'booking_number', 'recipient', 'recipient_name',
            'notification_type', 'notification_type_display',
            'channel', 'channel_display', 'status', 'status_display',
            'title', 'message', 'scheduled_for', 'sent_at', 'read_at',
            'external_id', 'error_message', 'created_at'
        ]
        read_only_fields = ['id', 'sent_at', 'read_at', 'external_id', 'created_at']
