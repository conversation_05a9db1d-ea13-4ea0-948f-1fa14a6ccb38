"""
Django management command to create test accounts for Vierla backend development
Creates service provider and customer test accounts with proper roles and verification
"""
import os
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from catalog.models import ServiceProvider, ServiceCategory, Service
from catalog.security import TestAccountSecurity, require_development_environment

User = get_user_model()


class Command(BaseCommand):
    help = 'Create test accounts for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing test accounts',
        )
        parser.add_argument(
            '--quick',
            action='store_true',
            help='Create only basic test accounts (faster)',
        )
        parser.add_argument(
            '--customers-only',
            action='store_true',
            help='Create only customer test accounts',
        )
        parser.add_argument(
            '--providers-only',
            action='store_true',
            help='Create only provider test accounts',
        )

    def handle(self, *args, **options):
        # Security check - only allow in development/testing
        TestAccountSecurity.validate_test_account_operation()

        self.force = options['force']
        self.quick = options['quick']
        self.customers_only = options['customers_only']
        self.providers_only = options['providers_only']

        self.stdout.write(
            self.style.SUCCESS('🚀 Creating test accounts for Vierla backend...')
        )

        if self.quick:
            self.stdout.write(
                self.style.WARNING('⚡ Quick mode: Creating basic test accounts only')
            )

        try:
            with transaction.atomic():
                created_accounts = self.create_test_accounts()
                self.print_summary(created_accounts)
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to create test accounts: {e}')
            )
            raise CommandError(f'Test account creation failed: {e}')

    def is_development_environment(self):
        """Check if we're in a development environment"""
        environment = getattr(settings, 'ENVIRONMENT', 'development')
        debug_mode = getattr(settings, 'DEBUG', False)
        
        # Allow in development, testing, or when DEBUG is True
        return environment in ['development', 'testing'] or debug_mode

    def create_test_accounts(self):
        """Create all test accounts"""
        created_accounts = {
            'admins': [],
            'customers': [],
            'providers': [],
            'services': []
        }

        # Create service categories first
        self.ensure_service_categories()

        # Create admin accounts
        if not self.providers_only and not self.customers_only:
            created_accounts['admins'] = self.create_admin_accounts()

        # Create customer accounts
        if not self.providers_only:
            created_accounts['customers'] = self.create_customer_accounts()

        # Create provider accounts
        if not self.customers_only:
            provider_accounts = self.create_provider_accounts()
            created_accounts['providers'] = provider_accounts

            # Create sample services for providers
            if provider_accounts and not self.quick:
                created_accounts['services'] = self.create_sample_services(provider_accounts)

        return created_accounts

    def ensure_service_categories(self):
        """Ensure basic service categories exist"""
        categories = [
            {
                'name': 'Hair & Beauty',
                'slug': 'hair-beauty',
                'description': 'Hair styling and beauty services',
                'is_active': True,
                'is_popular': True,
                'sort_order': 1
            },
            {
                'name': 'Wellness & Spa',
                'slug': 'wellness-spa',
                'description': 'Wellness and spa treatments',
                'is_active': True,
                'is_popular': True,
                'sort_order': 2
            },
            {
                'name': 'Fitness & Training',
                'slug': 'fitness-training',
                'description': 'Personal training and fitness services',
                'is_active': True,
                'is_popular': False,
                'sort_order': 3
            }
        ]

        for category_data in categories:
            category, created = ServiceCategory.objects.get_or_create(
                slug=category_data['slug'],
                defaults=category_data
            )
            if created:
                self.stdout.write(f'✅ Created category: {category.name}')

    def create_admin_accounts(self):
        """Create administrative test accounts according to specification"""
        self.stdout.write('\n👑 Creating admin test accounts...')

        admin_data = [
            {
                'email': '<EMAIL>',
                'first_name': 'System',
                'last_name': 'Administrator',
                'is_staff': True,
                'is_superuser': True,
                'password': 'VierlaAdmin123!'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Customer',
                'last_name': 'Support',
                'is_staff': True,
                'is_superuser': False,
                'password': 'VierlaSupport123!'
            }
        ]

        created_admins = []
        for data in admin_data:
            admin = self.create_admin_account(data)
            if admin:
                created_admins.append(admin)

        return created_admins

    def create_admin_account(self, data):
        """Create a single admin account"""
        email = data['email']

        if User.objects.filter(email=email).exists():
            if self.force:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'🗑️  Deleted existing admin: {email}')
            else:
                self.stdout.write(f'📋 Admin already exists: {email}')
                return None

        # Create admin user
        admin = User.objects.create_user(
            email=email,
            username=email,
            password=data['password'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            role='admin',
            is_active=True,
            is_verified=True,
            is_staff=data['is_staff'],
            is_superuser=data['is_superuser'],
            email_verified_at=timezone.now(),
            account_status='active',
            is_test_account=True
        )

        self.stdout.write(f'✅ Created admin: {admin.get_full_name()} ({email})')
        return admin

    def get_city_coordinates(self, city):
        """Get latitude and longitude for Canadian cities"""
        coordinates = {
            'Ottawa': (Decimal('45.4215'), Decimal('-75.6972')),
            'Toronto': (Decimal('43.6532'), Decimal('-79.3832')),
            'Vancouver': (Decimal('49.2827'), Decimal('-123.1207')),
            'Calgary': (Decimal('51.0447'), Decimal('-114.0719')),
            'Montreal': (Decimal('45.5017'), Decimal('-73.5673')),
        }
        return coordinates.get(city, coordinates['Ottawa'])

    def create_customer_accounts(self):
        """Create customer test accounts according to specification"""
        self.stdout.write('\n👤 Creating customer test accounts...')

        customer_data = [
            {
                'email': '<EMAIL>',
                'first_name': 'Emma',
                'last_name': 'Thompson',
                'phone': '+****************',
                'city': 'Ottawa',
                'state': 'Ontario',
                'country': 'Canada',
                'profile': 'Marketing Manager, High budget, Regular user'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'phone': '+****************',
                'city': 'Toronto',
                'state': 'Ontario',
                'country': 'Canada',
                'profile': 'Premium customer, frequent bookings'
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Michael',
                'last_name': 'Chen',
                'phone': '+****************',
                'city': 'Vancouver',
                'state': 'British Columbia',
                'country': 'Canada',
                'profile': 'Regular customer, occasional bookings'
            }
        ]

        if not self.quick:
            # Add more customers for comprehensive testing
            customer_data.extend([
                {
                    'email': '<EMAIL>',
                    'first_name': 'Priya',
                    'last_name': 'Patel',
                    'phone': '+****************',
                    'city': 'Calgary',
                    'state': 'Alberta',
                    'country': 'Canada',
                    'profile': 'New customer, first-time user'
                }
            ])

        created_customers = []
        for data in customer_data:
            customer = self.create_customer_account(data)
            if customer:
                created_customers.append(customer)

        return created_customers

    def create_customer_account(self, data):
        """Create a single customer account"""
        email = data['email']

        if User.objects.filter(email=email).exists():
            if self.force:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'🗑️  Deleted existing customer: {email}')
            else:
                self.stdout.write(f'📋 Customer already exists: {email}')
                return None

        # Create customer user
        customer = User.objects.create_user(
            email=email,
            username=email,  # Use email as username to avoid conflicts
            password='VierlaTest123!',  # Updated to match specification
            first_name=data['first_name'],
            last_name=data['last_name'],
            role='customer',
            is_active=True,
            is_verified=True,
            email_verified_at=timezone.now(),
            phone=data.get('phone'),
            account_status='active',
            bio=data.get('profile', ''),
            is_test_account=True
        )

        self.stdout.write(f'✅ Created customer: {customer.get_full_name()} ({email})')
        return customer

    def create_provider_accounts(self):
        """Create service provider test accounts according to specification"""
        self.stdout.write('\n🏢 Creating service provider test accounts...')

        provider_data = [
            {
                'email': '<EMAIL>',
                'first_name': 'Emma',
                'last_name': 'Rodriguez',
                'business_name': 'Trendy Cuts Salon',
                'business_description': 'Professional hair styling and beauty services',
                'category_slug': 'hair-beauty',
                'phone': '+****************',
                'business_email': '<EMAIL>',
                'address': '123 Rideau Street',
                'city': 'Ottawa',
                'state': 'Ontario',
                'zip_code': 'K1N 5X8',
                'rating': '4.8',
                'years_experience': 8,
                'is_verified': True
            },
            {
                'email': '<EMAIL>',
                'first_name': 'Marcus',
                'last_name': 'Johnson',
                'business_name': 'Elite Cuts Barbershop',
                'business_description': 'Premium barbershop services for modern gentlemen',
                'category_slug': 'hair-beauty',
                'phone': '+****************',
                'business_email': '<EMAIL>',
                'address': '456 Queen Street West',
                'city': 'Toronto',
                'state': 'Ontario',
                'zip_code': 'M5V 2A8',
                'rating': '4.9',
                'years_experience': 12,
                'is_verified': True
            }
        ]

        if not self.quick:
            # Add more providers for comprehensive testing
            provider_data.extend([
                {
                    'email': '<EMAIL>',
                    'first_name': 'Lisa',
                    'last_name': 'Wang',
                    'business_name': 'Luxe Nail Lounge',
                    'business_description': 'Premium nail care and nail art services',
                    'category_slug': 'wellness-spa',
                    'phone': '+****************',
                    'business_email': '<EMAIL>',
                    'address': '789 Bank Street',
                    'city': 'Ottawa',
                    'state': 'Ontario',
                    'zip_code': 'K1S 3T4',
                    'rating': '4.7',
                    'years_experience': 6,
                    'is_verified': True
                },
                {
                    'email': '<EMAIL>',
                    'first_name': 'Sophia',
                    'last_name': 'Martinez',
                    'business_name': 'Lash Studio Elite',
                    'business_description': 'Professional lash extensions and brow services',
                    'category_slug': 'wellness-spa',
                    'phone': '+****************',
                    'business_email': '<EMAIL>',
                    'address': '321 Robson Street',
                    'city': 'Vancouver',
                    'state': 'British Columbia',
                    'zip_code': 'V6B 3C2',
                    'rating': '4.9',
                    'years_experience': 5,
                    'is_verified': True
                },
                {
                    'email': '<EMAIL>',
                    'first_name': 'David',
                    'last_name': 'Thompson',
                    'business_name': 'Wellness Massage Therapy',
                    'business_description': 'Therapeutic massage and wellness treatments',
                    'category_slug': 'wellness-spa',
                    'phone': '+****************',
                    'business_email': '<EMAIL>',
                    'address': '654 17th Avenue SW',
                    'city': 'Calgary',
                    'state': 'Alberta',
                    'zip_code': 'T2S 0B5',
                    'rating': '4.8',
                    'years_experience': 10,
                    'is_verified': True
                },
                {
                    'email': '<EMAIL>',
                    'first_name': 'Alex',
                    'last_name': 'Rivera',
                    'business_name': 'New Beauty Studio',
                    'business_description': 'Emerging beauty studio with fresh approaches',
                    'category_slug': 'hair-beauty',
                    'phone': '+****************',
                    'business_email': '<EMAIL>',
                    'address': '987 Saint-Catherine Street',
                    'city': 'Montreal',
                    'state': 'Quebec',
                    'zip_code': 'H3A 1E1',
                    'rating': '4.2',
                    'years_experience': 2,
                    'is_verified': False
                }
            ])

        created_providers = []
        for data in provider_data:
            provider = self.create_provider_account(data)
            if provider:
                created_providers.append(provider)

        return created_providers

    def create_provider_account(self, data):
        """Create a single provider account"""
        email = data['email']

        if User.objects.filter(email=email).exists():
            if self.force:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'🗑️  Deleted existing provider: {email}')
            else:
                self.stdout.write(f'📋 Provider already exists: {email}')
                return None

        # Create provider user
        provider_user = User.objects.create_user(
            email=email,
            username=email,  # Use email as username to avoid conflicts
            password='VierlaTest123!',  # Updated to match specification
            first_name=data['first_name'],
            last_name=data['last_name'],
            role='service_provider',
            is_active=True,
            is_verified=data.get('is_verified', True),
            email_verified_at=timezone.now(),
            phone=data.get('phone'),
            account_status='active',
            is_test_account=True
        )

        # Get category
        try:
            category = ServiceCategory.objects.get(slug=data['category_slug'])
        except ServiceCategory.DoesNotExist:
            category = ServiceCategory.objects.first()

        # Create service provider profile
        provider_profile = ServiceProvider.objects.create(
            user=provider_user,
            business_name=data['business_name'],
            business_description=data['business_description'],
            business_phone=data.get('phone', '+***********'),
            business_email=data.get('business_email', email),
            address=data.get('address', '123 Test Street'),
            city=data.get('city', 'Ottawa'),
            state=data.get('state', 'Ontario'),
            zip_code=data.get('zip_code', 'K1N 5X8'),
            country='Canada',
            latitude=self.get_city_coordinates(data.get('city', 'Ottawa'))[0],
            longitude=self.get_city_coordinates(data.get('city', 'Ottawa'))[1],
            is_verified=data.get('is_verified', True),
            is_active=True,
            rating=Decimal(str(data.get('rating', '4.5'))),
            review_count=10,
            years_of_experience=data.get('years_experience', 5)
        )

        # Add category to provider
        provider_profile.categories.add(category)

        self.stdout.write(
            f'✅ Created provider: {data["business_name"]} ({email})'
        )
        return provider_profile

    def create_sample_services(self, providers):
        """Create sample services for providers"""
        self.stdout.write('\n🛍️  Creating sample services...')

        service_templates = {
            'hair-beauty': [
                {
                    'name': 'Women\'s Haircut & Style',
                    'description': 'Professional haircut with wash, cut, and style',
                    'base_price': 75.00,
                    'duration': 60
                },
                {
                    'name': 'Men\'s Haircut',
                    'description': 'Classic men\'s haircut with styling',
                    'base_price': 45.00,
                    'duration': 45
                },
                {
                    'name': 'Hair Color (Full)',
                    'description': 'Complete hair coloring service',
                    'base_price': 150.00,
                    'duration': 180
                },
                {
                    'name': 'Highlights/Lowlights',
                    'description': 'Professional hair highlighting service',
                    'base_price': 120.00,
                    'duration': 150
                },
                {
                    'name': 'Beard Trim & Style',
                    'description': 'Professional beard trimming and styling',
                    'base_price': 25.00,
                    'duration': 30
                },
                {
                    'name': 'Blowout & Style',
                    'description': 'Professional blowout and styling',
                    'base_price': 50.00,
                    'duration': 45
                }
            ],
            'wellness-spa': [
                {
                    'name': 'Classic Manicure',
                    'description': 'Traditional nail care and polish',
                    'base_price': 35.00,
                    'duration': 45
                },
                {
                    'name': 'Gel Manicure',
                    'description': 'Long-lasting gel nail polish',
                    'base_price': 50.00,
                    'duration': 60
                },
                {
                    'name': 'Classic Pedicure',
                    'description': 'Foot care and nail polish',
                    'base_price': 45.00,
                    'duration': 60
                },
                {
                    'name': 'Lash Extensions (Full Set)',
                    'description': 'Complete eyelash extension application',
                    'base_price': 180.00,
                    'duration': 120
                },
                {
                    'name': 'Lash Fill',
                    'description': 'Maintenance for existing lash extensions',
                    'base_price': 80.00,
                    'duration': 75
                },
                {
                    'name': 'Eyebrow Shaping',
                    'description': 'Professional eyebrow shaping and styling',
                    'base_price': 30.00,
                    'duration': 30
                },
                {
                    'name': 'Relaxation Massage (60 min)',
                    'description': 'Full body therapeutic massage',
                    'base_price': 120.00,
                    'duration': 60
                },
                {
                    'name': 'Deep Tissue Massage',
                    'description': 'Therapeutic deep tissue massage',
                    'base_price': 140.00,
                    'duration': 60
                }
            ]
        }

        created_services = []
        for provider in providers:
            # Get provider's primary category
            category = provider.categories.first()
            if not category:
                continue

            category_slug = category.slug
            templates = service_templates.get(category_slug, [])

            for template in templates:
                service = Service.objects.create(
                    provider=provider,
                    category=category,
                    name=template['name'],
                    description=template['description'],
                    base_price=Decimal(str(template['base_price'])),
                    duration=template['duration'],
                    buffer_time=15,
                    is_available=True,
                    is_active=True,
                    booking_count=5  # Some initial booking history
                )
                created_services.append(service)
                self.stdout.write(
                    f'   ✅ Created service: {service.name} (${service.base_price})'
                )

        return created_services

    def print_summary(self, created_accounts):
        """Print creation summary"""
        admins = created_accounts.get('admins', [])
        customers = created_accounts['customers']
        providers = created_accounts['providers']
        services = created_accounts['services']

        self.stdout.write('\n' + '='*60)
        self.stdout.write(
            self.style.SUCCESS('🎉 TEST ACCOUNTS CREATED SUCCESSFULLY!')
        )
        self.stdout.write('='*60)

        # Summary statistics
        self.stdout.write(f'\n📊 Summary:')
        self.stdout.write(f'   👑 Admins: {len(admins)}')
        self.stdout.write(f'   👤 Customers: {len(customers)}')
        self.stdout.write(f'   🏢 Providers: {len(providers)}')
        self.stdout.write(f'   🛍️  Services: {len(services)}')

        # Test credentials
        self.stdout.write(f'\n🔑 Test Credentials:')
        self.stdout.write(f'   Password for all accounts: TestPass123!')

        if customers:
            self.stdout.write(f'\n👤 Customer Accounts:')
            for customer in customers[:3]:  # Show first 3
                self.stdout.write(f'   📧 {customer.email}')

        if providers:
            self.stdout.write(f'\n🏢 Provider Accounts:')
            for provider in providers[:3]:  # Show first 3
                self.stdout.write(f'   📧 {provider.user.email} ({provider.business_name})')

        # Usage instructions
        self.stdout.write(f'\n📱 Usage:')
        self.stdout.write(f'   🌐 API Base: http://localhost:8000/api/')
        self.stdout.write(f'   📚 API Docs: http://localhost:8000/api/docs/')
        self.stdout.write(f'   🔧 Admin Panel: http://localhost:8000/admin/')

        self.stdout.write(f'\n💡 Tips:')
        self.stdout.write(f'   • Use --force to recreate existing accounts')
        self.stdout.write(f'   • Use --quick for faster account creation')
        self.stdout.write(f'   • Use --customers-only or --providers-only for specific types')

        self.stdout.write(f'\n🧹 Cleanup:')
        self.stdout.write(f'   python manage.py cleanup_test_accounts')

        self.stdout.write('\n✨ Ready for development and testing!')
