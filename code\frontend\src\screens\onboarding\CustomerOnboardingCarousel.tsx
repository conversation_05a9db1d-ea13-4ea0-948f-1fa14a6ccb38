/**
 * CustomerOnboardingCarousel Component
 * 
 * Onboarding carousel specifically for customers showing app features
 * and benefits relevant to service seekers.
 */

import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button } from '../../components/ui';
import { colors, spacing } from '../../theme';

const { width } = Dimensions.get('window');

type CustomerOnboardingNavigationProp = StackNavigationProp<any, 'CustomerOnboarding'>;

interface OnboardingSlide {
  id: string;
  title: string;
  description: string;
  icon: string;
  features: string[];
}

const onboardingSlides: OnboardingSlide[] = [
  {
    id: 'discover',
    title: 'Discover Local Services',
    description: 'Find trusted service providers in your area with just a few taps',
    icon: '🔍',
    features: [
      'Browse by category',
      'Filter by location & price',
      'View detailed profiles',
      'Check availability',
    ],
  },
  {
    id: 'reviews',
    title: 'Read Verified Reviews',
    description: 'Make informed decisions with authentic reviews from real customers',
    icon: '⭐',
    features: [
      'Verified customer reviews',
      'Photo & video testimonials',
      'Rating breakdown',
      'Recent feedback',
    ],
  },
  {
    id: 'booking',
    title: 'Easy Booking & Payment',
    description: 'Book services instantly and pay securely through the app',
    icon: '📅',
    features: [
      'Instant booking confirmation',
      'Secure payment processing',
      'Flexible scheduling',
      'Booking reminders',
    ],
  },
  {
    id: 'communication',
    title: 'Direct Communication',
    description: 'Chat directly with service providers to discuss your needs',
    icon: '💬',
    features: [
      'In-app messaging',
      'Share photos & details',
      'Real-time updates',
      'Support assistance',
    ],
  },
];

export const CustomerOnboardingCarousel: React.FC = () => {
  const navigation = useNavigation<CustomerOnboardingNavigationProp>();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleNext = () => {
    if (currentSlide < onboardingSlides.length - 1) {
      const nextSlide = currentSlide + 1;
      setCurrentSlide(nextSlide);
      scrollViewRef.current?.scrollTo({
        x: nextSlide * width,
        animated: true,
      });
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      const prevSlide = currentSlide - 1;
      setCurrentSlide(prevSlide);
      scrollViewRef.current?.scrollTo({
        x: prevSlide * width,
        animated: true,
      });
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = async () => {
    setIsLoading(true);

    try {
      // Mark onboarding as completed
      await AsyncStorage.setItem('onboarding_completed', 'true');
      await AsyncStorage.setItem('onboarding_step', 'completed');
      await AsyncStorage.setItem('user_type', 'customer');

      // Navigate to authentication screens
      navigation.reset({
        index: 0,
        routes: [{ name: 'Auth' }],
      });
    } catch (error) {
      console.error('Error completing onboarding:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleScroll = (event: any) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setCurrentSlide(slideIndex);
  };

  const renderSlide = (slide: OnboardingSlide, index: number) => (
    <View key={slide.id} style={styles.slide}>
      <View style={styles.slideContent}>
        {/* Icon */}
        <View style={styles.iconContainer}>
          <Text variant="display" style={styles.slideIcon}>
            {slide.icon}
          </Text>
        </View>

        {/* Title & Description */}
        <Text variant="h2" style={styles.slideTitle}>
          {slide.title}
        </Text>
        
        <Text variant="body" color="secondary" style={styles.slideDescription}>
          {slide.description}
        </Text>

        {/* Features List */}
        <View style={styles.featuresList}>
          {slide.features.map((feature, featureIndex) => (
            <View key={featureIndex} style={styles.featureItem}>
              <View style={styles.featureBullet} />
              <Text variant="body" style={styles.featureText}>
                {feature}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  const renderProgressIndicators = () => (
    <View style={styles.progressContainer}>
      {onboardingSlides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.progressDot,
            index === currentSlide && styles.progressDotActive,
          ]}
        />
      ))}
    </View>
  );

  const isLastSlide = currentSlide === onboardingSlides.length - 1;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <View style={styles.header}>
        <Button
          variant="outline"
          onPress={handleSkip}
          style={styles.skipButton}
          testID="skip-button"
        >
          Skip
        </Button>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        style={styles.scrollView}
        testID="onboarding-carousel"
      >
        {onboardingSlides.map(renderSlide)}
      </ScrollView>

      {/* Progress Indicators */}
      {renderProgressIndicators()}

      {/* Navigation */}
      <View style={styles.navigation}>
        <Button
          variant="outline"
          onPress={handlePrevious}
          disabled={currentSlide === 0}
          style={[styles.navButton, styles.previousButton]}
          testID="previous-button"
        >
          Previous
        </Button>

        <Button
          onPress={handleNext}
          loading={isLoading && isLastSlide}
          style={[styles.navButton, styles.nextButton]}
          testID={isLastSlide ? "get-started-button" : "next-button"}
        >
          {isLastSlide ? "Get Started" : "Next"}
        </Button>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
  },
  skipButton: {
    paddingHorizontal: spacing.lg,
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    width,
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  slideContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  iconContainer: {
    marginBottom: spacing.xl,
  },
  slideIcon: {
    fontSize: 80,
    textAlign: 'center',
  },
  slideTitle: {
    textAlign: 'center',
    marginBottom: spacing.md,
    color: colors.text.primary,
  },
  slideDescription: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  featuresList: {
    alignSelf: 'stretch',
    maxWidth: 280,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  featureBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginRight: spacing.md,
  },
  featureText: {
    flex: 1,
    color: colors.text.primary,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.border,
    marginHorizontal: spacing.xs,
  },
  progressDotActive: {
    backgroundColor: colors.primary,
    width: 24,
  },
  navigation: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    gap: spacing.md,
  },
  navButton: {
    flex: 1,
  },
  previousButton: {
    // Additional styles for previous button if needed
  },
  nextButton: {
    // Additional styles for next button if needed
  },
});

export default CustomerOnboardingCarousel;
