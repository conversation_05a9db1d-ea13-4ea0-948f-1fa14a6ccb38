"""
Core permission classes for the Vierla API
Provides common permission patterns used across the application
"""

from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()


class IsActiveUser(permissions.BasePermission):
    """
    Permission to check if user is active
    """

    def has_permission(self, request, view):
        """
        Check if user is authenticated and active
        """
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_active
        )


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed for any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.user == request.user


class IsProviderOrReadOnly(permissions.BasePermission):
    """
    Permission to check if user is a provider for write operations
    """

    def has_permission(self, request, view):
        """
        Check if user is authenticated and active
        """
        if not (request.user and request.user.is_authenticated and request.user.is_active):
            return False

        # Read permissions for all authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for providers
        return request.user.role == 'service_provider'


class IsCustomerOrReadOnly(permissions.BasePermission):
    """
    Permission to check if user is a customer for write operations
    """

    def has_permission(self, request, view):
        """
        Check if user is authenticated and active
        """
        if not (request.user and request.user.is_authenticated and request.user.is_active):
            return False

        # Read permissions for all authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for customers
        return request.user.role == 'customer'


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Permission to check if user is an admin for write operations
    """

    def has_permission(self, request, view):
        """
        Check if user is authenticated and active
        """
        if not (request.user and request.user.is_authenticated and request.user.is_active):
            return False

        # Read permissions for all authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for admins
        return request.user.role == 'admin' or request.user.is_staff


class IsOwnerOrProvider(permissions.BasePermission):
    """
    Permission to check if user is the owner or a provider
    """

    def has_object_permission(self, request, view, obj):
        """
        Check if user is the owner or a provider
        """
        # Check if user is the owner
        if hasattr(obj, 'user') and obj.user == request.user:
            return True

        # Check if user is the customer
        if hasattr(obj, 'customer') and obj.customer == request.user:
            return True

        # Check if user is the provider
        if hasattr(obj, 'provider') and hasattr(request.user, 'provider_profile'):
            return obj.provider == request.user.provider_profile

        return False


class CanManageProfile(permissions.BasePermission):
    """
    Permission to manage user profiles
    """

    def has_object_permission(self, request, view, obj):
        """
        Check if user can manage the profile
        """
        # Users can manage their own profile
        if hasattr(obj, 'user') and obj.user == request.user:
            return True

        # Admins can manage any profile
        if request.user.role == 'admin' or request.user.is_staff:
            return True

        return False
