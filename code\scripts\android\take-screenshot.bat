@echo off
REM ============================================================================
REM Android Emulator Screenshot Automation Script (Batch Wrapper)
REM ============================================================================
REM This batch file provides a simple wrapper for the PowerShell screenshot script
REM
REM Usage: .\code\scripts\android\take-screenshot.bat [filename]
REM Example: .\code\scripts\android\take-screenshot.bat login-screen
REM ============================================================================

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_DIR=%~dp0
set PS_SCRIPT=%SCRIPT_DIR%take-screenshot.ps1

REM Check if PowerShell script exists
if not exist "%PS_SCRIPT%" (
    echo Error: PowerShell script not found at %PS_SCRIPT%
    exit /b 1
)

REM Parse arguments
set FILENAME=%1
if "%FILENAME%"=="" (
    REM No filename provided, let PowerShell script generate one
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
) else (
    REM Filename provided
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Filename "%FILENAME%"
)

exit /b %ERRORLEVEL%
