from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone as django_timezone
from apps.catalog.models import ServiceProvider, Service
from apps.bookings.models import Booking

User = get_user_model()


class Review(models.Model):
    """Customer reviews for service providers and services"""

    # Relationships
    booking = models.OneToOneField(
        Booking,
        on_delete=models.CASCADE,
        related_name='review',
        help_text="Booking this review is for"
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='reviews',
        help_text="Customer who wrote the review"
    )
    provider = models.ForeignKey(
        ServiceProvider,
        on_delete=models.CASCADE,
        related_name='reviews',
        help_text="Service provider being reviewed"
    )
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='reviews',
        help_text="Service being reviewed"
    )

    # Review Content
    rating = models.PositiveIntegerField(
        validators=[MinV<PERSON>ueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    comment = models.TextField(
        help_text="Review comment text"
    )

    # Review Media
    images = models.JSONField(
        default=list,
        blank=True,
        help_text="List of image URLs for the review"
    )

    # Status and Features
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this review has been verified"
    )
    is_featured = models.BooleanField(
        default=False,
        help_text="Whether this review is featured"
    )

    # Timestamps
    created_at = models.DateTimeField(
        default=django_timezone.now,
        help_text="When the review was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="When the review was last updated"
    )

    class Meta:
        db_table = 'reviews'
        verbose_name = 'Review'
        verbose_name_plural = 'Reviews'
        ordering = ['-created_at']
        unique_together = ['customer', 'provider', 'booking']
        indexes = [
            models.Index(fields=['provider', 'rating']),
            models.Index(fields=['service', 'rating']),
            models.Index(fields=['customer']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_verified', 'is_featured']),
        ]

    def __str__(self):
        return f"Review by {self.customer.first_name} for {self.provider.business_name} - {self.rating} stars"

    @property
    def star_display(self):
        """Return star rating as string for display"""
        return "★" * self.rating + "☆" * (5 - self.rating)
