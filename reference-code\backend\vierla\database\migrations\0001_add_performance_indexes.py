"""
Database Performance Optimization Migration
Based on Backend Agent Consultation for Production Performance
"""

from django.db import migrations, models

class Migration(migrations.Migration):
    
    dependencies = [
        ('catalog', '0003_searchhistory_searchhistory_unique_user_query_type'),
        ('bookings', '0001_initial'),
        ('authentication', '0002_alter_user_groups_alter_user_user_permissions'),
    ]

    operations = [
        # ServiceProvider performance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_rating_reviews ON catalog_service_providers (rating DESC, review_count DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_rating_reviews;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_location_active ON catalog_service_providers (latitude, longitude, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_location_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_search ON catalog_service_providers USING gin(to_tsvector('english', business_name || ' ' || business_description));",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_search;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_featured_active ON catalog_service_providers (is_featured DESC, is_active, rating DESC) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_featured_active;"
        ),

        # Service performance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_service_provider_active ON catalog_services (provider_id, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_service_provider_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_service_category_price ON catalog_services (category_id, base_price, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_service_category_price;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_service_popular ON catalog_services (is_popular DESC, booking_count DESC, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_service_popular;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_service_search ON catalog_services USING gin(to_tsvector('english', name || ' ' || description));",
            reverse_sql="DROP INDEX IF EXISTS idx_service_search;"
        ),

        # Booking performance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_customer_status_date ON bookings_booking (customer_id, status, scheduled_datetime DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_booking_customer_status_date;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_provider_date_status ON bookings_booking (provider_id, scheduled_datetime DESC, status);",
            reverse_sql="DROP INDEX IF EXISTS idx_booking_provider_date_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_upcoming ON bookings_booking (scheduled_datetime, status) WHERE status IN ('pending', 'confirmed');",
            reverse_sql="DROP INDEX IF EXISTS idx_booking_upcoming;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_payment_status ON bookings_booking (payment_status, total_amount) WHERE payment_status != 'paid';",
            reverse_sql="DROP INDEX IF EXISTS idx_booking_payment_status;"
        ),

        # TimeSlot performance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_timeslot_provider_date_available ON bookings_time_slot (provider_id, date, is_available) WHERE is_available = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_timeslot_provider_date_available;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_timeslot_service_date ON bookings_time_slot (service_id, date, start_time) WHERE is_available = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_timeslot_service_date;"
        ),

        # User authentication indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_email_active ON authentication_user (email, is_active) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_user_email_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_active ON authentication_user (role, is_active, date_joined DESC) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_user_role_active;"
        ),

        # ServiceCategory performance indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_category_active_order ON catalog_service_categories (is_active, display_order) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_category_active_order;"
        ),

        # Composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_category_rating ON catalog_service_providers (id) INCLUDE (rating, review_count, is_verified);",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_category_rating;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_service_booking_count ON catalog_services (booking_count DESC, created_at DESC) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_service_booking_count;"
        ),

        # Partial indexes for frequently filtered data
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_active_providers_only ON catalog_service_providers (business_name, rating DESC) WHERE is_active = true AND is_verified = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_active_providers_only;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_available_services_only ON catalog_services (name, base_price) WHERE is_active = true AND is_available = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_available_services_only;"
        ),

        # Indexes for analytics and reporting
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_booking_analytics ON bookings_booking (created_at, status, total_amount) WHERE status = 'completed';",
            reverse_sql="DROP INDEX IF EXISTS idx_booking_analytics;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_performance ON catalog_service_providers (created_at, rating, review_count, total_bookings);",
            reverse_sql="DROP INDEX IF EXISTS idx_provider_performance;"
        ),

        # Geospatial optimization (if PostGIS is available)
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'postgis') THEN
                    EXECUTE 'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_location_gist ON catalog_service_providers USING GIST (ll_to_earth(latitude, longitude))';
                END IF;
            END $$;
            """,
            reverse_sql="DROP INDEX IF EXISTS idx_provider_location_gist;"
        ),

        # Update table statistics for better query planning
        migrations.RunSQL(
            "ANALYZE catalog_service_providers, catalog_services, bookings_booking, bookings_time_slot, authentication_user;",
            reverse_sql=""
        ),
    ]
