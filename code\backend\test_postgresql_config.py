"""
PostgreSQL Database Configuration Tests
Tests to verify PostgreSQL connection, data integrity, and migration success
"""

import os
import pytest
import psycopg2
from django.test import TestCase, override_settings
from django.db import connection, connections
from django.core.management import call_command
from django.conf import settings
from django.contrib.auth import get_user_model
from authentication.models import User
from catalog.models import Service, ServiceCategory, ServiceProvider

# Test database configuration for PostgreSQL
TEST_POSTGRESQL_CONFIG = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'vierla_test_db',
        'USER': 'vierla_test_user',
        'PASSWORD': 'vierla_test_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'TEST': {
            'NAME': 'test_vierla_db',
        }
    }
}


class PostgreSQLConnectionTest(TestCase):
    """Test PostgreSQL database connection and basic operations"""
    
    def test_postgresql_connection_available(self):
        """Test that PostgreSQL connection can be established"""
        try:
            # Test direct psycopg2 connection
            conn = psycopg2.connect(
                host=os.environ.get('DB_HOST', 'localhost'),
                port=os.environ.get('DB_PORT', '5432'),
                database=os.environ.get('DB_NAME', 'vierla_db'),
                user=os.environ.get('DB_USER', 'vierla_user'),
                password=os.environ.get('DB_PASSWORD', 'vierla_password')
            )
            conn.close()
            self.assertTrue(True, "PostgreSQL connection successful")
        except psycopg2.OperationalError as e:
            self.fail(f"PostgreSQL connection failed: {e}")
    
    def test_django_database_connection(self):
        """Test Django database connection works with PostgreSQL"""
        # Test Django connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            result = cursor.fetchone()
            self.assertIsNotNone(result)
            self.assertIn('PostgreSQL', result[0])
    
    def test_database_settings_configuration(self):
        """Test that database settings are properly configured for PostgreSQL"""
        db_config = settings.DATABASES['default']
        self.assertEqual(db_config['ENGINE'], 'django.db.backends.postgresql')
        self.assertIsNotNone(db_config['NAME'])
        self.assertIsNotNone(db_config['USER'])
        self.assertIsNotNone(db_config['HOST'])
        self.assertIsNotNone(db_config['PORT'])


class PostgreSQLMigrationTest(TestCase):
    """Test database migrations work correctly with PostgreSQL"""
    
    def test_migrations_applied_successfully(self):
        """Test that all migrations have been applied successfully"""
        from django.db.migrations.executor import MigrationExecutor
        
        executor = MigrationExecutor(connection)
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
        
        # If plan is empty, all migrations are applied
        self.assertEqual(len(plan), 0, "All migrations should be applied")
    
    def test_authentication_tables_exist(self):
        """Test that authentication app tables exist"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'authentication_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['authentication_user']
            for table in expected_tables:
                self.assertIn(table, tables, f"Table {table} should exist")
    
    def test_catalog_tables_exist(self):
        """Test that catalog app tables exist"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'catalog_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['catalog_service', 'catalog_servicecategory', 'catalog_serviceprovider']
            for table in expected_tables:
                self.assertIn(table, tables, f"Table {table} should exist")


class PostgreSQLDataIntegrityTest(TestCase):
    """Test data integrity and CRUD operations with PostgreSQL"""
    
    def setUp(self):
        """Set up test data"""
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'customer'
        }
    
    def test_user_creation_and_retrieval(self):
        """Test creating and retrieving user data"""
        # Create user
        user = User.objects.create_user(**self.user_data)
        self.assertIsNotNone(user.id)
        
        # Retrieve user
        retrieved_user = User.objects.get(email=self.user_data['email'])
        self.assertEqual(retrieved_user.email, self.user_data['email'])
        self.assertEqual(retrieved_user.first_name, self.user_data['first_name'])
        self.assertEqual(retrieved_user.role, self.user_data['role'])
    
    def test_service_category_operations(self):
        """Test service category CRUD operations"""
        # Create category
        category = ServiceCategory.objects.create(
            name='Test Category',
            slug='test-category',
            description='Test category description'
        )
        self.assertIsNotNone(category.id)
        
        # Update category
        category.description = 'Updated description'
        category.save()
        
        # Retrieve and verify update
        updated_category = ServiceCategory.objects.get(id=category.id)
        self.assertEqual(updated_category.description, 'Updated description')
        
        # Delete category
        category_id = category.id
        category.delete()
        
        # Verify deletion
        with self.assertRaises(ServiceCategory.DoesNotExist):
            ServiceCategory.objects.get(id=category_id)
    
    def test_foreign_key_relationships(self):
        """Test foreign key relationships work correctly"""
        # Create user
        user = User.objects.create_user(**self.user_data)
        
        # Create category
        category = ServiceCategory.objects.create(
            name='Test Category',
            slug='test-category'
        )
        
        # Create service provider
        provider = ServiceProvider.objects.create(
            user=user,
            business_name='Test Business',
            category=category
        )
        
        # Create service
        service = Service.objects.create(
            provider=provider,
            name='Test Service',
            description='Test service description',
            price=50.00,
            duration=60
        )
        
        # Test relationships
        self.assertEqual(service.provider.user.email, self.user_data['email'])
        self.assertEqual(service.provider.category.name, 'Test Category')
        self.assertEqual(provider.services.count(), 1)


class PostgreSQLPerformanceTest(TestCase):
    """Test PostgreSQL performance and optimization"""
    
    def test_database_indexes_exist(self):
        """Test that important database indexes exist"""
        with connection.cursor() as cursor:
            # Check for indexes on frequently queried fields
            cursor.execute("""
                SELECT indexname, tablename, indexdef
                FROM pg_indexes 
                WHERE schemaname = 'public'
                AND (tablename LIKE 'authentication_%' OR tablename LIKE 'catalog_%')
            """)
            indexes = cursor.fetchall()
            
            # Should have indexes on primary keys and foreign keys
            self.assertGreater(len(indexes), 0, "Database should have indexes")
    
    def test_connection_pooling_settings(self):
        """Test that connection pooling is properly configured"""
        db_config = settings.DATABASES['default']
        
        # Check for connection pooling options
        if 'OPTIONS' in db_config:
            options = db_config['OPTIONS']
            # These are optional but recommended for production
            self.assertIsInstance(options, dict)


class PostgreSQLEnvironmentTest(TestCase):
    """Test PostgreSQL environment configuration"""
    
    def test_environment_variables_loaded(self):
        """Test that PostgreSQL environment variables are loaded"""
        # These should be set when using PostgreSQL
        required_env_vars = ['DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT']
        
        for var in required_env_vars:
            value = os.environ.get(var)
            if value is not None:  # Only test if the variable is set
                self.assertIsNotNone(value, f"Environment variable {var} should be set")
    
    def test_sqlite_fallback_disabled(self):
        """Test that SQLite fallback is disabled when using PostgreSQL"""
        use_sqlite = os.environ.get('USE_SQLITE', 'False').lower()
        if use_sqlite == 'false':
            db_engine = settings.DATABASES['default']['ENGINE']
            self.assertEqual(db_engine, 'django.db.backends.postgresql')
    
    def test_postgresql_version_compatibility(self):
        """Test PostgreSQL version compatibility"""
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version_info = cursor.fetchone()[0]
            
            # Extract version number (e.g., "PostgreSQL 13.7")
            version_parts = version_info.split()
            if len(version_parts) >= 2:
                version_number = version_parts[1].split('.')[0]
                version_major = int(version_number)
                
                # PostgreSQL 10+ is recommended for Django 5.x
                self.assertGreaterEqual(version_major, 10, 
                                      f"PostgreSQL version {version_major} should be 10 or higher")


if __name__ == '__main__':
    # Run tests with pytest
    pytest.main([__file__, '-v'])
