#!/usr/bin/env python
"""
Clear Throttle Cache Script
Clears all throttling cache entries to reset rate limits
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

import django
django.setup()

from django.core.cache import cache


def clear_throttle_cache():
    """Clear all throttling cache entries"""
    try:
        # Get all cache keys
        if hasattr(cache, '_cache'):
            # For dummy cache or simple backends
            cache.clear()
            print("✅ All cache cleared (simple backend)")
        else:
            # For more complex cache backends, clear throttle-specific keys
            # This is a more targeted approach
            cache_keys = []
            
            # Try to get throttle-related keys
            try:
                # Pattern for DRF throttle cache keys: throttle_<scope>_<ident>
                all_keys = cache._cache.keys() if hasattr(cache, '_cache') else []
                throttle_keys = [key for key in all_keys if key.startswith('throttle_')]
                
                for key in throttle_keys:
                    cache.delete(key)
                    cache_keys.append(key)
                    
                print(f"✅ Cleared {len(cache_keys)} throttle cache entries")
                if cache_keys:
                    print("   Cleared keys:", cache_keys[:5], "..." if len(cache_keys) > 5 else "")
                    
            except Exception as e:
                # Fallback: clear all cache
                cache.clear()
                print(f"✅ Cleared all cache (fallback method): {e}")
                
        print("🔄 Rate limiting has been reset")
        print("🚀 You can now test login functionality again")
        
    except Exception as e:
        print(f"❌ Error clearing cache: {e}")
        print("💡 Try restarting the Django server to reset rate limits")


if __name__ == "__main__":
    print("🧹 Clearing Vierla Throttle Cache...")
    print("=" * 40)
    clear_throttle_cache()
