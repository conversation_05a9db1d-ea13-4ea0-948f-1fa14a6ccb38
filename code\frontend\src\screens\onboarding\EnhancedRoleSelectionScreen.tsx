/**
 * Enhanced RoleSelectionScreen Component
 * Rebuilt with shadcn/ui design patterns for better styling and user experience
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  Animated,
  ScrollView,
  } from 'react-native';
import { SafeAreaView } from '../../components/ui/SafeAreaViewWrapper';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Text, Button, Card } from '../../components/ui';
import { colors, spacing } from '../../theme';

type RoleSelectionNavigationProp = StackNavigationProp<any, 'RoleSelection'>;

type UserRole = 'customer' | 'service_provider';

interface RoleOption {
  id: UserRole;
  title: string;
  description: string;
  icon: string;
  benefits: string[];
  gradient: string[];
}

const roleOptions: RoleOption[] = [
  {
    id: 'customer',
    title: 'I need services',
    description: 'Find and book trusted service providers in your area',
    icon: '🔍',
    benefits: [
      'Browse local services',
      'Read verified reviews',
      'Secure booking & payment',
      'Direct communication',
    ],
    gradient: ['#667eea', '#764ba2'],
  },
  {
    id: 'service_provider',
    title: 'I provide services',
    description: 'Grow your business by connecting with customers',
    icon: '🛠️',
    benefits: [
      'Reach more customers',
      'Manage your bookings',
      'Secure payments',
      'Build your reputation',
    ],
    gradient: ['#f093fb', '#f5576c'],
  },
];

export const EnhancedRoleSelectionScreen: React.FC = () => {
  const navigation = useNavigation<RoleSelectionNavigationProp>();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const contentOpacity = useRef(new Animated.Value(0)).current;
  const slideUpAnimation = useRef(new Animated.Value(30)).current;
  const roleAnimations = useRef(
    roleOptions.map(() => new Animated.Value(0))
  ).current;

  useEffect(() => {
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Header animation
    Animated.timing(headerOpacity, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();

    // Content slide up animation
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideUpAnimation, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }, 200);

    // Staggered role card animations
    setTimeout(() => {
      const roleAnimationSequence = roleAnimations.map((animation, index) =>
        Animated.timing(animation, {
          toValue: 1,
          duration: 500,
          delay: index * 150,
          useNativeDriver: true,
        })
      );

      Animated.stagger(150, roleAnimationSequence).start();
    }, 400);
  };

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
    
    // Add a subtle animation for selection feedback
    const selectedIndex = roleOptions.findIndex(option => option.id === role);
    if (selectedIndex !== -1) {
      Animated.sequence([
        Animated.timing(roleAnimations[selectedIndex], {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(roleAnimations[selectedIndex], {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  const handleContinue = async () => {
    if (!selectedRole) {
      Alert.alert('Please select a role', 'Choose whether you need services or provide services to continue.');
      return;
    }

    setIsLoading(true);

    try {
      await AsyncStorage.setItem('selected_role', selectedRole);
      await AsyncStorage.setItem('onboarding_step', 'role_selected');

      if (selectedRole === 'customer') {
        navigation.navigate('CustomerOnboarding');
      } else {
        navigation.navigate('ProviderOnboarding');
      }
    } catch (error) {
      console.error('Error saving role selection:', error);
      Alert.alert('Error', 'Failed to save your selection. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderRoleOption = (option: RoleOption, index: number) => {
    const isSelected = selectedRole === option.id;
    
    const animatedStyle = {
      opacity: roleAnimations[index],
      transform: [
        {
          translateY: roleAnimations[index].interpolate({
            inputRange: [0, 1],
            outputRange: [30, 0],
          }),
        },
        {
          scale: roleAnimations[index].interpolate({
            inputRange: [0, 1],
            outputRange: [0.95, 1],
          }),
        },
      ],
    };

    return (
      <Animated.View key={option.id} style={animatedStyle}>
        <TouchableOpacity
          onPress={() => handleRoleSelect(option.id)}
          activeOpacity={0.8}
        >
          <Card
            variant={isSelected ? 'elevated' : 'outline'}
            style={[
              styles.roleCard,
              isSelected && styles.roleCardSelected,
            ]}
          >
            <View style={styles.roleHeader}>
              <View style={[styles.roleIconContainer, isSelected && styles.roleIconSelected]}>
                <Text variant="h2" style={styles.roleIcon}>
                  {option.icon}
                </Text>
              </View>
              
              <View style={styles.roleInfo}>
                <Text variant="h3" weight="semibold" style={styles.roleTitle}>
                  {option.title}
                </Text>
                
                <Text variant="body" color="secondary" style={styles.roleDescription}>
                  {option.description}
                </Text>
              </View>

              <View style={[
                styles.selectionIndicator,
                isSelected && styles.selectionIndicatorSelected,
              ]}>
                {isSelected && (
                  <Text variant="caption" style={styles.checkmark}>
                    ✓
                  </Text>
                )}
              </View>
            </View>

            <View style={styles.benefitsList}>
              {option.benefits.map((benefit, benefitIndex) => (
                <View key={benefitIndex} style={styles.benefitItem}>
                  <View style={styles.benefitBullet} />
                  <Text variant="caption" color="secondary" style={styles.benefitText}>
                    {benefit}
                  </Text>
                </View>
              ))}
            </View>
          </Card>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      
      {/* Header */}
      <Animated.View style={[styles.header, { opacity: headerOpacity }]}>
        <TouchableOpacity 
          onPress={handleBack} 
          style={styles.backButton}
        >
          <Text variant="button" color="accent">
            ← Back
          </Text>
        </TouchableOpacity>
        
        <Text variant="h2" weight="bold" style={styles.headerTitle}>
          How will you use Vierla?
        </Text>
        
        <Text variant="body" color="secondary" style={styles.headerSubtitle}>
          Choose your role to get a personalized experience
        </Text>
      </Animated.View>

      {/* Content */}
      <Animated.View
        style={[
          styles.content,
          {
            opacity: contentOpacity,
            transform: [{ translateY: slideUpAnimation }]
          }
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.roleOptions}>
            {roleOptions.map(renderRoleOption)}
          </View>
        </ScrollView>
      </Animated.View>

      {/* Footer */}
      <Animated.View style={[styles.footer, { opacity: contentOpacity }]}>
        <Button
          variant="default"
          size="lg"
          onPress={handleContinue}
          loading={isLoading}
          disabled={!selectedRole}
          style={styles.continueButton}
        >
          Continue
        </Button>
        
        <Text variant="caption" color="secondary" align="center" style={styles.disclaimer}>
          You can change this later in your profile settings
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.lg,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: spacing.md,
    paddingVertical: spacing.xs,
  },
  headerTitle: {
    marginBottom: spacing.sm,
  },
  headerSubtitle: {
    lineHeight: 22,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  roleOptions: {
    gap: spacing.lg,
  },
  roleCard: {
    padding: spacing.lg,
  },
  roleCardSelected: {
    borderColor: colors.primary,
    borderWidth: 2,
    backgroundColor: `${colors.primary}08`,
  },
  roleHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  roleIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.backgroundSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  roleIconSelected: {
    backgroundColor: colors.primaryLight,
  },
  roleIcon: {
    fontSize: 24,
  },
  roleInfo: {
    flex: 1,
  },
  roleTitle: {
    marginBottom: spacing.xs,
  },
  roleDescription: {
    lineHeight: 20,
  },
  selectionIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.sm,
  },
  selectionIndicatorSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary,
  },
  checkmark: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 12,
  },
  benefitsList: {
    gap: spacing.sm,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  benefitBullet: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: spacing.sm,
  },
  benefitText: {
    flex: 1,
    lineHeight: 18,
  },
  footer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    backgroundColor: colors.white,
  },
  continueButton: {
    marginBottom: spacing.sm,
  },
  disclaimer: {
    lineHeight: 16,
    opacity: 0.8,
  },
});

export default EnhancedRoleSelectionScreen;
