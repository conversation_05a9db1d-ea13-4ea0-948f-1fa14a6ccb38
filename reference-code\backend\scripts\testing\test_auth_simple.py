#!/usr/bin/env python
"""
Simple test script for Vierla backend authentication system
Tests role-based access control for Service Catalog API endpoints
"""
import os
import sys
import django
import requests
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import Django modules after setup
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

# API Base URL
BASE_URL = 'http://127.0.0.1:8000'

def get_jwt_token(email, password):
    """Get JWT token for user authentication"""
    try:
        user = User.objects.get(email=email)
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    except User.DoesNotExist:
        return None

def test_endpoint(endpoint, token=None):
    """Test an API endpoint with authentication"""
    url = f"{BASE_URL}{endpoint}"
    headers = {'Content-Type': 'application/json'}
    
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    try:
        response = requests.get(url, headers=headers)
        print(f"  {endpoint}: Status {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict) and 'results' in data:
                    print(f"    Found {len(data['results'])} items")
                elif isinstance(data, list):
                    print(f"    Found {len(data)} items")
                else:
                    print(f"    Response: {str(data)[:100]}...")
            except:
                print(f"    Response: {response.text[:100]}...")
        else:
            print(f"    Error: {response.text[:100]}...")
        
        return response.status_code
        
    except Exception as e:
        print(f"  {endpoint}: ERROR - {str(e)}")
        return 'ERROR'

def main():
    print("🔐 Testing Vierla Backend Authentication")
    print("=" * 50)
    
    # Get tokens for test accounts
    print("\n📋 Getting authentication tokens...")
    
    admin_token = get_jwt_token('<EMAIL>', 'admin123')
    customer_token = get_jwt_token('<EMAIL>', 'testpass123')
    provider_token = get_jwt_token('<EMAIL>', 'testpass123')
    
    print(f"Admin token: {'✅' if admin_token else '❌'}")
    print(f"Customer token: {'✅' if customer_token else '❌'}")
    print(f"Provider token: {'✅' if provider_token else '❌'}")
    
    print("\n🧪 Testing API Endpoints...")
    
    # Test public access
    print("\n1. Public Access (no authentication):")
    test_endpoint('/api/catalog/categories/')
    test_endpoint('/api/catalog/providers/')
    test_endpoint('/api/catalog/services/')
    
    # Test customer access
    if customer_token:
        print("\n2. Customer Access:")
        test_endpoint('/api/catalog/categories/', customer_token)
        test_endpoint('/api/catalog/providers/', customer_token)
        test_endpoint('/api/catalog/services/', customer_token)
    
    # Test provider access
    if provider_token:
        print("\n3. Provider Access:")
        test_endpoint('/api/catalog/categories/', provider_token)
        test_endpoint('/api/catalog/providers/', provider_token)
        test_endpoint('/api/catalog/services/', provider_token)
    
    # Test admin access
    if admin_token:
        print("\n4. Admin Access:")
        test_endpoint('/api/catalog/categories/', admin_token)
        test_endpoint('/api/catalog/providers/', admin_token)
        test_endpoint('/api/catalog/services/', admin_token)
    
    # Test protected endpoints
    print("\n5. Protected Endpoints (should require authentication):")
    test_endpoint('/api/catalog/operating-hours/')
    test_endpoint('/api/catalog/service-availability/')
    test_endpoint('/api/catalog/gallery/')
    
    print("\n🎉 Testing completed!")

if __name__ == '__main__':
    main()
