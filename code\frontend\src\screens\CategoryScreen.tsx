import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

// Import components
import { ServiceList } from '../components';
import type { Service, ServiceCategory } from '../components';

// Mock data
const mockCategory: ServiceCategory = {
  id: '1',
  name: 'Hair Services',
  slug: 'hair-services',
  icon: '💇‍♀️',
  color: '#FF5722',
  mobile_icon: '✂️',
  is_popular: true,
  service_count: 15,
  description: 'Professional hair services including cuts, styling, coloring, and treatments',
};

const mockServices: Service[] = [
  {
    id: '1',
    name: 'Premium Haircut',
    short_description: 'Professional haircut with styling consultation',
    base_price: 75.00,
    price_type: 'fixed',
    display_price: '$75.00',
    duration: 60,
    display_duration: '1h',
    image: undefined,
    is_popular: true,
    is_available: true,
    booking_count: 25,
    provider_name: 'Hair Studio Elite',
    provider_rating: 4.8,
    provider_city: 'Toronto',
    category_name: 'Hair Services',
    category_icon: '💇‍♀️',
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Hair Coloring',
    short_description: 'Professional hair coloring and highlights',
    base_price: 150.00,
    price_type: 'range',
    max_price: 250.00,
    display_price: '$150 - $250',
    duration: 180,
    display_duration: '3h',
    image: undefined,
    is_popular: false,
    is_available: true,
    booking_count: 18,
    provider_name: 'Color Studio Pro',
    provider_rating: 4.6,
    provider_city: 'Vancouver',
    category_name: 'Hair Services',
    category_icon: '💇‍♀️',
    created_at: '2024-01-02T00:00:00Z',
  },
  {
    id: '3',
    name: 'Hair Treatment',
    short_description: 'Deep conditioning and repair treatment',
    base_price: 95.00,
    price_type: 'fixed',
    display_price: '$95.00',
    duration: 90,
    display_duration: '1h 30m',
    image: undefined,
    is_popular: true,
    is_available: true,
    booking_count: 32,
    provider_name: 'Hair Wellness Spa',
    provider_rating: 4.9,
    provider_city: 'Montreal',
    category_name: 'Hair Services',
    category_icon: '💇‍♀️',
    created_at: '2024-01-03T00:00:00Z',
  },
];

// Colors (following brand guidelines)
const Colors = {
  primary: {
    main: '#D81B60',
    light: '#FFD180',
    white: '#FFFFFF',
  },
  background: {
    light: '#F5F5F5',
    surface: '#FFFFFF',
  },
  text: {
    primary: '#212121',
    secondary: '#616161',
  },
  border: '#E0E0E0',
};

// Spacing (8pt grid)
const Spacing = {
  micro: 4,
  small: 8,
  medium: 16,
  large: 24,
  xl: 32,
};

export const CategoryScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [category, setCategory] = useState<ServiceCategory | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [favoriteServices, setFavoriteServices] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<string>('relevance');

  // Get category ID from route params
  const categoryId = (route.params as any)?.categoryId || '1';

  useEffect(() => {
    loadCategoryData();
  }, [categoryId]);

  const loadCategoryData = useCallback(async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API calls
      // const [categoryResponse, servicesResponse] = await Promise.all([
      //   serviceApi.getCategory(categoryId),
      //   serviceApi.getCategoryServices(categoryId)
      // ]);
      // setCategory(categoryResponse.data);
      // setServices(servicesResponse.data);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCategory(mockCategory);
      setServices(mockServices);
    } catch (error) {
      Alert.alert('Error', 'Failed to load category data');
    } finally {
      setLoading(false);
    }
  }, [categoryId]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCategoryData();
    setRefreshing(false);
  }, [loadCategoryData]);

  const handleBackPress = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  const handleServicePress = useCallback((service: Service) => {
    // TODO: Navigate to service details screen
    // navigation.navigate('ServiceDetails', { serviceId: service.id });
    Alert.alert('Service Selected', `You selected: ${service.name}`);
  }, []);

  const handleFavoriteToggle = useCallback((serviceId: string) => {
    setFavoriteServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  }, []);

  const handleSortPress = useCallback(() => {
    // TODO: Show sort options modal
    Alert.alert('Sort Options', 'Sort functionality coming soon');
  }, []);

  const handleFilterPress = useCallback(() => {
    // TODO: Show filter options modal
    Alert.alert('Filter Options', 'Filter functionality coming soon');
  }, []);

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={handleBackPress}
        testID="category-back-button"
      >
        <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
      </TouchableOpacity>
      
      <View style={styles.headerContent}>
        <Text style={styles.headerTitle} testID="category-title">
          {category?.name}
        </Text>
        <Text style={styles.headerSubtitle} testID="category-count">
          {services.length} services available
        </Text>
      </View>
      
      <View style={styles.headerActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleSortPress}
          testID="category-sort-button"
        >
          <Ionicons name="swap-vertical" size={20} color={Colors.text.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleFilterPress}
          testID="category-filter-button"
        >
          <Ionicons name="filter" size={20} color={Colors.text.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCategoryInfo = () => {
    if (!category?.description) return null;
    
    return (
      <View style={styles.categoryInfo}>
        <View style={styles.categoryIconContainer}>
          <Text style={styles.categoryIcon}>{category.icon}</Text>
        </View>
        <Text style={styles.categoryDescription} testID="category-description">
          {category.description}
        </Text>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="search-outline" size={64} color={Colors.text.secondary} />
      <Text style={styles.emptyTitle}>No services found</Text>
      <Text style={styles.emptySubtitle}>
        There are no services available in this category at the moment.
      </Text>
    </View>
  );

  if (loading && !category) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading category...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} testID="category-screen">
      {renderHeader()}
      
      <View style={styles.content}>
        {renderCategoryInfo()}
        
        {services.length > 0 ? (
          <ServiceList
            services={services}
            onServicePress={handleServicePress}
            onFavoriteToggle={handleFavoriteToggle}
            favoriteServices={favoriteServices}
            loading={loading}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            variant="list"
            testID="category-services-list"
            emptyMessage="No services found"
            emptySubtitle="Try adjusting your filters or check back later"
          />
        ) : (
          !loading && renderEmptyState()
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.medium,
    paddingVertical: Spacing.medium,
    backgroundColor: Colors.background.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    padding: Spacing.small,
    marginRight: Spacing.small,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: Spacing.micro,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: Spacing.small,
    marginLeft: Spacing.small,
  },
  content: {
    flex: 1,
  },
  categoryInfo: {
    backgroundColor: Colors.background.surface,
    padding: Spacing.large,
    marginBottom: Spacing.small,
    alignItems: 'center',
  },
  categoryIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  categoryIcon: {
    fontSize: 32,
  },
  categoryDescription: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: Spacing.large,
    marginBottom: Spacing.small,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CategoryScreen;
