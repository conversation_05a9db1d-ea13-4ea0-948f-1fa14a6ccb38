#!/usr/bin/env python3
"""
Test script to simulate the frontend authentication flow
This tests the exact same API calls that the frontend makes
"""
import requests
import json

# API Configuration (same as frontend)
API_BASE_URL = 'http://************:8000/api'

def test_frontend_auth_flow():
    """Test the exact authentication flow used by the frontend"""
    print("🔐 Testing Frontend Authentication Flow")
    print("=" * 50)
    
    # Test credentials (same as available in backend)
    test_credentials = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "remember_me": True
    }
    
    print(f"📧 Testing login with: {test_credentials['email']}")
    print("-" * 30)
    
    # Step 1: Login (same as frontend authService.login)
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers={'Content-Type': 'application/json'},
            json=test_credentials,
            timeout=10
        )
        
        print(f"🌐 Login API Response: {response.status_code}")
        
        if response.status_code == 200:
            auth_data = response.json()
            print("✅ Login successful!")
            print(f"👤 User: {auth_data['user']['first_name']} {auth_data['user']['last_name']}")
            print(f"📧 Email: {auth_data['user']['email']}")
            print(f"🎭 Role: {auth_data['user']['role']}")
            print(f"🔑 Access Token: {auth_data['access'][:50]}...")
            print(f"🔄 Refresh Token: {auth_data['refresh'][:50]}...")
            
            # Step 2: Test token validation (simulate session creation)
            access_token = auth_data['access']
            user_id = auth_data['user']['id']
            
            print("\n🔒 Testing token validation...")
            
            # Test protected endpoint with token
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
            
            profile_response = requests.get(
                f"{API_BASE_URL}/auth/profile/",
                headers=headers,
                timeout=10
            )
            
            if profile_response.status_code == 200:
                print("✅ Token validation successful!")
                print("✅ Session would be created successfully")
                print("✅ AuthGuard would redirect to main app")
                
                return {
                    'success': True,
                    'user': auth_data['user'],
                    'tokens': {
                        'access': auth_data['access'],
                        'refresh': auth_data['refresh']
                    }
                }
            else:
                print(f"❌ Token validation failed: {profile_response.status_code}")
                return {'success': False, 'error': 'Token validation failed'}
                
        else:
            error_data = response.json() if response.content else {}
            print(f"❌ Login failed: {error_data.get('detail', 'Unknown error')}")
            return {'success': False, 'error': error_data.get('detail', 'Login failed')}
            
    except Exception as e:
        print(f"❌ Network error: {e}")
        return {'success': False, 'error': str(e)}

def test_auth_flow_with_different_accounts():
    """Test authentication with different account types"""
    print("\n🧪 Testing Different Account Types")
    print("=" * 50)
    
    test_accounts = [
        ("<EMAIL>", "testpass123", "Customer"),
        ("<EMAIL>", "testpass123", "Provider"),
        ("<EMAIL>", "admin123", "Admin")
    ]
    
    results = []
    
    for email, password, account_type in test_accounts:
        print(f"\n🔍 Testing {account_type} account: {email}")
        
        credentials = {
            "email": email,
            "password": password,
            "remember_me": True
        }
        
        try:
            response = requests.post(
                f"{API_BASE_URL}/auth/login/",
                headers={'Content-Type': 'application/json'},
                json=credentials,
                timeout=10
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                print(f"✅ {account_type} login successful!")
                print(f"   User: {auth_data['user']['first_name']} {auth_data['user']['last_name']}")
                print(f"   Role: {auth_data['user']['role']}")
                results.append({'account_type': account_type, 'success': True})
            else:
                print(f"❌ {account_type} login failed: {response.status_code}")
                results.append({'account_type': account_type, 'success': False})
                
        except Exception as e:
            print(f"❌ {account_type} error: {e}")
            results.append({'account_type': account_type, 'success': False})
    
    return results

if __name__ == "__main__":
    # Test main authentication flow
    main_result = test_frontend_auth_flow()
    
    # Test different account types
    account_results = test_auth_flow_with_different_accounts()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    print(f"Main auth flow: {'✅ PASS' if main_result['success'] else '❌ FAIL'}")
    
    for result in account_results:
        status = '✅ PASS' if result['success'] else '❌ FAIL'
        print(f"{result['account_type']} account: {status}")
    
    if main_result['success']:
        print("\n🎉 Authentication flow is working correctly!")
        print("   Frontend should be able to login and navigate to main app.")
    else:
        print("\n⚠️  Authentication flow has issues that need to be fixed.")
