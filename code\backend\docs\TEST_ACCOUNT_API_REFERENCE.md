# Test Account API Reference

## Overview

This document provides a comprehensive reference for all API endpoints available when using test accounts in the Vierla backend system.

## Authentication

All API requests require authentication using test account credentials.

### Login

**Endpoint:** `POST /api/auth/login/`

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "TestPass123!"
}
```

**Response:**
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Customer",
        "role": "customer",
        "is_verified": true
    }
}
```

### Token Refresh

**Endpoint:** `POST /api/auth/refresh/`

**Request:**
```json
{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Customer API Endpoints

### Browse Services

**Endpoint:** `GET /api/services/`

**Parameters:**
- `category` (optional): Filter by category slug
- `search` (optional): Search in service names and descriptions
- `min_price` (optional): Minimum price filter
- `max_price` (optional): Maximum price filter
- `location` (optional): Filter by location
- `page` (optional): Page number for pagination

**Response:**
```json
{
    "count": 25,
    "next": "http://localhost:8000/api/services/?page=2",
    "previous": null,
    "results": [
        {
            "id": "uuid-here",
            "name": "Haircut & Style",
            "description": "Professional haircut with styling",
            "category": {
                "id": "uuid-here",
                "name": "Hair & Beauty",
                "slug": "hair-beauty"
            },
            "provider": {
                "id": "uuid-here",
                "business_name": "Trendy Cuts & Color Studio",
                "rating": 4.5,
                "review_count": 23
            },
            "base_price": 65.00,
            "price_type": "fixed",
            "duration": 60,
            "is_available": true
        }
    ]
}
```

### Browse Providers

**Endpoint:** `GET /api/providers/`

**Parameters:**
- `category` (optional): Filter by category
- `location` (optional): Filter by location
- `rating_min` (optional): Minimum rating filter
- `verified_only` (optional): Show only verified providers

**Response:**
```json
{
    "count": 15,
    "results": [
        {
            "id": "uuid-here",
            "business_name": "Trendy Cuts & Color Studio",
            "business_description": "Specializing in modern cuts and color",
            "rating": 4.5,
            "review_count": 23,
            "is_verified": true,
            "categories": [
                {
                    "id": "uuid-here",
                    "name": "Hair & Beauty",
                    "slug": "hair-beauty"
                }
            ],
            "location": {
                "address": "123 Queen Street West",
                "city": "Toronto",
                "state": "Ontario"
            }
        }
    ]
}
```

## Provider API Endpoints

### Dashboard Summary

**Endpoint:** `GET /api/provider/services/dashboard_summary/`

**Authentication:** Provider account required

**Response:**
```json
{
    "provider_info": {
        "business_name": "Trendy Cuts & Color Studio",
        "is_verified": true,
        "rating": 4.5,
        "total_bookings": 156
    },
    "service_stats": {
        "total_services": 8,
        "active_services": 6,
        "inactive_services": 2,
        "popular_services": 2,
        "service_limit": null,
        "services_remaining": null
    },
    "revenue_stats": {
        "total_revenue": 12450.00,
        "average_service_price": 78.12
    },
    "recent_services": [
        {
            "id": "uuid-here",
            "name": "Balayage",
            "base_price": 150.00,
            "is_available": true,
            "booking_count": 12
        }
    ],
    "top_services": [
        {
            "id": "uuid-here",
            "name": "Haircut & Style",
            "base_price": 65.00,
            "booking_count": 45
        }
    ]
}
```

### List Provider Services

**Endpoint:** `GET /api/provider/services/`

**Authentication:** Provider account required

**Parameters:**
- `search` (optional): Search in service names
- `category` (optional): Filter by category
- `is_available` (optional): Filter by availability
- `ordering` (optional): Sort order

**Response:**
```json
{
    "count": 8,
    "results": [
        {
            "id": "uuid-here",
            "name": "Haircut & Style",
            "description": "Professional haircut with styling",
            "category": {
                "id": "uuid-here",
                "name": "Hair & Beauty",
                "slug": "hair-beauty"
            },
            "base_price": 65.00,
            "price_type": "fixed",
            "max_price": null,
            "duration": 60,
            "buffer_time": 15,
            "is_available": true,
            "is_active": true,
            "is_popular": false,
            "booking_count": 45,
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-20T14:22:00Z"
        }
    ],
    "provider_summary": {
        "total_services": 8,
        "active_services": 6,
        "inactive_services": 2,
        "is_verified": true,
        "service_limit": null
    }
}
```

### Create Service

**Endpoint:** `POST /api/provider/services/`

**Authentication:** Provider account required

**Request:**
```json
{
    "name": "Deep Conditioning Treatment",
    "description": "Intensive hair treatment for damaged hair",
    "short_description": "Hair repair treatment",
    "category": "uuid-here",
    "base_price": 45.00,
    "price_type": "fixed",
    "duration": 45,
    "buffer_time": 15,
    "requirements": "Clean, dry hair required",
    "preparation_instructions": "Wash hair 24 hours before appointment"
}
```

**Response:**
```json
{
    "id": "uuid-here",
    "name": "Deep Conditioning Treatment",
    "description": "Intensive hair treatment for damaged hair",
    "category": {
        "id": "uuid-here",
        "name": "Hair & Beauty",
        "slug": "hair-beauty"
    },
    "base_price": 45.00,
    "duration": 45,
    "is_available": true,
    "is_active": true,
    "created_at": "2024-01-22T09:15:00Z"
}
```

### Update Service

**Endpoint:** `PUT /api/provider/services/{id}/`

**Authentication:** Provider account required (own services only)

**Request:**
```json
{
    "name": "Deep Conditioning Treatment - Premium",
    "base_price": 55.00,
    "duration": 60,
    "is_available": true
}
```

### Delete Service

**Endpoint:** `DELETE /api/provider/services/{id}/`

**Authentication:** Provider account required (own services only)

**Response:** `204 No Content`

### Toggle Service Status

**Endpoint:** `POST /api/provider/services/{id}/toggle_status/`

**Authentication:** Provider account required

**Response:**
```json
{
    "is_available": false,
    "message": "Service status updated successfully"
}
```

### Bulk Update Services

**Endpoint:** `POST /api/provider/services/bulk_update/`

**Authentication:** Provider account required

**Request:**
```json
{
    "service_ids": ["uuid-1", "uuid-2", "uuid-3"],
    "action": "activate"
}
```

**Actions:** `activate`, `deactivate`, `delete`

**Response:**
```json
{
    "updated_count": 3,
    "message": "Services updated successfully"
}
```

### Duplicate Service

**Endpoint:** `POST /api/provider/services/{id}/duplicate/`

**Authentication:** Provider account required

**Response:**
```json
{
    "id": "new-uuid-here",
    "name": "Haircut & Style (Copy)",
    "description": "Professional haircut with styling",
    "base_price": 65.00,
    "is_available": false,
    "created_at": "2024-01-22T10:30:00Z"
}
```

### Categories Summary

**Endpoint:** `GET /api/provider/services/categories_summary/`

**Authentication:** Provider account required

**Response:**
```json
[
    {
        "category_id": "uuid-here",
        "category_name": "Hair & Beauty",
        "services_count": 6,
        "active_services_count": 5,
        "total_bookings": 123,
        "services": [
            {
                "id": "uuid-here",
                "name": "Haircut & Style",
                "base_price": 65.00,
                "is_available": true,
                "booking_count": 45
            }
        ]
    }
]
```

## Service Categories

### List Categories

**Endpoint:** `GET /api/categories/`

**Response:**
```json
[
    {
        "id": "uuid-here",
        "name": "Hair & Beauty",
        "slug": "hair-beauty",
        "description": "Hair styling and beauty services",
        "is_active": true,
        "is_popular": true,
        "service_count": 25
    }
]
```

## Error Responses

### Validation Errors

**Status:** `400 Bad Request`

```json
{
    "name": ["Service name is required"],
    "base_price": ["Base price must be greater than 0"],
    "duration": ["Duration must be greater than 0 minutes"]
}
```

### Authentication Errors

**Status:** `401 Unauthorized`

```json
{
    "detail": "Authentication credentials were not provided."
}
```

### Permission Errors

**Status:** `403 Forbidden`

```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Not Found Errors

**Status:** `404 Not Found`

```json
{
    "detail": "Not found."
}
```

### Service Limit Errors

**Status:** `403 Forbidden`

```json
{
    "error": "Service limit reached. Verify your account to add more services."
}
```

## Rate Limiting

API endpoints are rate-limited for test accounts:

- **Login attempts:** 10 per hour
- **Service creation:** 20 per hour
- **General API calls:** 1000 per hour

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Testing Examples

### cURL Examples

**Login:**
```bash
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!"}'
```

**Get Dashboard:**
```bash
curl -X GET http://localhost:8000/api/provider/services/dashboard_summary/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Create Service:**
```bash
curl -X POST http://localhost:8000/api/provider/services/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Service",
    "description": "A test service",
    "category": "CATEGORY_UUID",
    "base_price": 50.00,
    "duration": 60
  }'
```

### JavaScript Examples

**Login:**
```javascript
const response = await fetch('http://localhost:8000/api/auth/login/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'TestPass123!'
  })
});

const data = await response.json();
const accessToken = data.access;
```

**Get Services:**
```javascript
const response = await fetch('http://localhost:8000/api/provider/services/', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
  }
});

const services = await response.json();
```

## Postman Collection

A Postman collection is available with pre-configured requests for all test account endpoints:

1. Import the collection from `/docs/postman/vierla-test-accounts.json`
2. Set the environment variables:
   - `base_url`: `http://localhost:8000`
   - `access_token`: (will be set automatically after login)
3. Run the "Login" request first to authenticate
4. Use any other requests with automatic token handling

## Development Tools

### API Browser

Access the browsable API at:
- http://localhost:8000/api/

### Admin Interface

Access test account data via Django admin:
- http://localhost:8000/admin/
- Login with superuser credentials or any test provider account

### API Documentation

Interactive API documentation:
- **Swagger UI:** http://localhost:8000/api/docs/
- **ReDoc:** http://localhost:8000/api/redoc/
