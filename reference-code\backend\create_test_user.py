#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def create_test_user():
    """Create a test user for login testing"""
    try:
        # Check if user already exists
        if User.objects.filter(email='<EMAIL>').exists():
            print("Test user already exists!")
            user = User.objects.get(email='<EMAIL>')
            print(f"Existing user: {user.email} with role: {user.role}")
            return user
        
        # Create new test user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            role='customer'
        )
        print(f"Created test user: {user.email} with role: {user.role}")
        return user
        
    except Exception as e:
        print(f"Error creating test user: {e}")
        return None

if __name__ == '__main__':
    create_test_user()
