#!/usr/bin/env python
"""
Test location-based search functionality for the Service Catalog
"""
import os
import sys
import django
import requests
import json
import pytest

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.catalog.models import ServiceProvider, Service
from apps.catalog.utils import LocationUtils

@pytest.mark.django_db
def test_location_search():
    """Test location-based search functionality"""
    
    print("🌍 Testing Location-Based Search Functionality")
    print("=" * 60)
    
    # Test coordinates (Toronto downtown)
    user_lat = 43.6532
    user_lon = -79.3832
    
    print(f"📍 User location: {user_lat}, {user_lon} (Toronto downtown)")
    
    # Test 1: LocationUtils distance calculation
    print(f"\n🧮 Testing LocationUtils distance calculation...")
    
    providers = ServiceProvider.objects.filter(
        latitude__isnull=False,
        longitude__isnull=False
    )
    
    for provider in providers:
        distance = LocationUtils.calculate_distance(
            user_lat, user_lon,
            float(provider.latitude), float(provider.longitude)
        )
        print(f"   📍 {provider.business_name}: {distance:.2f} km away")
        print(f"      Location: {provider.address}, {provider.city}")
    
    # Test 2: Provider filtering by location
    print(f"\n🔍 Testing provider filtering by location...")
    
    # Test different radius values
    radius_tests = [5, 10, 25, 50]
    
    for radius in radius_tests:
        filtered_providers = LocationUtils.filter_providers_by_location(
            ServiceProvider.objects.all(),
            user_lat, user_lon,
            radius_km=radius
        )
        print(f"   📏 Within {radius}km: {len(filtered_providers)} providers")
        for provider, distance in filtered_providers:
            print(f"      - {provider.business_name}: {distance:.2f}km")
    
    # Test 3: Service filtering by location
    print(f"\n🛍️ Testing service filtering by location...")
    
    filtered_services = LocationUtils.filter_services_by_location(
        Service.objects.all(),
        user_lat, user_lon,
        radius_km=25
    )
    
    print(f"   🎯 Found {len(filtered_services)} services within 25km:")
    for service, distance in filtered_services:
        print(f"      - {service.name} at {service.provider.business_name}: {distance:.2f}km")
        print(f"        Price: ${service.base_price} | Duration: {service.duration}min")
    
    # Test 4: API endpoint testing
    print(f"\n🌐 Testing API endpoints with location parameters...")
    
    base_url = "http://localhost:8000/api/catalog"
    
    # Test providers endpoint with location
    try:
        response = requests.get(f"{base_url}/providers/", params={
            'lat': user_lat,
            'lon': user_lon,
            'radius': 25
        })
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Providers API: {len(data.get('results', []))} results")
        else:
            print(f"   ❌ Providers API error: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print(f"   ⚠️  API server not running - skipping API tests")
    
    # Test 5: Bounding box calculation
    print(f"\n📦 Testing bounding box calculation...")
    
    bbox = LocationUtils.get_bounding_box(user_lat, user_lon, 10)
    print(f"   📍 10km bounding box around Toronto downtown:")
    print(f"      North: {bbox['max_lat']:.4f}")
    print(f"      South: {bbox['min_lat']:.4f}")
    print(f"      East: {bbox['max_lon']:.4f}")
    print(f"      West: {bbox['min_lon']:.4f}")
    
    # Test 6: Travel time estimation
    print(f"\n🚗 Testing travel time estimation...")
    
    test_distances = [1, 5, 10, 25]
    modes = ['driving', 'walking', 'transit']
    
    for distance in test_distances:
        print(f"   📏 {distance}km distance:")
        for mode in modes:
            time = LocationUtils.get_travel_time_estimate(distance, mode)
            print(f"      {mode.capitalize()}: {time} minutes")
    
    print(f"\n🎉 Location-based search testing completed!")
    
    return True

if __name__ == '__main__':
    test_location_search()
