import React from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Text } from './ui/Text';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { colors, spacing, borderRadius, shadows } from '../theme';

const { width: screenWidth } = Dimensions.get('window');

// Types
export interface Service {
  id: string;
  name: string;
  short_description?: string;
  base_price: number;
  price_type: 'fixed' | 'hourly' | 'range' | 'consultation';
  max_price?: number;
  display_price: string;
  duration: number;
  display_duration: string;
  image?: string;
  is_popular: boolean;
  is_available: boolean;
  booking_count: number;
  provider_name: string;
  provider_rating: number;
  provider_city: string;
  category_name: string;
  category_icon: string;
  created_at: string;
}

export interface ServiceCardProps {
  service: Service;
  onPress: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
  variant?: 'default' | 'compact' | 'featured';
  testID?: string;
}



export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onPress,
  onFavorite,
  isFavorite = false,
  variant = 'default',
  testID,
}) => {
  const renderImage = () => {
    if (service.image) {
      return (
        <Image
          source={{ uri: service.image }}
          style={styles.image}
          testID={`${testID}-image`}
        />
      );
    }
    
    return (
      <View style={styles.placeholderImage} testID={`${testID}-placeholder`}>
        <Ionicons
          name="image-outline"
          size={32}
          color={colors.text.secondary}
        />
      </View>
    );
  };

  const renderFavoriteButton = () => {
    if (!onFavorite) return null;

    return (
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={onFavorite}
        testID={`${testID}-favorite-button`}
      >
        <Ionicons
          name={isFavorite ? 'heart' : 'heart-outline'}
          size={20}
          color={isFavorite ? colors.error : colors.text.secondary}
        />
      </TouchableOpacity>
    );
  };

  const renderPopularBadge = () => {
    if (!service.is_popular) return null;

    return (
      <Badge
        variant="default"
        size="sm"
        style={styles.popularBadge}
        testID={`${testID}-popular-badge`}
      >
        Popular
      </Badge>
    );
  };

  const renderRating = () => {
    return (
      <View style={styles.ratingContainer} testID={`${testID}-rating`}>
        <Ionicons name="star" size={14} color={colors.accent} />
        <Text style={styles.ratingText}>
          {service.provider_rating.toFixed(1)}
        </Text>
      </View>
    );
  };

  const getCardStyle = () => {
    const baseStyle = {};
    switch (variant) {
      case 'compact':
        return [baseStyle, styles.compactCard];
      case 'featured':
        return [baseStyle, styles.featuredCard];
      default:
        return baseStyle;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      testID={testID}
      activeOpacity={0.7}
    >
      <Card style={getCardStyle()}>
        <View style={styles.imageContainer}>
          {renderImage()}
          {renderFavoriteButton()}
          {renderPopularBadge()}
        </View>

        <CardContent style={styles.content}>
          <Text variant="h4" numberOfLines={2} testID={`${testID}-name`}>
            {service.name}
          </Text>

          {service.short_description && (
            <Text
              variant="body"
              style={styles.description}
              numberOfLines={2}
              testID={`${testID}-description`}
            >
              {service.short_description}
            </Text>
          )}

          <View style={styles.providerInfo} testID={`${testID}-provider-info`}>
            <Text variant="body" style={styles.providerName} numberOfLines={1}>
              {service.provider_name}
            </Text>
            {renderRating()}
            <Text variant="caption" style={styles.providerCity} numberOfLines={1}>
              • {service.provider_city}
            </Text>
          </View>

          <View style={styles.serviceDetails} testID={`${testID}-details`}>
            <Text variant="h4" style={styles.price} testID={`${testID}-price`}>
              {service.display_price}
            </Text>
            <Text variant="caption" style={styles.duration} testID={`${testID}-duration`}>
              {service.display_duration}
            </Text>
          </View>

          {!service.is_available && (
            <Badge
              variant="destructive"
              size="sm"
              style={styles.unavailableBadge}
              testID={`${testID}-unavailable`}
            >
              Currently Unavailable
            </Badge>
          )}
        </CardContent>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  compactCard: {
    marginBottom: spacing.sm,
  },
  featuredCard: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  imageContainer: {
    position: 'relative',
    height: 160,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.background.light,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.background.secondary,
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.sm,
  },
  popularBadge: {
    position: 'absolute',
    top: spacing.sm,
    left: spacing.sm,
  },
  content: {
    padding: spacing.md,
  },
  description: {
    marginBottom: spacing.sm,
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  providerName: {
    fontWeight: '500',
    flex: 1,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: spacing.sm,
  },
  ratingText: {
    marginLeft: 2,
  },
  providerCity: {
    // Styles handled by Text component
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontWeight: '700',
    color: colors.primary,
  },
  duration: {
    // Styles handled by Text component
  },
  unavailableBadge: {
    marginTop: spacing.sm,
    alignSelf: 'flex-start',
  },
});

export default ServiceCard;
