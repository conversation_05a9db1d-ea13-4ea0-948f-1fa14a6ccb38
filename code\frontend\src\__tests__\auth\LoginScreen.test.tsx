/**
 * LoginScreen Component Tests
 * Tests for login screen functionality and user interactions
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '../setup/testUtils';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { LoginScreen } from '../../screens/auth/LoginScreen';
import { authAPI } from '../../services/api/auth';

// Mock the auth API
jest.mock('../../services/api/auth');
const mockedAuthAPI = authAPI as jest.Mocked<typeof authAPI>;

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock AsyncStorage
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  replace: jest.fn(),
  goBack: jest.fn(),
  reset: jest.fn(),
};

// Mock useMutation specifically for this test
const mockMutation = {
  mutate: jest.fn(),
  mutateAsync: jest.fn(),
  isPending: false,
  isError: false,
  isSuccess: false,
  error: null,
  data: null,
  reset: jest.fn(),
};

jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useMutation: jest.fn(() => mockMutation),
}));

// Use centralized test utilities
import { TestWrapper, createTestQueryClient } from '../setup/testUtils';

describe('LoginScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.multiSet.mockResolvedValue();
    mockAsyncStorage.getItem.mockResolvedValue(null);
  });

  const renderLoginScreen = () => {
    return render(
      <TestWrapper>
        <LoginScreen navigation={mockNavigation} />
      </TestWrapper>
    );
  };

  describe('Rendering', () => {
    it('should render login form elements', () => {
      const { root } = renderLoginScreen();

      // Just check that the component renders without throwing an error
      expect(root).toBeTruthy();
    });

    it('should render social login buttons', () => {
      renderLoginScreen();

      expect(screen.getByTestId('social-buttons-container')).toBeTruthy();
    });

    it('should render sign up link', () => {
      renderLoginScreen();

      expect(screen.getByText("Don't have an account?")).toBeTruthy();
      expect(screen.getByText('Sign Up')).toBeTruthy();
    });
  });

  describe('Form Validation', () => {
    it('should show error when email is empty', async () => {
      renderLoginScreen();

      const signInButton = screen.getByText('Sign In');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeTruthy();
      });
    });

    it('should show error when email is invalid', async () => {
      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, 'invalid-email');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeTruthy();
      });
    });

    it('should show error when password is empty', async () => {
      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.getByText('Password is required')).toBeTruthy();
      });
    });

    it('should not show errors when form is valid', async () => {
      mockedAuthAPI.login.mockResolvedValue({
        access: 'access-token',
        refresh: 'refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      });

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(screen.queryByText('Email is required')).toBeNull();
        expect(screen.queryByText('Password is required')).toBeNull();
      });
    });
  });

  describe('Login Functionality', () => {
    it('should call login API with correct credentials', async () => {
      mockedAuthAPI.login.mockResolvedValue({
        access: 'access-token',
        refresh: 'refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      });

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(mockedAuthAPI.login).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });

    it('should store tokens and user data on successful login', async () => {
      const mockResponse = {
        access: 'access-token',
        refresh: 'refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer' as const,
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      };

      mockedAuthAPI.login.mockResolvedValue(mockResponse);

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(mockAsyncStorage.multiSet).toHaveBeenCalledWith([
          ['access_token', 'access-token'],
          ['refresh_token', 'refresh-token'],
          ['user', JSON.stringify(mockResponse.user)],
        ]);
      });
    });

    it('should navigate to main app on successful login', async () => {
      mockedAuthAPI.login.mockResolvedValue({
        access: 'access-token',
        refresh: 'refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      });

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(mockNavigation.replace).toHaveBeenCalledWith('Main');
      });
    });
  });

  describe('Error Handling', () => {
    it('should show alert for 400 error with detail message', async () => {
      const mockError = {
        response: {
          status: 400,
          data: { detail: 'Invalid credentials' },
        },
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'wrongpassword');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Login Failed', 'Invalid credentials');
      });
    });

    it('should show alert for 423 account locked error', async () => {
      const mockError = {
        response: {
          status: 423,
          data: {},
        },
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Account Locked',
          'Your account is temporarily locked due to multiple failed login attempts.'
        );
      });
    });

    it('should show generic error for unexpected errors', async () => {
      const mockError = {
        response: {
          status: 500,
          data: {},
        },
      };

      mockedAuthAPI.login.mockRejectedValue(mockError);

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith(
          'Error',
          'An unexpected error occurred. Please try again.'
        );
      });
    });

    it('should handle AsyncStorage errors', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
      mockAsyncStorage.multiSet.mockRejectedValue(new Error('Storage error'));

      mockedAuthAPI.login.mockResolvedValue({
        access: 'access-token',
        refresh: 'refresh-token',
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          role: 'customer',
          is_verified: true,
          account_status: 'active',
          created_at: '2025-08-06T00:00:00Z',
        },
      });

      renderLoginScreen();

      const emailInput = screen.getByPlaceholderText('Enter your email');
      const passwordInput = screen.getByPlaceholderText('Enter your password');
      const signInButton = screen.getByText('Sign In');

      fireEvent.changeText(emailInput, '<EMAIL>');
      fireEvent.changeText(passwordInput, 'password123');
      fireEvent.press(signInButton);

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to save login information');
      });

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Navigation', () => {
    it('should navigate to forgot password screen', () => {
      renderLoginScreen();

      const forgotPasswordButton = screen.getByText('Forgot Password?');
      fireEvent.press(forgotPasswordButton);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('ForgotPassword');
    });

    it('should navigate to register screen', () => {
      renderLoginScreen();

      const signUpLink = screen.getByText('Sign Up');
      fireEvent.press(signUpLink);

      expect(mockNavigation.navigate).toHaveBeenCalledWith('Register');
    });
  });

  describe('Social Authentication', () => {
    it('should show Google Sign-In placeholder', async () => {
      renderLoginScreen();

      // Note: This test would need to be updated when actual Google Sign-In is implemented
      // For now, it tests the placeholder functionality
    });

    it('should show Apple Sign-In placeholder', async () => {
      renderLoginScreen();

      // Note: This test would need to be updated when actual Apple Sign-In is implemented
      // For now, it tests the placeholder functionality
    });
  });
});
