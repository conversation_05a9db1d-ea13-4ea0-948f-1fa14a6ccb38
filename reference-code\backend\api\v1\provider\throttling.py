"""
Provider API Throttling - Enhanced based on Backend Agent feedback
Role-based API throttling for service provider endpoints
"""

from rest_framework.throttling import UserRateThrottle
from django.core.cache import cache
from django.utils import timezone


class ProviderAPIThrottle(UserRateThrottle):
    """
    Throttling class for provider API endpoints
    Business-focused rate limits
    """
    scope = 'provider_api'
    
    def get_cache_key(self, request, view):
        """Generate cache key for provider throttling"""
        if request.user and request.user.is_authenticated:
            ident = f"provider_{request.user.id}"
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }


class ProviderBookingThrottle(UserRateThrottle):
    """
    Specialized throttling for provider booking operations
    More generous limits for business operations
    """
    scope = 'provider_booking'
    
    def allow_request(self, request, view):
        """Custom throttling logic for provider bookings"""
        # Different rates based on operation
        action = getattr(view, 'action', None)
        
        if action in ['confirm', 'complete', 'cancel']:
            self.rate = '100/hour'  # 100 booking actions per hour
        else:
            self.rate = '200/hour'  # 200 reads per hour
        
        return super().allow_request(request, view)


class ProviderAnalyticsThrottle(UserRateThrottle):
    """
    Throttling for analytics operations
    Moderate limits for business intelligence
    """
    scope = 'provider_analytics'
    rate = '30/minute'  # 30 analytics requests per minute


class ProviderStoreThrottle(UserRateThrottle):
    """
    Throttling for store management operations
    """
    scope = 'provider_store'
    rate = '20/minute'  # 20 store operations per minute


class AdaptiveProviderThrottle(UserRateThrottle):
    """
    Adaptive throttling based on provider verification and behavior
    Verified providers get higher limits
    """
    scope = 'adaptive_provider'
    
    def get_rate(self):
        """Get adaptive rate based on provider status"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return '20/hour'  # Default for unauthenticated
        
        user = self.request.user
        
        # Check if user has provider profile
        if not hasattr(user, 'service_provider'):
            return '50/hour'  # Basic rate for non-providers
        
        provider = user.service_provider
        
        # Base rate calculation
        base_rate = 100  # Base requests per hour
        
        # Verification bonus
        if provider.is_verified:
            base_rate += 100  # Verified providers get double
        
        # Business age bonus
        if hasattr(provider, 'created_at'):
            business_age_days = (timezone.now() - provider.created_at).days
            if business_age_days > 365:  # 1+ years
                base_rate += 50
            elif business_age_days > 90:  # 3+ months
                base_rate += 25
        
        # Performance bonus
        cache_key = f"provider_performance_{provider.id}"
        performance_data = cache.get(cache_key, {})
        
        # High-performing providers get bonus
        avg_rating = performance_data.get('avg_rating', 0)
        if avg_rating >= 4.5:
            base_rate += 50
        elif avg_rating >= 4.0:
            base_rate += 25
        
        # Booking volume bonus
        monthly_bookings = performance_data.get('monthly_bookings', 0)
        if monthly_bookings > 100:
            base_rate += 100
        elif monthly_bookings > 50:
            base_rate += 50
        elif monthly_bookings > 20:
            base_rate += 25
        
        # Penalties for violations
        violations = performance_data.get('recent_violations', 0)
        if violations > 0:
            base_rate = max(50, base_rate - (violations * 25))
        
        return f'{base_rate}/hour'
    
    def allow_request(self, request, view):
        """Check if request should be allowed with adaptive rate"""
        self.request = request
        self.rate = self.get_rate()
        return super().allow_request(request, view)


class BusinessHoursThrottle(UserRateThrottle):
    """
    Throttling that adjusts based on business hours
    Higher limits during business hours
    """
    scope = 'business_hours_provider'
    
    def get_rate(self):
        """Get rate based on current time and business hours"""
        current_hour = timezone.now().hour
        
        # Business hours (9 AM - 6 PM) get higher limits
        if 9 <= current_hour <= 18:
            return '300/hour'  # Business hours rate
        else:
            return '150/hour'  # Off-hours rate
    
    def allow_request(self, request, view):
        """Apply business hours throttling"""
        self.rate = self.get_rate()
        return super().allow_request(request, view)


class PremiumProviderThrottle(UserRateThrottle):
    """
    Enhanced throttling for premium/enterprise providers
    """
    scope = 'premium_provider'
    
    def get_rate(self):
        """Get rate based on provider tier"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return '20/hour'
        
        user = self.request.user
        
        if not hasattr(user, 'service_provider'):
            return '50/hour'
        
        provider = user.service_provider
        
        # Check subscription tier
        if hasattr(provider, 'subscription'):
            subscription = provider.subscription
            if subscription.tier == 'enterprise':
                return '1000/hour'  # Enterprise rate
            elif subscription.tier == 'premium':
                return '500/hour'   # Premium rate
            elif subscription.tier == 'pro':
                return '300/hour'   # Pro rate
        
        # Default for verified providers
        if provider.is_verified:
            return '200/hour'
        else:
            return '100/hour'
    
    def allow_request(self, request, view):
        """Apply premium provider throttling"""
        self.request = request
        self.rate = self.get_rate()
        return super().allow_request(request, view)


class ProviderOperationThrottle(UserRateThrottle):
    """
    Operation-specific throttling for different provider actions
    """
    scope = 'provider_operation'
    
    def allow_request(self, request, view):
        """Apply operation-specific throttling"""
        action = getattr(view, 'action', None)
        
        # Different rates for different operations
        if action in ['create', 'update', 'partial_update']:
            self.rate = '50/hour'   # Write operations
        elif action in ['destroy', 'cancel']:
            self.rate = '20/hour'   # Destructive operations
        elif action in ['list', 'retrieve']:
            self.rate = '200/hour'  # Read operations
        else:
            self.rate = '100/hour'  # Default rate
        
        return super().allow_request(request, view)
