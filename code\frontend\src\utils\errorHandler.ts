/**
 * Enhanced Error Handling Utility
 * Provides consistent error handling and user-friendly error messages
 */

import { AxiosError } from 'axios';

export interface EnhancedError extends AxiosError {
  isNetworkError?: boolean;
  isServerError?: boolean;
  isClientError?: boolean;
  isAuthError?: boolean;
  isAccountLocked?: boolean;
  isRateLimited?: boolean;
  errorCode?: string;
  retryAfter?: number;
}

export interface ErrorInfo {
  title: string;
  message: string;
  actionable: boolean;
  retryable: boolean;
  retryAfter?: number;
  errorCode?: string;
}

/**
 * Parse API error and return user-friendly error information
 */
export const parseApiError = (error: EnhancedError): ErrorInfo => {
  // Network errors
  if (error.isNetworkError) {
    return {
      title: 'Connection Error',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      actionable: true,
      retryable: true,
    };
  }

  // Server errors (5xx)
  if (error.isServerError) {
    return {
      title: 'Server Error',
      message: 'The server is currently experiencing issues. Please try again in a few minutes.',
      actionable: true,
      retryable: true,
    };
  }

  // Account locked (423)
  if (error.isAccountLocked) {
    const retryAfter = error.retryAfter || 1800; // Default 30 minutes
    const minutes = Math.ceil(retryAfter / 60);
    return {
      title: 'Account Temporarily Locked',
      message: `Your account has been temporarily locked due to multiple failed login attempts. Please try again in ${minutes} minutes.`,
      actionable: false,
      retryable: true,
      retryAfter,
      errorCode: 'ACCOUNT_LOCKED',
    };
  }

  // Rate limited (429)
  if (error.isRateLimited) {
    return {
      title: 'Too Many Attempts',
      message: 'You have made too many requests. Please wait a moment before trying again.',
      actionable: false,
      retryable: true,
      retryAfter: error.retryAfter || 60,
    };
  }

  // Authentication errors (401, 403)
  if (error.isAuthError) {
    if (error.response?.status === 401) {
      return {
        title: 'Session Expired',
        message: 'Your session has expired. Please sign in again.',
        actionable: true,
        retryable: false,
      };
    } else {
      return {
        title: 'Access Denied',
        message: 'You do not have permission to perform this action.',
        actionable: false,
        retryable: false,
      };
    }
  }

  // Client errors (4xx)
  if (error.isClientError) {
    const errorData = error.response?.data;
    
    // Handle validation errors
    if (error.response?.status === 400) {
      if (errorData?.detail) {
        return {
          title: 'Invalid Request',
          message: errorData.detail,
          actionable: true,
          retryable: false,
          errorCode: errorData.error_code,
        };
      } else if (errorData && typeof errorData === 'object') {
        // Handle field-specific validation errors
        const fieldErrors = Object.entries(errorData)
          .map(([field, messages]) => {
            if (Array.isArray(messages)) {
              return `${field}: ${messages.join(', ')}`;
            }
            return `${field}: ${messages}`;
          })
          .join('\n');
        
        return {
          title: 'Validation Error',
          message: fieldErrors,
          actionable: true,
          retryable: false,
        };
      }
    }

    return {
      title: 'Request Error',
      message: errorData?.detail || 'There was an error with your request. Please check your input and try again.',
      actionable: true,
      retryable: false,
      errorCode: errorData?.error_code,
    };
  }

  // Default error
  return {
    title: 'Unexpected Error',
    message: 'An unexpected error occurred. Please try again.',
    actionable: true,
    retryable: true,
  };
};

/**
 * Get user-friendly error message for login-specific errors
 */
export const getLoginErrorMessage = (error: EnhancedError): string => {
  const errorInfo = parseApiError(error);
  
  // Special handling for login errors
  if (error.response?.status === 400) {
    const errorData = error.response.data;
    if (errorData?.detail === 'Invalid credentials') {
      return 'The email or password you entered is incorrect. Please check your credentials and try again.';
    }
  }
  
  return errorInfo.message;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: EnhancedError): boolean => {
  const errorInfo = parseApiError(error);
  return errorInfo.retryable;
};

/**
 * Get retry delay in seconds
 */
export const getRetryDelay = (error: EnhancedError): number => {
  const errorInfo = parseApiError(error);
  return errorInfo.retryAfter || 5; // Default 5 seconds
};

/**
 * Format error for logging
 */
export const formatErrorForLogging = (error: EnhancedError, context?: string): string => {
  const timestamp = new Date().toISOString();
  const contextStr = context ? ` [${context}]` : '';
  
  if (error.isNetworkError) {
    return `${timestamp}${contextStr} Network Error: ${error.message}`;
  }
  
  if (error.response) {
    const { status, statusText, data } = error.response;
    const errorCode = data?.error_code ? ` (${data.error_code})` : '';
    return `${timestamp}${contextStr} HTTP ${status} ${statusText}${errorCode}: ${data?.detail || error.message}`;
  }
  
  return `${timestamp}${contextStr} Unknown Error: ${error.message}`;
};

/**
 * Check if error indicates invalid credentials
 */
export const isInvalidCredentialsError = (error: EnhancedError): boolean => {
  return (
    error.response?.status === 400 &&
    error.response?.data?.detail === 'Invalid credentials'
  );
};

/**
 * Check if error indicates account lockout
 */
export const isAccountLockedError = (error: EnhancedError): boolean => {
  return error.isAccountLocked || error.response?.status === 423;
};

/**
 * Check if error indicates rate limiting
 */
export const isRateLimitedError = (error: EnhancedError): boolean => {
  return error.isRateLimited || error.response?.status === 429;
};

/**
 * Extract error details for display
 */
export const extractErrorDetails = (error: EnhancedError): {
  title: string;
  message: string;
  details?: any;
} => {
  const errorInfo = parseApiError(error);
  
  return {
    title: errorInfo.title,
    message: errorInfo.message,
    details: error.response?.data,
  };
};
