#!/usr/bin/env python
"""
Comprehensive test script for the Booking System
Tests all booking functionality including state machine, notifications, and API endpoints
"""
from apps.authentication.models import User
from apps.catalog.models import ServiceProvider, Service, ServiceCategory
from apps.bookings.models import Booking, BookingStateChange, TimeSlot, BookingNotification
from django.utils import timezone as django_timezone
import os
import sys
import django
import requests
import json
from datetime import datetime, timedelta, time
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()


def test_booking_system():
    """Comprehensive test of the booking system"""

    print("🎯 Testing Vierla Booking System")
    print("=" * 60)

    # Test 1: Database Models and Relationships
    print(f"\n📊 Test 1: Database Models and Relationships")
    print("-" * 40)

    try:
        # Check existing data
        users = User.objects.count()
        providers = ServiceProvider.objects.count()
        services = Service.objects.count()
        bookings = Booking.objects.count()

        print(f"✅ Users: {users}")
        print(f"✅ Providers: {providers}")
        print(f"✅ Services: {services}")
        print(f"✅ Existing Bookings: {bookings}")

        # Get test data
        customer = User.objects.filter(role='customer').first()
        provider_obj = ServiceProvider.objects.first()
        service = Service.objects.first()

        if not customer or not provider_obj or not service:
            print("❌ Missing test data. Please run create_services.py first.")
            return False

        print(f"✅ Test customer: {customer.email}")
        print(f"✅ Test provider: {provider_obj.business_name}")
        print(f"✅ Test service: {service.name}")

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

    # Test 2: Create a Test Booking
    print(f"\n📝 Test 2: Create Test Booking")
    print("-" * 40)

    try:
        # Create a booking for tomorrow
        scheduled_time = django_timezone.now() + timedelta(days=1)

        booking = Booking.objects.create(
            customer=customer,
            provider=provider_obj,
            service=service,
            scheduled_datetime=scheduled_time,
            duration_minutes=service.duration,
            base_price=service.base_price,
            total_amount=service.base_price,
            location_type=Booking.LocationType.SALON,
            customer_notes="Test booking for system validation"
        )

        print(f"✅ Created booking: {booking.booking_number}")
        print(f"✅ Status: {booking.status}")
        print(f"✅ Scheduled: {booking.scheduled_datetime}")
        print(f"✅ Total: ${booking.total_amount}")
        print(f"✅ Duration: {booking.get_duration_display()}")

    except Exception as e:
        print(f"❌ Booking creation failed: {e}")
        return False

    # Test 3: Booking State Machine
    print(f"\n🔄 Test 3: Booking State Machine")
    print("-" * 40)

    try:
        # Test state transitions
        print(f"Initial state: {booking.status}")

        # Test confirmation
        if booking.can_be_confirmed():
            booking.confirm_booking(confirmed_by=provider_obj.user)
            print(f"✅ Confirmed booking: {booking.status}")
        else:
            print(f"❌ Cannot confirm booking in state: {booking.status}")

        # Test starting
        if booking.can_be_started():
            booking.start_booking(started_by=provider_obj.user)
            print(f"✅ Started booking: {booking.status}")
        else:
            print(f"❌ Cannot start booking in state: {booking.status}")

        # Test completion
        if booking.can_be_completed():
            booking.complete_booking(completed_by=provider_obj.user)
            print(f"✅ Completed booking: {booking.status}")
        else:
            print(f"❌ Cannot complete booking in state: {booking.status}")

        # Check state changes
        state_changes = BookingStateChange.objects.filter(
            booking=booking).count()
        print(f"✅ State changes recorded: {state_changes}")

    except Exception as e:
        print(f"❌ State machine test failed: {e}")
        return False

    # Test 4: Time Slots
    print(f"\n⏰ Test 4: Time Slots Management")
    print("-" * 40)

    try:
        # Create test time slots
        tomorrow = (django_timezone.now() + timedelta(days=1)).date()

        time_slot = TimeSlot.objects.create(
            provider=provider_obj,
            service=service,
            date=tomorrow,
            start_time=time(9, 0),
            end_time=time(10, 0),
            is_available=True,
            max_bookings=1,
            current_bookings=0
        )

        print(f"✅ Created time slot: {time_slot}")
        print(f"✅ Available: {time_slot.is_available}")
        print(f"✅ Can be booked: {time_slot.can_be_booked()}")
        print(f"✅ Available spots: {time_slot.get_available_spots()}")

        # Test booking the slot
        if time_slot.can_be_booked():
            time_slot.book_slot()
            print(
                f"✅ Booked slot - Current bookings: {time_slot.current_bookings}")
            print(f"✅ Still available: {time_slot.can_be_booked()}")

    except Exception as e:
        print(f"❌ Time slots test failed: {e}")
        return False

    # Test 5: Notifications
    print(f"\n🔔 Test 5: Booking Notifications")
    print("-" * 40)

    try:
        # Create test notification
        notification = BookingNotification.objects.create(
            booking=booking,
            recipient=customer,
            notification_type=BookingNotification.NotificationType.BOOKING_COMPLETED,
            channel=BookingNotification.NotificationChannel.PUSH,
            title="Service Completed",
            message=f"Your {service.name} service has been completed successfully!"
        )

        print(f"✅ Created notification: {notification.title}")
        print(f"✅ Type: {notification.get_notification_type_display()}")
        print(f"✅ Channel: {notification.get_channel_display()}")
        print(f"✅ Status: {notification.get_status_display()}")

        # Test notification state changes
        notification.mark_as_sent(external_id="test-123")
        print(f"✅ Marked as sent: {notification.status}")

        notification.mark_as_read()
        print(f"✅ Marked as read: {notification.status}")

    except Exception as e:
        print(f"❌ Notifications test failed: {e}")
        return False

    # Test 6: API Endpoints (if server is running)
    print(f"\n🌐 Test 6: API Endpoints")
    print("-" * 40)

    try:
        base_url = "http://localhost:8000/api"

        # Test public endpoints
        response = requests.get(f"{base_url}/catalog/services/")
        if response.status_code == 200:
            services_data = response.json()
            print(
                f"✅ Services API: {len(services_data.get('results', []))} services")
        else:
            print(f"⚠️  Services API: {response.status_code}")

        # Test providers endpoint
        response = requests.get(f"{base_url}/catalog/providers/")
        if response.status_code == 200:
            providers_data = response.json()
            print(
                f"✅ Providers API: {len(providers_data.get('results', []))} providers")
        else:
            print(f"⚠️  Providers API: {response.status_code}")

        # Note: Booking endpoints require authentication
        print(f"ℹ️  Booking endpoints require authentication (tested separately)")

    except requests.exceptions.ConnectionError:
        print(f"⚠️  API server not accessible - skipping API tests")
    except Exception as e:
        print(f"❌ API test failed: {e}")

    # Test 7: Business Logic Validation
    print(f"\n💼 Test 7: Business Logic Validation")
    print("-" * 40)

    try:
        # Test booking validation
        past_booking_time = django_timezone.now() - timedelta(hours=1)

        try:
            invalid_booking = Booking(
                customer=customer,
                provider=provider_obj,
                service=service,
                scheduled_datetime=past_booking_time,
                duration_minutes=service.duration,
                base_price=service.base_price,
                total_amount=service.base_price
            )
            # This should be caught by validation in the serializer/view
            print(f"⚠️  Past booking validation should be handled in API layer")
        except Exception:
            print(f"✅ Past booking validation working")

        # Test cancellation deadline
        booking.scheduled_datetime = django_timezone.now(
        ) + timedelta(hours=12)  # 12 hours from now
        booking.save()

        can_cancel = booking.can_be_cancelled()
        deadline = booking.get_cancellation_deadline()
        print(f"✅ Can cancel (12h notice): {can_cancel}")
        print(f"✅ Cancellation deadline: {deadline}")

        # Test with short notice
        booking.scheduled_datetime = django_timezone.now(
        ) + timedelta(hours=12)  # 12 hours from now
        booking.save()

        can_cancel_short = booking.can_be_cancelled()
        print(f"✅ Can cancel (12h notice): {can_cancel_short}")

    except Exception as e:
        print(f"❌ Business logic test failed: {e}")
        return False

    # Test 8: Performance and Optimization
    print(f"\n⚡ Test 8: Performance and Optimization")
    print("-" * 40)

    try:
        # Test database queries optimization
        from django.db import connection
        from django.test.utils import override_settings

        # Reset query count
        connection.queries_log.clear()

        # Test optimized booking query
        bookings_with_relations = Booking.objects.select_related(
            'customer', 'provider', 'service'
        ).prefetch_related('state_changes')[:5]

        for booking in bookings_with_relations:
            _ = booking.customer.get_full_name()
            _ = booking.provider.business_name
            _ = booking.service.name
            _ = list(booking.state_changes.all())

        query_count = len(connection.queries)
        print(
            f"✅ Optimized queries: {query_count} queries for 5 bookings with relations")

        # Test mobile optimization fields
        mobile_optimized = Booking.objects.filter(
            is_mobile_optimized=True).count()
        print(f"✅ Mobile optimized bookings: {mobile_optimized}")

    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

    # Summary
    print(f"\n🎉 Test Summary")
    print("=" * 60)
    print(f"✅ Database Models: Working")
    print(f"✅ Booking Creation: Working")
    print(f"✅ State Machine: Working")
    print(f"✅ Time Slots: Working")
    print(f"✅ Notifications: Working")
    print(f"✅ Business Logic: Working")
    print(f"✅ Performance: Optimized")

    print(f"\n📊 Final Statistics:")
    print(f"   Total Bookings: {Booking.objects.count()}")
    print(f"   Total State Changes: {BookingStateChange.objects.count()}")
    print(f"   Total Time Slots: {TimeSlot.objects.count()}")
    print(f"   Total Notifications: {BookingNotification.objects.count()}")

    print(f"\n🎯 Booking System Test: PASSED ✅")
    return True


if __name__ == '__main__':
    success = test_booking_system()
    sys.exit(0 if success else 1)
