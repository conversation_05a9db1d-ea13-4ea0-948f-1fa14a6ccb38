/**
 * Theme Configuration
 * Basic theme constants for the Vierla application
 */

// Vierla Brand Colors
export const colors = {
  // Primary brand colors
  primary: '#364035', // Forest Green
  primaryLight: '#8B9A8C', // Sage Green
  primaryDark: '#2D2A26', // Charcoal
  
  // Background colors
  background: {
    primary: '#F4F1E8', // Cream
    secondary: '#FFFFFF', // White
    light: '#F9FAFB',
  },
  
  // Text colors
  text: {
    primary: '#2D2A26', // Charcoal
    secondary: '#364035', // Forest Green
    tertiary: '#8B9A8C', // Sage Green
  },
  
  // Utility colors
  white: '#FFFFFF',
  black: '#000000',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Additional colors
  accent: '#B8956A', // Gold
  taupe: '#C9BEB0', // Taupe
};

// Spacing system
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Typography system
export const typography = {
  fontFamily: {
    primary: 'System',
    mono: 'monospace',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// Border radius
export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};

// Shadows
export const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

// Export default theme object
export const theme = {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
};

export default theme;
