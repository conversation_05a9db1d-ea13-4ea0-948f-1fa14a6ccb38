"""
Unit tests for Bookings app
"""
import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.bookings.models import Booking, BookingStateChange
from apps.bookings.serializers import BookingListSerializer, BookingDetailSerializer, BookingCreateSerializer


@pytest.mark.unit
@pytest.mark.bookings
class TestBookingModel:
    """Test Booking model functionality"""

    @pytest.mark.django_db
    def test_create_booking_success(self, customer_user, service_provider, service):
        """Test creating a booking"""
        scheduled_time = timezone.now() + timedelta(days=1)
        
        booking = Booking.objects.create(
            customer=customer_user,
            provider=service_provider,
            service=service,
            scheduled_datetime=scheduled_time,
            duration_minutes=60,
            base_price=service.base_price,
            total_amount=service.base_price,
            status='pending',
            location_type='salon',
            customer_notes='Test booking notes'
        )
        
        assert booking.customer == customer_user
        assert booking.provider == service_provider
        assert booking.service == service
        assert booking.scheduled_datetime == scheduled_time
        assert booking.duration_minutes == 60
        assert booking.base_price == service.base_price
        assert booking.total_amount == service.base_price
        assert booking.status == 'pending'
        assert booking.location_type == 'salon'
        assert booking.customer_notes == 'Test booking notes'

    @pytest.mark.django_db
    def test_booking_string_representation(self, booking):
        """Test booking string representation"""
        expected = f"Booking {booking.booking_number} - {booking.customer.get_full_name()} with {booking.provider.business_name}"
        assert str(booking) == expected

    @pytest.mark.django_db
    def test_booking_status_choices(self, customer_user, service_provider, service):
        """Test booking status validation"""
        scheduled_time = timezone.now() + timedelta(days=1)
        
        # Valid statuses
        valid_statuses = ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled']
        
        for status in valid_statuses:
            booking = Booking.objects.create(
                customer=customer_user,
                provider=service_provider,
                service=service,
                scheduled_datetime=scheduled_time,
                duration_minutes=60,
                base_price=Decimal('50.00'),
                total_amount=Decimal('50.00'),
                status=status,
                location_type='salon'
            )
            assert booking.status == status

    @pytest.mark.django_db
    def test_booking_location_type_choices(self, customer_user, service_provider, service):
        """Test booking location type validation"""
        scheduled_time = timezone.now() + timedelta(days=1)
        
        # Valid location types
        valid_locations = ['salon', 'mobile', 'customer_location']
        
        for location_type in valid_locations:
            booking = Booking.objects.create(
                customer=customer_user,
                provider=service_provider,
                service=service,
                scheduled_datetime=scheduled_time,
                duration_minutes=60,
                base_price=Decimal('50.00'),
                total_amount=Decimal('50.00'),
                status='pending',
                location_type=location_type
            )
            assert booking.location_type == location_type

    @pytest.mark.django_db
    def test_booking_price_calculation(self, customer_user, service_provider, service):
        """Test booking price calculations"""
        scheduled_time = timezone.now() + timedelta(days=1)
        
        booking = Booking.objects.create(
            customer=customer_user,
            provider=service_provider,
            service=service,
            scheduled_datetime=scheduled_time,
            duration_minutes=60,
            base_price=Decimal('50.00'),
            additional_charges=Decimal('5.00'),
            tax_amount=Decimal('7.50'),
            total_amount=Decimal('62.50'),
            status='pending',
            location_type='salon'
        )

        assert booking.base_price == Decimal('50.00')
        assert booking.additional_charges == Decimal('5.00')
        assert booking.tax_amount == Decimal('7.50')
        assert booking.total_amount == Decimal('62.50')

    @pytest.mark.django_db
    def test_booking_end_datetime_property(self, booking):
        """Test booking end datetime calculation"""
        expected_end = booking.scheduled_datetime + timedelta(minutes=booking.duration_minutes)
        assert booking.end_datetime == expected_end

    @pytest.mark.django_db
    def test_booking_is_upcoming_property(self, customer_user, service_provider, service):
        """Test booking is_upcoming property"""
        # Past booking
        past_time = timezone.now() - timedelta(hours=1)
        past_booking = Booking.objects.create(
            customer=customer_user,
            provider=service_provider,
            service=service,
            scheduled_datetime=past_time,
            duration_minutes=60,
            base_price=Decimal('50.00'),
            total_amount=Decimal('50.00'),
            status='completed',
            location_type='salon'
        )
        assert past_booking.is_upcoming() is False

        # Future booking
        future_time = timezone.now() + timedelta(hours=1)
        future_booking = Booking.objects.create(
            customer=customer_user,
            provider=service_provider,
            service=service,
            scheduled_datetime=future_time,
            duration_minutes=60,
            base_price=Decimal('50.00'),
            total_amount=Decimal('50.00'),
            status='pending',
            location_type='salon'
        )
        assert future_booking.is_upcoming() is True


@pytest.mark.unit
@pytest.mark.bookings
class TestBookingStateChangeModel:
    """Test BookingStateChange model functionality"""

    @pytest.mark.django_db
    def test_create_state_change(self, booking):
        """Test creating booking state change"""
        state_change = BookingStateChange.objects.create(
            booking=booking,
            from_status='pending',
            to_status='confirmed',
            changed_by=booking.customer,
            notes='Customer confirmed the booking'
        )

        assert state_change.booking == booking
        assert state_change.from_status == 'pending'
        assert state_change.to_status == 'confirmed'
        assert state_change.changed_by == booking.customer
        assert state_change.notes == 'Customer confirmed the booking'
        assert state_change.created_at is not None

    @pytest.mark.django_db
    def test_state_change_string_representation(self, booking):
        """Test state change string representation"""
        state_change = BookingStateChange.objects.create(
            booking=booking,
            from_status='pending',
            to_status='confirmed',
            changed_by=booking.customer
        )

        expected = f"{booking.booking_number}: pending → confirmed"
        assert str(state_change) == expected





@pytest.mark.unit
@pytest.mark.bookings
class TestBookingSerializers:
    """Test booking serializers"""

    @pytest.mark.django_db
    def test_booking_list_serializer(self, booking):
        """Test booking list serializer"""
        serializer = BookingListSerializer(booking)
        data = serializer.data

        assert data['customer_name'] == booking.customer.get_full_name()
        assert data['provider_name'] == booking.provider.business_name
        assert data['service_name'] == booking.service.name
        assert data['duration_minutes'] == booking.duration_minutes
        assert Decimal(data['total_amount']) == booking.total_amount
        assert data['status'] == booking.status
        assert data['location_type'] == booking.location_type



    @pytest.mark.django_db
    def test_booking_create_serializer(self, customer_user, service_provider, service):
        """Test creating booking via serializer"""
        scheduled_time = timezone.now() + timedelta(days=1)

        data = {
            'provider_id': str(service_provider.id),
            'service_id': str(service.id),
            'scheduled_datetime': scheduled_time.isoformat(),
            'duration_minutes': 60,
            'location_type': 'salon',
            'customer_notes': 'Test booking via serializer'
        }

        serializer = BookingCreateSerializer(data=data, context={'request': type('obj', (object,), {'user': customer_user})()})
        assert serializer.is_valid(), serializer.errors

        booking = serializer.save()
        assert booking.customer == customer_user
        assert booking.provider == service_provider
        assert booking.service == service
        assert booking.duration_minutes == 60
        assert booking.location_type == 'salon'
        assert booking.customer_notes == 'Test booking via serializer'
