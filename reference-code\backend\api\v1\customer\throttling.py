"""
Customer API Throttling - Enhanced based on Backend Agent feedback
Role-based API throttling for customer endpoints
"""

from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from django.core.cache import cache
from django.utils import timezone
import hashlib


class CustomerAPIThrottle(UserRateThrottle):
    """
    Throttling class for customer API endpoints
    More generous limits for authenticated customers
    """
    scope = 'customer_api'
    
    def get_cache_key(self, request, view):
        """Generate cache key for customer throttling"""
        if request.user and request.user.is_authenticated:
            ident = f"customer_{request.user.id}"
        else:
            ident = self.get_ident(request)
        
        return self.cache_format % {
            'scope': self.scope,
            'ident': ident
        }


class CustomerBookingThrottle(UserRateThrottle):
    """
    Specialized throttling for booking operations
    Stricter limits to prevent spam bookings
    """
    scope = 'customer_booking'
    
    def allow_request(self, request, view):
        """Custom throttling logic for bookings"""
        # More restrictive for booking creation
        if request.method == 'POST':
            self.rate = '10/hour'  # 10 bookings per hour
        else:
            self.rate = '100/hour'  # 100 reads per hour
        
        return super().allow_request(request, view)


class CustomerSearchThrottle(UserRateThrottle):
    """
    Throttling for search operations
    Balanced limits for search functionality
    """
    scope = 'customer_search'
    rate = '60/minute'  # 60 searches per minute


class CustomerFavoriteThrottle(UserRateThrottle):
    """
    Throttling for favorite operations
    Moderate limits for favorite management
    """
    scope = 'customer_favorite'
    rate = '30/minute'  # 30 favorite operations per minute


class AdaptiveCustomerThrottle(UserRateThrottle):
    """
    Adaptive throttling based on customer behavior
    Adjusts limits based on user activity patterns
    """
    scope = 'adaptive_customer'
    
    def get_rate(self):
        """Get adaptive rate based on user behavior"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return '20/hour'  # Default for unauthenticated
        
        user = self.request.user
        
        # Check user's historical behavior
        cache_key = f"user_behavior_{user.id}"
        behavior_data = cache.get(cache_key, {})
        
        # Factors that increase rate limits
        trust_score = 0
        
        # Account age (older accounts get higher limits)
        if hasattr(user, 'date_joined'):
            account_age_days = (timezone.now() - user.date_joined).days
            if account_age_days > 365:  # 1+ years
                trust_score += 3
            elif account_age_days > 90:  # 3+ months
                trust_score += 2
            elif account_age_days > 30:  # 1+ month
                trust_score += 1
        
        # Email verification
        if getattr(user, 'email_verified', False):
            trust_score += 2
        
        # Phone verification
        if getattr(user, 'phone_verified', False):
            trust_score += 1
        
        # Successful booking history
        successful_bookings = behavior_data.get('successful_bookings', 0)
        if successful_bookings > 50:
            trust_score += 3
        elif successful_bookings > 20:
            trust_score += 2
        elif successful_bookings > 5:
            trust_score += 1
        
        # No recent violations
        recent_violations = behavior_data.get('recent_violations', 0)
        if recent_violations == 0:
            trust_score += 1
        else:
            trust_score -= recent_violations
        
        # Determine rate based on trust score
        if trust_score >= 8:
            return '200/hour'  # High trust
        elif trust_score >= 5:
            return '150/hour'  # Medium trust
        elif trust_score >= 2:
            return '100/hour'  # Low trust
        else:
            return '50/hour'   # Very low trust
    
    def allow_request(self, request, view):
        """Check if request should be allowed with adaptive rate"""
        self.request = request
        self.rate = self.get_rate()
        return super().allow_request(request, view)


class BurstCustomerThrottle(UserRateThrottle):
    """
    Burst throttling for handling traffic spikes
    Allows short bursts but maintains overall limits
    """
    scope = 'burst_customer'
    
    def __init__(self):
        super().__init__()
        self.burst_rate = '20/minute'  # Burst allowance
        self.sustained_rate = '100/hour'  # Sustained rate
    
    def allow_request(self, request, view):
        """Check both burst and sustained rates"""
        # Check burst rate first
        self.rate = self.burst_rate
        burst_allowed = super().allow_request(request, view)
        
        if not burst_allowed:
            return False
        
        # Check sustained rate
        self.rate = self.sustained_rate
        self.scope = f"{self.scope}_sustained"
        sustained_allowed = super().allow_request(request, view)
        
        return sustained_allowed


class PremiumCustomerThrottle(UserRateThrottle):
    """
    Enhanced throttling for premium customers
    Higher limits for premium account holders
    """
    scope = 'premium_customer'
    
    def get_rate(self):
        """Get rate based on customer tier"""
        if not hasattr(self.request, 'user') or not self.request.user.is_authenticated:
            return '20/hour'
        
        user = self.request.user
        
        # Check if user has premium features
        if hasattr(user, 'subscription') and user.subscription.is_premium:
            return '500/hour'  # Premium rate
        elif hasattr(user, 'is_verified') and user.is_verified:
            return '200/hour'  # Verified user rate
        else:
            return '100/hour'  # Standard rate
    
    def allow_request(self, request, view):
        """Apply premium throttling logic"""
        self.request = request
        self.rate = self.get_rate()
        return super().allow_request(request, view)
