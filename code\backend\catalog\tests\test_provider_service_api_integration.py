"""
Comprehensive Integration Tests for Provider Service Management API

This test suite covers all CRUD operations and business logic for the provider
service management endpoints, ensuring proper authentication, authorization,
validation, and data integrity.

Test Coverage:
- Service creation with validation
- Service listing with filtering and pagination
- Service retrieval with ownership validation
- Service updates with partial and full updates
- Service deletion (soft delete)
- Bulk operations
- Permission and authorization checks
- Error handling and edge cases
"""

import pytest
import json
from decimal import Decimal
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from catalog.models import ServiceProvider, ServiceCategory, Service
from catalog.factories import ProviderFactory, ServiceFactory

User = get_user_model()


@pytest.mark.django_db
class TestProviderServiceAPIIntegration(TestCase):
    """Integration tests for Provider Service Management API"""

    def setUp(self):
        """Set up test data for each test"""
        self.client = APIClient()
        
        # Create test users
        self.provider_user = User.objects.create_user(
            username='provider1',
            email='<EMAIL>',
            password='testpass123',
            first_name='John',
            last_name='Doe',
            role='service_provider'
        )

        self.other_provider_user = User.objects.create_user(
            username='provider2',
            email='<EMAIL>',
            password='testpass123',
            first_name='Jane',
            last_name='Smith',
            role='service_provider'
        )

        self.customer_user = User.objects.create_user(
            username='customer1',
            email='<EMAIL>',
            password='testpass123',
            role='customer'
        )
        
        # Create service categories
        self.hair_category = ServiceCategory.objects.create(
            name='Hair Services',
            slug='hair-services',
            description='Professional hair styling and cutting services',
            is_active=True
        )
        
        self.beauty_category = ServiceCategory.objects.create(
            name='Beauty Services',
            slug='beauty-services',
            description='Beauty and skincare services',
            is_active=True
        )
        
        # Create service providers
        self.provider = ServiceProvider.objects.create(
            user=self.provider_user,
            business_name='Elite Hair Studio',
            business_description='Premium hair styling services',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='123 Main St, City, State 12345',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A8',
            is_verified=True,
            is_active=True
        )

        self.other_provider = ServiceProvider.objects.create(
            user=self.other_provider_user,
            business_name='Beauty Boutique',
            business_description='Full-service beauty salon',
            business_phone='+**********',
            business_email='<EMAIL>',
            address='456 Oak Ave, City, State 12345',
            city='Toronto',
            state='Ontario',
            zip_code='M5V 3A9',
            is_verified=True,
            is_active=True
        )
        
        # Create test services
        self.service1 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Premium Haircut',
            description='Professional haircut with styling',
            short_description='Premium cut & style',
            base_price=Decimal('75.00'),
            price_type='fixed',
            duration=90,
            buffer_time=15,
            is_active=True,
            is_available=True
        )
        
        self.service2 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Hair Coloring',
            description='Professional hair coloring service',
            short_description='Hair color treatment',
            base_price=Decimal('120.00'),
            price_type='fixed',
            duration=180,
            buffer_time=30,
            is_active=True,
            is_available=True
        )
        
        # Service from other provider
        self.other_service = Service.objects.create(
            provider=self.other_provider,
            category=self.beauty_category,
            name='Facial Treatment',
            description='Relaxing facial treatment',
            base_price=Decimal('85.00'),
            duration=60,
            is_active=True,
            is_available=True
        )
        
        # API endpoints
        self.services_url = reverse('provider:provider-service-list')
        self.service_detail_url = lambda pk: reverse('provider:provider-service-detail', kwargs={'pk': pk})

    def authenticate_provider(self, user=None):
        """Authenticate as provider user"""
        if user is None:
            user = self.provider_user
        self.client.force_authenticate(user=user)

    def authenticate_customer(self):
        """Authenticate as customer user"""
        self.client.force_authenticate(user=self.customer_user)

    def test_list_services_authenticated_provider(self):
        """Test listing services for authenticated provider"""
        self.authenticate_provider()
        
        response = self.client.get(self.services_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 2)  # Only provider's services
        
        # Verify service data structure
        service_data = response.data['results'][0]
        expected_fields = [
            'id', 'name', 'short_description', 'base_price', 'price_type',
            'duration', 'is_available', 'booking_count', 'provider_name',
            'provider_rating', 'provider_city', 'category_name', 'created_at'
        ]
        for field in expected_fields:
            self.assertIn(field, service_data)

    def test_list_services_unauthenticated(self):
        """Test that unauthenticated users cannot access provider services"""
        response = self.client.get(self.services_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_list_services_customer_user(self):
        """Test that customer users cannot access provider services"""
        self.authenticate_customer()
        response = self.client.get(self.services_url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_service_success(self):
        """Test successful service creation"""
        self.authenticate_provider()
        
        service_data = {
            'category': self.hair_category.id,
            'name': 'Beard Trim',
            'description': 'Professional beard trimming and styling',
            'short_description': 'Beard trim & style',
            'base_price': '35.00',
            'price_type': 'fixed',
            'duration': 30,
            'buffer_time': 10,
            'requirements': 'Please arrive with clean, dry beard',
            'is_active': True,
            'is_available': True
        }
        
        response = self.client.post(self.services_url, service_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Beard Trim')
        self.assertEqual(Decimal(response.data['base_price']), Decimal('35.00'))
        self.assertEqual(response.data['duration'], 30)
        # Note: Create response doesn't include provider info, only service data
        
        # Verify service was created in database by name (since id not in response)
        created_service = Service.objects.get(name='Beard Trim', provider=self.provider)
        self.assertEqual(created_service.provider, self.provider)
        self.assertEqual(created_service.name, 'Beard Trim')

    def test_create_service_validation_errors(self):
        """Test service creation with validation errors"""
        self.authenticate_provider()
        
        # Test missing required fields
        invalid_data = {
            'name': '',  # Empty name
            'description': '',  # Empty description
            'base_price': '-10.00',  # Negative price
            'duration': 0,  # Zero duration
        }
        
        response = self.client.post(self.services_url, invalid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('name', response.data)
        self.assertIn('description', response.data)
        self.assertIn('base_price', response.data)
        self.assertIn('duration', response.data)

    def test_retrieve_service_success(self):
        """Test retrieving a specific service"""
        self.authenticate_provider()
        
        response = self.client.get(self.service_detail_url(self.service1.id))
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(self.service1.id))
        self.assertEqual(response.data['name'], self.service1.name)
        self.assertEqual(Decimal(response.data['base_price']), self.service1.base_price)

    def test_retrieve_service_ownership_validation(self):
        """Test that providers can only retrieve their own services"""
        self.authenticate_provider()
        
        # Try to access other provider's service
        response = self.client.get(self.service_detail_url(self.other_service.id))
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_service_success(self):
        """Test successful service update"""
        self.authenticate_provider()
        
        update_data = {
            'name': 'Premium Haircut & Style',
            'description': 'Premium haircut with advanced styling techniques',
            'base_price': '85.00',
            'duration': 105,
            'is_active': True
        }
        
        response = self.client.patch(self.service_detail_url(self.service1.id), update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Premium Haircut & Style')
        self.assertEqual(Decimal(response.data['base_price']), Decimal('85.00'))
        self.assertEqual(response.data['duration'], 105)
        
        # Verify database was updated
        self.service1.refresh_from_db()
        self.assertEqual(self.service1.name, 'Premium Haircut & Style')
        self.assertEqual(self.service1.base_price, Decimal('85.00'))

    def test_update_service_ownership_validation(self):
        """Test that providers can only update their own services"""
        self.authenticate_provider()
        
        update_data = {'name': 'Unauthorized Update'}
        
        response = self.client.patch(self.service_detail_url(self.other_service.id), update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Verify other service was not modified
        self.other_service.refresh_from_db()
        self.assertNotEqual(self.other_service.name, 'Unauthorized Update')

    def test_delete_service_success(self):
        """Test successful service deletion (soft delete)"""
        self.authenticate_provider()
        
        response = self.client.delete(self.service_detail_url(self.service1.id))
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify service was soft deleted
        self.service1.refresh_from_db()
        self.assertFalse(self.service1.is_active)

    def test_delete_service_ownership_validation(self):
        """Test that providers can only delete their own services"""
        self.authenticate_provider()
        
        response = self.client.delete(self.service_detail_url(self.other_service.id))
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Verify other service was not deleted
        self.other_service.refresh_from_db()
        self.assertTrue(self.other_service.is_active)

    def test_service_filtering(self):
        """Test service filtering capabilities"""
        self.authenticate_provider()
        
        # Test filtering by category
        response = self.client.get(self.services_url, {'category': self.hair_category.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        
        # Test filtering by active status
        response = self.client.get(self.services_url, {'is_active': True})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        
        # Test search by name
        response = self.client.get(self.services_url, {'search': 'Premium'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Premium Haircut')

    def test_service_ordering(self):
        """Test service ordering capabilities"""
        self.authenticate_provider()
        
        # Test ordering by price (ascending)
        response = self.client.get(self.services_url, {'ordering': 'base_price'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data['results']
        self.assertEqual(len(results), 2)
        self.assertLessEqual(Decimal(results[0]['base_price']), Decimal(results[1]['base_price']))
        
        # Test ordering by price (descending)
        response = self.client.get(self.services_url, {'ordering': '-base_price'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data['results']
        self.assertGreaterEqual(Decimal(results[0]['base_price']), Decimal(results[1]['base_price']))

    def test_pagination(self):
        """Test API pagination"""
        self.authenticate_provider()
        
        # Create additional services to test pagination
        for i in range(8):
            Service.objects.create(
                provider=self.provider,
                category=self.hair_category,
                name=f'Test Service {i}',
                description=f'Test service description {i}',
                base_price=Decimal('50.00'),
                duration=60
            )
        
        # Test first page
        response = self.client.get(self.services_url, {'page': 1, 'page_size': 5})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 5)
        self.assertIsNotNone(response.data['next'])
        self.assertIsNone(response.data['previous'])
        
        # Test second page
        response = self.client.get(self.services_url, {'page': 2, 'page_size': 5})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 5)
        self.assertIsNotNone(response.data['previous'])

    def test_service_metrics_in_response(self):
        """Test that service metrics are included in API responses"""
        self.authenticate_provider()
        
        response = self.client.get(self.service_detail_url(self.service1.id))
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('booking_count', response.data)
        self.assertEqual(response.data['booking_count'], 0)  # No bookings yet

    def test_concurrent_service_updates(self):
        """Test handling of concurrent service updates"""
        self.authenticate_provider()
        
        # Simulate concurrent updates by making multiple requests
        update_data1 = {'name': 'Updated Name 1', 'is_active': True}
        update_data2 = {'description': 'Updated Description 2', 'is_active': True}
        
        response1 = self.client.patch(self.service_detail_url(self.service1.id), update_data1, format='json')
        response2 = self.client.patch(self.service_detail_url(self.service1.id), update_data2, format='json')
        
        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        
        # Verify final state
        self.service1.refresh_from_db()
        self.assertEqual(self.service1.name, 'Updated Name 1')
        self.assertIn('Updated Description 2', self.service1.description)

    def test_service_category_validation(self):
        """Test service category validation"""
        self.authenticate_provider()
        
        # Test with invalid category ID
        service_data = {
            'category': 99999,  # Non-existent category
            'name': 'Test Service',
            'description': 'Test description',
            'base_price': '50.00',
            'duration': 60
        }
        
        response = self.client.post(self.services_url, service_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('category', response.data)

    def test_service_price_validation(self):
        """Test service price validation"""
        self.authenticate_provider()
        
        # Test with invalid price formats
        invalid_prices = ['-10.00', '0.00', 'invalid', '999999.99']
        
        for invalid_price in invalid_prices:
            service_data = {
                'category': self.hair_category.id,
                'name': 'Test Service',
                'description': 'Test description',
                'base_price': invalid_price,
                'duration': 60
            }
            
            response = self.client.post(self.services_url, service_data, format='json')
            
            if invalid_price in ['-10.00', '0.00']:
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('base_price', response.data)

    def test_service_duration_validation(self):
        """Test service duration validation"""
        self.authenticate_provider()
        
        # Test with invalid durations
        invalid_durations = [0, -30, 'invalid']
        
        for invalid_duration in invalid_durations:
            service_data = {
                'category': self.hair_category.id,
                'name': 'Test Service',
                'description': 'Test description',
                'base_price': '50.00',
                'duration': invalid_duration
            }
            
            response = self.client.post(self.services_url, service_data, format='json')
            
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('duration', response.data)

    def test_bulk_service_operations(self):
        """Test bulk service operations"""
        self.authenticate_provider()

        # Create additional services for bulk testing
        service3 = Service.objects.create(
            provider=self.provider,
            category=self.hair_category,
            name='Shampoo & Blow Dry',
            description='Hair washing and blow drying service',
            base_price=Decimal('45.00'),
            duration=45,
            is_active=True
        )

        # Test bulk activation/deactivation
        bulk_data = {
            'service_ids': [str(self.service1.id), str(service3.id)],
            'action': 'deactivate'
        }

        # Note: This assumes bulk endpoint exists - may need to be implemented
        bulk_url = reverse('provider:provider-service-bulk-update')
        response = self.client.post(bulk_url, bulk_data, format='json')

        # If bulk endpoint doesn't exist yet, this test will help identify the need
        if response.status_code == status.HTTP_404_NOT_FOUND:
            self.skipTest("Bulk operations endpoint not yet implemented")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify services were deactivated
        self.service1.refresh_from_db()
        service3.refresh_from_db()
        self.assertFalse(self.service1.is_active)
        self.assertFalse(service3.is_active)

    def test_service_analytics_data(self):
        """Test that service analytics data is properly calculated"""
        self.authenticate_provider()

        response = self.client.get(self.services_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check if provider summary is included
        if 'provider_summary' in response.data:
            summary = response.data['provider_summary']
            self.assertIn('total_services', summary)
            self.assertIn('active_services', summary)
            self.assertIn('inactive_services', summary)
            self.assertEqual(summary['total_services'], 2)
            self.assertEqual(summary['active_services'], 2)
            self.assertEqual(summary['inactive_services'], 0)

    def test_service_image_handling(self):
        """Test service image upload and handling"""
        self.authenticate_provider()

        # Test creating service with image
        service_data = {
            'category': self.hair_category.id,
            'name': 'Hair Styling with Photo',
            'description': 'Professional hair styling with portfolio photo',
            'base_price': '65.00',
            'duration': 75,
            # Note: Image upload testing would require multipart form data
            # This is a placeholder for when image handling is implemented
        }

        response = self.client.post(self.services_url, service_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Additional image-related assertions would go here

    def test_service_availability_management(self):
        """Test service availability management"""
        self.authenticate_provider()

        # Test toggling service availability
        update_data = {'is_available': False, 'is_active': True}

        response = self.client.patch(self.service_detail_url(self.service1.id), update_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['is_available'])

        # Verify database update
        self.service1.refresh_from_db()
        self.assertFalse(self.service1.is_available)

    def test_service_requirements_and_instructions(self):
        """Test service requirements and preparation instructions"""
        self.authenticate_provider()

        service_data = {
            'category': self.hair_category.id,
            'name': 'Chemical Hair Treatment',
            'description': 'Professional chemical hair treatment',
            'base_price': '150.00',
            'duration': 240,
            'requirements': 'Hair must be unwashed for 48 hours before treatment',
            'preparation_instructions': 'Please arrive with dry hair and no styling products'
        }

        response = self.client.post(self.services_url, service_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['requirements'], service_data['requirements'])
        self.assertEqual(response.data['preparation_instructions'], service_data['preparation_instructions'])

    def test_service_price_type_validation(self):
        """Test different price types and validation"""
        self.authenticate_provider()

        # Test range pricing
        service_data = {
            'category': self.hair_category.id,
            'name': 'Custom Hair Service',
            'description': 'Custom hair service with variable pricing',
            'base_price': '50.00',
            'price_type': 'range',
            'max_price': '150.00',
            'duration': 120
        }

        response = self.client.post(self.services_url, service_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['price_type'], 'range')
        self.assertEqual(Decimal(response.data['max_price']), Decimal('150.00'))

    def test_service_buffer_time_handling(self):
        """Test service buffer time functionality"""
        self.authenticate_provider()

        service_data = {
            'category': self.hair_category.id,
            'name': 'Quick Touch-up',
            'description': 'Quick hair touch-up service',
            'base_price': '25.00',
            'duration': 20,
            'buffer_time': 10
        }

        response = self.client.post(self.services_url, service_data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['buffer_time'], 10)

        # Test calculated total duration
        if 'total_duration_with_buffer' in response.data:
            self.assertEqual(response.data['total_duration_with_buffer'], 30)

    def test_error_handling_and_edge_cases(self):
        """Test error handling and edge cases"""
        self.authenticate_provider()

        # Test extremely long service name
        long_name = 'A' * 300  # Assuming max length is 255
        service_data = {
            'category': self.hair_category.id,
            'name': long_name,
            'description': 'Test description',
            'base_price': '50.00',
            'duration': 60
        }

        response = self.client.post(self.services_url, service_data, format='json')

        if response.status_code == status.HTTP_400_BAD_REQUEST:
            self.assertIn('name', response.data)

        # Test service with inactive category
        self.hair_category.is_active = False
        self.hair_category.save()

        service_data = {
            'category': self.hair_category.id,
            'name': 'Test Service',
            'description': 'Test description',
            'base_price': '50.00',
            'duration': 60
        }

        response = self.client.post(self.services_url, service_data, format='json')

        # Should either reject or handle gracefully
        if response.status_code == status.HTTP_400_BAD_REQUEST:
            self.assertIn('category', response.data)

        # Restore category for other tests
        self.hair_category.is_active = True
        self.hair_category.save()

    def test_service_search_functionality(self):
        """Test advanced search functionality"""
        self.authenticate_provider()

        # Test search by description
        response = self.client.get(self.services_url, {'search': 'Professional'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreater(len(response.data['results']), 0)

        # Test search with no results
        response = self.client.get(self.services_url, {'search': 'NonexistentService'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)

    def test_service_performance_metrics(self):
        """Test service performance and metrics"""
        self.authenticate_provider()

        # Test that response includes performance metrics
        response = self.client.get(self.service_detail_url(self.service1.id))

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Check for performance-related fields
        expected_metric_fields = ['booking_count', 'created_at', 'updated_at']
        for field in expected_metric_fields:
            self.assertIn(field, response.data)

    def tearDown(self):
        """Clean up after each test"""
        # Clear authentication
        self.client.force_authenticate(user=None)
