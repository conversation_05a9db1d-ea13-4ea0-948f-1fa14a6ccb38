"""
<PERSON><PERSON><PERSON> Backend - Development Settings
"""
from .base import *

# Development specific settings
DEBUG = True

# Allow connections from network IP for mobile development
ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '************', '********']

# Database for development (SQLite for quick setup, PostgreSQL for full testing)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Development middleware - TODO: Add debug toolbar after installing django-debug-toolbar
# MIDDLEWARE += [
#     'debug_toolbar.middleware.DebugToolbarMiddleware',
# ]

# Debug toolbar configuration
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Celery configuration for development
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True

# Development-friendly throttle rates
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '1000/hour',  # Much higher for development
    'user': '10000/hour',
    'mobile': '2000/hour',
    'login': '100/minute',  # Much higher for testing
    'register': '50/minute',
    'password_reset': '30/hour',
    'customer_api': '10000/hour',  # Customer API throttle rate
    'provider_api': '10000/hour',  # Provider API throttle rate
    # Additional customer throttle scopes
    'customer_booking': '10000/hour',
    'customer_search': '10000/hour',
    'customer_favorite': '10000/hour',
    'adaptive_customer': '10000/hour',
    'burst_customer': '10000/hour',
    'premium_customer': '10000/hour',
    # Additional provider throttle scopes
    'provider_booking': '10000/hour',
    'provider_service': '10000/hour',
    'provider_analytics': '10000/hour',
    'provider_store': '10000/hour',
    'adaptive_provider': '10000/hour',
    'provider_operation': '10000/hour',
}

# Disable HTTPS requirements in development
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = None

# Development logging
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['django']['level'] = 'DEBUG'
LOGGING['loggers']['apps']['level'] = 'DEBUG'

# Development cache (use local memory)
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    },
    'sessions': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Mobile optimization disabled for development
MOBILE_OPTIMIZATION['BATTERY_AWARE_PROCESSING'] = False
MOBILE_OPTIMIZATION['NETWORK_ADAPTATION'] = False
