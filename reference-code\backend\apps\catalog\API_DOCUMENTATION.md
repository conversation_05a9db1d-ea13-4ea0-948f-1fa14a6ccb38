# Service Catalog API Documentation

## Overview

The Service Catalog API provides comprehensive endpoints for managing and discovering services, providers, and categories in the Vierla platform. All endpoints follow RESTful conventions and return JSON responses.

## Base URL

```
https://api.vierla.com/api/v1/catalog/
```

## Authentication

All API endpoints require authentication using JWT tokens:

```http
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### Categories

#### List Categories
```http
GET /categories/
```

**Query Parameters:**
- `parent`: Filter by parent category ID
- `is_popular`: Filter popular categories (true/false)
- `is_active`: Filter active categories (true/false)
- `search`: Search by name or description

**Response:**
```json
{
  "count": 25,
  "next": "https://api.vierla.com/api/v1/catalog/categories/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Beauty & Wellness",
      "slug": "beauty-wellness",
      "description": "Professional beauty and wellness services",
      "parent": null,
      "icon": "💄",
      "mobile_icon": "💄",
      "color": "#FF69B4",
      "image": "https://api.vierla.com/media/categories/beauty.jpg",
      "is_active": true,
      "is_popular": true,
      "sort_order": 1,
      "level": 0,
      "service_count": 156,
      "subcategories": [
        {
          "id": 2,
          "name": "Hair Services",
          "slug": "hair-services",
          "service_count": 89
        }
      ],
      "created_at": "2025-06-17T10:00:00Z",
      "updated_at": "2025-06-17T15:30:00Z"
    }
  ]
}
```

#### Get Category Details
```http
GET /categories/{id}/
```

**Response:**
```json
{
  "id": 1,
  "name": "Beauty & Wellness",
  "slug": "beauty-wellness",
  "description": "Professional beauty and wellness services",
  "parent": null,
  "icon": "💄",
  "mobile_icon": "💄",
  "color": "#FF69B4",
  "image": "https://api.vierla.com/media/categories/beauty.jpg",
  "is_active": true,
  "is_popular": true,
  "sort_order": 1,
  "level": 0,
  "service_count": 156,
  "subcategories": [...],
  "services": [...],
  "created_at": "2025-06-17T10:00:00Z",
  "updated_at": "2025-06-17T15:30:00Z"
}
```

### Service Providers

#### List Providers
```http
GET /providers/
```

**Query Parameters:**
- `category`: Filter by category ID
- `city`: Filter by city
- `state`: Filter by state
- `is_verified`: Filter verified providers (true/false)
- `is_featured`: Filter featured providers (true/false)
- `latitude`: User latitude for distance calculation
- `longitude`: User longitude for distance calculation
- `radius`: Search radius in kilometers (default: 10)
- `min_rating`: Minimum rating filter
- `search`: Search by business name or description

**Response:**
```json
{
  "count": 45,
  "next": "https://api.vierla.com/api/v1/catalog/providers/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "user": {
        "id": 123,
        "email": "<EMAIL>",
        "first_name": "Beauty",
        "last_name": "Salon"
      },
      "business_name": "Elite Beauty Salon",
      "business_description": "Premium beauty services in downtown",
      "business_phone": "+**********",
      "business_email": "<EMAIL>",
      "address": "123 Main St",
      "city": "New York",
      "state": "NY",
      "zip_code": "10001",
      "country": "USA",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "website": "https://elitebeauty.com",
      "instagram_handle": "elitebeauty",
      "facebook_url": "https://facebook.com/elitebeauty",
      "profile_image": "https://api.vierla.com/media/providers/elite_profile.jpg",
      "cover_image": "https://api.vierla.com/media/providers/elite_cover.jpg",
      "categories": [
        {
          "id": 1,
          "name": "Beauty & Wellness",
          "icon": "💄"
        }
      ],
      "is_active": true,
      "is_verified": true,
      "is_featured": true,
      "mobile_optimized": true,
      "rating": 4.8,
      "review_count": 127,
      "total_bookings": 1543,
      "years_of_experience": 8,
      "distance_km": 2.3,
      "service_count": 12,
      "operating_hours": [...],
      "gallery": [...],
      "created_at": "2025-06-17T10:00:00Z",
      "updated_at": "2025-06-17T15:30:00Z"
    }
  ]
}
```

#### Get Provider Details
```http
GET /providers/{id}/
```

**Query Parameters:**
- `latitude`: User latitude for distance calculation
- `longitude`: User longitude for distance calculation

**Response:**
```json
{
  "id": 1,
  "user": {...},
  "business_name": "Elite Beauty Salon",
  "business_description": "Premium beauty services...",
  "contact_info": {...},
  "location": {...},
  "social_media": {...},
  "media": {...},
  "categories": [...],
  "status": {...},
  "statistics": {...},
  "services": [
    {
      "id": 1,
      "name": "Professional Haircut",
      "base_price": 50.00,
      "duration": 60,
      "is_available": true
    }
  ],
  "operating_hours": [
    {
      "day": 1,
      "day_name": "Monday",
      "is_open": true,
      "open_time": "09:00:00",
      "close_time": "18:00:00",
      "break_start": "12:00:00",
      "break_end": "13:00:00"
    }
  ],
  "gallery": [...],
  "distance_km": 2.3,
  "created_at": "2025-06-17T10:00:00Z",
  "updated_at": "2025-06-17T15:30:00Z"
}
```

### Services

#### List Services
```http
GET /services/
```

**Query Parameters:**
- `category`: Filter by category ID
- `provider`: Filter by provider ID
- `min_price`: Minimum price filter
- `max_price`: Maximum price filter
- `duration_min`: Minimum duration in minutes
- `duration_max`: Maximum duration in minutes
- `is_available`: Filter available services (true/false)
- `is_popular`: Filter popular services (true/false)
- `latitude`: User latitude for distance calculation
- `longitude`: User longitude for distance calculation
- `radius`: Search radius in kilometers
- `search`: Search by name or description

**Response:**
```json
{
  "count": 234,
  "next": "https://api.vierla.com/api/v1/catalog/services/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "provider": {
        "id": 1,
        "business_name": "Elite Beauty Salon",
        "rating": 4.8,
        "is_verified": true,
        "distance_km": 2.3
      },
      "category": {
        "id": 2,
        "name": "Hair Services",
        "icon": "💇"
      },
      "name": "Professional Haircut",
      "description": "Expert haircut with professional styling",
      "short_description": "Professional haircut and styling",
      "mobile_description": "Expert cut & style",
      "pricing": {
        "base_price": 50.00,
        "price_type": "fixed",
        "max_price": null,
        "display_price": "$50.00"
      },
      "duration": 60,
      "buffer_time": 15,
      "display_duration": "1 hour",
      "requirements": "Please arrive with clean, dry hair",
      "preparation_instructions": "No special preparation needed",
      "image": "https://api.vierla.com/media/services/haircut.jpg",
      "is_active": true,
      "is_available": true,
      "is_popular": true,
      "booking_count": 89,
      "availability": {
        "availability_type": "scheduled",
        "min_advance_booking": 2,
        "max_advance_booking": 30,
        "instant_booking": false,
        "weekend_available": true
      },
      "location": {
        "location_type": "provider_location",
        "travel_radius": null,
        "travel_fee": null
      },
      "created_at": "2025-06-17T10:00:00Z",
      "updated_at": "2025-06-17T15:30:00Z"
    }
  ]
}
```

#### Get Service Details
```http
GET /services/{id}/
```

**Response:**
```json
{
  "id": 1,
  "provider": {...},
  "category": {...},
  "name": "Professional Haircut",
  "description": "Expert haircut with professional styling and consultation",
  "short_description": "Professional haircut and styling",
  "mobile_description": "Expert cut & style",
  "pricing": {...},
  "duration": 60,
  "buffer_time": 15,
  "display_duration": "1 hour",
  "requirements": "Please arrive with clean, dry hair",
  "preparation_instructions": "No special preparation needed",
  "image": "https://api.vierla.com/media/services/haircut.jpg",
  "status": {...},
  "booking_count": 89,
  "availability": {
    "availability_type": "scheduled",
    "min_advance_booking": 2,
    "max_advance_booking": 30,
    "max_bookings_per_day": 8,
    "max_bookings_per_slot": 1,
    "cancellation_hours": 24,
    "weekend_available": true,
    "holiday_available": false,
    "instant_booking": false
  },
  "location": {
    "location_type": "provider_location",
    "travel_radius": null,
    "travel_fee": null,
    "service_areas": null,
    "virtual_platform": null,
    "location_notes": null
  },
  "gallery": [...],
  "created_at": "2025-06-17T10:00:00Z",
  "updated_at": "2025-06-17T15:30:00Z"
}
```

### Search

#### Global Search
```http
GET /search/
```

**Query Parameters:**
- `q`: Search query (required)
- `type`: Filter by type (categories, providers, services)
- `latitude`: User latitude for distance calculation
- `longitude`: User longitude for distance calculation
- `radius`: Search radius in kilometers

**Response:**
```json
{
  "query": "hair salon",
  "total_results": 45,
  "categories": [
    {
      "id": 2,
      "name": "Hair Services",
      "service_count": 89,
      "relevance_score": 0.95
    }
  ],
  "providers": [
    {
      "id": 1,
      "business_name": "Elite Beauty Salon",
      "rating": 4.8,
      "distance_km": 2.3,
      "relevance_score": 0.87
    }
  ],
  "services": [
    {
      "id": 1,
      "name": "Professional Haircut",
      "provider_name": "Elite Beauty Salon",
      "base_price": 50.00,
      "distance_km": 2.3,
      "relevance_score": 0.92
    }
  ]
}
```

### Nearby Services

#### Find Nearby Services
```http
GET /nearby/
```

**Query Parameters:**
- `latitude`: User latitude (required)
- `longitude`: User longitude (required)
- `radius`: Search radius in kilometers (default: 10)
- `category`: Filter by category ID
- `limit`: Maximum results (default: 20)

**Response:**
```json
{
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "radius_km": 10
  },
  "total_results": 156,
  "services": [
    {
      "id": 1,
      "name": "Professional Haircut",
      "provider": {
        "id": 1,
        "business_name": "Elite Beauty Salon",
        "rating": 4.8
      },
      "category": {
        "id": 2,
        "name": "Hair Services"
      },
      "base_price": 50.00,
      "duration": 60,
      "distance_km": 2.3,
      "is_available": true,
      "instant_booking": false
    }
  ]
}
```

## Error Responses

### Standard Error Format
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field_name": ["This field is required."]
    }
  }
}
```

### Common Error Codes
- `VALIDATION_ERROR`: Input validation failed
- `NOT_FOUND`: Resource not found
- `PERMISSION_DENIED`: Insufficient permissions
- `AUTHENTICATION_REQUIRED`: Authentication token required
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `SERVICE_UNAVAILABLE`: Service temporarily unavailable

## Rate Limiting

API requests are limited to:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated users

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints use cursor-based pagination:

**Request:**
```http
GET /services/?page=2&page_size=20
```

**Response:**
```json
{
  "count": 234,
  "next": "https://api.vierla.com/api/v1/catalog/services/?page=3",
  "previous": "https://api.vierla.com/api/v1/catalog/services/?page=1",
  "results": [...]
}
```

## Filtering and Sorting

### Filtering
Most list endpoints support filtering:
```http
GET /services/?category=2&min_price=25&max_price=100&is_available=true
```

### Sorting
Use the `ordering` parameter:
```http
GET /services/?ordering=-rating,price
```

Available sort fields:
- `name`: Alphabetical
- `price`: Price (base_price)
- `rating`: Provider rating
- `distance`: Distance from user (requires lat/lng)
- `created_at`: Creation date
- `booking_count`: Popularity

## Mobile Optimization

### Reduced Payloads
Use the `mobile=true` parameter for optimized responses:
```http
GET /services/?mobile=true
```

This returns:
- Compressed image URLs
- Essential fields only
- Reduced nested data
- Mobile-specific descriptions

### Image Sizes
Images are available in multiple sizes:
- `thumbnail`: 150x150px
- `small`: 300x300px
- `medium`: 600x600px
- `large`: 1200x1200px

Add size parameter to image URLs:
```
https://api.vierla.com/media/services/haircut.jpg?size=small
```

## Caching

### Response Caching
- Category data: 1 hour
- Provider data: 30 minutes
- Service data: 15 minutes
- Search results: 5 minutes

### Cache Headers
```http
Cache-Control: public, max-age=3600
ETag: "abc123"
Last-Modified: Mon, 17 Jun 2025 15:30:00 GMT
```

## Webhooks

Subscribe to real-time updates:

### Available Events
- `provider.created`
- `provider.updated`
- `provider.verified`
- `service.created`
- `service.updated`
- `service.availability_changed`

### Webhook Payload
```json
{
  "event": "service.created",
  "timestamp": "2025-06-17T15:30:00Z",
  "data": {
    "id": 1,
    "name": "Professional Haircut",
    "provider_id": 1,
    "category_id": 2
  }
}
```

## SDK and Libraries

### JavaScript/TypeScript
```bash
npm install @vierla/catalog-api
```

### Python
```bash
pip install vierla-catalog-api
```

### React Native
```bash
npm install @vierla/react-native-catalog
```

## Support

For API support:
- Documentation: https://docs.vierla.com/api/catalog
- Support Email: <EMAIL>
- Status Page: https://status.vierla.com
- GitHub Issues: https://github.com/vierla/api-issues
