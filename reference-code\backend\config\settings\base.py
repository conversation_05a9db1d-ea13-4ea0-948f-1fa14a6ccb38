"""
<PERSON><PERSON><PERSON>end - Base Settings
Following the new architecture design with mobile optimizations
"""
import os
from pathlib import Path
from datetime import timedelta
import environ

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Environment variables
env = environ.Env(
    DEBUG=(bool, False)
)

# Take environment variables from .env file
environ.Env.read_env(BASE_DIR / '.env')

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG')

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=[])

# Application definition
DJANGO_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # TODO: Add PostGIS support after database setup
    # 'django.contrib.gis',  # PostGIS support
]

THIRD_PARTY_APPS = [
    'rest_framework',
    'corsheaders',
    # API and authentication packages
    'rest_framework_simplejwt',
    'django_filters',
    'drf_spectacular',  # Re-enabled for API documentation
    'channels',  # WebSocket support for real-time messaging
    # 'django_redis',
    # 'django_celery_beat',
    # 'django_celery_results',
    # 'oauth2_provider',
]

LOCAL_APPS = [
    'apps.core',
    'apps.authentication',  # Required for AUTH_USER_MODEL
    'apps.catalog',
    'apps.bookings',
    # MVP Critical Feature - Payment Processing
    'apps.payments',
    'apps.messaging',
    'apps.reviews',
    # 'apps.analytics',
    # 'apps.notifications',
]

INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # 'oauth2_provider.middleware.OAuth2TokenMiddleware',  # Disabled for startup
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # API Response Optimization Middleware - ENABLED AFTER AUTHENTICATION
    # 'vierla.middleware.response_optimization.APIRateLimitingMiddleware',  # TEMPORARILY DISABLED FOR LOGIN TESTING
    'vierla.middleware.response_optimization.ResponseCachingMiddleware',
    'vierla.middleware.response_optimization.ResponseCompressionMiddleware',
    'vierla.middleware.response_optimization.PaginationOptimizationMiddleware',
    # Performance Monitoring Middleware - ENABLED FOR PRODUCTION READINESS
    'vierla.middleware.performance_monitoring.PerformanceMonitoringMiddleware',
    'vierla.middleware.performance_monitoring.SystemHealthMiddleware',
    # Legacy middleware - TEMPORARILY DISABLED FOR STARTUP
    # 'apps.analytics.middleware.PerformanceMonitoringMiddleware',
    # 'apps.analytics.middleware.DatabasePerformanceMiddleware',
    # 'apps.analytics.middleware.CachePerformanceMiddleware',
    # TODO: Create custom middleware
    # 'apps.core.middleware.MobileOptimizationMiddleware',
    # 'apps.core.middleware.RequestLoggingMiddleware',
]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'
ASGI_APPLICATION = 'config.asgi.application'

# Database - Optimized PostgreSQL Configuration for Production Performance
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',  # Regular PostgreSQL for now
        'NAME': env('DB_NAME'),
        'USER': env('DB_USER'),
        'PASSWORD': env('DB_PASSWORD'),
        'HOST': env('DB_HOST', default='localhost'),
        'PORT': env('DB_PORT', default='5432'),
        'OPTIONS': {
            'sslmode': env('DB_SSLMODE', default='prefer'),
            # Performance optimization options
            'connect_timeout': 10,
            'options': '-c default_transaction_isolation=read_committed'
        },
        # Connection pooling and performance settings
        'CONN_MAX_AGE': 600,  # 10 minutes
        'CONN_HEALTH_CHECKS': True,
        'ATOMIC_REQUESTS': False,  # Disabled for better performance
    }
}

# Database Query Optimization Settings - Based on Backend Agent Consultation
DATABASE_QUERY_OPTIMIZATION = {
    'ENABLE_QUERY_CACHING': True,
    'CACHE_TIMEOUT': 300,  # 5 minutes
    'SLOW_QUERY_THRESHOLD': 100,  # milliseconds
    'ENABLE_QUERY_LOGGING': True,
    'MAX_QUERY_CACHE_SIZE': 1000,
    'ENABLE_QUERY_ANALYSIS': True,
    'PREFETCH_OPTIMIZATION': True,
}

# API Response Optimization Settings - Based on Backend Agent Consultation
API_RESPONSE_OPTIMIZATION = {
    # Response Compression
    'COMPRESSION_MIN_LENGTH': 1024,  # 1KB minimum for compression
    'COMPRESSION_LEVEL': 6,  # Balanced compression level

    # Response Caching
    'API_CACHE_TIMEOUT': 300,  # 5 minutes
    'CACHE_VARY_HEADERS': ['Accept-Language', 'Authorization'],

    # Pagination
    'DEFAULT_PAGE_SIZE': 20,
    'MAX_PAGE_SIZE': 100,
    'PAGINATION_PREFETCH': True,

    # Rate Limiting
    'API_RATE_LIMIT': 1000,  # requests per hour
    'RATE_LIMIT_WINDOW': 3600,  # 1 hour
    'RATE_LIMIT_BY_USER': True,
}

# Cache Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': env('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'vierla',
        'TIMEOUT': 300,
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': env('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'vierla_sessions',
        'TIMEOUT': 3600,
    }
}

# Session Configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'
SESSION_COOKIE_AGE = 3600  # 1 hour

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom User Model
AUTH_USER_MODEL = 'authentication.User'

# Django REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        # 'oauth2_provider.contrib.rest_framework.OAuth2Authentication',  # Temporarily disabled
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.MultiPartParser',
        'rest_framework.parsers.FormParser',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
        # TODO: Create custom throttling class
        # 'apps.core.throttling.MobileAdaptiveThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '10000/hour',  # TEMPORARILY INCREASED FOR LOGIN TESTING
        'user': '10000/hour',  # TEMPORARILY INCREASED FOR LOGIN TESTING
        'mobile': '10000/hour',  # TEMPORARILY INCREASED FOR LOGIN TESTING
        'login': '1000/minute',  # TEMPORARILY INCREASED FOR LOGIN TESTING
        'register': '1000/minute',  # TEMPORARILY INCREASED FOR LOGIN TESTING
        'password_reset': '3/hour',
        'customer_api': '10000/hour',  # Customer API throttle rate
        'provider_api': '10000/hour',  # Provider API throttle rate
    },
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=30),  # Mobile-friendly
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',  # Use RS256 in production
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=60),  # Mobile sliding tokens
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=1),
}

# OAuth2 Configuration
OAUTH2_PROVIDER = {
    'SCOPES': {
        'read': 'Read scope',
        'write': 'Write scope',
    },
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,  # 7 days
}

# API Documentation
SPECTACULAR_SETTINGS = {
    'TITLE': 'Vierla Beauty Services API',
    'DESCRIPTION': 'Comprehensive API for beauty services marketplace with mobile optimizations',
    'VERSION': '2.0.0',
    'SERVE_INCLUDE_SCHEMA': True,
    'SCHEMA_PATH_PREFIX': '/api/v[0-9]',
    'COMPONENT_SPLIT_REQUEST': True,
    'SORT_OPERATIONS': False,
    'DISABLE_ERRORS_AND_WARNINGS': True,
    'SCHEMA_PATH_PREFIX_TRIM': True,
    'ENUM_NAME_OVERRIDES': {
        'ValidationErrorEnum': 'django.core.exceptions.ValidationError',
    },
    'PREPROCESSING_HOOKS': [
        'drf_spectacular.hooks.preprocess_exclude_path_format',
        'config.schema_hooks.preprocess_exclude_problematic_serializers',
    ],
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': True,
    },
    'REDOC_UI_SETTINGS': {
        'hideDownloadButton': False,
        'expandResponses': 'all',
    },
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://localhost:8081",
    "http://127.0.0.1:8081",
    "exp://*************:8081",  # Expo development
]

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only in development

# Celery Configuration
CELERY_BROKER_URL = env('REDIS_URL')
CELERY_RESULT_BACKEND = env('REDIS_URL')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# Mobile Optimization Settings
MOBILE_OPTIMIZATION = {
    'CACHE_TIMEOUT': env('MOBILE_CACHE_TIMEOUT', default=300),
    'MAX_PAYLOAD_SIZE': env('MOBILE_MAX_PAYLOAD_SIZE', default=1048576),  # 1MB
    'BATTERY_AWARE_PROCESSING': env('BATTERY_AWARE_PROCESSING', default=True),
    'NETWORK_ADAPTATION': True,
    'COMPRESSION_ENABLED': True,
}

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(asctime)s %(name)s %(levelname)s %(message)s'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': env('DJANGO_LOG_LEVEL', default='INFO'),
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'vierla.performance': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# ============================================================================
# CHANNELS CONFIGURATION - WebSocket Support
# ============================================================================

# ASGI Application
ASGI_APPLICATION = 'config.asgi.application'

# Channel Layers Configuration
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# ============================================================================
# STRIPE PAYMENT CONFIGURATION - MVP Critical Feature
# ============================================================================

# Stripe API Keys (use environment variables in production)
STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY', 'pk_test_...')  # Replace with actual test key
STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY', 'sk_test_...')  # Replace with actual test key
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET', 'whsec_...')  # Replace with actual webhook secret

# Payment Configuration
PAYMENT_SETTINGS = {
    'CURRENCY': 'CAD',
    'SERVICE_FEE_PERCENTAGE': 5.0,  # 5% service fee
    'TAX_PERCENTAGE': 13.0,  # 13% HST for Ontario
    'MAX_PAYMENT_AMOUNT': 10000.00,  # $10,000 CAD maximum
    'MIN_PAYMENT_AMOUNT': 0.50,  # $0.50 CAD minimum
}
