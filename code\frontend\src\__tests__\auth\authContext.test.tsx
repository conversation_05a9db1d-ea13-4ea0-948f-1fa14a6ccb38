/**
 * Authentication Context Tests
 * Tests for EPIC-05-CRITICAL: Authentication & UI Enhancement
 * 
 * This test suite covers:
 * 1. AuthContext initialization and state management
 * 2. Login functionality with proper token storage
 * 3. Token refresh mechanism
 * 4. User session handling
 * 5. Authentication state persistence
 * 6. Error handling and recovery
 */

import React from 'react';
import { render, renderHook, act, waitFor } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';
import { authAPI } from '../../services/api/auth';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock authAPI
jest.mock('../../services/api/auth', () => ({
  authAPI: {
    login: jest.fn(),
    checkAuthStatus: jest.fn(),
    getProfile: jest.fn(),
  },
}));

const mockedAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockedAuthAPI = authAPI as jest.Mocked<typeof authAPI>;

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockedAsyncStorage.getItem.mockResolvedValue(null);
    mockedAsyncStorage.setItem.mockResolvedValue();
    mockedAsyncStorage.multiSet.mockResolvedValue();
    mockedAsyncStorage.multiRemove.mockResolvedValue();
  });

  describe('Initialization', () => {
    it('should initialize with loading state', () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
    });

    it('should load existing user from AsyncStorage', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        role: 'customer',
        name: 'Test User',
      };

      mockedAsyncStorage.getItem
        .mockResolvedValueOnce('mock_access_token') // access_token
        .mockResolvedValueOnce(JSON.stringify(mockUser)); // user

      mockedAuthAPI.checkAuthStatus.mockResolvedValue(mockUser);

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockedAuthAPI.checkAuthStatus).toHaveBeenCalled();
    });

    it('should handle expired token during initialization', async () => {
      mockedAsyncStorage.getItem
        .mockResolvedValueOnce('expired_token')
        .mockResolvedValueOnce('{"id": 1}');

      mockedAuthAPI.checkAuthStatus.mockRejectedValue(new Error('Token expired'));

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockedAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
    });

    it('should handle AsyncStorage errors gracefully', async () => {
      mockedAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
    });
  });

  describe('Login Functionality', () => {
    it('should login successfully with valid credentials', async () => {
      const mockAuthResponse = {
        access: 'new_access_token',
        refresh: 'new_refresh_token',
        user: {
          id: 1,
          email: '<EMAIL>',
          role: 'customer',
          name: 'Test User',
        },
      };

      mockedAuthAPI.login.mockResolvedValue(mockAuthResponse);

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      let loginResult: boolean;
      await act(async () => {
        loginResult = await result.current.login('<EMAIL>', 'password123');
      });

      expect(loginResult!).toBe(true);
      expect(result.current.user).toEqual(mockAuthResponse.user);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockedAsyncStorage.multiSet).toHaveBeenCalledWith([
        ['access_token', mockAuthResponse.access],
        ['refresh_token', mockAuthResponse.refresh],
        ['user', JSON.stringify(mockAuthResponse.user)],
      ]);
    });

    it('should handle login failure', async () => {
      mockedAuthAPI.login.mockRejectedValue(new Error('Invalid credentials'));

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      let loginResult: boolean;
      await act(async () => {
        loginResult = await result.current.login('<EMAIL>', 'wrongpassword');
      });

      expect(loginResult!).toBe(false);
      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockedAsyncStorage.multiSet).not.toHaveBeenCalled();
    });

    it('should set loading state during login', async () => {
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve;
      });

      mockedAuthAPI.login.mockReturnValue(loginPromise as any);

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      act(() => {
        result.current.login('<EMAIL>', 'password123');
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        resolveLogin!({
          access: 'token',
          refresh: 'refresh',
          user: { id: 1, email: '<EMAIL>', role: 'customer' },
        });
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
    });
  });

  describe('Logout Functionality', () => {
    it('should logout and clear stored data', async () => {
      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      // Set initial authenticated state
      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      await act(async () => {
        await result.current.logout();
      });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockedAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
    });
  });

  describe('User Profile Refresh', () => {
    it('should refresh user profile successfully', async () => {
      const updatedUser = {
        id: 1,
        email: '<EMAIL>',
        role: 'customer',
        name: 'Updated Name',
      };

      mockedAuthAPI.getProfile.mockResolvedValue(updatedUser);

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await act(async () => {
        await result.current.refreshUser();
      });

      expect(result.current.user).toEqual(updatedUser);
      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith(
        'user',
        JSON.stringify(updatedUser)
      );
    });

    it('should handle profile refresh failure', async () => {
      mockedAuthAPI.getProfile.mockRejectedValue(new Error('Profile fetch failed'));

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await act(async () => {
        await result.current.refreshUser();
      });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockedAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'access_token',
        'refresh_token',
        'user',
      ]);
    });
  });

  describe('User Role Properties', () => {
    it('should correctly identify customer role', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        role: 'customer',
        name: 'Customer User',
      };

      mockedAuthAPI.login.mockResolvedValue({
        access: 'token',
        refresh: 'refresh',
        user: mockUser,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      expect(result.current.isCustomer).toBe(true);
      expect(result.current.isProvider).toBe(false);
      expect(result.current.isAdmin).toBe(false);
    });

    it('should correctly identify provider role', async () => {
      const mockUser = {
        id: 2,
        email: '<EMAIL>',
        role: 'service_provider',
        name: 'Provider User',
      };

      mockedAuthAPI.login.mockResolvedValue({
        access: 'token',
        refresh: 'refresh',
        user: mockUser,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      expect(result.current.isCustomer).toBe(false);
      expect(result.current.isProvider).toBe(true);
      expect(result.current.isAdmin).toBe(false);
    });

    it('should correctly identify admin role', async () => {
      const mockUser = {
        id: 3,
        email: '<EMAIL>',
        role: 'admin',
        name: 'Admin User',
      };

      mockedAuthAPI.login.mockResolvedValue({
        access: 'token',
        refresh: 'refresh',
        user: mockUser,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: AuthProvider,
      });

      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      expect(result.current.isCustomer).toBe(false);
      expect(result.current.isProvider).toBe(false);
      expect(result.current.isAdmin).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle context usage outside provider', () => {
      // Suppress console.error for this test
      const originalError = console.error;
      console.error = jest.fn();

      expect(() => {
        renderHook(() => useAuth());
      }).toThrow('useAuth must be used within an AuthProvider');

      console.error = originalError;
    });
  });
});
