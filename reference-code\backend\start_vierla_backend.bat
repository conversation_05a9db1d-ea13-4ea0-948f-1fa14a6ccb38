@echo off
title Vierla Backend Development Server

echo.
echo ================================================================================
echo                        VIERLA BACKEND DEVELOPMENT SERVER
echo ================================================================================
echo Starting Django server with comprehensive mock data...
echo Mobile-ready API server for frontend development
echo ================================================================================
echo.

REM Change to the backend directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please create virtual environment first:
    echo   python -m venv venv
    echo   venv\Scripts\activate.bat
    echo   pip install -r requirements/development.txt
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if Django is installed
python -c "import django; print(f'Django {django.get_version()} detected')" 2>nul
if %errorlevel% neq 0 (
    echo ERROR: Django not installed!
    echo Please install requirements:
    echo   pip install -r requirements/development.txt
    echo.
    pause
    exit /b 1
)

echo.
echo Starting Vierla Backend with automatic data seeding...
echo.

REM Start the backend with data seeding
python start_with_data.py

echo.
echo ================================================================================
echo                           VIERLA BACKEND STOPPED
echo ================================================================================
echo Thank you for using Vierla Backend Development Server!
echo.
pause
