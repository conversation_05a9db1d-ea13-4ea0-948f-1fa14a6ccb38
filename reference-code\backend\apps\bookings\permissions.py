"""
Booking permissions for Vierla Beauty Services Marketplace
Comprehensive permission system for booking management
"""
from rest_framework import permissions
from django.core.exceptions import ObjectDoesNotExist

from .models import Booking, TimeSlot, BookingNotification


class IsBookingOwnerOrProvider(permissions.BasePermission):
    """
    Permission to check if user is the booking owner (customer) or the provider
    """

    def has_object_permission(self, request, view, obj):
        # Admin can access everything
        if request.user.role == 'admin':
            return True

        # Customer can access their own bookings
        if request.user.role == 'customer' and obj.customer == request.user:
            return True

        # Provider can access bookings for their services
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        return False


class CanViewBookings(permissions.BasePermission):
    """
    Permission to view bookings based on user role
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All authenticated users can view bookings (filtered by role in viewset)
        return request.user.role in ['customer', 'service_provider', 'admin']


class CanManageBookings(permissions.BasePermission):
    """
    Permission to manage bookings (confirm, start, complete, etc.)
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # Only providers and admins can manage bookings
        return request.user.role in ['service_provider', 'admin']

    def has_object_permission(self, request, view, obj):
        # Admin can manage all bookings
        if request.user.role == 'admin':
            return True

        # Provider can only manage their own bookings
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        return False


class CanCreateBookings(permissions.BasePermission):
    """
    Permission to create new bookings
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # Customers and admins can create bookings
        return request.user.role in ['customer', 'admin']


class CanCancelBookings(permissions.BasePermission):
    """
    Permission to cancel bookings
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All user types can cancel bookings (with restrictions in view)
        return request.user.role in ['customer', 'service_provider', 'admin']

    def has_object_permission(self, request, view, obj):
        # Admin can cancel any booking
        if request.user.role == 'admin':
            return True

        # Customer can cancel their own bookings
        if request.user.role == 'customer' and obj.customer == request.user:
            return True

        # Provider can cancel bookings for their services
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        return False


class CanManageTimeSlots(permissions.BasePermission):
    """
    Permission to manage time slots
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # Only providers and admins can manage time slots
        if view.action in ['create', 'update', 'partial_update', 'destroy']:
            return request.user.role in ['service_provider', 'admin']

        # All authenticated users can view time slots
        return True

    def has_object_permission(self, request, view, obj):
        # Admin can manage all time slots
        if request.user.role == 'admin':
            return True

        # Provider can only manage their own time slots
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        # For read operations, allow if user can view the time slot
        if view.action in ['retrieve', 'list']:
            return True

        return False


class CanViewTimeSlots(permissions.BasePermission):
    """
    Permission to view time slots
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All authenticated users can view time slots
        return True


class CanManageNotifications(permissions.BasePermission):
    """
    Permission to manage booking notifications
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All authenticated users can view their notifications
        return True

    def has_object_permission(self, request, view, obj):
        # Admin can manage all notifications
        if request.user.role == 'admin':
            return True

        # Users can only manage their own notifications
        return obj.recipient == request.user


class IsProviderOwner(permissions.BasePermission):
    """
    Permission to check if user owns the provider profile
    """

    def has_permission(self, request, view):
        # Must be authenticated and be a provider
        if not request.user.is_authenticated:
            return False

        return request.user.role == 'service_provider' and hasattr(request.user, 'provider_profile')

    def has_object_permission(self, request, view, obj):
        # Admin can access everything
        if request.user.role == 'admin':
            return True

        # Check if the object has a provider field and user owns it
        if hasattr(obj, 'provider'):
            try:
                return hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile
            except ObjectDoesNotExist:
                pass

        return False


class CanAccessBookingDetails(permissions.BasePermission):
    """
    Permission for accessing detailed booking information
    """

    def has_object_permission(self, request, view, obj):
        # Admin can access all booking details
        if request.user.role == 'admin':
            return True

        # Customer can access their own booking details
        if request.user.role == 'customer' and obj.customer == request.user:
            return True

        # Provider can access details of bookings for their services
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        return False


class CanModifyBookingStatus(permissions.BasePermission):
    """
    Permission for modifying booking status
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # Only providers and admins can modify booking status
        return request.user.role in ['service_provider', 'admin']

    def has_object_permission(self, request, view, obj):
        # Admin can modify any booking status
        if request.user.role == 'admin':
            return True

        # Provider can only modify status of their own bookings
        if request.user.role == 'service_provider':
            try:
                if hasattr(request.user, 'provider_profile') and obj.provider == request.user.provider_profile:
                    return True
            except ObjectDoesNotExist:
                pass

        return False


class CanViewBookingHistory(permissions.BasePermission):
    """
    Permission for viewing booking history
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All authenticated users can view their own booking history
        return request.user.role in ['customer', 'service_provider', 'admin']


class CanViewBookingStats(permissions.BasePermission):
    """
    Permission for viewing booking statistics
    """

    def has_permission(self, request, view):
        # Must be authenticated
        if not request.user.is_authenticated:
            return False

        # All authenticated users can view their own stats
        return request.user.role in ['customer', 'service_provider', 'admin']
