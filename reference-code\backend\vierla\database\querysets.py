"""
Optimized QuerySets for Vierla Backend
Based on Backend Agent Consultation for Database Query Optimization
"""

from django.db import models
from django.db.models import Prefetch, Q, F, Count, Avg, Sum, Case, When
from django.core.cache import cache
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, List, Dict, Any

from .optimization import query_optimizer, cache_manager, OPTIMIZATION_CONFIGS


class OptimizedQuerySetMixin:
    """
    Mixin to add optimization methods to QuerySets.
    """
    
    def with_performance_optimization(self, optimization_key: str):
        """Apply predefined optimization configuration."""
        if optimization_key in OPTIMIZATION_CONFIGS:
            config = OPTIMIZATION_CONFIGS[optimization_key]
            return query_optimizer.optimize_queryset(self, config)
        return self
    
    def cached(self, cache_key: str, timeout: int = 300):
        """Cache queryset results."""
        return cache_manager.cache_model_queryset(
            self.model, cache_key, self, timeout
        )


class ServiceProviderQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """
    Optimized queryset for ServiceProvider model.
    """
    
    def active(self):
        """Get only active service providers."""
        return self.filter(is_active=True)
    
    def verified(self):
        """Get only verified service providers."""
        return self.filter(is_verified=True)
    
    def with_location(self):
        """Get providers with valid location data."""
        return self.filter(
            latitude__isnull=False,
            longitude__isnull=False
        ).exclude(
            latitude=0,
            longitude=0
        )
    
    def by_rating(self, min_rating: float = 4.0):
        """Filter by minimum rating."""
        return self.filter(rating__gte=min_rating)
    
    def popular(self):
        """Get popular providers (high rating and review count)."""
        return self.filter(
            rating__gte=4.0,
            review_count__gte=10
        ).order_by('-rating', '-review_count')
    
    def near_location(self, latitude: float, longitude: float, radius_km: float = 10):
        """
        Find providers near a location using efficient distance calculation.
        """
        # Use Haversine formula approximation for better performance
        lat_range = radius_km / 111.0  # Approximate km per degree latitude
        lng_range = radius_km / (111.0 * abs(latitude))  # Adjust for latitude
        
        return self.filter(
            latitude__range=(latitude - lat_range, latitude + lat_range),
            longitude__range=(longitude - lng_range, longitude + lng_range)
        ).extra(
            select={
                'distance': """
                    6371 * acos(
                        cos(radians(%s)) * cos(radians(latitude)) *
                        cos(radians(longitude) - radians(%s)) +
                        sin(radians(%s)) * sin(radians(latitude))
                    )
                """
            },
            select_params=[latitude, longitude, latitude]
        ).having(distance__lte=radius_km).order_by('distance')
    
    def with_services_and_reviews(self):
        """Optimize for providers with services and reviews."""
        return self.select_related('user').prefetch_related(
            Prefetch(
                'services',
                queryset=models.QuerySet().filter(is_active=True).select_related('category')
            ),
            'categories'
        ).annotate(
            active_services_count=Count('services', filter=Q(services__is_active=True)),
            avg_service_price=Avg('services__base_price', filter=Q(services__is_active=True))
        )
    
    def for_search(self, query: str):
        """Optimized search queryset."""
        if not query:
            return self.none()
        
        # Use database full-text search if available
        return self.filter(
            Q(business_name__icontains=query) |
            Q(business_description__icontains=query) |
            Q(services__name__icontains=query) |
            Q(categories__name__icontains=query)
        ).distinct().select_related('user').prefetch_related('categories')
    
    def dashboard_summary(self):
        """Optimized queryset for dashboard summary."""
        return self.active().annotate(
            total_bookings=Count('bookings'),
            completed_bookings=Count('bookings', filter=Q(bookings__status='completed')),
            total_revenue=Sum('bookings__total_amount', filter=Q(bookings__status='completed')),
            avg_rating=Avg('reviews__rating')
        )


class ServiceQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """
    Optimized queryset for Service model.
    """
    
    def active(self):
        """Get only active services."""
        return self.filter(is_active=True, is_available=True)
    
    def by_category(self, category_id: str):
        """Filter by category with optimization."""
        return self.filter(category_id=category_id).select_related('category', 'provider')
    
    def by_provider(self, provider_id: str):
        """Filter by provider with optimization."""
        return self.filter(provider_id=provider_id).select_related('provider', 'category')
    
    def popular(self):
        """Get popular services."""
        return self.filter(
            is_popular=True,
            booking_count__gte=5
        ).order_by('-booking_count', '-created_at')
    
    def by_price_range(self, min_price: float = None, max_price: float = None):
        """Filter by price range."""
        queryset = self
        if min_price is not None:
            queryset = queryset.filter(base_price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(base_price__lte=max_price)
        return queryset
    
    def with_provider_info(self):
        """Include provider information for service listings."""
        return self.select_related(
            'provider__user',
            'category'
        ).annotate(
            provider_rating=F('provider__rating'),
            provider_review_count=F('provider__review_count'),
            provider_verified=F('provider__is_verified')
        )
    
    def for_booking(self):
        """Optimized for booking flow."""
        return self.select_related(
            'provider',
            'category'
        ).filter(
            is_active=True,
            is_available=True,
            provider__is_active=True
        )


class BookingQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """
    Optimized queryset for Booking model.
    """
    
    def for_customer(self, customer_id: str):
        """Get bookings for a specific customer."""
        return self.filter(customer_id=customer_id).select_related(
            'service__provider',
            'service__category'
        ).order_by('-scheduled_datetime')
    
    def for_provider(self, provider_id: str):
        """Get bookings for a specific provider."""
        return self.filter(provider_id=provider_id).select_related(
            'customer',
            'service'
        ).order_by('-scheduled_datetime')
    
    def upcoming(self):
        """Get upcoming bookings."""
        return self.filter(
            scheduled_datetime__gte=timezone.now(),
            status__in=['pending', 'confirmed']
        )
    
    def today(self):
        """Get today's bookings."""
        today = timezone.now().date()
        return self.filter(
            scheduled_datetime__date=today
        ).order_by('scheduled_datetime')
    
    def this_week(self):
        """Get this week's bookings."""
        start_of_week = timezone.now().date() - timedelta(days=timezone.now().weekday())
        end_of_week = start_of_week + timedelta(days=6)
        return self.filter(
            scheduled_datetime__date__range=[start_of_week, end_of_week]
        )
    
    def completed(self):
        """Get completed bookings."""
        return self.filter(status='completed')
    
    def pending_payment(self):
        """Get bookings with pending payments."""
        return self.filter(
            payment_status__in=['pending', 'failed']
        ).select_related('customer', 'service')
    
    def analytics_summary(self, start_date=None, end_date=None):
        """Optimized queryset for analytics."""
        queryset = self
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        
        return queryset.aggregate(
            total_bookings=Count('id'),
            completed_bookings=Count('id', filter=Q(status='completed')),
            cancelled_bookings=Count('id', filter=Q(status='cancelled')),
            total_revenue=Sum('total_amount', filter=Q(status='completed')),
            avg_booking_value=Avg('total_amount', filter=Q(status='completed'))
        )


class TimeSlotQuerySet(models.QuerySet, OptimizedQuerySetMixin):
    """
    Optimized queryset for TimeSlot model.
    """
    
    def available(self):
        """Get available time slots."""
        return self.filter(
            is_available=True,
            date__gte=timezone.now().date()
        ).exclude(is_break=True)
    
    def for_provider(self, provider_id: str):
        """Get time slots for a specific provider."""
        return self.filter(provider_id=provider_id).select_related('service')
    
    def for_date_range(self, start_date, end_date):
        """Get time slots for a date range."""
        return self.filter(
            date__range=[start_date, end_date]
        ).order_by('date', 'start_time')
    
    def bookable(self):
        """Get bookable time slots (available and not fully booked)."""
        return self.available().annotate(
            available_spots=F('max_bookings') - F('current_bookings')
        ).filter(available_spots__gt=0)


# Custom managers using optimized querysets
class ServiceProviderManager(models.Manager):
    def get_queryset(self):
        return ServiceProviderQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def verified(self):
        return self.get_queryset().verified()
    
    def popular(self):
        return self.get_queryset().popular()


class ServiceManager(models.Manager):
    def get_queryset(self):
        return ServiceQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def popular(self):
        return self.get_queryset().popular()


class BookingManager(models.Manager):
    def get_queryset(self):
        return BookingQuerySet(self.model, using=self._db)
    
    def upcoming(self):
        return self.get_queryset().upcoming()
    
    def today(self):
        return self.get_queryset().today()


class TimeSlotManager(models.Manager):
    def get_queryset(self):
        return TimeSlotQuerySet(self.model, using=self._db)
    
    def available(self):
        return self.get_queryset().available()
    
    def bookable(self):
        return self.get_queryset().bookable()
