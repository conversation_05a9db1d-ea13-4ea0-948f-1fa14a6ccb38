/**
 * Skeleton Component
 * shadcn/ui inspired skeleton loading component
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
} from 'react-native';

import { colors, spacing, borderRadius } from '../../theme';
import { cn } from '../../lib/utils';

export interface SkeletonProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  style?: ViewStyle;
  animated?: boolean;
  testID?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius: borderRadiusValue = borderRadius.sm,
  style,
  animated = true,
  testID = 'skeleton',
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (animated) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: false,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: false,
          }),
        ])
      );
      animation.start();

      return () => animation.stop();
    }
  }, [animated, animatedValue]);

  const backgroundColor = animated
    ? animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [colors.background.light, colors.primaryLight],
      })
    : colors.background.light;

  return (
    <Animated.View
      style={cn(
        styles.skeleton,
        {
          width,
          height,
          borderRadius: borderRadiusValue,
          backgroundColor: animated ? undefined : colors.background.light,
        },
        style
      )}
      testID={testID}
    >
      {animated && (
        <Animated.View
          style={[
            StyleSheet.absoluteFillObject,
            {
              backgroundColor,
              borderRadius: borderRadiusValue,
            },
          ]}
        />
      )}
    </Animated.View>
  );
};

// Predefined skeleton components for common use cases
export const SkeletonText: React.FC<Omit<SkeletonProps, 'height'> & { lines?: number }> = ({
  lines = 1,
  ...props
}) => {
  return (
    <View style={styles.textContainer}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          height={16}
          width={index === lines - 1 ? '75%' : '100%'}
          style={index > 0 ? { marginTop: spacing.xs } : undefined}
          {...props}
        />
      ))}
    </View>
  );
};

export const SkeletonAvatar: React.FC<Omit<SkeletonProps, 'width' | 'height' | 'borderRadius'> & { size?: number }> = ({
  size = 40,
  ...props
}) => {
  return (
    <Skeleton
      width={size}
      height={size}
      borderRadius={size / 2}
      {...props}
    />
  );
};

export const SkeletonCard: React.FC<SkeletonProps> = ({ style, ...props }) => {
  return (
    <View style={cn(styles.card, style)}>
      <View style={styles.cardHeader}>
        <SkeletonAvatar size={32} />
        <View style={styles.cardHeaderText}>
          <Skeleton height={14} width="60%" />
          <Skeleton height={12} width="40%" style={{ marginTop: spacing.xs }} />
        </View>
      </View>
      <SkeletonText lines={3} style={{ marginTop: spacing.md }} />
      <View style={styles.cardFooter}>
        <Skeleton height={32} width={80} borderRadius={borderRadius.md} />
        <Skeleton height={32} width={60} borderRadius={borderRadius.md} />
      </View>
    </View>
  );
};

export const SkeletonList: React.FC<{ items?: number; itemHeight?: number; style?: ViewStyle }> = ({
  items = 5,
  itemHeight = 60,
  style,
}) => {
  return (
    <View style={cn(styles.list, style)}>
      {Array.from({ length: items }).map((_, index) => (
        <View key={index} style={[styles.listItem, { height: itemHeight }]}>
          <SkeletonAvatar size={40} />
          <View style={styles.listItemContent}>
            <Skeleton height={16} width="70%" />
            <Skeleton height={14} width="50%" style={{ marginTop: spacing.xs }} />
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  skeleton: {
    overflow: 'hidden',
  },
  textContainer: {
    // Container for text skeletons
  },
  card: {
    padding: spacing.md,
    backgroundColor: colors.background.secondary,
    borderRadius: borderRadius.md,
    borderWidth: 1,
    borderColor: colors.primaryLight,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardHeaderText: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.md,
  },
  list: {
    // Container for list skeletons
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.background.light,
  },
  listItemContent: {
    flex: 1,
    marginLeft: spacing.sm,
  },
});
