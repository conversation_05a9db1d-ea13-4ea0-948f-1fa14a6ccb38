import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Input } from './ui/Input';
import { colors, spacing, borderRadius } from '../theme';

// Types
export interface SearchBarProps {
  value?: string;
  placeholder?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (query: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
  autoFocus?: boolean;
  showCancelButton?: boolean;
  onCancel?: () => void;
  testID?: string;
}



export const SearchBar: React.FC<SearchBarProps> = ({
  value = '',
  placeholder = 'Search services...',
  onChangeText,
  onSearch,
  onFocus,
  onBlur,
  onClear,
  autoFocus = false,
  showCancelButton = false,
  onCancel,
  testID = 'search-bar',
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [internalValue, setInternalValue] = useState(value);
  const cancelButtonWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  useEffect(() => {
    if (showCancelButton || isFocused) {
      Animated.timing(cancelButtonWidth, {
        toValue: 80,
        duration: 200,
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(cancelButtonWidth, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [showCancelButton, isFocused, cancelButtonWidth]);

  const handleChangeText = useCallback((text: string) => {
    setInternalValue(text);
    onChangeText?.(text);
  }, [onChangeText]);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    onFocus?.();
  }, [onFocus]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    onBlur?.();
  }, [onBlur]);

  const handleSearch = useCallback(() => {
    onSearch?.(internalValue);
    Keyboard.dismiss();
  }, [onSearch, internalValue]);

  const handleClear = useCallback(() => {
    setInternalValue('');
    onChangeText?.('');
    onClear?.();
  }, [onChangeText, onClear]);

  const handleCancel = useCallback(() => {
    setInternalValue('');
    onChangeText?.('');
    onCancel?.();
    Keyboard.dismiss();
  }, [onChangeText, onCancel]);

  const handleSubmitEditing = useCallback(() => {
    handleSearch();
  }, [handleSearch]);

  return (
    <View style={styles.container} testID={testID}>
      <Input
        value={internalValue}
        onChangeText={handleChangeText}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onSubmitEditing={handleSubmitEditing}
        placeholder={placeholder}
        autoFocus={autoFocus}
        returnKeyType="search"
        leftIcon={
          <Ionicons
            name="search"
            size={20}
            color={isFocused ? colors.primary : colors.text.secondary}
          />
        }
        rightIcon={
          internalValue.length > 0 ? (
            <TouchableOpacity
              onPress={handleClear}
              testID={`${testID}-clear-button`}
            >
              <Ionicons
                name="close-circle"
                size={20}
                color={colors.text.secondary}
              />
            </TouchableOpacity>
          ) : undefined
        }
        style={[
          styles.searchInput,
          isFocused && styles.searchInputFocused
        ]}
        testID={`${testID}-input`}
      />
      
      {(showCancelButton || isFocused) && (
        <Animated.View style={[styles.cancelButtonContainer, { width: cancelButtonWidth }]}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleCancel}
            testID={`${testID}-cancel-button`}
          >
            <Ionicons
              name="close"
              size={20}
              color={colors.primary}
            />
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchInput: {
    flex: 1,
  },
  searchInputFocused: {
    borderColor: colors.primary,
  },
  cancelButtonContainer: {
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    padding: spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SearchBar;
