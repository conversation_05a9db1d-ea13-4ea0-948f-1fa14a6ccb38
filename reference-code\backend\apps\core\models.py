"""
Core models for Vierla Beauty Services Marketplace
Base models and utilities for the application
"""
from django.db import models
import uuid


class BaseModel(models.Model):
    """
    Abstract base model with UUID primary key
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text="Unique identifier"
    )

    class Meta:
        abstract = True


class TimestampedModel(models.Model):
    """
    Abstract model with created_at and updated_at timestamps
    """
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this record was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="When this record was last updated"
    )

    class Meta:
        abstract = True
