#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a ServiceProvider profile for the test provider user
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User
from apps.catalog.models import ServiceProvider, ServiceCategory

def create_provider_profile():
    """Create a ServiceProvider profile for the test provider user"""
    
    # Get the provider user
    try:
        provider_user = User.objects.get(email='<EMAIL>', role='service_provider')
        print(f"Found provider user: {provider_user.email}")
    except User.DoesNotExist:
        print("Provider user not found!")
        return
    
    # Check if provider profile already exists
    if hasattr(provider_user, 'provider_profile'):
        print("Provider profile already exists!")
        return
    
    # Create the provider profile
    provider_profile = ServiceProvider.objects.create(
        user=provider_user,
        business_name="Test Beauty Salon",
        business_description="A professional beauty salon offering comprehensive beauty services including hair styling, makeup, and skincare treatments.",
        business_phone="+**********",
        business_email="<EMAIL>",
        address="123 Beauty Street",
        city="Toronto",
        state="Ontario",
        zip_code="M5V 3A8",
        country="Canada",
        latitude=43.6532,
        longitude=-79.3832,
        website="https://testbeautysalon.com",
        instagram_handle="@testbeautysalon",
        years_of_experience=5,
        is_verified=True,
        is_active=True,
        mobile_optimized=True
    )
    
    # Add some categories
    try:
        hair_category = ServiceCategory.objects.get(name="Hair Styling")
        makeup_category = ServiceCategory.objects.get(name="Makeup")
        provider_profile.categories.add(hair_category, makeup_category)
        print(f"Added categories: Hair Styling, Makeup")
    except ServiceCategory.DoesNotExist:
        print("Categories not found, skipping category assignment")
    
    print(f"✅ Created ServiceProvider profile for {provider_user.email}")
    print(f"   Business Name: {provider_profile.business_name}")
    print(f"   Profile ID: {provider_profile.id}")
    
    return provider_profile

if __name__ == "__main__":
    create_provider_profile()
