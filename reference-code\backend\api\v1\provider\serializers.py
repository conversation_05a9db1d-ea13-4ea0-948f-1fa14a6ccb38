"""
Provider API Serializers - Enhanced based on Backend Agent feedback
Provider-specific serializers with business-focused data structures
"""

from rest_framework import serializers
from django.utils import timezone
from django.db.models import Count, Avg, Sum
from apps.catalog.models import Service, ServiceProvider
from apps.bookings.models import Booking


class ProviderServiceSerializer(serializers.ModelSerializer):
    """
    Provider-optimized service serializer
    Includes business metrics and management features
    """
    booking_count = serializers.SerializerMethodField()
    revenue_generated = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    next_booking = serializers.SerializerMethodField()
    availability_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Service
        fields = [
            'id',
            'name',
            'description',
            'price',
            'duration',
            'category',
            'is_active',
            'booking_count',
            'revenue_generated',
            'average_rating',
            'next_booking',
            'availability_status',
            'created_at',
            'updated_at',
        ]
    
    def get_booking_count(self, obj):
        """Get total booking count for this service"""
        return Booking.objects.filter(service=obj).count()
    
    def get_revenue_generated(self, obj):
        """Get total revenue from this service"""
        revenue = Booking.objects.filter(
            service=obj,
            status='completed'
        ).aggregate(total=Sum('total_amount'))['total']
        return float(revenue or 0)
    
    def get_average_rating(self, obj):
        """Get average rating for this service"""
        avg_rating = Booking.objects.filter(
            service=obj,
            rating__isnull=False
        ).aggregate(avg=Avg('rating'))['avg']
        return round(float(avg_rating or 0), 2)
    
    def get_next_booking(self, obj):
        """Get next upcoming booking for this service"""
        next_booking = Booking.objects.filter(
            service=obj,
            scheduled_datetime__gte=timezone.now(),
            status__in=['confirmed', 'pending']
        ).order_by('scheduled_datetime').first()
        
        if next_booking:
            return {
                'id': next_booking.id,
                'scheduled_datetime': next_booking.scheduled_datetime,
                'customer_name': f"{next_booking.customer.first_name} {next_booking.customer.last_name}",
                'status': next_booking.status,
            }
        return None
    
    def get_availability_status(self, obj):
        """Get current availability status"""
        # This would check against availability slots
        # For now, return a simple status
        return 'available' if obj.is_active else 'unavailable'


class ProviderBookingSerializer(serializers.ModelSerializer):
    """
    Provider-optimized booking serializer
    Includes customer information and management features
    """
    customer_name = serializers.SerializerMethodField()
    customer_phone = serializers.ReadOnlyField(source='customer.phone')
    customer_email = serializers.ReadOnlyField(source='customer.email')
    service_name = serializers.ReadOnlyField(source='service.name')
    service_duration = serializers.ReadOnlyField(source='service.duration')
    time_until_appointment = serializers.SerializerMethodField()
    can_confirm = serializers.SerializerMethodField()
    can_cancel = serializers.SerializerMethodField()
    can_reschedule = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = [
            'id',
            'customer_name',
            'customer_phone',
            'customer_email',
            'service_name',
            'service_duration',
            'scheduled_datetime',
            'status',
            'total_amount',
            'notes',
            'time_until_appointment',
            'can_confirm',
            'can_cancel',
            'can_reschedule',
            'created_at',
            'updated_at',
        ]
    
    def get_customer_name(self, obj):
        """Get formatted customer name"""
        return f"{obj.customer.first_name} {obj.customer.last_name}".strip()
    
    def get_time_until_appointment(self, obj):
        """Get time until appointment in seconds"""
        if obj.scheduled_datetime > timezone.now():
            return (obj.scheduled_datetime - timezone.now()).total_seconds()
        return 0
    
    def get_can_confirm(self, obj):
        """Check if booking can be confirmed"""
        return obj.status == 'pending'
    
    def get_can_cancel(self, obj):
        """Check if booking can be cancelled"""
        return obj.status in ['pending', 'confirmed']
    
    def get_can_reschedule(self, obj):
        """Check if booking can be rescheduled"""
        return obj.status in ['pending', 'confirmed']


class ProviderStoreSerializer(serializers.ModelSerializer):
    """
    Provider store management serializer
    Includes customization and portfolio features
    """
    total_services = serializers.SerializerMethodField()
    total_bookings = serializers.SerializerMethodField()
    average_rating = serializers.SerializerMethodField()
    monthly_revenue = serializers.SerializerMethodField()
    instagram_connected = serializers.SerializerMethodField()
    store_performance = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceProvider
        fields = [
            'id',
            'business_name',
            'description',
            'phone',
            'address',
            'is_verified',
            'is_active',
            'theme_colors',
            'layout_preferences',
            'instagram_portfolio',
            'total_services',
            'total_bookings',
            'average_rating',
            'monthly_revenue',
            'instagram_connected',
            'store_performance',
            'created_at',
            'updated_at',
        ]
        read_only_fields = ['is_verified', 'created_at', 'updated_at']
    
    def get_total_services(self, obj):
        """Get total number of active services"""
        return obj.services.filter(is_active=True).count()
    
    def get_total_bookings(self, obj):
        """Get total number of bookings"""
        return Booking.objects.filter(service__provider=obj).count()
    
    def get_average_rating(self, obj):
        """Get average rating across all services"""
        avg_rating = Booking.objects.filter(
            service__provider=obj,
            rating__isnull=False
        ).aggregate(avg=Avg('rating'))['avg']
        return round(float(avg_rating or 0), 2)
    
    def get_monthly_revenue(self, obj):
        """Get revenue for current month"""
        from datetime import timedelta
        month_ago = timezone.now().date() - timedelta(days=30)
        
        revenue = Booking.objects.filter(
            service__provider=obj,
            created_at__date__gte=month_ago,
            status='completed'
        ).aggregate(total=Sum('total_amount'))['total']
        
        return float(revenue or 0)
    
    def get_instagram_connected(self, obj):
        """Check if Instagram is connected"""
        return bool(obj.instagram_portfolio)
    
    def get_store_performance(self, obj):
        """Get store performance metrics"""
        # This would calculate various performance metrics
        return {
            'booking_conversion_rate': 0.75,  # Placeholder
            'customer_retention_rate': 0.60,  # Placeholder
            'average_response_time': 120,     # Placeholder (seconds)
            'peak_booking_hours': ['10:00', '14:00', '16:00'],  # Placeholder
        }


class ProviderDashboardSerializer(serializers.Serializer):
    """
    Provider dashboard data serializer
    Comprehensive business overview
    """
    total_bookings = serializers.IntegerField()
    weekly_bookings = serializers.IntegerField()
    monthly_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    active_services = serializers.IntegerField()
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    upcoming_appointments = ProviderBookingSerializer(many=True)
    recent_reviews = serializers.ListField(child=serializers.DictField())
    performance_trends = serializers.DictField()
    business_insights = serializers.ListField(child=serializers.CharField())
    
    class Meta:
        fields = [
            'total_bookings',
            'weekly_bookings',
            'monthly_revenue',
            'active_services',
            'average_rating',
            'upcoming_appointments',
            'recent_reviews',
            'performance_trends',
            'business_insights',
        ]


class ProviderAnalyticsSerializer(serializers.Serializer):
    """
    Provider analytics data serializer
    Business intelligence and insights
    """
    period = serializers.CharField()
    booking_trends = serializers.ListField(child=serializers.DictField())
    revenue_trends = serializers.ListField(child=serializers.DictField())
    customer_satisfaction = serializers.DecimalField(max_digits=3, decimal_places=2)
    popular_services = serializers.ListField(child=serializers.DictField())
    peak_hours = serializers.ListField(child=serializers.CharField())
    conversion_rate = serializers.DecimalField(max_digits=5, decimal_places=4)
    growth_metrics = serializers.DictField()
    recommendations = serializers.ListField(child=serializers.CharField())
    
    class Meta:
        fields = [
            'period',
            'booking_trends',
            'revenue_trends',
            'customer_satisfaction',
            'popular_services',
            'peak_hours',
            'conversion_rate',
            'growth_metrics',
            'recommendations',
        ]


class ProviderProfileSerializer(serializers.ModelSerializer):
    """
    Provider profile management serializer
    """
    user_email = serializers.ReadOnlyField(source='user.email')
    user_first_name = serializers.CharField(source='user.first_name')
    user_last_name = serializers.CharField(source='user.last_name')
    total_earnings = serializers.SerializerMethodField()
    join_date = serializers.ReadOnlyField(source='created_at')
    verification_status = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceProvider
        fields = [
            'id',
            'user_email',
            'user_first_name',
            'user_last_name',
            'business_name',
            'business_description',
            'business_phone',
            'business_email',
            'address',
            'city',
            'state',
            'zip_code',
            'country',
            'website',
            'instagram_handle',
            'facebook_url',
            'is_verified',
            'is_active',
            'rating',
            'total_earnings',
            'join_date',
            'verification_status',
        ]
        read_only_fields = ['is_verified', 'total_earnings', 'join_date']
    
    def get_total_earnings(self, obj):
        """Get total lifetime earnings"""
        earnings = Booking.objects.filter(
            service__provider=obj,
            status='completed'
        ).aggregate(total=Sum('total_amount'))['total']
        return float(earnings or 0)
    
    def get_verification_status(self, obj):
        """Get detailed verification status"""
        return {
            'is_verified': obj.is_verified,
            'email_verified': getattr(obj.user, 'email_verified', False),
            'phone_verified': getattr(obj, 'phone_verified', False),
            'business_verified': obj.is_verified,
            'documents_submitted': getattr(obj, 'documents_submitted', False),
        }
    
    def update(self, instance, validated_data):
        """Update provider profile including user fields"""
        user_data = {}
        if 'user' in validated_data:
            user_data = validated_data.pop('user')
        
        # Update user fields
        if user_data:
            for attr, value in user_data.items():
                setattr(instance.user, attr, value)
            instance.user.save()
        
        # Update provider fields
        return super().update(instance, validated_data)
