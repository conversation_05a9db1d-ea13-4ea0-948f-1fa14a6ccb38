# EPIC-03: Service Management UI/UX Architecture

## Overview
This document outlines the UI/UX architecture for service creation and management functionality for providers in the Vierla application, following the established design system guidelines and mobile-first principles.

## Design System Foundation

### Color Palette (Following COLOR_PALETTE.md)
```typescript
const ProviderColors = {
  // Primary Actions
  primary: '#007AFF',        // Interactive elements, CTAs
  primaryDark: '#0056CC',    // Hover/pressed states
  primaryLight: '#E6F3FF',   // Background highlights
  
  // Status Colors
  success: '#34C759',        // Service active, confirmations
  warning: '#FF9500',        // Attention states, pending
  error: '#FF3B30',          // Errors, destructive actions
  info: '#5AC8FA',           // Information, neutral states
  
  // Vierla Brand Colors
  forest: '#364035',         // Headers, navigation
  sage: '#8B9A8C',          // Accents, secondary elements
  cream: '#F4F1E8',         // Card backgrounds
  gold: '#B8956A',          // Premium features, highlights
  
  // Neutral Colors
  textPrimary: '#1C1C1E',    // Main content
  textSecondary: '#8E8E93',  // Supporting text
  backgroundPrimary: '#FFFFFF',
  backgroundSecondary: '#F2F2F7',
  border: '#E5E5EA',
}
```

### Typography Scale
```typescript
const ProviderTypography = {
  h1: { fontSize: 32, fontWeight: 'bold', lineHeight: 40 },      // Dashboard title
  h2: { fontSize: 24, fontWeight: 'bold', lineHeight: 32 },      // Section headers
  h3: { fontSize: 20, fontWeight: '600', lineHeight: 28 },       // Card titles
  bodyLarge: { fontSize: 18, fontWeight: 'normal', lineHeight: 24 }, // Primary content
  body: { fontSize: 16, fontWeight: 'normal', lineHeight: 22 },      // Standard content
  caption: { fontSize: 14, fontWeight: 'normal', lineHeight: 20 },   // Supporting text
  small: { fontSize: 12, fontWeight: 'normal', lineHeight: 16 },     // Labels, fine print
}
```

### Spacing System (8pt Grid)
```typescript
const Spacing = {
  xs: 4,    // Tight elements
  sm: 8,    // Related elements
  md: 16,   // Component padding
  lg: 24,   // Section separation
  xl: 32,   // Major layout divisions
  xxl: 48,  // Screen margins
}
```

## Screen Architecture

### 1. Provider Dashboard Screen
**Purpose**: Main hub for service providers to manage their business

**Layout Structure**:
```
┌─────────────────────────────────────┐
│ Header (Business Name + Avatar)     │
├─────────────────────────────────────┤
│ Quick Stats Cards (2x2 Grid)       │
│ ┌─────────┐ ┌─────────┐            │
│ │ Active  │ │ Total   │            │
│ │Services │ │Bookings │            │
│ └─────────┘ └─────────┘            │
│ ┌─────────┐ ┌─────────┐            │
│ │ Revenue │ │ Rating  │            │
│ │ (Month) │ │ Average │            │
│ └─────────┘ └─────────┘            │
├─────────────────────────────────────┤
│ Services Section                    │
│ ┌─────────────────────────────────┐ │
│ │ "My Services" + Add Button      │ │
│ │ ┌─────────────────────────────┐ │ │
│ │ │ Service Card 1              │ │ │
│ │ │ Service Card 2              │ │ │
│ │ │ Service Card 3              │ │ │
│ │ └─────────────────────────────┘ │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Recent Activity                     │
└─────────────────────────────────────┘
```

**Key Components**:
- `DashboardHeader`: Business info + quick actions
- `StatsCard`: Metric display with icon + value + trend
- `ServiceManagementSection`: Service list + add button
- `ProviderServiceCard`: Service info + quick actions
- `ActivityFeed`: Recent bookings/updates

### 2. Service Creation/Edit Screen
**Purpose**: Form for creating or editing services

**Layout Structure**:
```
┌─────────────────────────────────────┐
│ Header (Back + Title + Save)        │
├─────────────────────────────────────┤
│ Service Image Upload                │
│ ┌─────────────────────────────────┐ │
│ │ [+] Add Photo                   │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Basic Information                   │
│ ┌─────────────────────────────────┐ │
│ │ Service Name *                  │ │
│ │ Category Selector *             │ │
│ │ Description *                   │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Pricing & Duration                  │
│ ┌─────────────────────────────────┐ │
│ │ Price Type (Fixed/Hourly/Range) │ │
│ │ Base Price *                    │ │
│ │ Duration (minutes) *            │ │
│ │ Buffer Time                     │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Additional Details                  │
│ ┌─────────────────────────────────┐ │
│ │ Requirements                    │ │
│ │ Preparation Instructions        │ │
│ │ Active Status Toggle            │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Action Buttons                      │
│ ┌─────────────────────────────────┐ │
│ │ [Cancel] [Save Service]         │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**Key Components**:
- `ServiceForm`: Main form container with validation
- `ImageUploader`: Photo selection and preview
- `CategorySelector`: Dropdown/modal for category selection
- `PriceTypeSelector`: Radio buttons for pricing model
- `DurationPicker`: Time input with validation
- `FormActions`: Cancel/save buttons with loading states

### 3. Service List Management Screen
**Purpose**: Overview and bulk management of all services

**Layout Structure**:
```
┌─────────────────────────────────────┐
│ Header (Title + Add + Filter)       │
├─────────────────────────────────────┤
│ Filter Bar                          │
│ ┌─────────────────────────────────┐ │
│ │ [All] [Active] [Inactive]       │ │
│ │ Sort: [Name ↓] Search: [____]   │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Service List                        │
│ ┌─────────────────────────────────┐ │
│ │ ┌─────┐ Service Name      [⋮]  │ │
│ │ │ IMG │ $50 • 60min • Active   │ │
│ │ └─────┘ 15 bookings this month  │ │
│ │ ─────────────────────────────── │ │
│ │ ┌─────┐ Service Name      [⋮]  │ │
│ │ │ IMG │ $75 • 90min • Inactive │ │
│ │ │ └─────┘ 8 bookings this month  │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Floating Add Button                 │
└─────────────────────────────────────┘
```

**Key Components**:
- `ServiceListHeader`: Title + actions
- `ServiceFilterBar`: Status filters + search + sort
- `ServiceListItem`: Compact service display with actions
- `ServiceActionMenu`: Edit/delete/toggle options
- `FloatingActionButton`: Quick add service

## Component Specifications

### ProviderServiceCard
```typescript
interface ProviderServiceCardProps {
  service: Service;
  onEdit: (serviceId: string) => void;
  onToggleActive: (serviceId: string) => void;
  onDelete: (serviceId: string) => void;
  variant?: 'dashboard' | 'list' | 'compact';
}
```

**Features**:
- Service image with fallback
- Name, price, duration display
- Status indicator (active/inactive)
- Quick action buttons
- Booking count and metrics
- Responsive layout variants

### ServiceForm
```typescript
interface ServiceFormProps {
  mode: 'create' | 'edit';
  initialData?: Partial<Service>;
  onSubmit: (data: ServiceFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}
```

**Features**:
- Real-time validation
- Auto-save draft functionality
- Image upload with preview
- Category selection modal
- Price type switching
- Duration picker with presets
- Error handling and display

### DashboardStatsCard
```typescript
interface DashboardStatsCardProps {
  title: string;
  value: string | number;
  icon: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
    period: string;
  };
  color?: 'primary' | 'success' | 'warning' | 'info';
}
```

**Features**:
- Large value display
- Icon with color theming
- Trend indicator with percentage
- Responsive sizing
- Loading state support

## Navigation Flow

### Provider Tab Navigation
```
┌─────────────────────────────────────┐
│ [Dashboard] [Services] [Bookings]   │
│     [Messages] [Profile]            │
└─────────────────────────────────────┘
```

### Service Management Flow
```
Dashboard → Services List → Service Form
    ↓           ↓              ↓
Quick Add → Service Details → Save/Cancel
```

## Accessibility Features

### WCAG 2.2 AA Compliance
- **Color Contrast**: 4.5:1 minimum for text
- **Touch Targets**: 44px minimum size
- **Focus Indicators**: Clear visual focus states
- **Screen Reader**: Proper ARIA labels and descriptions
- **Keyboard Navigation**: Full keyboard accessibility

### Inclusive Design
- **Text Scaling**: Support for system text size preferences
- **High Contrast**: Alternative color schemes
- **Reduced Motion**: Respect motion preferences
- **Voice Control**: Voice navigation support

## Responsive Design

### Breakpoints
- **Mobile**: 320px - 767px (primary focus)
- **Tablet**: 768px - 1023px (adapted layout)
- **Desktop**: 1024px+ (future web version)

### Layout Adaptations
- **Mobile**: Single column, bottom navigation
- **Tablet**: Two-column layout, side navigation
- **Desktop**: Multi-column dashboard, top navigation

## Implementation Strategy

### Phase 1: Core Components
1. ProviderServiceCard
2. ServiceForm
3. DashboardStatsCard
4. ServiceListItem

### Phase 2: Screen Implementation
1. Provider Dashboard Screen
2. Service Creation/Edit Screen
3. Service List Management Screen

### Phase 3: Enhanced Features
1. Bulk operations
2. Advanced filtering
3. Analytics integration
4. Offline support

This architecture ensures a consistent, accessible, and user-friendly experience for service providers while maintaining the Vierla design system standards.
