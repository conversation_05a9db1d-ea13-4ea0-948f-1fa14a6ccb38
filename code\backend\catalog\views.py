"""
Service Catalog API views for Vierla Beauty Services Marketplace
Enhanced Django REST Framework views with filtering and search
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Prefetch

from .models import ServiceCategory, ServiceProvider, Service
from .serializers import (
    ServiceCategorySerializer, ServiceCategoryListSerializer,
    ServiceProviderSerializer, ServiceProviderListSerializer,
    ServiceSerializer, ServiceListSerializer, ServiceCreateUpdateSerializer,
    ServiceSearchSerializer
)
from .filters import ServiceFilter, ServiceProviderFilter, ServiceCategoryFilter


class ServiceCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for ServiceCategory with read-only operations
    Supports hierarchical categories and filtering
    """

    queryset = ServiceCategory.objects.filter(is_active=True)
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceCategoryFilter
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceCategoryListSerializer
        return ServiceCategorySerializer

    def get_queryset(self):
        """Optimize queryset with prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'retrieve':
            # Include subcategories for detail view
            queryset = queryset.prefetch_related(
                Prefetch(
                    'subcategories',
                    queryset=ServiceCategory.objects.filter(is_active=True)
                )
            )

        return queryset

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services for a specific category"""
        category = self.get_object()
        services = Service.objects.filter(
            category=category,
            is_active=True
        ).select_related('provider', 'category')

        # Apply service filtering
        service_filter = ServiceFilter(request.GET, queryset=services)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)


class ServiceProviderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for ServiceProvider with read-only operations
    Supports filtering by location, rating, and categories
    """

    queryset = ServiceProvider.objects.filter(is_active=True)
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceProviderFilter
    search_fields = ['business_name', 'business_description', 'city']
    ordering_fields = ['business_name', 'rating', 'created_at', 'total_bookings']
    ordering = ['-is_featured', '-rating', 'business_name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceProviderListSerializer
        return ServiceProviderSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset()

        queryset = queryset.select_related('user').prefetch_related('categories')

        return queryset

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """Get services for a specific provider"""
        provider = self.get_object()
        services = Service.objects.filter(
            provider=provider,
            is_active=True
        ).select_related('category')

        # Apply service filtering
        service_filter = ServiceFilter(request.GET, queryset=services)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Service with full CRUD operations
    Supports comprehensive filtering, search, and sorting
    """

    queryset = Service.objects.filter(is_active=True)
    permission_classes = [AllowAny]  # TODO: Implement proper permissions
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = ServiceFilter
    search_fields = ['name', 'description', 'provider__business_name', 'category__name']
    ordering_fields = ['name', 'base_price', 'duration', 'created_at', 'booking_count']
    ordering = ['-is_popular', 'base_price', 'name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ServiceCreateUpdateSerializer
        return ServiceSerializer

    def get_queryset(self):
        """Optimize queryset with select_related and prefetch_related"""
        queryset = super().get_queryset()

        if self.action == 'list':
            # Optimized for list view
            queryset = queryset.select_related('provider', 'category')
        else:
            # Full optimization for detail view
            queryset = queryset.select_related(
                'provider__user', 'category'
            ).prefetch_related('provider__categories')

        return queryset

    @action(detail=False, methods=['get'])
    def search(self, request):
        """
        Enhanced search endpoint with comprehensive filtering
        """
        # Validate search parameters
        search_serializer = ServiceSearchSerializer(data=request.GET)
        if not search_serializer.is_valid():
            return Response(
                search_serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get base queryset
        queryset = self.get_queryset()

        # Apply filters
        service_filter = ServiceFilter(request.GET, queryset=queryset)
        filtered_services = service_filter.qs

        # Paginate results
        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            response_data = self.get_paginated_response(serializer.data).data

            # Add search metadata
            response_data['search_metadata'] = {
                'total_results': filtered_services.count(),
                'filters_applied': {
                    key: value for key, value in request.GET.items()
                    if value and key != 'page'
                }
            }

            return Response(response_data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response({
            'results': serializer.data,
            'search_metadata': {
                'total_results': len(serializer.data),
                'filters_applied': {
                    key: value for key, value in request.GET.items()
                    if value
                }
            }
        })

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """Get popular services"""
        popular_services = self.get_queryset().filter(is_popular=True)

        # Apply additional filtering if provided
        service_filter = ServiceFilter(request.GET, queryset=popular_services)
        filtered_services = service_filter.qs

        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get services from featured providers"""
        featured_services = self.get_queryset().filter(provider__is_featured=True)

        # Apply additional filtering if provided
        service_filter = ServiceFilter(request.GET, queryset=featured_services)
        filtered_services = service_filter.qs

        page = self.paginate_queryset(filtered_services)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(filtered_services, many=True)
        return Response(serializer.data)
