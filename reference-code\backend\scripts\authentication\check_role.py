#!/usr/bin/env python
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

user = User.objects.get(email='<EMAIL>')
print(f'User role in database: "{user.role}"')
print(f'Role choices: {User.ROLE_CHOICES}')

# Check all users and their roles
print('\nAll users and roles:')
for u in User.objects.all():
    print(f'  {u.email}: "{u.role}"')
