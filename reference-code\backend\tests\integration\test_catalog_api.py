"""
Integration tests for Catalog API endpoints
"""
import pytest
from django.urls import reverse
from rest_framework import status
from decimal import Decimal
from apps.catalog.models import ServiceCategory, ServiceProvider, Service


@pytest.mark.integration
@pytest.mark.catalog
@pytest.mark.api
class TestServiceCategoryAPI:
    """Test service category API endpoints"""

    @pytest.mark.django_db
    def test_list_categories(self, api_client, service_category):
        """Test listing service categories"""
        url = reverse('catalog:categories-list')
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1
        
        category_data = response.data['results'][0]
        assert category_data['name'] == service_category.name
        assert category_data['slug'] == service_category.slug

    @pytest.mark.django_db
    def test_get_category_detail(self, api_client, service_category):
        """Test getting category detail"""
        url = reverse('catalog:categories-detail', kwargs={'pk': service_category.id})
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == service_category.name
        assert response.data['slug'] == service_category.slug
        assert response.data['description'] == service_category.description

    @pytest.mark.django_db
    def test_create_category_admin_required(self, authenticated_client):
        """Test creating category requires admin permissions"""
        url = reverse('catalog:categories-list')
        data = {
            'name': 'New Category',
            'slug': 'new-category',
            'description': 'A new category',
            'icon': '🎨',
            'color': '#FF5722'
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        # Should require admin permissions
        assert response.status_code in [status.HTTP_403_FORBIDDEN, status.HTTP_401_UNAUTHORIZED]

    @pytest.mark.django_db
    def test_create_category_as_admin(self, admin_client):
        """Test creating category as admin"""
        url = reverse('catalog:categories-list')
        data = {
            'name': 'New Category',
            'slug': 'new-category',
            'description': 'A new category',
            'icon': '🎨',
            'color': '#FF5722',
            'is_active': True,
            'sort_order': 10
        }
        
        response = admin_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'New Category'
        assert response.data['slug'] == 'new-category'


@pytest.mark.integration
@pytest.mark.catalog
@pytest.mark.api
class TestServiceProviderAPI:
    """Test service provider API endpoints"""

    @pytest.mark.django_db
    def test_list_providers(self, api_client, service_provider):
        """Test listing service providers"""
        url = reverse('catalog:providers-list')
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1
        
        provider_data = response.data['results'][0]
        assert provider_data['business_name'] == service_provider.business_name

    @pytest.mark.django_db
    def test_get_provider_detail(self, api_client, service_provider):
        """Test getting provider detail"""
        url = reverse('catalog:providers-detail', kwargs={'pk': service_provider.id})
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['business_name'] == service_provider.business_name
        assert response.data['business_description'] == service_provider.business_description
        assert response.data['city'] == service_provider.city

    @pytest.mark.django_db
    def test_create_provider_profile_authenticated(self, provider_client, provider_user, service_category):
        """Test creating provider profile when authenticated as provider"""
        url = reverse('catalog:providers-list')
        data = {
            'business_name': 'New Beauty Salon',
            'business_description': 'Professional beauty services',
            'business_phone': '+**********',
            'business_email': '<EMAIL>',
            'address': '456 New Street',
            'city': 'Ottawa',
            'state': 'ON',
            'zip_code': 'K1A 0A6',
            'country': 'Canada',
            'categories': [service_category.id]
        }
        
        response = provider_client.post(url, data, format='json')
        
        # Should either create or update existing provider profile
        assert response.status_code in [status.HTTP_201_CREATED, status.HTTP_200_OK]

    @pytest.mark.django_db
    def test_update_provider_profile_owner_only(self, provider_client, service_provider):
        """Test updating provider profile as owner"""
        url = reverse('catalog:providers-detail', kwargs={'pk': service_provider.id})
        data = {
            'business_description': 'Updated description'
        }
        
        response = provider_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['business_description'] == 'Updated description'

    @pytest.mark.django_db
    def test_update_provider_profile_unauthorized(self, authenticated_client, service_provider):
        """Test updating provider profile as non-owner"""
        url = reverse('catalog:providers-detail', kwargs={'pk': service_provider.id})
        data = {
            'business_description': 'Unauthorized update'
        }
        
        response = authenticated_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.django_db
    def test_filter_providers_by_category(self, api_client, service_provider, service_category):
        """Test filtering providers by category"""
        url = reverse('catalog:providers-list')
        
        response = api_client.get(url, {'category': service_category.id})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1

    @pytest.mark.django_db
    def test_filter_providers_by_location(self, api_client, service_provider):
        """Test filtering providers by location"""
        url = reverse('catalog:providers-list')
        
        response = api_client.get(url, {'city': service_provider.city})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1


@pytest.mark.integration
@pytest.mark.catalog
@pytest.mark.api
class TestServiceAPI:
    """Test service API endpoints"""

    @pytest.mark.django_db
    def test_list_services(self, api_client, service):
        """Test listing services"""
        url = reverse('catalog:services-list')
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1
        
        service_data = response.data['results'][0]
        assert service_data['name'] == service.name

    @pytest.mark.django_db
    def test_get_service_detail(self, api_client, service):
        """Test getting service detail"""
        url = reverse('catalog:services-detail', kwargs={'pk': service.id})
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == service.name
        assert response.data['description'] == service.description
        assert Decimal(response.data['base_price']) == service.base_price

    @pytest.mark.django_db
    def test_create_service_as_provider(self, provider_client, service_provider, service_category):
        """Test creating service as provider"""
        url = reverse('catalog:services-list')
        data = {
            'category': service_category.id,
            'name': 'New Service',
            'description': 'A new service offering',
            'base_price': '75.00',
            'duration': 90,
            'is_active': True,
            'is_available': True
        }
        
        response = provider_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['name'] == 'New Service'
        assert Decimal(response.data['base_price']) == Decimal('75.00')

    @pytest.mark.django_db
    def test_create_service_unauthorized(self, authenticated_client, service_provider, service_category):
        """Test creating service as non-provider"""
        url = reverse('catalog:services-list')
        data = {
            'provider': service_provider.id,
            'category': service_category.id,
            'name': 'Unauthorized Service',
            'description': 'Should not be created',
            'base_price': '50.00',
            'duration': 60
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.django_db
    def test_update_service_as_provider(self, provider_client, service):
        """Test updating service as provider owner"""
        url = reverse('catalog:services-detail', kwargs={'pk': service.id})
        data = {
            'category': service.category.id,
            'description': 'Updated service description',
            'base_price': '60.00'
        }

        response = provider_client.patch(url, data, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['description'] == 'Updated service description'
        assert Decimal(response.data['base_price']) == Decimal('60.00')

    @pytest.mark.django_db
    def test_filter_services_by_category(self, api_client, service, service_category):
        """Test filtering services by category"""
        url = reverse('catalog:services-list')
        
        response = api_client.get(url, {'category': service_category.id})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1

    @pytest.mark.django_db
    def test_filter_services_by_provider(self, api_client, service, service_provider):
        """Test filtering services by provider"""
        url = reverse('catalog:services-list')
        
        response = api_client.get(url, {'provider': service_provider.id})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1

    @pytest.mark.django_db
    def test_filter_services_by_price_range(self, api_client, service):
        """Test filtering services by price range"""
        url = reverse('catalog:services-list')
        
        response = api_client.get(url, {
            'min_price': '40.00',
            'max_price': '60.00'
        })
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1

    @pytest.mark.django_db
    def test_search_services(self, api_client, service):
        """Test searching services by name"""
        url = reverse('catalog:services-list')
        
        response = api_client.get(url, {'search': service.name})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1
