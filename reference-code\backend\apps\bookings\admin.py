"""
Booking admin configuration for Vierla Beauty Services Marketplace
Comprehensive admin interface for booking management
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone as django_timezone

from .models import Booking, BookingStateChange, TimeSlot, BookingNotification


class BookingStateChangeInline(admin.TabularInline):
    """Inline admin for booking state changes"""
    model = BookingStateChange
    extra = 0
    readonly_fields = ['from_status', 'to_status',
                       'changed_by', 'notes', 'created_at']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    """Admin interface for bookings"""

    list_display = [
        'booking_number', 'customer_name', 'provider_name', 'service_name',
        'status_badge', 'payment_status_badge', 'scheduled_datetime', 'total_amount',
        'created_at'
    ]
    list_filter = [
        'status', 'payment_status', 'location_type', 'created_at',
        'scheduled_datetime', 'provider', 'service__category'
    ]
    search_fields = [
        'booking_number', 'customer__first_name', 'customer__last_name',
        'customer__email', 'provider__business_name', 'service__name'
    ]
    readonly_fields = [
        'booking_number', 'end_datetime', 'confirmed_at', 'started_at',
        'completed_at', 'cancelled_at', 'created_at', 'updated_at'
    ]

    fieldsets = (
        ('Booking Information', {
            'fields': (
                'booking_number', 'customer', 'provider', 'service',
                'status', 'payment_status'
            )
        }),
        ('Scheduling', {
            'fields': (
                'scheduled_datetime', 'duration_minutes', 'end_datetime',
                'location_type', 'service_address', 'service_latitude', 'service_longitude'
            )
        }),
        ('Pricing', {
            'fields': (
                'base_price', 'additional_charges', 'discount_amount',
                'tax_amount', 'total_amount'
            )
        }),
        ('Notes', {
            'fields': ('customer_notes', 'provider_notes', 'internal_notes'),
            'classes': ('collapse',)
        }),
        ('Cancellation', {
            'fields': ('cancelled_by', 'cancellation_reason'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                'confirmed_at', 'started_at', 'completed_at', 'cancelled_at',
                'created_at', 'updated_at'
            ),
            'classes': ('collapse',)
        }),
    )

    inlines = [BookingStateChangeInline]

    def customer_name(self, obj):
        return obj.customer.get_full_name()
    customer_name.short_description = 'Customer'

    def provider_name(self, obj):
        return obj.provider.business_name
    provider_name.short_description = 'Provider'

    def service_name(self, obj):
        return obj.service.name
    service_name.short_description = 'Service'

    def status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'confirmed': 'blue',
            'in_progress': 'purple',
            'completed': 'green',
            'cancelled': 'red',
            'no_show': 'gray',
            'rescheduled': 'yellow'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def payment_status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'paid': 'green',
            'partially_paid': 'yellow',
            'refunded': 'blue',
            'failed': 'red'
        }
        color = colors.get(obj.payment_status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_payment_status_display()
        )
    payment_status_badge.short_description = 'Payment'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'customer', 'provider', 'service', 'cancelled_by'
        )


@admin.register(BookingStateChange)
class BookingStateChangeAdmin(admin.ModelAdmin):
    """Admin interface for booking state changes"""

    list_display = [
        'booking_number', 'from_status', 'to_status', 'changed_by_name',
        'created_at'
    ]
    list_filter = ['from_status', 'to_status', 'created_at']
    search_fields = [
        'booking__booking_number', 'changed_by__first_name',
        'changed_by__last_name', 'notes'
    ]
    readonly_fields = ['created_at', 'updated_at']

    def booking_number(self, obj):
        return obj.booking.booking_number
    booking_number.short_description = 'Booking'

    def changed_by_name(self, obj):
        return obj.changed_by.get_full_name() if obj.changed_by else 'System'
    changed_by_name.short_description = 'Changed By'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'booking', 'changed_by'
        )


@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    """Admin interface for time slots"""

    list_display = [
        'provider_name', 'service_name', 'date', 'time_range',
        'availability_status', 'booking_info', 'created_at'
    ]
    list_filter = [
        'is_available', 'is_break', 'is_recurring', 'date',
        'provider', 'service'
    ]
    search_fields = [
        'provider__business_name', 'service__name', 'notes'
    ]
    readonly_fields = ['duration_minutes', 'created_at', 'updated_at']

    fieldsets = (
        ('Time Slot Information', {
            'fields': ('provider', 'service', 'date', 'start_time', 'end_time', 'duration_minutes')
        }),
        ('Availability', {
            'fields': (
                'is_available', 'max_bookings', 'current_bookings',
                'is_break', 'is_recurring'
            )
        }),
        ('Pricing', {
            'fields': ('override_price',)
        }),
        ('Notes', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def provider_name(self, obj):
        return obj.provider.business_name
    provider_name.short_description = 'Provider'

    def service_name(self, obj):
        return obj.service.name if obj.service else 'General'
    service_name.short_description = 'Service'

    def time_range(self, obj):
        return f"{obj.start_time} - {obj.end_time}"
    time_range.short_description = 'Time'

    def availability_status(self, obj):
        if obj.is_break:
            return format_html('<span style="color: gray;">Break</span>')
        elif not obj.is_available:
            return format_html('<span style="color: red;">Unavailable</span>')
        elif obj.is_fully_booked():
            return format_html('<span style="color: orange;">Fully Booked</span>')
        else:
            return format_html('<span style="color: green;">Available</span>')
    availability_status.short_description = 'Status'

    def booking_info(self, obj):
        return f"{obj.current_bookings}/{obj.max_bookings}"
    booking_info.short_description = 'Bookings'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('provider', 'service')


@admin.register(BookingNotification)
class BookingNotificationAdmin(admin.ModelAdmin):
    """Admin interface for booking notifications"""

    list_display = [
        'booking_number', 'recipient_name', 'notification_type',
        'channel', 'status_badge', 'scheduled_for', 'sent_at', 'created_at'
    ]
    list_filter = [
        'notification_type', 'channel', 'status', 'created_at',
        'scheduled_for', 'sent_at'
    ]
    search_fields = [
        'booking__booking_number', 'recipient__first_name',
        'recipient__last_name', 'title', 'message'
    ]
    readonly_fields = ['sent_at', 'read_at', 'created_at', 'updated_at']

    fieldsets = (
        ('Notification Information', {
            'fields': (
                'booking', 'recipient', 'notification_type',
                'channel', 'status'
            )
        }),
        ('Content', {
            'fields': ('title', 'message')
        }),
        ('Scheduling', {
            'fields': ('scheduled_for', 'sent_at', 'read_at')
        }),
        ('Metadata', {
            'fields': ('external_id', 'error_message'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def booking_number(self, obj):
        return obj.booking.booking_number
    booking_number.short_description = 'Booking'

    def recipient_name(self, obj):
        return obj.recipient.get_full_name()
    recipient_name.short_description = 'Recipient'

    def status_badge(self, obj):
        colors = {
            'pending': 'orange',
            'sent': 'blue',
            'delivered': 'green',
            'failed': 'red',
            'read': 'purple'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'booking', 'recipient'
        )
