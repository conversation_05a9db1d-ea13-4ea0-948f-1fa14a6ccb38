"""
Integration tests for Authentication API endpoints
"""
import pytest
from django.urls import reverse
from rest_framework import status
from django.contrib.auth import get_user_model

User = get_user_model()


@pytest.mark.integration
@pytest.mark.auth
@pytest.mark.api
class TestAuthenticationAPI:
    """Test authentication API endpoints"""

    @pytest.mark.django_db
    def test_user_registration_success(self, api_client):
        """Test successful user registration"""
        url = reverse('authentication:register')
        data = {
            'email': '<EMAIL>',
            'password': 'strongpass123',
            'password_confirm': 'strongpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer',
            'terms_accepted': True
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert 'message' in response.data
        assert 'user' in response.data
        assert 'Registration successful' in response.data['message']
        
        # Verify user was created
        user = User.objects.get(email='<EMAIL>')
        assert user.first_name == 'New'
        assert user.last_name == 'User'
        assert user.role == 'customer'

    @pytest.mark.django_db
    def test_user_registration_duplicate_email(self, api_client, customer_user):
        """Test registration with duplicate email"""
        url = reverse('authentication:register')
        data = {
            'email': customer_user.email,
            'password': 'strongpass123',
            'password_confirm': 'strongpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'email' in response.data

    @pytest.mark.django_db
    def test_user_registration_password_mismatch(self, api_client):
        """Test registration with password mismatch"""
        url = reverse('authentication:register')
        data = {
            'email': '<EMAIL>',
            'password': 'strongpass123',
            'password_confirm': 'differentpass123',
            'first_name': 'New',
            'last_name': 'User',
            'role': 'customer',
            'terms_accepted': True
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'password_confirm' in response.data

    @pytest.mark.django_db
    def test_user_login_success(self, api_client, customer_user):
        """Test successful user login"""
        # Set password for the user
        customer_user.set_password('testpass123')
        customer_user.save()
        
        url = reverse('authentication:login')
        data = {
            'email': customer_user.email,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        assert 'refresh' in response.data
        assert 'user' in response.data
        assert response.data['user']['email'] == customer_user.email

    @pytest.mark.django_db
    def test_user_login_invalid_credentials(self, api_client, customer_user):
        """Test login with invalid credentials"""
        url = reverse('authentication:login')
        data = {
            'email': customer_user.email,
            'password': 'wrongpassword'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.django_db
    def test_user_login_inactive_user(self, api_client, customer_user):
        """Test login with inactive user"""
        customer_user.set_password('testpass123')
        customer_user.is_active = False
        customer_user.save()
        
        url = reverse('authentication:login')
        data = {
            'email': customer_user.email,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.django_db
    def test_get_user_profile_authenticated(self, authenticated_client, customer_user):
        """Test getting user profile when authenticated"""
        url = reverse('authentication:profile')
        
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['email'] == customer_user.email
        assert response.data['first_name'] == customer_user.first_name
        assert response.data['last_name'] == customer_user.last_name
        assert response.data['role'] == customer_user.role

    def test_get_user_profile_unauthenticated(self, api_client):
        """Test getting user profile when not authenticated"""
        url = reverse('authentication:profile')
        
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.django_db
    def test_update_user_profile(self, authenticated_client, customer_user):
        """Test updating user profile"""
        url = reverse('authentication:profile')
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'profile': {
                'bio': 'Updated bio'
            }
        }
        
        response = authenticated_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['first_name'] == 'Updated'
        assert response.data['last_name'] == 'Name'
        
        # Verify user was updated
        customer_user.refresh_from_db()
        assert customer_user.first_name == 'Updated'
        assert customer_user.last_name == 'Name'

    @pytest.mark.django_db
    def test_token_refresh(self, api_client, customer_user):
        """Test JWT token refresh"""
        # First login to get tokens
        customer_user.set_password('testpass123')
        customer_user.save()
        
        login_url = reverse('authentication:login')
        login_data = {
            'email': customer_user.email,
            'password': 'testpass123'
        }
        
        login_response = api_client.post(login_url, login_data, format='json')
        refresh_token = login_response.data['refresh']
        
        # Test token refresh
        refresh_url = reverse('authentication:token_refresh')
        refresh_data = {
            'refresh': refresh_token
        }
        
        response = api_client.post(refresh_url, refresh_data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data

    @pytest.mark.django_db
    def test_logout(self, authenticated_client):
        """Test user logout"""
        url = reverse('authentication:logout')
        
        response = authenticated_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.django_db
    def test_change_password(self, authenticated_client, customer_user):
        """Test changing user password"""
        # Set current password
        customer_user.set_password('oldpass123')
        customer_user.save()
        
        url = reverse('authentication:change_password')
        data = {
            'old_password': 'oldpass123',
            'new_password': 'newpass123',
            'new_password_confirm': 'newpass123'
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        
        # Verify password was changed
        customer_user.refresh_from_db()
        assert customer_user.check_password('newpass123')

    @pytest.mark.django_db
    def test_change_password_wrong_old_password(self, authenticated_client, customer_user):
        """Test changing password with wrong old password"""
        customer_user.set_password('oldpass123')
        customer_user.save()
        
        url = reverse('authentication:change_password')
        data = {
            'old_password': 'wrongpass123',
            'new_password': 'newpass123',
            'new_password_confirm': 'newpass123'
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.django_db
    def test_change_password_mismatch(self, authenticated_client, customer_user):
        """Test changing password with new password mismatch"""
        customer_user.set_password('oldpass123')
        customer_user.save()
        
        url = reverse('authentication:change_password')
        data = {
            'old_password': 'oldpass123',
            'new_password': 'newpass123',
            'new_password_confirm': 'differentpass123'
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
