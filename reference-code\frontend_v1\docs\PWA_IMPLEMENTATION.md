# PWA Implementation Guide

## Overview

Vierla has been enhanced with Progressive Web App (PWA) capabilities to provide an app-like experience with offline functionality, installability, and improved performance.

## Features Implemented

### 1. Service Worker (`/public/sw.js`)
- **Caching Strategies**: Implements network-first, cache-first, and stale-while-revalidate strategies
- **Offline Support**: Provides offline fallbacks for navigation and API requests
- **Background Sync**: Queues failed requests for retry when connection is restored
- **Push Notifications**: Supports web push notifications
- **Cache Management**: Automatic cleanup of old caches

### 2. Web App Manifest (`/public/manifest.json`)
- **App Identity**: Name, description, icons, and branding
- **Display Mode**: Standalone app experience
- **Shortcuts**: Quick actions for common tasks
- **Screenshots**: App store-style screenshots
- **File Handlers**: Support for importing CSV/JSON files
- **Share Target**: Allows sharing content to the app

### 3. PWA Service (`/src/services/pwaService.ts`)
- **Service Worker Registration**: Automatic registration and updates
- **Install Prompts**: Manages beforeinstallprompt events
- **Connection Monitoring**: Tracks online/offline status
- **App Lifecycle**: Handles visibility changes and background sync

### 4. React Hooks (`/src/hooks/usePWA.ts`)
- **usePWA**: Complete PWA state and actions
- **useOfflineDetection**: Simple online/offline detection
- **usePWAInstall**: Install prompt management

### 5. UI Components
- **PWAInstallBanner**: Prompts users to install the app
- **OfflineIndicator**: Shows connection status and offline capabilities

## Configuration

### App.json Configuration
```json
{
  "web": {
    "favicon": "./assets/favicon.png",
    "bundler": "metro",
    "output": "static",
    "meta": {
      "viewport": "width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no, viewport-fit=cover",
      "themeColor": "#5A7A63"
    },
    "name": "Vierla - Beauty Services",
    "shortName": "Vierla",
    "description": "Find and book trusted beauty experts in your community",
    "startUrl": "/",
    "display": "standalone",
    "orientation": "portrait",
    "backgroundColor": "#5A7A63",
    "themeColor": "#5A7A63"
  }
}
```

### Package.json Scripts
```json
{
  "scripts": {
    "web:pwa": "expo start --web --https",
    "build:pwa": "expo export --platform web"
  }
}
```

## Usage

### Basic PWA Hook Usage
```typescript
import { usePWA } from '../hooks/usePWA';

function MyComponent() {
  const {
    isOnline,
    isInstallable,
    isInstalled,
    showInstallPrompt,
    updateApp
  } = usePWA();

  return (
    <div>
      {isInstallable && (
        <button onClick={showInstallPrompt}>
          Install App
        </button>
      )}
      {!isOnline && <div>You're offline</div>}
    </div>
  );
}
```

### Offline Detection
```typescript
import { useOfflineDetection } from '../hooks/usePWA';

function OfflineComponent() {
  const { isOnline, isOffline } = useOfflineDetection();
  
  return (
    <div>
      Status: {isOnline ? 'Online' : 'Offline'}
    </div>
  );
}
```

### Install Prompt
```typescript
import { usePWAInstall } from '../hooks/usePWA';

function InstallButton() {
  const { isInstallable, showInstallPrompt } = usePWAInstall();
  
  if (!isInstallable) return null;
  
  return (
    <button onClick={showInstallPrompt}>
      Install Vierla
    </button>
  );
}
```

## Caching Strategy

### Static Assets (Cache First)
- Images, fonts, CSS, JS files
- App icons and manifest
- Cached immediately on install

### API Endpoints (Network First)
- Real-time data (bookings, messages, notifications)
- Falls back to cache when offline
- Background sync for failed requests

### Navigation (Network First with Offline Fallback)
- Tries network first
- Falls back to cached pages
- Ultimate fallback to offline page

## Offline Functionality

### Available Offline
- Browse cached provider profiles
- View saved favorite services
- Access location-based content
- View booking history
- Read cached messages

### Requires Connection
- New bookings
- Real-time messaging
- Payment processing
- Profile updates
- Live notifications

## Installation

### Desktop (Chrome/Edge)
1. Visit the web app
2. Look for install prompt in address bar
3. Click "Install Vierla"
4. App appears in applications menu

### Mobile (Chrome/Safari)
1. Visit the web app
2. Tap "Add to Home Screen" in browser menu
3. App icon appears on home screen
4. Opens in standalone mode

### Programmatic Install
The app automatically shows install banners when:
- PWA criteria are met
- User has engaged with the site
- App is not already installed

## Performance Benefits

### Faster Loading
- Service worker caches critical resources
- Subsequent visits load instantly
- Background updates keep content fresh

### Reduced Data Usage
- Cached content reduces network requests
- Smart caching strategies minimize bandwidth
- Offline functionality reduces failed requests

### Better User Experience
- App-like navigation and interactions
- Smooth transitions and animations
- Consistent performance across devices

## Browser Support

### Full Support
- Chrome 67+
- Edge 79+
- Firefox 44+
- Safari 11.1+

### Partial Support
- Internet Explorer (no service worker)
- Older mobile browsers

### Graceful Degradation
- App works without PWA features
- Progressive enhancement approach
- Fallbacks for unsupported browsers

## Development

### Testing PWA Features
```bash
# Start with HTTPS for PWA testing
npm run web:pwa

# Build for production
npm run build:pwa
```

### Service Worker Development
- Use Chrome DevTools > Application > Service Workers
- Test offline scenarios with Network throttling
- Verify caching strategies in Cache Storage

### Manifest Validation
- Use Chrome DevTools > Application > Manifest
- Test install prompts and shortcuts
- Verify icons and metadata

## Deployment

### Production Checklist
- [ ] HTTPS enabled
- [ ] Service worker registered
- [ ] Manifest linked in HTML
- [ ] Icons generated for all sizes
- [ ] Offline page created
- [ ] Caching strategies tested
- [ ] Install prompts working
- [ ] Background sync functional

### CDN Configuration
Ensure your CDN/hosting provider:
- Serves service worker with proper headers
- Caches static assets appropriately
- Supports HTTPS
- Allows service worker registration

## Monitoring

### Analytics
- Track PWA install events
- Monitor offline usage patterns
- Measure performance improvements
- Track user engagement metrics

### Error Tracking
- Service worker errors
- Cache failures
- Install prompt issues
- Background sync failures

## Future Enhancements

### Planned Features
- Web Share API integration
- Background app refresh
- Advanced caching strategies
- Push notification campaigns
- Offline form submissions

### Performance Optimizations
- Preload critical resources
- Implement app shell architecture
- Add skeleton screens
- Optimize cache strategies

## Troubleshooting

### Common Issues
1. **Service worker not registering**: Check HTTPS and file paths
2. **Install prompt not showing**: Verify PWA criteria and user engagement
3. **Offline functionality broken**: Check service worker caching strategies
4. **Icons not displaying**: Verify icon paths and sizes in manifest

### Debug Tools
- Chrome DevTools > Application tab
- Lighthouse PWA audit
- Service worker logs in console
- Network tab for cache verification
