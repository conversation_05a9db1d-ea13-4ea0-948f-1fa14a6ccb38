#!/usr/bin/env python
"""
Quick authentication test for <PERSON>ier<PERSON> backend
Tests login endpoint with existing test accounts
"""
import requests
import json

BASE_URL = 'http://127.0.0.1:8000'

def test_login(email, password):
    """Test login endpoint"""
    login_data = {
        'email': email,
        'password': password
    }
    
    try:
        response = requests.post(
            f'{BASE_URL}/api/auth/login/', 
            json=login_data, 
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f'Login {email}: Status {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'  ✅ Success! User: {data.get("user", {}).get("email", "N/A")}')
            print(f'  🔑 Token: {data.get("access", "N/A")[:50]}...')
            return data.get("access")
        else:
            print(f'  ❌ Failed: {response.text}')
            return None
            
    except Exception as e:
        print(f'  ❌ Error: {e}')
        return None

def test_protected_endpoint(token):
    """Test a protected endpoint with token"""
    if not token:
        return
        
    try:
        response = requests.get(
            f'{BASE_URL}/api/auth/profile/',
            headers={
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            },
            timeout=10
        )
        
        print(f'Profile endpoint: Status {response.status_code}')
        if response.status_code == 200:
            print('  ✅ Protected endpoint accessible!')
        else:
            print(f'  ❌ Failed: {response.text}')
            
    except Exception as e:
        print(f'  ❌ Error: {e}')

def main():
    print("🔐 Testing Vierla Authentication")
    print("=" * 40)
    
    # Test accounts
    test_accounts = [
        ('<EMAIL>', 'admin123'),
        ('<EMAIL>', 'testpass123'),
        ('<EMAIL>', 'testpass123')
    ]
    
    tokens = []
    
    for email, password in test_accounts:
        token = test_login(email, password)
        if token:
            tokens.append(token)
        print()
    
    # Test protected endpoint with first successful token
    if tokens:
        print("🔒 Testing protected endpoint...")
        test_protected_endpoint(tokens[0])
    
    print("\n✅ Authentication test complete!")

if __name__ == '__main__':
    main()
