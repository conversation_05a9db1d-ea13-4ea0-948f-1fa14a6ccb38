# Vierla API Reference

## Base URL
```
Development: http://127.0.0.1:8000/api
Production: https://api.vierla.com/api
```

## Authentication

All authenticated endpoints require a JWT token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Authentication Endpoints

### Login
Authenticate user with email and password.

**Endpoint:** `POST /auth/login/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "1",
    "email": "<EMAIL>",
    "username": "user",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "full_name": "<PERSON>",
    "role": "customer",
    "is_verified": true,
    "account_status": "active",
    "phone": "+**********",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

**Error Responses:**
- `400 Bad Request`: Invalid credentials or validation errors
- `423 Locked`: Account locked due to failed attempts

### Register
Create a new user account.

**Endpoint:** `POST /auth/register/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "password": "password123",
  "password_confirm": "password123",
  "first_name": "Jane",
  "last_name": "Smith",
  "phone": "+**********",
  "role": "customer"
}
```

**Response (201 Created):**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "2",
    "email": "<EMAIL>",
    "username": "newuser",
    "first_name": "Jane",
    "last_name": "Smith",
    "full_name": "Jane Smith",
    "role": "customer",
    "is_verified": false,
    "account_status": "pending_verification",
    "phone": "+**********",
    "created_at": "2023-01-01T00:00:00Z"
  },
  "message": "Registration successful. Please check your email to verify your account."
}
```

### Social Authentication
Authenticate using Google or Apple Sign-In.

**Endpoint:** `POST /auth/social/`

**Request Body:**
```json
{
  "provider": "google",
  "identity_token": "google_id_token_here",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "user_id": "google_user_id"
}
```

**Response (200 OK):**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "3",
    "email": "<EMAIL>",
    "username": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "role": "customer",
    "is_verified": true,
    "account_status": "active",
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

### Logout
Invalidate the current refresh token.

**Endpoint:** `POST /auth/logout/`
**Authentication:** Required

**Request Body:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200 OK):**
```json
{
  "message": "Successfully logged out."
}
```

### Token Refresh
Refresh an expired access token.

**Endpoint:** `POST /auth/token/refresh/`

**Request Body:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response (200 OK):**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Profile Management

### Get Profile
Retrieve the current user's profile.

**Endpoint:** `GET /auth/profile/`
**Authentication:** Required

**Response (200 OK):**
```json
{
  "id": "1",
  "email": "<EMAIL>",
  "username": "user",
  "first_name": "John",
  "last_name": "Doe",
  "full_name": "John Doe",
  "role": "customer",
  "is_verified": true,
  "account_status": "active",
  "phone": "+**********",
  "created_at": "2023-01-01T00:00:00Z"
}
```

### Update Profile
Update user profile information.

**Endpoint:** `PATCH /auth/profile/update/`
**Authentication:** Required

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "+**********"
}
```

**Response (200 OK):**
```json
{
  "id": "1",
  "email": "<EMAIL>",
  "username": "user",
  "first_name": "John",
  "last_name": "Smith",
  "full_name": "John Smith",
  "role": "customer",
  "is_verified": true,
  "account_status": "active",
  "phone": "+**********",
  "created_at": "2023-01-01T00:00:00Z"
}
```

### Check Auth Status
Check if the current token is valid and get user info.

**Endpoint:** `GET /auth/status/`
**Authentication:** Required

**Response (200 OK):**
```json
{
  "id": "1",
  "email": "<EMAIL>",
  "username": "user",
  "first_name": "John",
  "last_name": "Doe",
  "full_name": "John Doe",
  "role": "customer",
  "is_verified": true,
  "account_status": "active",
  "phone": "+**********",
  "created_at": "2023-01-01T00:00:00Z"
}
```

## Email Verification

### Verify Email
Verify user's email address with a token.

**Endpoint:** `POST /auth/verify-email/`

**Request Body:**
```json
{
  "token": "verification_token_here"
}
```

**Response (200 OK):**
```json
{
  "message": "Email verified successfully"
}
```

### Resend Verification
Resend email verification link.

**Endpoint:** `POST /auth/resend-verification/`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "message": "If the email exists, a verification link has been sent."
}
```

## Password Management

### Change Password
Change password for authenticated user.

**Endpoint:** `POST /auth/change-password/`
**Authentication:** Required

**Request Body:**
```json
{
  "current_password": "oldpassword123",
  "new_password": "newpassword456"
}
```

**Response (200 OK):**
```json
{
  "message": "Password changed successfully."
}
```

### Request Password Reset
Request a password reset link.

**Endpoint:** `POST /auth/password/reset/`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "message": "If the email exists, a password reset link has been sent."
}
```

### Confirm Password Reset
Reset password using a reset token.

**Endpoint:** `POST /auth/password/reset/confirm/`

**Request Body:**
```json
{
  "token": "reset_token_here",
  "password": "newpassword123",
  "password_confirm": "newpassword123"
}
```

**Response (200 OK):**
```json
{
  "message": "Password reset successfully."
}
```

## Error Responses

### Common Error Formats

**Validation Error (400 Bad Request):**
```json
{
  "email": ["This field is required."],
  "password": ["This field is required."]
}
```

**Authentication Error (401 Unauthorized):**
```json
{
  "detail": "Invalid credentials"
}
```

**Permission Error (403 Forbidden):**
```json
{
  "detail": "You do not have permission to perform this action."
}
```

**Account Locked (423 Locked):**
```json
{
  "detail": "Account locked due to multiple failed login attempts"
}
```

**Server Error (500 Internal Server Error):**
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:
- **Authentication endpoints**: 5 requests per minute per IP
- **Profile endpoints**: 30 requests per minute per user
- **Password reset**: 3 requests per hour per IP

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 4
X-RateLimit-Reset: **********
```

## Data Models

### User Model
```json
{
  "id": "string",
  "email": "string",
  "username": "string",
  "first_name": "string",
  "last_name": "string",
  "full_name": "string (computed)",
  "role": "customer | provider",
  "is_verified": "boolean",
  "account_status": "active | suspended | pending_verification",
  "phone": "string (optional)",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Token Response
```json
{
  "access": "string (JWT token)",
  "refresh": "string (JWT token)",
  "user": "User object"
}
```

## Security Headers

All API responses include security headers:
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=********; includeSubDomains
```

## CORS Configuration

The API supports CORS for the following origins:
- `http://localhost:3000` (Development)
- `https://app.vierla.com` (Production)
- `exp://localhost:19000` (Expo Development)

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

Response format:
```json
{
  "count": 100,
  "next": "http://api.example.com/endpoint/?page=3",
  "previous": "http://api.example.com/endpoint/?page=1",
  "results": []
}
```
