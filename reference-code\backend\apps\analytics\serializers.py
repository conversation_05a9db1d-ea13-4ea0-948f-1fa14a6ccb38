"""
Performance Monitoring Serializers
Serializers for performance monitoring API endpoints
"""
from rest_framework import serializers
from .models import (
    PerformanceMetric, SystemHealthMetric, BusinessMetric,
    RealTimeMetric, PerformanceAlert, PerformanceDashboard
)

class PerformanceMetricSerializer(serializers.ModelSerializer):
    """
    Serializer for PerformanceMetric model
    """
    class Meta:
        model = PerformanceMetric
        fields = [
            'id', 'metric_type', 'value', 'unit', 'endpoint', 'method',
            'status_code', 'user_id', 'session_id', 'timestamp', 'metadata'
        ]
        read_only_fields = ['id', 'timestamp']

class SystemHealthMetricSerializer(serializers.ModelSerializer):
    """
    Serializer for SystemHealthMetric model
    """
    class Meta:
        model = SystemHealthMetric
        fields = [
            'id', 'health_type', 'value', 'unit', 'server_instance',
            'timestamp', 'metadata'
        ]
        read_only_fields = ['id', 'timestamp']

class BusinessMetricSerializer(serializers.ModelSerializer):
    """
    Serializer for BusinessMetric model
    """
    class Meta:
        model = BusinessMetric
        fields = [
            'id', 'business_type', 'value', 'unit', 'region', 'category',
            'timestamp', 'metadata'
        ]
        read_only_fields = ['id', 'timestamp']

class RealTimeMetricSerializer(serializers.ModelSerializer):
    """
    Serializer for RealTimeMetric model
    """
    class Meta:
        model = RealTimeMetric
        fields = [
            'id', 'metric_type', 'value', 'unit', 'timestamp', 'metadata'
        ]
        read_only_fields = ['id', 'timestamp']

class PerformanceAlertSerializer(serializers.ModelSerializer):
    """
    Serializer for PerformanceAlert model
    """
    acknowledged_by_name = serializers.CharField(
        source='acknowledged_by.get_full_name', read_only=True
    )
    
    class Meta:
        model = PerformanceAlert
        fields = [
            'id', 'alert_type', 'severity', 'status', 'threshold_value',
            'actual_value', 'unit', 'message', 'endpoint', 'triggered_at',
            'acknowledged_at', 'resolved_at', 'acknowledged_by',
            'acknowledged_by_name', 'metadata'
        ]
        read_only_fields = [
            'id', 'triggered_at', 'acknowledged_at', 'resolved_at',
            'acknowledged_by', 'acknowledged_by_name'
        ]

class PerformanceDashboardSerializer(serializers.ModelSerializer):
    """
    Serializer for PerformanceDashboard model
    """
    created_by_name = serializers.CharField(
        source='created_by.get_full_name', read_only=True
    )
    
    class Meta:
        model = PerformanceDashboard
        fields = [
            'id', 'name', 'dashboard_type', 'config', 'is_active',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]

class PerformanceSummarySerializer(serializers.Serializer):
    """
    Serializer for performance summary data
    """
    api_performance = serializers.DictField()
    system_health = serializers.DictField()
    active_alerts = serializers.IntegerField()
    monitoring_period_hours = serializers.IntegerField()
    generated_at = serializers.DateTimeField()

class DashboardDataSerializer(serializers.Serializer):
    """
    Serializer for dashboard data
    """
    cpu_usage = serializers.FloatField(required=False)
    memory_usage = serializers.FloatField(required=False)
    avg_response_time = serializers.FloatField(required=False)
    active_alerts = serializers.IntegerField(required=False)
    total_requests = serializers.IntegerField(required=False)
    avg_latency = serializers.FloatField(required=False)
    error_rate = serializers.FloatField(required=False)
    rps = serializers.FloatField(required=False)
    timestamp = serializers.DateTimeField()

class AlertSummarySerializer(serializers.Serializer):
    """
    Serializer for alert summary data
    """
    total_active = serializers.IntegerField()
    by_severity = serializers.DictField()
    by_type = serializers.DictField()
    recent_alerts = PerformanceAlertSerializer(many=True)

class LatencyTrendSerializer(serializers.Serializer):
    """
    Serializer for latency trend data
    """
    endpoint = serializers.CharField()
    avg_latency = serializers.FloatField()
    max_latency = serializers.FloatField()
    request_count = serializers.IntegerField()

class SystemStatusSerializer(serializers.Serializer):
    """
    Serializer for current system status
    """
    cpu_usage = serializers.DictField(required=False)
    memory_usage = serializers.DictField(required=False)
    disk_usage = serializers.DictField(required=False)
    active_connections = serializers.DictField(required=False)
