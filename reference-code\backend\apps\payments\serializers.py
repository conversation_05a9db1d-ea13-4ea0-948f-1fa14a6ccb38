"""
Payment Serializers - MVP Critical Feature
Serializers for payment processing API endpoints
"""

from rest_framework import serializers
from decimal import Decimal
from .models import PaymentMethod, PaymentIntent, Transaction


class PaymentMethodSerializer(serializers.ModelSerializer):
    """Serializer for payment methods"""
    
    class Meta:
        model = PaymentMethod
        fields = [
            'id', 'type', 'is_default', 'is_active',
            'last_four', 'brand', 'expires_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, data):
        """Validate payment method data"""
        if data.get('type') == 'card':
            if not data.get('last_four') or not data.get('brand'):
                raise serializers.ValidationError(
                    "Card payment methods require last_four and brand fields"
                )
        return data


class PaymentIntentSerializer(serializers.ModelSerializer):
    """Serializer for payment intents"""
    
    class Meta:
        model = PaymentIntent
        fields = [
            'id', 'stripe_payment_intent_id', 'amount', 'currency',
            'status', 'client_secret', 'payment_method_id',
            'metadata', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'stripe_payment_intent_id', 'client_secret',
            'created_at', 'updated_at'
        ]


class CreatePaymentIntentSerializer(serializers.Serializer):
    """Serializer for creating payment intents"""
    
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2,
        min_value=Decimal('0.01')
    )
    currency = serializers.CharField(max_length=3, default='CAD')
    booking_id = serializers.UUIDField(required=False)
    payment_method_id = serializers.CharField(max_length=255, required=False)
    
    def validate_amount(self, value):
        """Validate payment amount"""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than 0")
        if value > Decimal('10000.00'):  # $10,000 CAD limit
            raise serializers.ValidationError("Amount exceeds maximum limit")
        return value


class ProcessPaymentSerializer(serializers.Serializer):
    """Serializer for processing payments"""
    
    booking_id = serializers.UUIDField()
    payment_method_id = serializers.CharField(max_length=255)
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2,
        min_value=Decimal('0.01')
    )
    payment_intent_id = serializers.CharField(max_length=255, required=False)
    
    def validate_amount(self, value):
        """Validate payment amount"""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than 0")
        return value


class TransactionSerializer(serializers.ModelSerializer):
    """Serializer for transactions"""
    
    payment_method_display = serializers.CharField(
        source='payment_method.__str__', 
        read_only=True
    )
    booking_service_name = serializers.CharField(
        source='booking.service.name', 
        read_only=True
    )
    
    class Meta:
        model = Transaction
        fields = [
            'id', 'type', 'status', 'amount', 'service_fee',
            'tax_amount', 'total_amount', 'currency',
            'stripe_charge_id', 'processed_at', 'failure_reason',
            'payment_method_display', 'booking_service_name',
            'metadata', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'stripe_charge_id', 'processed_at',
            'payment_method_display', 'booking_service_name',
            'created_at', 'updated_at'
        ]


class ApplePaySerializer(serializers.Serializer):
    """Serializer for Apple Pay payments"""
    
    booking_id = serializers.UUIDField()
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2,
        min_value=Decimal('0.01')
    )
    payment_data = serializers.JSONField()
    
    def validate_payment_data(self, value):
        """Validate Apple Pay payment data"""
        required_fields = ['paymentMethod', 'transactionIdentifier']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Missing required field: {field}")
        return value


class GooglePaySerializer(serializers.Serializer):
    """Serializer for Google Pay payments"""
    
    booking_id = serializers.UUIDField()
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2,
        min_value=Decimal('0.01')
    )
    payment_data = serializers.JSONField()
    
    def validate_payment_data(self, value):
        """Validate Google Pay payment data"""
        required_fields = ['paymentMethodData', 'paymentData']
        for field in required_fields:
            if field not in value:
                raise serializers.ValidationError(f"Missing required field: {field}")
        return value


class RefundSerializer(serializers.Serializer):
    """Serializer for processing refunds"""
    
    transaction_id = serializers.UUIDField()
    amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2,
        min_value=Decimal('0.01'),
        required=False
    )
    reason = serializers.CharField(max_length=255, required=False)
    
    def validate(self, data):
        """Validate refund data"""
        # If amount is not provided, it will be a full refund
        if 'amount' not in data:
            data['amount'] = None  # Full refund
        return data


class PaymentSummarySerializer(serializers.Serializer):
    """Serializer for payment summary information"""
    
    subtotal = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    service_fee = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    tax_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    currency = serializers.CharField(max_length=3, read_only=True)
    
    def to_representation(self, instance):
        """Calculate payment summary"""
        if hasattr(instance, 'service') and hasattr(instance.service, 'price'):
            subtotal = instance.service.price
        else:
            subtotal = Decimal('0.00')
        
        service_fee = subtotal * Decimal('0.05')  # 5% service fee
        tax_amount = subtotal * Decimal('0.13')   # 13% HST
        total_amount = subtotal + service_fee + tax_amount
        
        return {
            'subtotal': subtotal,
            'service_fee': service_fee,
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'currency': 'CAD'
        }
