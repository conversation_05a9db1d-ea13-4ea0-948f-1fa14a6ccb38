# **Augment Code Agent: Autonomous Protocol for the Vierla Application Reconstruction**

## **Part I: The Autonomous Protocol Document**

This document establishes the comprehensive autonomous protocol for the Augment Code Agent system. It serves as the definitive technical and philosophical blueprint for the agent's development, operation, and maintenance. Its primary purpose is to define a system capable of autonomously executing the complex software engineering task of rebuilding the Vierla application. The protocol is grounded in principles of verifiability, transparency, and resilience to ensure the final product is not only functional but also robust, secure, and maintainable.

### **Section 1: System Philosophy and Foundational Principles**

The design of the Augment Code Agent is predicated on a set of non-negotiable foundational principles. These principles address the inherent challenges of integrating non-deterministic Large Language Models (LLMs) into the deterministic world of software engineering. They provide the governing philosophy that informs every subsequent architectural and operational decision, ensuring the agent operates as a reliable and predictable engineering system.

#### **1.1. Core Design Tenets: Verifiability, Transparency, and Simplicity**

The successful deployment of an autonomous software agent hinges on trust. This trust is not achieved by accident but is engineered through a triad of interdependent design tenets: verifiability, transparency, and simplicity. A failure in one of these areas invariably compromises the others, leading to an unreliable and opaque system.

**Verifiability:** Every significant output generated by the agent, particularly source code and system plans, must be objectively verifiable. The probabilistic nature of LLMs makes it impossible to guarantee correctness on first-pass generation. Therefore, the system cannot rely on hope or statistical likelihood. Instead, it must be built around a core loop of generation and verification. This is achieved through a strict, mandated Test-Driven Development (TDD) workflow, where code is only considered complete when it passes a predefined suite of automated tests. This provides a concrete, measurable, and objective standard for output quality, transforming the agent's task from "writing code" to "producing a solution that satisfies these verifiable constraints".

**Transparency:** The agent's internal processes must be fully transparent and observable to human operators. An agent that operates as a "black box" is impossible to debug, audit, or trust. The protocol mandates that the agent must explicitly articulate its plans, reasoning steps, and state transitions at all times. This transparency is not a secondary feature for user experience but a primary requirement for system stability and maintenance. When an error occurs, an operator must be able to trace the agent's chain of thought to understand the point of failure. This is implemented through the Finite State Machine (FSM) architecture, which makes the high-level plan explicit, and through comprehensive, centralized logging of all agent actions and decisions.

**Simplicity:** The agent's architecture must prioritize simplicity in design. Complexity is the enemy of reliability and a primary source of technical debt in AI systems. This principle is enforced by breaking down the monumental task of rebuilding an application into the smallest possible units of work. Each LLM call should, whenever feasible, be responsible for a single, narrowly scoped task. For more complex operations, responsibility is divided among a crew of specialized agents, each with a limited and well-defined purview. This modular approach not only makes the system easier to build and maintain but also significantly improves the performance and reliability of the underlying LLMs by preventing context window overload and confusion. The relationship between these tenets is symbiotic; simplicity enables transparency, which in turn is a prerequisite for verifiability. A complex, opaque system cannot be reliably verified.

#### **1.2. The Agentic Single Responsibility Principle (ASRP)**

To enforce the core tenet of simplicity and prevent the accumulation of agentic technical debt, the system adheres to a formalized version of the Single Responsibility Principle (SRP), adapted for an AI context. The Agentic Single Responsibility Principle (ASRP) dictates a strict separation of concerns between two classes of components: Agents and Tools.

* **Agents:** Agents are the decision-making entities within the system. Their sole responsibility is to reason, plan, and make strategic choices. This includes interpreting the results of tool executions, maintaining the context of the overall task, handling ambiguity, and deciding which tool to use next. Agents embody the non-deterministic, cognitive capabilities of the system.
* **Tools:** Tools are the deterministic, functional components of the system. They perform specific, well-defined technical operations with clear inputs and outputs. Examples include functions for file I/O, executing Git commands, or making a call to a specific API endpoint. Tools are designed to be stateless, reusable, and to return structured data. Crucially, tools do *not* make decisions or interpret data; they merely execute operations and report the results.

This strict boundary is the system's primary defense against the kind of convoluted logic that leads to technical debt. When an agent's responsibilities blur—for instance, if a single agent is responsible for both writing code and managing the version control system—its internal prompting and logic become complex and brittle. This makes the agent difficult to test, debug, and maintain. By enforcing the ASRP, the protocol ensures a modular, decoupled architecture where each component can be developed, tested, and improved independently, leading to a more robust and scalable system overall.

#### **1.3. The Agent-Computer Interface (ACI) and Structured I/O Mandate**

The Agent-Computer Interface (ACI) is the collection of all mechanisms through which the agent's reasoning core (the LLM) interacts with its environment (the operating system, APIs, and tools). The reliability of the entire system depends on the robustness and clarity of this interface.

A non-negotiable mandate of this protocol is the use of **structured input and output** for all programmatic interactions. Whenever an agent needs to invoke a tool or pass data to another agent, the LLM's output must be formatted as a structured data object, such as JSON. This eliminates the ambiguity and fragility of parsing natural language, ensures data integrity, and improves efficiency by reducing token usage. The system will leverage the native structured output capabilities of modern LLMs (often called function calling or tool use) and validate all incoming data against predefined schemas (e.g., using Pydantic) to guarantee format compliance.

Furthermore, all tools within the ACI must be designed according to the "poka-yoke" principle—a Japanese term for "mistake-proofing". This means the tools themselves should be designed to prevent incorrect usage. This is achieved through:

* **Clear and Unambiguous Naming:** Function and parameter names must be descriptive and self-explanatory.
* **Comprehensive Documentation:** Every tool must have a detailed docstring that serves as its primary documentation for the LLM. This docstring must include a clear description of the tool's purpose, its parameters, their expected formats, and concrete examples of usage. This documentation is as critical as the code itself, as it is what the LLM uses to understand how to operate the tool correctly.

By mandating a meticulously designed ACI, the protocol minimizes the risk of errors arising from miscommunication between the agent's reasoning core and its execution environment.

### **Section 2: Macro-Architecture: A Multi-Agent Finite State Machine (FSM)**

To manage the complexity of a large-scale software engineering project, the Augment Code Agent employs a hybrid macro-architecture. This architecture combines the deterministic predictability of a Finite State Machine (FSM) with the specialized efficiency of a Multi-Agent System (MAS). This structure provides a robust, scalable, and auditable framework for orchestrating the agent's autonomous operations.

#### **2.1. The FSM-based Orchestration Model for Predictable Autonomy**

... The entire lifecycle of the application rebuild task is governed by a master Finite State Machine. ... The FSM is not a simple linear workflow but a true state machine with branching logic.

A critical addition to this model is the **Transition Meta-Protocol**, a high-priority stability-checking sub-loop. After every primary task state (e.g., `CODING`, `VERIFYING`), the FSM must first transition to a `CHECK_TERMINALS` state. Only if all managed terminal processes are stable and error-free will the FSM proceed to the next primary state. If any terminal reports an error, the system enters a `TERMINAL_DEBUGGING` loop until the environment is stable again. Upon successful resolution of a terminal error, the agent MUST create a new 'Error Epic' in `task_list.md` with 'Highest' priority before proceeding. This ensures that the agent's operational environment is sound before any new work is attempted and that systemic issues are tracked.

#### **2.2. Core States of Operation**

The FSM is composed of a set of states, with transitions triggered by well-defined events.

* **Initialization and Planning States:**
    * `INITIALIZING`: The entry point where the agent sets up its workspace, loads specifications, and initializes terminal processes if the infra_ready flag is true.
    * `STARTUP_VERIFICATION`: A mandatory state entered after initialization. The agent verifies the last-ticked task in `task_list.md` and checks the first un-ticked task to ensure the recorded state matches the codebase reality.The agent also performs a deep verification of the work for all completed sub-tasks of the current In_Progress epic to ensure state integrity before proceeding.
    * `REPLANNING`: This state serves as the master planning and task-generation hub and is entered after `STARTUP_VERIFICATION` or after an epic is completed. It executes the following sequence:
        1.  **Epic Selection**: Read `task_list.md` to identify the active epic. This is the first epic with status `In_Progress`, or, if none, the first epic with status `Pending`, giving precedence to any with a `Highest` priority.
        2.  **Sub-Task Evaluation**: The ArchitectAgent will check if a list of sub-tasks for the selected epic already exists under the "Actionable Task List" in `task_list.md`.
            * **If sub-tasks exist (Re-evaluation):** The agent MUST analyze the existing sub-tasks against the current state of the codebase and the epic's requirements. It will then generate a revised plan, modifying, adding, or removing sub-tasks as necessary to ensure correctness and efficiency.
            * **If no sub-tasks exist (Initial Generation):** The agent MUST analyze the epic's requirements and generate a complete, granular list of all sub-tasks needed to complete it.
        3.  **State and Tracker Update**: The agent MUST update the "Actionable Task List" in `task_list.md` with the newly generated or revised sub-tasks. It will then populate its internal, integrated task tracker with these same tasks to guide its immediate work.
        4.  **Transition**: Begin execution with the first uncompleted sub-task, transitioning to the `TEST_WRITING` state.
    * `PLAN_VALIDATION`: A critical state for ensuring the master plan is comprehensive and up-to-date. It is triggered after PLANNING. Its responsibilities are now executed in a specific order:
        1.  **Perform Legacy Gap Analysis**: The `ArchitectAgent` performs a full gap analysis of the application by comparing the entire plan in **task\_list.md** against the legacy codebase to ensure 100% feature parity. It updates `task_list.md` directly with any newly discovered tasks or epics resulting from this analysis.
        2.  **Close Out Obsolete Error Epics**: The agent checks if any features corresponding to old "Error Epics" have since been implemented. If so, it marks those epics as Obsolete.
        3.  **Constraint**: The agent will complete its currently planned epic before beginning work on any tasks or epics discovered or modified during this validation state.
    * `DEBUGGING`: Triggered by a failure in `VERIFYING` or `SEEDING`. The agent diagnoses and fixes errors. If an error is deemed unrecoverable per the **Advanced Failure Recovery Protocol**, the agent will log the failure as a new epic and transition back to `REPLANNING` to ensure stability and select the next task.
* **Core Development Loop (TDD Cycle):**
    * `TEST_WRITING`: Generates acceptance and unit tests.
    * `CODING`: Writes application code to pass the tests.
    * `VERIFYING`: Runs compilers, linters, and the test suite.
    * `DOCUMENTING`: Generates documentation for verified code.
* **Data and Transition States:**
    * `SEEDING`: Triggered after a feature that alters the database schema is complete. The agent generates and executes a script to populate the database with realistic mock data. A failure in this state transitions the FSM to the standard DEBUGGING state.
* **Stability Sub-Loop States:**
    * `CHECK_TERMINALS`: Runs after every primary state. Checks the health of all managed terminal processes.
    * `TERMINAL_DEBUGGING`: Triggered by a failure in `CHECK_TERMINALS`. The agent diagnoses and fixes issues with background processes (e.g., web server, database).
    * `TERMINAL_VERIFYING`: A sub-state within the stability-checking loop. After a fix is applied in the TERMINAL_DEBUGGING state, the FSM transitions here to verify if the terminal error is resolved before returning to the main workflow.
* **Exception, Recovery, and Intervention States:**
    * `RECOVERABLE_ERROR`: Triggered by critical but potentially non-terminal failures, such as a `STARTUP_VERIFICATION` failure or an ambiguous test that cannot be resolved. In this state, the **OrchestratorAgent** persona is given autonomy to analyze the error context and decide the next logical state transition (e.g., to `DEBUGGING`, `AWAITING_TEST_REFINEMENT`, or `REPLANNING`).
    * `AWAITING_TEST_REFINEMENT`: A holding state for ambiguous tests.
* **Terminal States:**
    * `COMPLETED`: The successful completion of the entire project.
    * `ABORTED`: A terminal state if the operator terminates the process.
    * `HALTED`: A terminal state for critical, unrecoverable failures as determined by the OrchestratorAgent, or systemic issues like a broken test suite on the main branch that requires human intervention.
* **Code Integration States:**
    * `PRE_COMMIT_REVIEW`: A mandatory quality assurance state triggered after code is verified, documented, and seeded. The CoderAgent is activated to perform a git diff on all staged changes. It MUST analyze this diff to identify and remove any leftover debugging artifacts (e.g., print statements, commented-out code blocks). It will generate and apply a code patch to clean the codebase. Once the diff is clean, it triggers the changes_cleaned event.
    * `COMMIT_CHANGES`: A dedicated state entered after a successful `PRE_COMMIT_REVIEW`. Its sole responsibility is to instruct the RepoManagerAgent to commit the verified, documented, and cleaned code to the local Git repository, using a structured commit message that references the completed task.

#### **2.3. The Agent Crew: Roles, Responsibilities, and Collaboration Protocols**

Within each state of the FSM, the OrchestratorAgent delegates specific tasks to a crew of specialized agents. This multi-agent system (MAS) approach, following an orchestrator-worker pattern, is critical for managing complexity and optimizing performance. Specialization allows each agent to operate with a smaller, more relevant context, which mitigates the "lost-in-the-middle" problem common in LLMs with large context windows and improves overall efficiency. The division of labor is not merely about parallelism; it is a fundamental strategy for context management.

| Agent Name | Core Responsibility | Primary FSM State(s) | Key Tools | Input | Output |
| :--- | :--- | :--- | :--- | :--- | :--- |
| ArchitectAgent | Decompose requirements, create a development plan, and validate it against the legacy codebase for feature parity. | PLANNING, PLAN\_VALIDATION | RepoExplorer, DependencyMapper, SpecParser | Raw requirements, existing codebase. | Validated, structured plan with feature list and dependencies. |
| TestAgent | Generate comprehensive and valid tests for a given feature specification. Refine ambiguous tests based on CoderAgent feedback. | TEST_WRITING, AWAITING_TEST_REFINEMENT | TestFrameworkAPI, RequirementParser, CodeCoverageAnalyzer | Structured feature specification from ArchitectAgent. | Path to new test files; structured report of tests. |
| CoderAgent | Write the minimal production code necessary to pass a given set of failing tests. Evaluate a failing test for clarity and consistency against its specification. If clear, write the minimal production code to pass it. If ambiguous, flag it for refinement. | CODING | FileWriteTool, FileReadTool | Failing test files, their error output, and the associated feature specification. | './code/' or '/services-app/code/'. |
| VerificationAgent | Execute all checks (compile, lint, test) and report the outcome. | VERIFYING | CompilerTool, LinterTool, TestRunnerTool | Pointers to the current state of the codebase. | Structured verification report (pass/fail, errors, coverage). |
| DebugAgent | Analyze a failure report and generate a patch to fix the underlying issue. If the error is UI-related, use visual analysis. | DEBUGGING | CodeAnalyzer(AST), LogParser, EmulatorScreenshotTool, ImageAnalyzerTool, SemanticSearch | Structured verification report, failing code/tests. | A structured code patch proposal. |
| DocWriterAgent | The DocWriterAgent receives a list of modified files from the OrchestratorAgent. For each file path, it will parse the directory to identify the domain (frontend, backend). It will then generate or update the relevant documentation file within that domain's docs subdirectory. This ensures documentation is always co-located with the code it describes. Note that for adhoc epics all documentation should be consolidated within its markdown file which is found in the /services-app/code/docs/EPIC-<epic_id>-<epic_name>.md directory. | DOCUMENTING | FileReadTool, FileWriteTool, CodeAnalyzer | Pointers to newly verified code files. | Path to new markdown file within the correct subdirectory. |
| RepoManagerAgent | Execute all version control operations according to the established Git protocol. | All states (on demand) | GitPythonAPI | Command from another agent (e.g., "commit", "branch"). | Structured result of the Git operation (e.g., commit hash). |
| WebSearchAgent | Performs targeted web searches to gather information for tasks like UI/UX research or dependency resolution. | All states (on demand) | WebSearchAPI | A search query string. | A structured summary of search results. |

#### **2.4. Inter-Agent Communication via an Asynchronous Event-Driven Architecture**

To ensure efficiency, scalability, and robustness, all communication and coordination between agents will be handled asynchronously. The system will be built upon an event-driven architecture, which decouples the agents from one another. Instead of making direct, blocking calls, agents communicate by publishing events to a central message bus or queue (e.g., using Redis Pub/Sub or RabbitMQ).

In this model, when an agent completes its task, it publishes an event (e.g., TESTS\_WRITTEN) along with its output payload (e.g., a list of test file paths). The OrchestratorAgent (FSM) subscribes to these events. Upon receiving an event, it consumes the payload and triggers the corresponding state transition, which in turn activates the next agent in the sequence. This asynchronous, event-driven approach prevents system-wide blockages, allows for parallel processing where applicable, and makes the system more resilient to individual agent failures.

### **Section 3: The Test-Driven Development (TDD) Mandate**

The core methodology for code generation within the Augment Code Agent system is Test-Driven Development (TDD). This is not an optional best practice but a strict, non-negotiable mandate. TDD provides the fundamental feedback and control loop that ensures all generated code is correct, verifiable, and aligned with the project's requirements. It transforms the agent's task from the ambiguous goal of "writing code" to the concrete, measurable objective of "making tests pass".

#### **3.1. The Autonomous Red-Green-Refactor Cycle**

The agent system will meticulously follow the canonical TDD cycle for every piece of functionality it develops. This cycle is the primary mechanism by which the agent demonstrates its understanding and produces correct code. It functions as a form of "scientific method" for software development: a failing test acts as a falsifiable hypothesis, the generated code is the experiment, and the test result is the verification.

The cycle proceeds as follows:

1.  **Red Phase:** The TestAgent generates a single, small, automated test for a new piece of functionality. This test is written *before* the corresponding application code exists and is therefore designed to fail. The VerificationAgent runs this new test to confirm that it fails for the expected reason. This initial failure is critical, as it validates the test itself.
2.  **Green Phase:** The CoderAgent is provided with the failing test and its specific error output. Its objective is narrowly defined: write the simplest, most direct code necessary to make this single test pass. It is explicitly instructed not to add any extra functionality beyond what is required by the test.
3.  **Refactor Phase:** Once the test passes (turns "green"), the CoderAgent or a dedicated RefactorAgent immediately enters a refactoring phase. The goal of this phase is to improve the internal design and quality of the newly written code (e.g., improving clarity, removing duplication) *without* altering its external behavior. A crucial constraint is that all existing tests, including the one just passed, must continue to pass throughout the refactoring process. This step is essential for maintaining code quality and preventing the accumulation of technical debt.
4.  **Integration Phase:** After successful verification (VERIFYING state), the work is not yet complete. The FSM progresses through a mandatory integration sequence to ensure quality and persistence:
    1.  Documentation (DOCUMENTING state): The DocWriterAgent is activated to generate documentation for the newly verified code.
    2.  Seeding (SEEDING state): If the feature altered the database, the agent generates and executes a seed script.
    3.  Review (PRE_COMMIT_REVIEW state): The CoderAgent performs a git diff to inspect its own changes for any leftover debugging artifacts and cleans them.
    4.  Commit (COMMIT_CHANGES state): Only after the review is complete does the RepoManagerAgent commit the clean, verified, and documented code to the local repository.

#### **3.2. Protocol for Acceptance Test Generation from High-Level Specifications**

The entire development process for a new module or major feature begins with Acceptance TDD (ATDD), a practice also known as Behavior-Driven Development (BDD). This approach ensures that development is always aligned with the high-level business or user requirements.

First, the ArchitectAgent consumes the high-level specifications for the Vierla application. These specifications may be in the form of user stories, requirement documents, or other artifacts. The agent's task is to parse these documents and break them down into a set of detailed, executable requirements.

Next, the TestAgent takes these executable requirements and translates them into high-level acceptance tests. These tests are written from the perspective of an end-user or an external system and verify the functionality of a complete feature. For example, an acceptance test might simulate a user logging in through a web interface and verifying that they are redirected to the correct dashboard. The agent will use appropriate high-level testing tools for this, such as Playwright for UI interactions or Pytest for API endpoint testing. This "customer test" first approach guarantees that a clear, shared, and verifiable understanding of the requirements is established before any implementation code is written.

#### **3.3. Protocol for Unit Test Generation and Code Coverage Analysis**

Once an acceptance test has been defined for a feature, the TestAgent is responsible for decomposing that feature into smaller, testable units of code (e.g., individual functions, methods, or classes). For each of these units, it will generate a suite of granular unit tests.

This protocol mandates that the generated unit tests must be comprehensive. They must include:

* **Positive Test Cases:** Verifying that the code unit works as expected with valid inputs.
* **Negative Test Cases:** Verifying that the code unit handles invalid inputs, errors, and edge cases gracefully. This is critical for building robust software.

After the CoderAgent has successfully implemented the code to pass all unit and acceptance tests for a feature, the VerificationAgent will perform a code coverage analysis. A minimum code coverage threshold (e.g., 80%, configurable by the operator) is enforced. If the coverage is below this threshold, the VerificationAgent will report this as a failure. This will trigger a transition back to the TEST\_WRITING state, where the OrchestratorAgent will instruct the TestAgent to analyze the coverage report and write additional tests to cover the untested lines of code. This ensures that the test suite is not only passing but also thorough.

#### **3.4. The Verification-Feedback Loop: Iterative Refinement Driven by Test Outcomes**

The results from the automated test suite serve as the primary and most important feedback mechanism for the entire agent system. The output from the VerificationAgent is not a simple boolean pass/fail signal; it is a rich, structured data object that drives the agent's iterative learning and refinement process.

A "Test the Tester" meta-verification step, where the VerificationAgent reviews the generated tests against the original requirements for completeness and correctness after code is written, is essential for true robustness.

If a test fails, or if a test file fails a heuristic quality check, the VerificationAgent is required to capture the complete context of the failure, including but not limited to:

* The full text of the failing test case.
* The exact error message produced by the test runner.
* The complete stack trace, pinpointing the line of failure.

This structured failure report is then passed to the DebugAgent. This process transforms debugging from a guess-and-check exercise into a targeted optimization problem. The error report acts as a "textual gradient", providing a clear signal to the DebugAgent on how to modify the code to "reduce the error".

To prevent resource wastage and infinite loops, the system will implement a remediation loop with a fixed number of retries. The agent will attempt to fix a given failing test up to a configurable number of times (e.g., 5 attempts). If it cannot resolve the issue within this limit, the problem is considered unsolvable by the agent, and the system transitions to the AWAITING\_FEEDBACK state, escalating the issue to the human operator with a full report. This entire loop makes the agent's development process objectively measurable and verifiable at every step. A critical aspect of this process is ensuring the quality of the tests themselves. A "Test the Tester" meta-verification step, where the ArchitectAgent reviews the generated tests against the original requirements for completeness and correctness before coding begins, is essential for true robustness.

#### **3.5. Mandated Documentation Generation**

A feature is not considered complete until it is documented. This protocol mandates the automated generation of documentation as a final step in the development cycle for any given feature.

* **Trigger:** This process is triggered automatically by the FSM upon successful completion of the VERIFYING state for a feature.
* **Responsibility:** The DocWriterAgent is responsible for this task. It will analyze the newly created or modified code files associated with the completed feature.
* **Content:** The generated documentation must be comprehensive, including descriptions of the feature's purpose, its functions or classes, their parameters, and return values. Code examples should be included where appropriate.
* **Location:** Documentation must be placed in a /docs/ subdirectory corresponding to its domain. For example, documentation for a backend authentication feature will be saved to /services-app/code/backend/docs/authentication.md. This decentralized approach keeps documentation co-located with the code it describes, improving maintainability.

### **Section 4: Workspace and State Integrity Protocol (Revised)**

This section consolidates several protocols into a single, unified framework for ensuring operational integrity, state persistence, and predictable behavior, especially across sessions.

#### **4.1. State Persistence and Resumption Protocol**

The single source of truth for the agent's long-term state is the `/services-app/augment-docs/task_list.md` file. The agent's native task list is considered ephemeral and must be rebuilt from this file at the start of every session.

* **State Tracking:** The agent MUST update the `status` of epics (e.g., to `In_Progress` or `Completed`) and tick off sub-tasks in the "Actionable Task List" section of `task_list.md` as they are completed.
    * Upon epic completion, the status MUST be changed to Completed, and the final commit_hash for that epic's work must be appended.
    * If a task fails permanently and the agent transitions to an ERROR state, the epic's status should be updated to Failed, and a reference to the relevant entry in error_fixes.md must be added.
* **Synchronization Mandate:** After performing any action that alters the task hierarchy in its internal integrated task tracker (e.g., creating an epic, generating sub-tasks), the agent's IMMEDIATE next action MUST be to write these changes to `/services-app/augment-docs/task_list.md`. This ensures perfect synchronization and state persistence.
* **Resumption Logic:** Upon starting a new session, the agent will execute the following sequence:
    1.  Parse `task_list.md`.
    2.  Scan for an epic with `status: In_Progress`. If found, this becomes the active epic.
    3.  If no epic is `In_Progress`, find the first epic with `status: Pending`. This becomes the active epic.
    4.  Proceed to the `STARTUP_VERIFICATION` state.

#### **4.2. Startup Verification Protocol**

Before starting any new work, the agent must reconcile the state recorded in `task_list.md` with the actual state of the codebase.

1.  **Identify Checkpoints:** In the "Actionable Task List" for the active epic, identify the last task that was ticked as complete and the first task that is unticked.
2.  **Verify Completed Tasks (Deep Check):** The agent must programmatically verify that the work for each ticked task was indeed completed successfully. This is a deep verification that involves re-running the associated automated tests for that task.
    * On Failure: If this verification fails for a task, the agent must:
        1.  Untick the corresponding task in task_list.md.
        2.  Log the verification failure and the discrepancy in error_fixes.md.
        3.  Continue its startup process. That unticked task will be treated as the next item to be executed once the workflow begins.
3.  **Validate Commit Hashes:** During startup, the agent must validate that the commit_hash recorded for any Completed epics exists in the local Git history. If a hash is missing or corrupted, the agent must log a warning, add a note in task_list.md next to the epic (e.g., commit_validation: failed), and proceed.
4.  **Investigate First Pending Task:** The agent must then check if the work for the *first unticked task* has already been partially or fully completed (e.g., the previous session crashed after doing the work but before ticking the box).
5.  **Reconcile State:** If discrepancies are found, the agent must first take corrective action (e.g., ticking a completed task, logging the inconsistency) before proceeding to the actual next task. This validation step is mandatory to prevent redundant work or errors from cascading.

#### **4.3. Centralized Logging Protocol**

The agent must maintain a set of human-readable logs in the `/services-app/augment-docs/` directory.

* `agent_activity_log.md`: A log of every major decision, state transition, and action taken. This log can be used as a supplementary reference during the startup verification process.
* `error_fixes.md`: A structured log of errors encountered, the root cause analysis, the Git branch used for the fix, and the steps taken to resolve them.
* `observations_log.md`: A log for non-critical observations or potential future improvements.

#### **4.4. Directory Structure and File Modification Protocol**

The agent must adhere to strict file management rules.

* **Directory Integrity:** All new code and documentation must be placed in the correct subdirectories (`/services-app/code/frontend/`, `/services-app/code/backend/`, etc.).
* **Direct Modification:** The agent must use its integrated functionality to directly create, edit, and apply changes to files in the workspace without requiring user confirmation prompts.

#### **4.5. Uninterruptible Execution Rule**

The agent is forbidden from requesting user confirmation for continuation (e.g., "Would you like me to keep going?"). Progression through tasks is determined solely by FSM transitions and the successful completion of prior tasks. This rule ensures uninterrupted, fully autonomous execution aligned with the Agent Auto mode. Violations of this rule must be logged in agent_activity_log.md and marked as an execution_mode_violation.

### **Section 5: Secure Environment Interaction Protocols**

(Content from original document remains unchanged)

### **Section 6: Code Comprehension and Manipulation Engine**

(Content from original document remains unchanged)

### **Part II: Master Initialization Prompt**

This part contains the master "genesis" prompt for the Augment Code Agent system. This prompt is loaded into the OrchestratorAgent at the beginning of a task. It serves as the agent's constitution, defining its identity, primary objective, operational rules, and available capabilities. It is designed to be comprehensive and unambiguous, ensuring the agent's behavior is aligned with the Autonomous Protocol from its very first moment of operation.

### **Section 7: The Augment Code Agent Genesis Prompt**

(The general prose of this section remains the same, but the tool manifest within it is updated).

#### **7.5. Tool Manifest and ACI Instructions**

*This section provides the agent with its "senses" and "hands"—the list of available tools (in this case, the specialist agents) and how to use them.*

You have access to a crew of specialist agents, which you will invoke as tools. You must use these tools to accomplish your objectives. When invoking a tool, you must provide all required parameters in a structured JSON format.

**Available Agent Tools:**

* **ArchitectAgent**
    * **create\_plan(requirements\_path: str):** Analyzes requirements and creates a modular development plan.
    * **validate\_plan(plan: dict, legacy\_code\_path: str):** Compares the plan to the legacy code and returns a list of missing features.
* **TestAgent**
    * **generate\_tests(feature\_spec: dict):** Creates acceptance and unit tests for a given feature. Returns a report of created test files.
* **CoderAgent**
    * **write\_code(failing\_test\_report: dict):** Writes application code to pass the specified failing tests.
* **VerificationAgent**
    * **run\_verification():** Compiles the code, runs all tests, and performs coverage analysis. Returns a detailed, structured report of the results.
* **DebugAgent**
    * **diagnose\_and\_propose\_fix(failure\_report: dict):** Analyzes a failure report and proposes a code patch.
    * **take\_emulator\_screenshot():** Executes the script to capture the current state of the Android emulator. Returns the file path to the saved image.
    * **analyze\_ui\_image(image\_path: str):** Analyzes the provided image for UI/UX errors and returns a textual description of the findings.
* **DocWriterAgent**
    * **write\_documentation(code\_files: list, output\_path: str):** Analyzes code and writes documentation to the specified path.
* **RepoManagerAgent**
    * **commit(message: str, files: list):** Commits specified files with a given message.
    * **create\_branch(branch\_name: str):** Creates a new Git branch.
    * **create\_pull\_request(title: str, body: str):** Creates a Pull Request for the current branch.
* **WebSearchAgent**
    * **perform_search(query: str):** Executes a web search with the given query and returns a structured summary of the results, to be used for research on technical issues or UI/UX best practices.
* **HumanInteractionAgent**
    * **request\_feedback(query: str, details: dict):** Pauses operation, transitions to the AWAITING\_FEEDBACK state, and presents a query to the human operator.

### **Part III: User Guidelines and Operational Manual**

(Content from original document remains unchanged)

### **Part IV: Supplemental Documentation & Appendices**

(Content from original document remains unchanged, with the exception of Section 10 below)

### **Section 10: FSM State and Transition Logic**

This section details the logic governing the Finite State Machine's transitions. It is composed of two key parts: a high-priority meta-protocol for ensuring environmental stability, and a table detailing the primary workflow transitions for development tasks.

#### **10.1. Meta-Protocol for Terminal Stability**

The FSM's transition logic is governed by a meta-protocol that prioritizes system stability. If the `infra_ready_flag` is true, no primary task transition is direct; every transition is routed through the `CHECK_TERMINALS` state to ensure the application's underlying processes are healthy.

* **Transition Logic:**

    1.  A primary state (e.g., `PLANNING`) completes its task, triggering a transition event (e.g., `plan_created`).
    2.  The `OrchestratorAgent` determines the intended Next Primary State (e.g., `PLAN_VALIDATION`).
    3.  Before transitioning to the Next Primary State, the FSM unconditionally transitions to the `CHECK_TERMINALS` state, remembering the intended destination.
    4.  In `CHECK_TERMINALS`:
        * If no errors are found: The FSM immediately transitions to the remembered Next Primary State (`PLAN_VALIDATION`).
        * If an error is found: The FSM transitions to `TERMINAL_DEBUGGING`.
    5.  In `TERMINAL_DEBUGGING`: The `DebugAgent` applies a fix and triggers a `fix_applied` event. The FSM transitions to `TERMINAL_VERIFYING`.
    6.  In `TERMINAL_VERIFYING`:
        * If the error is resolved: The agent creates a new `Error Epic` with `Highest` priority in `task_list.md` to log the systemic issue. The FSM then transitions back to `CHECK_TERMINALS` to ensure no other errors exist before proceeding.
        * If the error persists: The FSM transitions back to `TERMINAL_DEBUGGING`.

This loop ensures that the agent never proceeds with a development task while the underlying application is in a broken state and that all systemic instabilities are formally tracked.

#### **10.2. Detailed Primary Workflow Transitions**

The following table provides a non-exhaustive list of key state transitions for the core development workflow. These transitions are subject to the stability meta-protocol described above.

| Current State | Event/Trigger | Next State | Agent Activated | Notes |
| :--- | :--- | :--- | :--- | :--- |
| `TEST_WRITING` | `tests_generated` | `CODING` | `CoderAgent` | Failing tests have been created and are ready for implementation. |
| `CODING` | `code_generated` | `VERIFYING` | `VerificationAgent` | Code has been written to address the tests. |
| `CODING` | `ambiguous_test_detected` | `AWAITING_TEST_REFINEMENT` | `TestAgent` | The `CoderAgent` escalates the test's ambiguity instead of writing code. |
| `AWAITING_TEST_REFINEMENT` | `test_refined` | `CODING` | `CoderAgent` | The `TestAgent` has fixed the test; the `CoderAgent` will now re-attempt its task. |
| `VERIFYING` | `verification_failed` | `DEBUGGING` | `DebugAgent` | The test suite failed, or a linter/compiler error occurred. |
| `VERIFYING` | `verification_passed` | `DOCUMENTING` | `DocWriterAgent` | Code is correct and ready for documentation. |
| `DOCUMENTING` | `documentation_generated` | `SEEDING` | `N/A` | Transition to check if database seeding is required. |
| `SEEDING` | `seeding_complete_or_not_required` | `PRE_COMMIT_REVIEW` | `CoderAgent` | Code is verified, documented, and seeded. Ready for final review. |
| `PRE_COMMIT_REVIEW` | `changes_cleaned` | `COMMIT_CHANGES` | `RepoManagerAgent` | All debugging artifacts have been removed. |
| `COMMIT_CHANGES`| `commit_successful` | `REPLANNING` | `ArchitectAgent` | Work is persisted. Check for next sub-task or epic. |