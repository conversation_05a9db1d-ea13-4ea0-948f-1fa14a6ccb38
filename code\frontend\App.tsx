import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Platform, StatusBar as RNStatusBar } from 'react-native';
import { AppNavigator } from './src/navigation';
import { ThemeProvider } from './src/contexts/ThemeContext';

// Custom SafeAreaProvider that provides fallback values
const CustomSafeAreaProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <View style={{
      flex: 1,
      paddingTop: Platform.OS === 'android' ? RNStatusBar.currentHeight || 0 : 0
    }}>
      {children}
    </View>
  );
};

export default function App() {
  return (
    <CustomSafeAreaProvider>
      <ThemeProvider>
        <AppNavigator />
        <StatusBar style="auto" />
      </ThemeProvider>
    </CustomSafeAreaProvider>
  );
}
