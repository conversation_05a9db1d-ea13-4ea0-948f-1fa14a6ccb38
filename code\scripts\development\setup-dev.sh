#!/bin/bash
# ============================================================================
# Vierla Development Environment Setup Script (Unix/Linux/macOS)
# ============================================================================
# This script sets up the development environment for the Vierla application
# including virtual environment, dependencies, and database initialization.
#
# Usage: ./scripts/development/setup-dev.sh
# ============================================================================

set -e  # Exit on any error

# Script configuration
SCRIPT_NAME="Vierla Development Setup"
SCRIPT_VERSION="1.0.0"
LOG_FILE="logs/setup-dev.log"
BACKEND_DIR="code/backend"
FRONTEND_DIR="code/frontend"

# Colors for output
COLOR_GREEN='\033[0;32m'
COLOR_YELLOW='\033[1;33m'
COLOR_RED='\033[0;31m'
COLOR_BLUE='\033[0;34m'
COLOR_RESET='\033[0m'

# Logging function
log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${COLOR_RESET}"
}

# Error handling
error_exit() {
    local message=$1
    log "ERROR" "$message"
    print_color "$COLOR_RED" "Error: $message"
    exit 1
}

# Main setup function
main() {
    print_color "$COLOR_BLUE" "============================================================================"
    print_color "$COLOR_BLUE" "$SCRIPT_NAME v$SCRIPT_VERSION"
    print_color "$COLOR_BLUE" "============================================================================"
    echo

    # Create logs directory if it doesn't exist
    mkdir -p logs

    log "INFO" "Starting development environment setup"

    # Check if we're in the correct directory
    if [ ! -d "$BACKEND_DIR" ]; then
        error_exit "Backend directory not found. Please run from project root."
    fi

    # Check Python installation
    log "INFO" "Checking Python installation"
    print_color "$COLOR_YELLOW" "Checking Python installation..."
    
    if ! command -v python3 &> /dev/null; then
        error_exit "Python 3 is not installed or not in PATH. Please install Python 3.11+"
    fi

    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    log "INFO" "Python version: $PYTHON_VERSION"
    print_color "$COLOR_GREEN" "Python $PYTHON_VERSION found"

    # Navigate to backend directory
    cd "$BACKEND_DIR"

    # Create virtual environment
    log "INFO" "Creating virtual environment"
    print_color "$COLOR_YELLOW" "Creating virtual environment..."
    
    if [ -d "venv" ]; then
        print_color "$COLOR_YELLOW" "Virtual environment already exists. Removing old one..."
        rm -rf venv
    fi

    python3 -m venv venv || error_exit "Failed to create virtual environment"

    # Activate virtual environment
    log "INFO" "Activating virtual environment"
    print_color "$COLOR_YELLOW" "Activating virtual environment..."
    source venv/bin/activate

    # Upgrade pip
    log "INFO" "Upgrading pip"
    print_color "$COLOR_YELLOW" "Upgrading pip..."
    python -m pip install --upgrade pip

    # Install Python dependencies
    log "INFO" "Installing Python dependencies"
    print_color "$COLOR_YELLOW" "Installing Python dependencies..."
    pip install -r requirements.txt || error_exit "Failed to install Python dependencies"

    # Run database migrations
    log "INFO" "Running database migrations"
    print_color "$COLOR_YELLOW" "Running database migrations..."
    python manage.py makemigrations
    python manage.py migrate || error_exit "Failed to run database migrations"

    # Create superuser (optional)
    print_color "$COLOR_YELLOW" "Would you like to create a superuser account? (y/n)"
    read -r CREATE_SUPERUSER
    if [[ "$CREATE_SUPERUSER" =~ ^[Yy]$ ]]; then
        log "INFO" "Creating superuser account"
        print_color "$COLOR_YELLOW" "Creating superuser account..."
        python manage.py createsuperuser
    fi

    # Navigate back to project root
    cd ../..

    # Setup frontend (if exists)
    if [ -d "$FRONTEND_DIR" ]; then
        log "INFO" "Setting up frontend dependencies"
        print_color "$COLOR_YELLOW" "Setting up frontend dependencies..."
        cd "$FRONTEND_DIR"
        
        # Check if Node.js is installed
        if ! command -v node &> /dev/null; then
            log "WARNING" "Node.js not found, skipping frontend setup"
            print_color "$COLOR_YELLOW" "Warning: Node.js not found. Frontend setup skipped."
        else
            npm install || {
                log "ERROR" "Failed to install frontend dependencies"
                print_color "$COLOR_RED" "Error: Failed to install frontend dependencies"
            }
            log "INFO" "Frontend dependencies installed successfully"
            print_color "$COLOR_GREEN" "Frontend dependencies installed successfully"
        fi
        
        cd ../..
    fi

    # Create environment file template
    log "INFO" "Creating environment configuration"
    print_color "$COLOR_YELLOW" "Creating environment configuration..."
    
    if [ ! -f "$BACKEND_DIR/.env" ]; then
        cat > "$BACKEND_DIR/.env" << EOF
# Vierla Development Environment Configuration
# Generated by setup-dev.sh on $(date)

# Django Settings
DEBUG=True
SECRET_KEY=dev-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings
USE_SQLITE=True
DATABASE_URL=sqlite:///db.sqlite3

# Server Settings
VIERLA_HOST=localhost
VIERLA_PORT=8000
EOF
        
        log "INFO" "Environment file created at $BACKEND_DIR/.env"
        print_color "$COLOR_GREEN" "Environment file created at $BACKEND_DIR/.env"
    fi

    # Success message
    log "INFO" "Development environment setup completed successfully"
    echo
    print_color "$COLOR_GREEN" "============================================================================"
    print_color "$COLOR_GREEN" "Development Environment Setup Complete!"
    print_color "$COLOR_GREEN" "============================================================================"
    echo
    print_color "$COLOR_YELLOW" "Next Steps:"
    echo "1. Start the backend server: ${COLOR_BLUE}./scripts/development/start-backend.sh${COLOR_RESET}"
    echo "2. Access the application at: ${COLOR_BLUE}http://localhost:8000${COLOR_RESET}"
    echo "3. Access the admin interface at: ${COLOR_BLUE}http://localhost:8000/admin${COLOR_RESET}"
    echo "4. View API documentation at: ${COLOR_BLUE}http://localhost:8000/api/docs${COLOR_RESET}"
    echo
    print_color "$COLOR_YELLOW" "Configuration:"
    echo "- Environment file: ${COLOR_BLUE}$BACKEND_DIR/.env${COLOR_RESET}"
    echo "- Virtual environment: ${COLOR_BLUE}$BACKEND_DIR/venv${COLOR_RESET}"
    echo "- Log file: ${COLOR_BLUE}$LOG_FILE${COLOR_RESET}"
    echo
}

# Make script executable and run main function
chmod +x "$0"
main "$@"
