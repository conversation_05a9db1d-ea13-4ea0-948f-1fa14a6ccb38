import os
import django
import pytest

# Setup Django environment first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.testing')
django.setup()

# Import Django modules after setup
from django.utils import timezone
from datetime import timedel<PERSON>
from decimal import Decimal
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model

User = get_user_model()


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def user_factory():
    def _create_user(email='<EMAIL>', password='testpass123', role='customer', **kwargs):
        user_data = {
            'email': email,
            'password': password,
            'role': role,
            'first_name': kwargs.get('first_name', 'Test'),
            'last_name': kwargs.get('last_name', 'User'),
            'is_active': kwargs.get('is_active', True),
            'is_verified': kwargs.get('is_verified', True),
            **kwargs
        }
        return User.objects.create_user(**user_data)
    return _create_user


@pytest.fixture
@pytest.mark.django_db
def customer_user(user_factory):
    return user_factory(email='<EMAIL>', role='customer', first_name='Customer', last_name='User')


@pytest.fixture
@pytest.mark.django_db
def provider_user(user_factory):
    return user_factory(email='<EMAIL>', role='service_provider', first_name='Provider', last_name='User')


@pytest.fixture
@pytest.mark.django_db
def admin_user(user_factory):
    return user_factory(email='<EMAIL>', role='admin', first_name='Admin', last_name='User', is_staff=True, is_superuser=True)


@pytest.fixture
def authenticated_client(api_client, customer_user):
    refresh = RefreshToken.for_user(customer_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def provider_client(api_client, provider_user):
    refresh = RefreshToken.for_user(provider_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
def admin_client(api_client, admin_user):
    refresh = RefreshToken.for_user(admin_user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
    return api_client


@pytest.fixture
@pytest.mark.django_db
def service_category():
    from apps.catalog.models import ServiceCategory
    return ServiceCategory.objects.create(
        name='Hair Services',
        slug='hair-services',
        description='Professional hair services',
        icon='✂️',
        color='#4CAF50',
        is_active=True,
        sort_order=1
    )


@pytest.fixture
@pytest.mark.django_db
def service_provider(provider_user, service_category):
    from apps.catalog.models import ServiceProvider
    provider = ServiceProvider.objects.create(
        user=provider_user,
        business_name='Test Beauty Salon',
        business_description='Professional beauty services',
        business_phone='+**********',
        business_email='<EMAIL>',
        address='123 Test Street',
        city='Toronto',
        state='ON',
        zip_code='M5V 3A8',
        country='Canada',
        latitude=Decimal('43.6532'),
        longitude=Decimal('-79.3832'),
        is_active=True,
        is_verified=True
    )
    provider.categories.add(service_category)
    return provider


@pytest.fixture
@pytest.mark.django_db
def service(service_provider, service_category):
    from apps.catalog.models import Service
    return Service.objects.create(
        provider=service_provider,
        category=service_category,
        name='Haircut',
        description='Professional haircut service',
        base_price=Decimal('50.00'),
        duration=60,
        is_active=True,
        is_available=True
    )


@pytest.fixture
@pytest.mark.django_db
def booking(customer_user, service_provider, service):
    from apps.bookings.models import Booking
    return Booking.objects.create(
        customer=customer_user,
        provider=service_provider,
        service=service,
        scheduled_datetime=timezone.now() + timedelta(days=1),
        duration_minutes=60,
        base_price=service.base_price,
        total_amount=service.base_price,
        status='pending',
        location_type='salon'
    )
