"""
Performance Monitoring API Views
Provides endpoints for performance dashboard and monitoring
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.utils import timezone
from datetime import timedelta
from django.db.models import Avg, Count, Max, Min
from .models import (
    PerformanceMetric, SystemHealthMetric, BusinessMetric,
    RealTimeMetric, PerformanceAlert, PerformanceDashboard
)
from .services import PerformanceMonitoringService
from .serializers import (
    PerformanceMetricSerializer, SystemHealthMetricSerializer,
    BusinessMetricSerializer, RealTimeMetricSerializer,
    PerformanceAlertSerializer, PerformanceDashboardSerializer
)
import logging

logger = logging.getLogger(__name__)


class PerformanceMetricViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for performance metrics
    """
    queryset = PerformanceMetric.objects.all()
    serializer_class = PerformanceMetricSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by metric type
        metric_type = self.request.query_params.get('metric_type')
        if metric_type:
            queryset = queryset.filter(metric_type=metric_type)

        # Filter by time range
        hours = self.request.query_params.get('hours', 24)
        try:
            hours = int(hours)
            start_time = timezone.now() - timedelta(hours=hours)
            queryset = queryset.filter(timestamp__gte=start_time)
        except ValueError:
            pass

        # Filter by endpoint
        endpoint = self.request.query_params.get('endpoint')
        if endpoint:
            queryset = queryset.filter(endpoint=endpoint)

        return queryset.order_by('-timestamp')

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get performance metrics summary"""
        try:
            hours = int(request.query_params.get('hours', 24))
            service = PerformanceMonitoringService()
            summary = service.get_performance_summary(hours=hours)
            return Response(summary)
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return Response(
                {'error': 'Failed to get performance summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def latency_trends(self, request):
        """Get API latency trends"""
        try:
            hours = int(request.query_params.get('hours', 24))
            start_time = timezone.now() - timedelta(hours=hours)

            metrics = PerformanceMetric.objects.filter(
                timestamp__gte=start_time,
                metric_type='api_latency'
            ).values('endpoint').annotate(
                avg_latency=Avg('value'),
                max_latency=Max('value'),
                request_count=Count('id')
            ).order_by('-avg_latency')

            return Response(list(metrics))
        except Exception as e:
            logger.error(f"Error getting latency trends: {e}")
            return Response(
                {'error': 'Failed to get latency trends'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SystemHealthMetricViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for system health metrics
    """
    queryset = SystemHealthMetric.objects.all()
    serializer_class = SystemHealthMetricSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by health type
        health_type = self.request.query_params.get('health_type')
        if health_type:
            queryset = queryset.filter(health_type=health_type)

        # Filter by time range
        hours = self.request.query_params.get('hours', 24)
        try:
            hours = int(hours)
            start_time = timezone.now() - timedelta(hours=hours)
            queryset = queryset.filter(timestamp__gte=start_time)
        except ValueError:
            pass

        return queryset.order_by('-timestamp')

    @action(detail=False, methods=['get'])
    def current_status(self, request):
        """Get current system health status"""
        try:
            current_status = {}
            health_types = ['cpu_usage', 'memory_usage',
                            'disk_usage', 'active_connections']

            for health_type in health_types:
                latest_metric = SystemHealthMetric.objects.filter(
                    health_type=health_type
                ).order_by('-timestamp').first()

                if latest_metric:
                    current_status[health_type] = {
                        'value': float(latest_metric.value),
                        'unit': latest_metric.unit,
                        'timestamp': latest_metric.timestamp,
                        'metadata': latest_metric.metadata
                    }

            return Response(current_status)
        except Exception as e:
            logger.error(f"Error getting current system status: {e}")
            return Response(
                {'error': 'Failed to get current system status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RealTimeMetricViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for real-time metrics
    """
    queryset = RealTimeMetric.objects.all()
    serializer_class = RealTimeMetricSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by metric type
        metric_type = self.request.query_params.get('metric_type')
        if metric_type:
            queryset = queryset.filter(metric_type=metric_type)

        # Default to last 5 minutes for real-time data
        minutes = self.request.query_params.get('minutes', 5)
        try:
            minutes = int(minutes)
            start_time = timezone.now() - timedelta(minutes=minutes)
            queryset = queryset.filter(timestamp__gte=start_time)
        except ValueError:
            pass

        return queryset.order_by('-timestamp')

    @action(detail=False, methods=['get'])
    def live_dashboard(self, request):
        """Get live dashboard data"""
        try:
            service = PerformanceMonitoringService()
            dashboard_data = service.get_dashboard_data('real_time_monitoring')
            return Response(dashboard_data)
        except Exception as e:
            logger.error(f"Error getting live dashboard data: {e}")
            return Response(
                {'error': 'Failed to get live dashboard data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceAlertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for performance alerts
    """
    queryset = PerformanceAlert.objects.all()
    serializer_class = PerformanceAlertSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Filter by severity
        severity = self.request.query_params.get('severity')
        if severity:
            queryset = queryset.filter(severity=severity)

        # Filter by alert type
        alert_type = self.request.query_params.get('alert_type')
        if alert_type:
            queryset = queryset.filter(alert_type=alert_type)

        return queryset.order_by('-triggered_at')

    @action(detail=True, methods=['post'])
    def acknowledge(self, request, pk=None):
        """Acknowledge an alert"""
        try:
            alert = self.get_object()
            alert.status = 'acknowledged'
            alert.acknowledged_at = timezone.now()
            alert.acknowledged_by = request.user
            alert.save()

            return Response({'message': 'Alert acknowledged successfully'})
        except Exception as e:
            logger.error(f"Error acknowledging alert: {e}")
            return Response(
                {'error': 'Failed to acknowledge alert'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        """Resolve an alert"""
        try:
            alert = self.get_object()
            alert.status = 'resolved'
            alert.resolved_at = timezone.now()
            alert.save()

            return Response({'message': 'Alert resolved successfully'})
        except Exception as e:
            logger.error(f"Error resolving alert: {e}")
            return Response(
                {'error': 'Failed to resolve alert'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def active_summary(self, request):
        """Get summary of active alerts"""
        try:
            active_alerts = PerformanceAlert.objects.filter(status='active')

            summary = {
                'total_active': active_alerts.count(),
                'by_severity': {},
                'by_type': {},
                'recent_alerts': []
            }

            # Group by severity
            for severity, _ in PerformanceAlert.SEVERITY_LEVELS:
                count = active_alerts.filter(severity=severity).count()
                summary['by_severity'][severity] = count

            # Group by type
            for alert_type, _ in PerformanceAlert.ALERT_TYPES:
                count = active_alerts.filter(alert_type=alert_type).count()
                if count > 0:
                    summary['by_type'][alert_type] = count

            # Recent alerts (last 5)
            recent = active_alerts.order_by('-triggered_at')[:5]
            summary['recent_alerts'] = PerformanceAlertSerializer(
                recent, many=True).data

            return Response(summary)
        except Exception as e:
            logger.error(f"Error getting active alerts summary: {e}")
            return Response(
                {'error': 'Failed to get active alerts summary'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PerformanceDashboardViewSet(viewsets.ModelViewSet):
    """
    ViewSet for performance dashboards
    """
    queryset = PerformanceDashboard.objects.all()
    serializer_class = PerformanceDashboardSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def system_overview(self, request):
        """Get system overview dashboard data"""
        try:
            service = PerformanceMonitoringService()
            dashboard_data = service.get_dashboard_data('system_overview')
            return Response(dashboard_data)
        except Exception as e:
            logger.error(f"Error getting system overview: {e}")
            return Response(
                {'error': 'Failed to get system overview'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def api_performance(self, request):
        """Get API performance dashboard data"""
        try:
            service = PerformanceMonitoringService()
            dashboard_data = service.get_dashboard_data('api_performance')
            return Response(dashboard_data)
        except Exception as e:
            logger.error(f"Error getting API performance data: {e}")
            return Response(
                {'error': 'Failed to get API performance data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
