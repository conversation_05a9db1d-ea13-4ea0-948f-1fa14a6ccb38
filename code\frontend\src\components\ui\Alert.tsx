/**
 * Alert Component
 * shadcn/ui inspired alert component for notifications and messages
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { Text } from './Text';
import { useTheme } from '../../contexts/ThemeContext';
import { createVariants, cn } from '../../lib/utils';

export interface AlertProps {
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info';
  title?: string;
  description?: string;
  children?: React.ReactNode;
  icon?: keyof typeof Ionicons.glyphMap;
  showIcon?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  style?: ViewStyle;
  testID?: string;
}

const createAlertVariants = (colors: any, spacing: any, borderRadius: any) => createVariants({
  base: {
    padding: spacing.md,
    borderRadius: borderRadius.md || 8,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  variants: {
    variant: {
      default: {
        backgroundColor: colors.background.secondary,
        borderColor: colors.text.tertiary,
      },
      destructive: {
        backgroundColor: colors.destructive + '20' || '#FEF2F2',
        borderColor: colors.destructive || '#EF4444',
      },
      success: {
        backgroundColor: colors.success + '20' || '#F0FDF4',
        borderColor: colors.success || '#10B981',
      },
      warning: {
        backgroundColor: colors.warning + '20' || '#FFFBEB',
        borderColor: colors.warning || '#F59E0B',
      },
      info: {
        backgroundColor: colors.info + '20' || '#EFF6FF',
        borderColor: colors.info || '#3B82F6',
      },
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

const getIconName = (variant: AlertProps['variant']): keyof typeof Ionicons.glyphMap => {
  switch (variant) {
    case 'destructive':
      return 'alert-circle';
    case 'success':
      return 'checkmark-circle';
    case 'warning':
      return 'warning';
    case 'info':
      return 'information-circle';
    default:
      return 'notifications';
  }
};

const getIconColor = (variant: AlertProps['variant'], colors: any): string => {
  switch (variant) {
    case 'destructive':
      return colors.destructive || '#EF4444';
    case 'success':
      return colors.success || '#10B981';
    case 'warning':
      return colors.warning || '#F59E0B';
    case 'info':
      return colors.info || '#3B82F6';
    default:
      return colors.primary;
  }
};

export const Alert: React.FC<AlertProps> = ({
  variant = 'default',
  title,
  description,
  children,
  icon,
  showIcon = true,
  dismissible = false,
  onDismiss,
  style,
  testID = 'alert',
}) => {
  const { colors, spacing, borderRadius } = useTheme();
  const alertVariants = createAlertVariants(colors, spacing, borderRadius);
  const alertStyle = alertVariants({ variant });
  const iconName = icon || getIconName(variant);
  const iconColor = getIconColor(variant, colors);

  const dynamicStyles = {
    iconContainer: {
      marginRight: spacing.sm,
      marginTop: 2,
    },
    content: {
      flex: 1,
    },
    title: {
      fontWeight: '600' as const,
      color: colors.text.primary,
      marginBottom: spacing.xs / 2,
    },
    description: {
      color: colors.text.secondary,
      lineHeight: 18,
    },
    dismissButton: {
      padding: spacing.xs,
      marginLeft: spacing.sm,
    },
  };

  return (
    <View style={cn(alertStyle, style)} testID={testID}>
      {showIcon && (
        <View style={dynamicStyles.iconContainer}>
          <Ionicons
            name={iconName}
            size={20}
            color={iconColor}
          />
        </View>
      )}

      <View style={dynamicStyles.content}>
        {title && (
          <Text variant="body" style={dynamicStyles.title}>
            {title}
          </Text>
        )}
        {description && (
          <Text variant="caption" style={dynamicStyles.description}>
            {description}
          </Text>
        )}
        {children}
      </View>

      {dismissible && onDismiss && (
        <TouchableOpacity
          style={dynamicStyles.dismissButton}
          onPress={onDismiss}
          testID="alert-dismiss-button"
        >
          <Ionicons
            name="close"
            size={16}
            color={colors.text.secondary}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

// Predefined alert variants for common use cases
export const AlertDestructive: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="destructive" {...props} />
);

export const AlertSuccess: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="success" {...props} />
);

export const AlertWarning: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="warning" {...props} />
);

export const AlertInfo: React.FC<Omit<AlertProps, 'variant'>> = (props) => (
  <Alert variant="info" {...props} />
);


