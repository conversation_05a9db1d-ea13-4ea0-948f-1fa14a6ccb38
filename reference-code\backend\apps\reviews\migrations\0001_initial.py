# Generated by Django 5.2.4 on 2025-07-23 16:46

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("bookings", "0001_initial"),
        ("catalog", "0004_populate_service_categories"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Review",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.PositiveIntegerField(
                        help_text="Rating from 1 to 5 stars",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("comment", models.TextField(help_text="Review comment text")),
                (
                    "images",
                    models.J<PERSON><PERSON>ield(
                        blank=True,
                        default=list,
                        help_text="List of image URLs for the review",
                    ),
                ),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False, help_text="Whether this review has been verified"
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False, help_text="Whether this review is featured"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="When the review was created",
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(
                        auto_now=True, help_text="When the review was last updated"
                    ),
                ),
                (
                    "booking",
                    models.OneToOneField(
                        help_text="Booking this review is for",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="review",
                        to="bookings.booking",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        help_text="Customer who wrote the review",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "provider",
                    models.ForeignKey(
                        help_text="Service provider being reviewed",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to="catalog.serviceprovider",
                    ),
                ),
                (
                    "service",
                    models.ForeignKey(
                        help_text="Service being reviewed",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reviews",
                        to="catalog.service",
                    ),
                ),
            ],
            options={
                "verbose_name": "Review",
                "verbose_name_plural": "Reviews",
                "db_table": "reviews",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["provider", "rating"], name="reviews_provide_e960ec_idx"
                    ),
                    models.Index(
                        fields=["service", "rating"], name="reviews_service_3707b3_idx"
                    ),
                    models.Index(
                        fields=["customer"], name="reviews_custome_a5b696_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="reviews_created_53b5d6_idx"
                    ),
                    models.Index(
                        fields=["is_verified", "is_featured"],
                        name="reviews_is_veri_6b0061_idx",
                    ),
                ],
                "unique_together": {("customer", "provider", "booking")},
            },
        ),
    ]
