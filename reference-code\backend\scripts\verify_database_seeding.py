#!/usr/bin/env python
"""
Database Verification & Summary Report
Comprehensive verification of all seeded data and platform readiness
"""

import os
import sys
import django
from decimal import Decimal
from pathlib import Path
from collections import defaultdict

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from django.db.models import Count, Avg, Sum, Min, Max, Q
from apps.catalog.models import ServiceProvider, Service, ServiceCategory
from apps.bookings.models import Booking
from apps.reviews.models import Review

User = get_user_model()

def print_section_header(title):
    """Print a formatted section header"""
    print(f"\n{'=' * 80}")
    print(f"  {title}")
    print(f"{'=' * 80}")

def print_subsection(title):
    """Print a formatted subsection header"""
    print(f"\n{'-' * 60}")
    print(f"  {title}")
    print(f"{'-' * 60}")

def verify_users():
    """Verify user accounts and profiles"""
    print_section_header("USER ACCOUNTS VERIFICATION")
    
    # Basic user counts
    total_users = User.objects.count()
    customers = User.objects.filter(role='customer').count()
    providers = User.objects.filter(role='service_provider').count()
    admins = User.objects.filter(role='admin').count()
    
    print(f"📊 User Account Summary:")
    print(f"   Total Users: {total_users}")
    print(f"   Customers: {customers}")
    print(f"   Service Providers: {providers}")
    print(f"   Admins: {admins}")
    
    # User verification status
    verified_users = User.objects.filter(is_verified=True).count()
    active_users = User.objects.filter(is_active=True).count()
    
    print(f"\n📋 Account Status:")
    print(f"   Verified Users: {verified_users} ({verified_users/total_users*100:.1f}%)")
    print(f"   Active Users: {active_users} ({active_users/total_users*100:.1f}%)")
    
    # Language distribution
    languages = User.objects.values('preferred_language').annotate(count=Count('id')).order_by('-count')
    print(f"\n🌍 Language Distribution:")
    for lang in languages:
        print(f"   {lang['preferred_language']}: {lang['count']} users")
    
    # Profile completeness
    users_with_profiles = User.objects.filter(profile__isnull=False).count()
    print(f"\n👤 Profile Completeness:")
    print(f"   Users with Profiles: {users_with_profiles} ({users_with_profiles/total_users*100:.1f}%)")
    
    return {
        'total_users': total_users,
        'customers': customers,
        'providers': providers,
        'verified_rate': verified_users/total_users*100,
        'active_rate': active_users/total_users*100
    }

def verify_service_providers():
    """Verify service provider data"""
    print_section_header("SERVICE PROVIDERS VERIFICATION")
    
    # Basic provider counts
    total_providers = ServiceProvider.objects.count()
    active_providers = ServiceProvider.objects.filter(is_active=True).count()
    verified_providers = ServiceProvider.objects.filter(is_verified=True).count()
    featured_providers = ServiceProvider.objects.filter(is_featured=True).count()
    
    print(f"📊 Service Provider Summary:")
    print(f"   Total Providers: {total_providers}")
    print(f"   Active Providers: {active_providers} ({active_providers/total_providers*100:.1f}%)")
    print(f"   Verified Providers: {verified_providers} ({verified_providers/total_providers*100:.1f}%)")
    print(f"   Featured Providers: {featured_providers} ({featured_providers/total_providers*100:.1f}%)")
    
    # Provider distribution by category
    print_subsection("Provider Distribution by Category")
    categories = ServiceCategory.objects.annotate(
        provider_count=Count('providers')
    ).order_by('-provider_count')
    
    for category in categories:
        print(f"   {category.name}: {category.provider_count} providers")
    
    # Rating distribution
    avg_rating = ServiceProvider.objects.aggregate(avg_rating=Avg('rating'))['avg_rating']
    rating_distribution = ServiceProvider.objects.values('rating').annotate(count=Count('id')).order_by('rating')
    
    print(f"\n⭐ Rating Analysis:")
    print(f"   Average Rating: {avg_rating:.2f}")
    print(f"   Rating Distribution:")
    for rating in rating_distribution:
        print(f"     {rating['rating']:.1f} stars: {rating['count']} providers")
    
    # Geographic distribution
    cities = ServiceProvider.objects.values('city').annotate(count=Count('id')).order_by('-count')
    print(f"\n🏙️ Geographic Distribution:")
    for city in cities[:10]:  # Top 10 cities
        print(f"   {city['city']}: {city['count']} providers")
    
    return {
        'total_providers': total_providers,
        'active_rate': active_providers/total_providers*100,
        'verified_rate': verified_providers/total_providers*100,
        'avg_rating': avg_rating
    }

def verify_services():
    """Verify service listings"""
    print_section_header("SERVICE LISTINGS VERIFICATION")
    
    # Basic service counts
    total_services = Service.objects.count()
    active_services = Service.objects.filter(is_active=True).count()
    popular_services = Service.objects.filter(is_popular=True).count()
    available_services = Service.objects.filter(is_available=True).count()
    
    print(f"📊 Service Listings Summary:")
    print(f"   Total Services: {total_services}")
    print(f"   Active Services: {active_services} ({active_services/total_services*100:.1f}%)")
    print(f"   Popular Services: {popular_services} ({popular_services/total_services*100:.1f}%)")
    print(f"   Available Services: {available_services} ({available_services/total_services*100:.1f}%)")
    
    # Services by category
    print_subsection("Services by Category")
    service_categories = Service.objects.values('category__name').annotate(
        count=Count('id')
    ).order_by('-count')
    
    for category in service_categories:
        print(f"   {category['category__name']}: {category['count']} services")
    
    # Price analysis
    price_stats = Service.objects.aggregate(
        min_price=Min('base_price'),
        max_price=Max('base_price'),
        avg_price=Avg('base_price')
    )
    
    print(f"\n💰 Pricing Analysis:")
    print(f"   Price Range: ${price_stats['min_price']} - ${price_stats['max_price']}")
    print(f"   Average Price: ${price_stats['avg_price']:.2f}")
    
    # Duration analysis
    duration_stats = Service.objects.aggregate(
        min_duration=Min('duration'),
        max_duration=Max('duration'),
        avg_duration=Avg('duration')
    )
    
    print(f"\n⏱️ Duration Analysis:")
    print(f"   Duration Range: {duration_stats['min_duration']} - {duration_stats['max_duration']} minutes")
    print(f"   Average Duration: {duration_stats['avg_duration']:.1f} minutes")
    
    # Price type distribution
    price_types = Service.objects.values('price_type').annotate(count=Count('id'))
    print(f"\n💵 Price Type Distribution:")
    for price_type in price_types:
        print(f"   {price_type['price_type']}: {price_type['count']} services")
    
    return {
        'total_services': total_services,
        'active_rate': active_services/total_services*100,
        'avg_price': price_stats['avg_price'],
        'avg_duration': duration_stats['avg_duration']
    }

def verify_bookings():
    """Verify booking history"""
    print_section_header("BOOKING HISTORY VERIFICATION")
    
    # Basic booking counts
    total_bookings = Booking.objects.count()
    completed_bookings = Booking.objects.filter(status='completed').count()
    cancelled_bookings = Booking.objects.filter(status='cancelled').count()
    pending_bookings = Booking.objects.filter(status='pending').count()
    confirmed_bookings = Booking.objects.filter(status='confirmed').count()
    no_show_bookings = Booking.objects.filter(status='no_show').count()
    
    print(f"📊 Booking Summary:")
    print(f"   Total Bookings: {total_bookings}")
    print(f"   Completed: {completed_bookings} ({completed_bookings/total_bookings*100:.1f}%)")
    print(f"   Cancelled: {cancelled_bookings} ({cancelled_bookings/total_bookings*100:.1f}%)")
    print(f"   Pending: {pending_bookings} ({pending_bookings/total_bookings*100:.1f}%)")
    print(f"   Confirmed: {confirmed_bookings} ({confirmed_bookings/total_bookings*100:.1f}%)")
    print(f"   No Show: {no_show_bookings} ({no_show_bookings/total_bookings*100:.1f}%)")
    
    # Revenue analysis
    revenue_stats = Booking.objects.filter(
        status='completed'
    ).aggregate(
        total_revenue=Sum('total_amount'),
        avg_booking_value=Avg('total_amount'),
        total_tax=Sum('tax_amount')
    )
    
    print(f"\n💰 Revenue Analysis (Completed Bookings):")
    print(f"   Total Revenue: ${revenue_stats['total_revenue']:,.2f}")
    print(f"   Average Booking Value: ${revenue_stats['avg_booking_value']:.2f}")
    print(f"   Total Tax Collected: ${revenue_stats['total_tax']:,.2f}")
    
    # Payment status distribution
    payment_statuses = Booking.objects.values('payment_status').annotate(count=Count('id'))
    print(f"\n💳 Payment Status Distribution:")
    for status in payment_statuses:
        print(f"   {status['payment_status']}: {status['count']} bookings")
    
    # Booking frequency by customer
    customer_booking_stats = Booking.objects.values('customer').annotate(
        booking_count=Count('id')
    ).aggregate(
        avg_bookings_per_customer=Avg('booking_count'),
        max_bookings=Max('booking_count')
    )
    
    print(f"\n👥 Customer Engagement:")
    print(f"   Average Bookings per Customer: {customer_booking_stats['avg_bookings_per_customer']:.1f}")
    print(f"   Most Active Customer: {customer_booking_stats['max_bookings']} bookings")
    
    return {
        'total_bookings': total_bookings,
        'completion_rate': completed_bookings/total_bookings*100,
        'total_revenue': revenue_stats['total_revenue'],
        'avg_booking_value': revenue_stats['avg_booking_value']
    }

def verify_reviews():
    """Verify review data"""
    print_section_header("REVIEWS VERIFICATION")
    
    # Basic review counts
    total_reviews = Review.objects.count()
    verified_reviews = Review.objects.filter(is_verified=True).count()
    featured_reviews = Review.objects.filter(is_featured=True).count()
    
    print(f"📊 Review Summary:")
    print(f"   Total Reviews: {total_reviews}")
    print(f"   Verified Reviews: {verified_reviews} ({verified_reviews/total_reviews*100:.1f}%)")
    print(f"   Featured Reviews: {featured_reviews} ({featured_reviews/total_reviews*100:.1f}%)")
    
    # Rating distribution
    rating_distribution = Review.objects.values('rating').annotate(count=Count('id')).order_by('rating')
    avg_rating = Review.objects.aggregate(avg_rating=Avg('rating'))['avg_rating']
    
    print(f"\n⭐ Rating Distribution:")
    print(f"   Average Rating: {avg_rating:.2f}")
    for rating in rating_distribution:
        stars = "★" * rating['rating'] + "☆" * (5 - rating['rating'])
        print(f"   {stars} ({rating['rating']}): {rating['count']} reviews ({rating['count']/total_reviews*100:.1f}%)")
    
    # Review rate calculation
    completed_bookings = Booking.objects.filter(status='completed').count()
    review_rate = (total_reviews / completed_bookings * 100) if completed_bookings > 0 else 0
    
    print(f"\n📝 Review Engagement:")
    print(f"   Review Rate: {review_rate:.1f}% (reviews per completed booking)")
    
    # Top reviewed providers
    top_providers = Review.objects.values('provider__business_name').annotate(
        review_count=Count('id'),
        avg_rating=Avg('rating')
    ).order_by('-review_count')[:5]
    
    print(f"\n🏆 Top Reviewed Providers:")
    for provider in top_providers:
        print(f"   {provider['provider__business_name']}: {provider['review_count']} reviews (avg: {provider['avg_rating']:.1f}★)")
    
    return {
        'total_reviews': total_reviews,
        'avg_rating': avg_rating,
        'review_rate': review_rate,
        'verified_rate': verified_reviews/total_reviews*100
    }

def generate_platform_readiness_report():
    """Generate overall platform readiness assessment"""
    print_section_header("PLATFORM READINESS ASSESSMENT")
    
    # Collect all verification data
    user_data = verify_users()
    provider_data = verify_service_providers()
    service_data = verify_services()
    booking_data = verify_bookings()
    review_data = verify_reviews()
    
    # Calculate readiness scores
    readiness_scores = {
        'User Base': min(100, (user_data['customers'] / 25) * 100),  # Target: 25+ customers
        'Provider Network': min(100, (provider_data['total_providers'] / 40) * 100),  # Target: 40+ providers
        'Service Catalog': min(100, (service_data['total_services'] / 150) * 100),  # Target: 150+ services
        'Transaction History': min(100, (booking_data['total_bookings'] / 100) * 100),  # Target: 100+ bookings
        'Review System': min(100, (review_data['total_reviews'] / 50) * 100),  # Target: 50+ reviews
    }
    
    overall_readiness = sum(readiness_scores.values()) / len(readiness_scores)
    
    print(f"\n🎯 Platform Readiness Scores:")
    for category, score in readiness_scores.items():
        status = "✅" if score >= 100 else "🟡" if score >= 75 else "🔴"
        print(f"   {status} {category}: {score:.1f}%")
    
    print(f"\n🏆 Overall Platform Readiness: {overall_readiness:.1f}%")
    
    # Readiness assessment
    if overall_readiness >= 90:
        status = "🚀 READY FOR LAUNCH"
        message = "Platform is fully seeded and ready for production deployment!"
    elif overall_readiness >= 75:
        status = "🟡 MOSTLY READY"
        message = "Platform is well-prepared with minor gaps that can be addressed."
    else:
        status = "🔴 NEEDS MORE DATA"
        message = "Platform needs additional seeding before launch."
    
    print(f"\n{status}")
    print(f"📋 Assessment: {message}")
    
    return {
        'overall_readiness': overall_readiness,
        'readiness_scores': readiness_scores,
        'status': status
    }

def main():
    """Main verification function"""
    print("🔍 VIERLA PLATFORM DATABASE VERIFICATION")
    print("=" * 80)
    print("Comprehensive analysis of seeded data and platform readiness")
    
    try:
        # Run all verification checks
        readiness_data = generate_platform_readiness_report()
        
        print_section_header("VERIFICATION COMPLETE")
        print(f"✅ Database verification completed successfully!")
        print(f"📊 Platform Readiness: {readiness_data['overall_readiness']:.1f}%")
        print(f"🎯 Status: {readiness_data['status']}")
        
        print(f"\n📝 Next Steps:")
        if readiness_data['overall_readiness'] >= 90:
            print("   1. ✅ Database seeding is complete")
            print("   2. ✅ Platform is ready for frontend integration")
            print("   3. ✅ Ready for user acceptance testing")
            print("   4. ✅ Prepared for production deployment")
        else:
            print("   1. 🔄 Consider adding more sample data if needed")
            print("   2. 🧪 Test platform functionality with current data")
            print("   3. 📈 Monitor user engagement patterns")
            print("   4. 🔧 Adjust seeding parameters as needed")
        
        print(f"\n🎉 Vierla Platform Database Seeding Project Complete!")
        
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
