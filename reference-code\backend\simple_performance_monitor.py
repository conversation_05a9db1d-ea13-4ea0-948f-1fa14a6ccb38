#!/usr/bin/env python3
"""
Simple Performance monitoring script for Vierla backend services.
Monitors CPU, memory, and process-specific metrics without Django dependencies.
"""

import time
import psutil
import os
import sys
from datetime import datetime

def monitor_performance():
    """Monitor system and process performance metrics."""
    print("=== VIERLA PERFORMANCE METRICS MONITORING ===")
    print("Monitoring system performance metrics...")
    print("Press Ctrl+C to stop monitoring")
    print("-" * 50)
    
    while True:
        try:
            # Get current timestamp
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network stats
            net_io = psutil.net_io_counters()
            
            # Find Django and Expo processes
            django_processes = []
            expo_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    if 'python' in proc_info['name'].lower():
                        cmdline = proc.cmdline()
                        if any('manage.py' in arg for arg in cmdline):
                            django_processes.append(proc_info)
                    elif 'node' in proc_info['name'].lower():
                        cmdline = proc.cmdline()
                        if any('expo' in arg for arg in cmdline):
                            expo_processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            
            # Display metrics
            print(f"[{timestamp}] CPU: {cpu_percent:5.1f}% | RAM: {memory.percent:5.1f}% | Disk: {disk.percent:5.1f}%")
            print(f"           Network: ↑{net_io.bytes_sent//1024:,}KB ↓{net_io.bytes_recv//1024:,}KB")
            
            if django_processes:
                proc = django_processes[0]
                print(f"           Django:  CPU {proc['cpu_percent']:5.1f}% RAM {proc['memory_percent']:5.1f}%")
            else:
                print("           Django:  Not running")
                
            if expo_processes:
                proc = expo_processes[0]
                print(f"           Expo:    CPU {proc['cpu_percent']:5.1f}% RAM {proc['memory_percent']:5.1f}%")
            else:
                print("           Expo:    Not running")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\nPerformance monitoring stopped.")
            break
        except Exception as e:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] Performance monitoring error: {e}")
        
        time.sleep(15)

if __name__ == "__main__":
    monitor_performance()
