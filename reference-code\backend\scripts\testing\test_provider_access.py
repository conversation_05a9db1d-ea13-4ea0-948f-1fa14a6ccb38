#!/usr/bin/env python
"""
Test script to verify provider access to booking endpoints
"""
import os
import django
import requests
import json

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.authentication.models import User

def get_auth_token(email, password):
    """Get JWT token for authentication"""
    url = 'http://localhost:8000/api/auth/login/'
    data = {
        'email': email,
        'password': password
    }
    
    response = requests.post(url, json=data)
    if response.status_code == 200:
        return response.json().get('access')
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_booking_access(token, user_role):
    """Test access to booking endpoints"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test booking list endpoint
    url = 'http://localhost:8000/api/bookings/bookings/'
    response = requests.get(url, headers=headers)
    
    print(f"\n=== Testing Booking Access for {user_role} ===")
    print(f"URL: {url}")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text[:200]}...")
    
    if response.status_code == 200:
        print("✅ SUCCESS: Provider can access booking endpoints!")
        data = response.json()
        print(f"Number of bookings returned: {len(data.get('results', []))}")
    else:
        print("❌ FAILED: Provider cannot access booking endpoints")
    
    return response.status_code == 200

def main():
    print("🔧 Testing Provider Access to Booking Endpoints")
    print("=" * 50)
    
    # Test provider access
    print("\n1. Testing Provider Access...")
    provider_token = get_auth_token('<EMAIL>', 'testpass123')
    
    if provider_token:
        print("✅ Provider login successful")
        provider_success = test_booking_access(provider_token, 'service_provider')
    else:
        print("❌ Provider login failed")
        provider_success = False
    
    # Test customer access for comparison
    print("\n2. Testing Customer Access for comparison...")
    customer_token = get_auth_token('<EMAIL>', 'testpass123')
    
    if customer_token:
        print("✅ Customer login successful")
        customer_success = test_booking_access(customer_token, 'customer')
    else:
        print("❌ Customer login failed")
        customer_success = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    print(f"Provider Access: {'✅ WORKING' if provider_success else '❌ FAILED'}")
    print(f"Customer Access: {'✅ WORKING' if customer_success else '❌ FAILED'}")
    
    if provider_success:
        print("\n🎉 AUTHENTICATION BREAKTHROUGH CONFIRMED!")
        print("Provider users can now access booking endpoints successfully.")
    else:
        print("\n⚠️ Issue still exists - provider access failed")

if __name__ == '__main__':
    main()
