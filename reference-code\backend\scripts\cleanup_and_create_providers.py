#!/usr/bin/env python
"""
Cleanup and Create Service Providers
Cleans up existing mock service provider accounts and creates fresh ones
"""

import os
import sys
import django
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from apps.catalog.models import ServiceProvider

User = get_user_model()

def cleanup_mock_providers():
    """Clean up existing mock service provider accounts"""
    print("🧹 Cleaning up existing mock service provider accounts...")
    
    # List of mock provider emails
    mock_emails = [
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>', '<EMAIL>', '<EMAIL>',
        '<EMAIL>'
    ]
    
    # Delete existing mock users
    deleted_count = 0
    for email in mock_emails:
        try:
            user = User.objects.get(email=email)
            user.delete()
            deleted_count += 1
            print(f"   ✅ Deleted: {email}")
        except User.DoesNotExist:
            print(f"   ⚠️ Not found: {email}")
    
    print(f"🗑️ Cleaned up {deleted_count} existing mock accounts")
    return deleted_count

def main():
    """Main cleanup function"""
    print("🚀 Mock Service Provider Cleanup")
    print("=" * 50)
    
    # Check current state
    total_users = User.objects.count()
    service_providers = User.objects.filter(role='service_provider').count()
    
    print(f"📊 Current state:")
    print(f"   Total users: {total_users}")
    print(f"   Service providers: {service_providers}")
    print()
    
    # Cleanup
    deleted = cleanup_mock_providers()
    
    # Check final state
    final_users = User.objects.count()
    final_providers = User.objects.filter(role='service_provider').count()
    
    print(f"\n📊 Final state:")
    print(f"   Total users: {final_users}")
    print(f"   Service providers: {final_providers}")
    print(f"   Users removed: {total_users - final_users}")
    
    print(f"\n✅ Cleanup complete! Ready to create fresh mock accounts.")

if __name__ == "__main__":
    main()
