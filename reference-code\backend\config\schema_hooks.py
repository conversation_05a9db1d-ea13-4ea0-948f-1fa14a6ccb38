"""
Custom schema hooks for DRF Spectacular
"""

def preprocess_exclude_problematic_serializers(result, generator, request, public):
    """
    Temporarily exclude problematic serializers from schema generation
    """
    # Remove problematic components that cause YAML representation errors
    if 'components' in result and 'schemas' in result['components']:
        schemas_to_remove = []
        for schema_name, schema_def in result['components']['schemas'].items():
            # Check if this schema contains problematic references
            if 'UserSerializer' in schema_name or 'User' in schema_name:
                # Skip problematic user-related schemas for now
                schemas_to_remove.append(schema_name)
        
        # Remove problematic schemas
        for schema_name in schemas_to_remove:
            if schema_name in result['components']['schemas']:
                del result['components']['schemas'][schema_name]
    
    return result
