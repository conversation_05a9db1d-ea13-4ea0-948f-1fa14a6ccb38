/**
 * Test Utilities
 * Common test helpers and providers for React Native testing
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from '../../contexts/ThemeContext';

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

// Test wrapper component that provides all necessary contexts
interface TestWrapperProps {
  children: React.ReactNode;
  queryClient?: QueryClient;
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  queryClient = createTestQueryClient() 
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
};

// Custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
}

const customRender = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { queryClient, ...renderOptions } = options;
  
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper queryClient={queryClient}>
      {children}
    </TestWrapper>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock theme data for tests
const mockTheme = {
  colors: {
    primary: '#007AFF',
    primaryLight: '#4DA3FF',
    primaryDark: '#0056CC',
    background: {
      primary: '#FFFFFF',
      secondary: '#F8F9FA',
      light: '#FAFBFC',
    },
    text: {
      primary: '#1A1A1A',
      secondary: '#666666',
      tertiary: '#999999',
    },
    white: '#FFFFFF',
    black: '#000000',
    gray: '#F5F5F5',
    lightGray: '#E5E5E5',
    darkGray: '#333333',
    red: '#FF3B30',
    green: '#34C759',
    blue: '#007AFF',
    orange: '#FF9500',
    yellow: '#FFCC00',
    purple: '#AF52DE',
    pink: '#FF2D92',
    indigo: '#5856D6',
    teal: '#5AC8FA',
    cyan: '#32D74B',
    mint: '#00C7BE',
    brown: '#A2845E',
    taupe: '#8E8E93',
    error: '#FF3B30',
    success: '#34C759',
    warning: '#FF9500',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  typography: {
    fontSize: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36,
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
};

// Mock ThemeContext
jest.mock('../../contexts/ThemeContext', () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useTheme: () => mockTheme,
}));

// Mock AuthContext
const mockAuthContext = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  updateProfile: jest.fn(),
};

jest.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => mockAuthContext,
}));

// Mock data generators
export const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'provider' as const,
  isVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
};

export const mockService = {
  id: 'service-1',
  name: 'Premium Haircut',
  description: 'Professional haircut service',
  category: 'Hair Services',
  price: 75.00,
  duration: 60,
  providerId: 'provider-1',
  isActive: true,
  bookingCount: 15,
  createdAt: '2024-01-01T00:00:00Z',
};

export const mockBooking = {
  id: 'booking-1',
  serviceId: 'service-1',
  serviceName: 'Premium Haircut',
  clientName: 'Sarah Johnson',
  clientEmail: '<EMAIL>',
  date: '2024-01-15',
  time: '10:00',
  status: 'confirmed' as const,
  price: 75.00,
  duration: 60,
  createdAt: '2024-01-01T00:00:00Z',
};

export const mockAuthTokens = {
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
  expiresIn: 3600,
  tokenType: 'Bearer',
};

// Test helpers
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0));

export const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Export everything
export * from '@testing-library/react-native';
export { customRender as render };
export { createTestQueryClient };
export { TestWrapper };
