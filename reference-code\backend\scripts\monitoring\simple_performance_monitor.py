#!/usr/bin/env python
"""
Simple Performance Monitoring Dashboard for Vierla Backend
Real-time system and API performance monitoring
"""

import os
import sys
import time
import requests
from datetime import datetime
import psutil


class SimplePerformanceDashboard:
    """Simple performance monitoring dashboard"""

    def __init__(self):
        self.api_base_url = "http://192.168.2.65:8000"
        self.refresh_interval = 5  # seconds

    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def get_colored_text(self, text, color):
        """Get colored text for terminal output"""
        colors = {
            'red': '\033[91m',
            'green': '\033[92m',
            'yellow': '\033[93m',
            'blue': '\033[94m',
            'cyan': '\033[96m',
            'white': '\033[97m',
            'bold': '\033[1m',
            'end': '\033[0m'
        }
        return f"{colors.get(color, '')}{text}{colors['end']}"

    def get_status_indicator(self, value, thresholds):
        """Get status indicator based on value and thresholds"""
        if value < thresholds['good']:
            return self.get_colored_text("●", "green")
        elif value < thresholds['warning']:
            return self.get_colored_text("●", "yellow")
        else:
            return self.get_colored_text("●", "red")

    def format_bytes(self, bytes_value):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"

    def get_system_metrics(self):
        """Get current system metrics"""
        try:
            # CPU Usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory Usage
            memory = psutil.virtual_memory()

            # Disk Usage (use current directory for Windows compatibility)
            disk = psutil.disk_usage('.')

            # Network Stats
            network = psutil.net_io_counters()

            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_total': memory.total,
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_used': disk.used,
                'disk_total': disk.total,
                'network_sent': network.bytes_sent,
                'network_recv': network.bytes_recv
            }
        except Exception as e:
            print(f"Error getting system metrics: {e}")
            return {}

    def test_api_connectivity(self):
        """Test API connectivity"""
        try:
            response = requests.get(f"{self.api_base_url}/api/", timeout=5)
            return response.status_code in [200, 404]  # 404 is OK for root API
        except:
            return False

    def test_api_endpoints(self):
        """Test specific API endpoints with food delivery app-analogous metrics"""
        endpoints = [
            ('/api/auth/register/', 'Auth Service', 'Authentication'),
            ('/api/catalog/categories/', 'Catalog Service', 'Menu/Services'),
            ('/api/bookings/', 'Booking Service', 'Orders'),
            ('/api/catalog/providers/', 'Provider Service', 'Services Store'),
            ('/api/auth/social/', 'Social Auth', 'User Login'),
        ]

        results = {}
        total_requests = 0
        successful_requests = 0
        total_latency = 0

        for endpoint, name, analogy in endpoints:
            try:
                start_time = time.time()
                response = requests.get(
                    f"{self.api_base_url}{endpoint}", timeout=5)
                latency = (time.time() - start_time) * 1000  # Convert to ms

                total_requests += 1
                if response.status_code < 500:
                    successful_requests += 1
                total_latency += latency

                # Determine service health based on latency and status
                if response.status_code < 400 and latency < 200:
                    health = 'excellent'
                elif response.status_code < 500 and latency < 500:
                    health = 'good'
                elif response.status_code < 500 and latency < 1000:
                    health = 'warning'
                else:
                    health = 'critical'

                results[name] = {
                    'status': response.status_code,
                    'latency': latency,
                    'online': response.status_code < 500,
                    'health': health,
                    'analogy': analogy
                }
            except Exception as e:
                total_requests += 1
                results[name] = {
                    'status': 'ERROR',
                    'latency': 0,
                    'online': False,
                    'health': 'critical',
                    'analogy': analogy,
                    'error': str(e)
                }

        # Calculate overall metrics
        success_rate = (successful_requests / total_requests *
                        100) if total_requests > 0 else 0
        avg_latency = (total_latency /
                       total_requests) if total_requests > 0 else 0

        results['_metrics'] = {
            'success_rate': success_rate,
            'avg_latency': avg_latency,
            'total_requests': total_requests,
            'successful_requests': successful_requests
        }

        return results

    def display_dashboard(self):
        """Display the performance dashboard"""
        self.clear_screen()

        # Header
        print(self.get_colored_text("=" * 80, "cyan"))
        print(self.get_colored_text(
            "🚀 VIERLA PERFORMANCE MONITORING DASHBOARD", "bold"))
        print(self.get_colored_text("Food Delivery App-Analogous Metrics", "cyan"))
        print(self.get_colored_text("=" * 80, "cyan"))
        print()

        # Current time
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(
            f"📊 Last Updated: {self.get_colored_text(current_time, 'white')}")
        print()

        # API Connectivity
        api_status = "🟢 ONLINE" if self.test_api_connectivity() else "🔴 OFFLINE"
        print(f"🌐 API Status: {api_status}")
        print()

        # System Metrics
        system_metrics = self.get_system_metrics()
        if system_metrics:
            print(self.get_colored_text("📈 SYSTEM HEALTH METRICS", "bold"))
            print("-" * 40)

            # CPU
            cpu_status = self.get_status_indicator(
                system_metrics['cpu_percent'], {'good': 70, 'warning': 85})
            print(
                f"{cpu_status} CPU Usage: {system_metrics['cpu_percent']:.1f}%")

            # Memory
            memory_status = self.get_status_indicator(
                system_metrics['memory_percent'], {'good': 70, 'warning': 85})
            memory_used = self.format_bytes(system_metrics['memory_used'])
            memory_total = self.format_bytes(system_metrics['memory_total'])
            print(
                f"{memory_status} Memory: {system_metrics['memory_percent']:.1f}% ({memory_used}/{memory_total})")

            # Disk
            disk_status = self.get_status_indicator(
                system_metrics['disk_percent'], {'good': 80, 'warning': 90})
            disk_used = self.format_bytes(system_metrics['disk_used'])
            disk_total = self.format_bytes(system_metrics['disk_total'])
            print(
                f"{disk_status} Disk: {system_metrics['disk_percent']:.1f}% ({disk_used}/{disk_total})")

            print()

        # API Endpoint Tests with Food Delivery Analogies
        api_tests = self.test_api_endpoints()
        metrics = api_tests.get('_metrics', {})

        # Overall Performance Summary
        print(self.get_colored_text(
            "🍕 FOOD DELIVERY APP-ANALOGOUS METRICS", "bold"))
        print("-" * 50)

        if metrics:
            # Success Rate (Order Success Rate)
            success_rate = metrics['success_rate']
            success_color = "green" if success_rate >= 95 else "yellow" if success_rate >= 90 else "red"
            success_indicator = self.get_status_indicator(
                100 - success_rate, {'good': 5, 'warning': 10})
            print(
                f"{success_indicator} Order Success Rate: {self.get_colored_text(f'{success_rate:.1f}%', success_color)}")

            # Average Response Time (Delivery Time)
            avg_latency = metrics['avg_latency']
            latency_color = "green" if avg_latency < 200 else "yellow" if avg_latency < 500 else "red"
            latency_indicator = self.get_status_indicator(
                avg_latency, {'good': 200, 'warning': 500})
            print(
                f"{latency_indicator} Avg Delivery Time: {self.get_colored_text(f'{avg_latency:.0f}ms', latency_color)}")

            # Request Volume (Orders per minute - simulated)
            # Requests per 5-second interval
            rps = metrics['total_requests'] / 5
            rpm = rps * 12  # Approximate requests per minute
            volume_color = "green" if rpm > 0 else "yellow"
            print(
                f"📊 Order Volume: {self.get_colored_text(f'{rpm:.1f} orders/min', volume_color)}")

        print()

        # Individual Service Performance
        print(self.get_colored_text(
            "⚡ SERVICE PERFORMANCE (Restaurant Analogy)", "bold"))
        print("-" * 50)

        for endpoint_name, result in api_tests.items():
            if endpoint_name == '_metrics':
                continue

            if result['online']:
                # Health indicator
                health_colors = {
                    'excellent': 'green',
                    'good': 'green',
                    'warning': 'yellow',
                    'critical': 'red'
                }
                health_color = health_colors.get(result['health'], 'red')
                status_indicator = self.get_colored_text("●", health_color)

                # Latency with color coding
                latency_color = "green" if result['latency'] < 200 else "yellow" if result['latency'] < 500 else "red"
                latency_text = self.get_colored_text(
                    f"{result['latency']:.0f}ms", latency_color)

                # Service analogy
                analogy_text = self.get_colored_text(
                    f"({result['analogy']})", "cyan")

                print(
                    f"{status_indicator} {endpoint_name}: {result['status']} - {latency_text} {analogy_text}")
            else:
                status_indicator = self.get_colored_text("●", "red")
                analogy_text = self.get_colored_text(
                    f"({result['analogy']})", "cyan")
                print(f"{status_indicator} {endpoint_name}: OFFLINE {analogy_text}")

        print()

        # Performance Insights
        print(self.get_colored_text("🎯 PERFORMANCE INSIGHTS", "bold"))
        print("-" * 30)

        if metrics:
            if metrics['success_rate'] >= 99:
                print("✅ Excellent service reliability - customers are happy!")
            elif metrics['success_rate'] >= 95:
                print("👍 Good service reliability - minor issues detected")
            else:
                print(
                    "⚠️  Service reliability needs attention - customer experience at risk")

            if metrics['avg_latency'] < 200:
                print("🚀 Lightning fast response times - orders processed quickly")
            elif metrics['avg_latency'] < 500:
                print("⏱️  Acceptable response times - room for optimization")
            else:
                print("🐌 Slow response times - customers may experience delays")

        print()

        # Footer
        print(self.get_colored_text("=" * 80, "cyan"))
        print(
            f"🔄 Auto-refresh every {self.refresh_interval} seconds | Press Ctrl+C to exit")
        print(self.get_colored_text("=" * 80, "cyan"))

    def run(self):
        """Run the performance monitoring dashboard"""
        print(self.get_colored_text(
            "🚀 Starting Vierla Performance Monitoring Dashboard...", "green"))
        print(f"📊 Monitoring backend at: {self.api_base_url}")
        print(f"🔄 Refresh interval: {self.refresh_interval} seconds")
        print()

        try:
            while True:
                self.display_dashboard()
                time.sleep(self.refresh_interval)
        except KeyboardInterrupt:
            print(self.get_colored_text(
                "\n\n🛑 Performance monitoring stopped.", "yellow"))
            sys.exit(0)
        except Exception as e:
            print(self.get_colored_text(f"\n\n❌ Error: {e}", "red"))
            sys.exit(1)


if __name__ == "__main__":
    dashboard = SimplePerformanceDashboard()
    dashboard.run()
