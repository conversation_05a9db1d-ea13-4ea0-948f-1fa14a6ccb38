"""
Database Query Optimization Utilities for Vierla Backend
Based on Backend Agent Consultation for Production Performance
"""

import time
import logging
from typing import Dict, List, Any, Optional, Union
from django.db import connection, connections
from django.core.cache import cache
from django.conf import settings
from django.db.models import QuerySet, Model
from django.db.models.query import Prefetch
from contextlib import contextmanager
import hashlib
import json

# Configure database optimization logger
db_logger = logging.getLogger('vierla.database')

class QueryOptimizer:
    """
    Advanced query optimization utilities for Vierla backend.
    """
    
    def __init__(self):
        self.query_cache = {}
        self.slow_query_threshold = 100  # milliseconds
        self.cache_timeout = 300  # 5 minutes default
    
    @contextmanager
    def query_profiler(self, query_name: str):
        """Context manager for profiling database queries."""
        start_time = time.time()
        start_queries = len(connection.queries)
        
        try:
            yield
        finally:
            end_time = time.time()
            end_queries = len(connection.queries)
            
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            query_count = end_queries - start_queries
            
            # Log performance metrics
            if execution_time > self.slow_query_threshold:
                db_logger.warning(
                    f"Slow query detected: {query_name} took {execution_time:.2f}ms "
                    f"with {query_count} database queries"
                )
            else:
                db_logger.info(
                    f"Query performance: {query_name} took {execution_time:.2f}ms "
                    f"with {query_count} database queries"
                )
    
    def optimize_queryset(self, queryset: QuerySet, optimization_config: Dict[str, Any]) -> QuerySet:
        """
        Apply optimization strategies to a queryset.
        
        Args:
            queryset: The Django queryset to optimize
            optimization_config: Configuration for optimization strategies
        
        Returns:
            Optimized queryset
        """
        optimized_qs = queryset
        
        # Apply select_related for foreign key relationships
        if 'select_related' in optimization_config:
            optimized_qs = optimized_qs.select_related(*optimization_config['select_related'])
        
        # Apply prefetch_related for many-to-many and reverse foreign key relationships
        if 'prefetch_related' in optimization_config:
            prefetch_fields = optimization_config['prefetch_related']
            if isinstance(prefetch_fields, list):
                optimized_qs = optimized_qs.prefetch_related(*prefetch_fields)
            elif isinstance(prefetch_fields, dict):
                # Advanced prefetch with custom querysets
                prefetch_objects = []
                for field, config in prefetch_fields.items():
                    if isinstance(config, dict) and 'queryset' in config:
                        prefetch_objects.append(Prefetch(field, queryset=config['queryset']))
                    else:
                        prefetch_objects.append(field)
                optimized_qs = optimized_qs.prefetch_related(*prefetch_objects)
        
        # Apply only() for field selection optimization
        if 'only' in optimization_config:
            optimized_qs = optimized_qs.only(*optimization_config['only'])
        
        # Apply defer() for field deferring optimization
        if 'defer' in optimization_config:
            optimized_qs = optimized_qs.defer(*optimization_config['defer'])
        
        # Apply distinct() if needed
        if optimization_config.get('distinct', False):
            optimized_qs = optimized_qs.distinct()
        
        return optimized_qs
    
    def cache_query_result(self, cache_key: str, queryset: QuerySet, timeout: Optional[int] = None) -> List[Any]:
        """
        Cache queryset results with intelligent cache key generation.
        
        Args:
            cache_key: Base cache key
            queryset: Django queryset to cache
            timeout: Cache timeout in seconds
        
        Returns:
            Cached or fresh query results
        """
        if timeout is None:
            timeout = self.cache_timeout
        
        # Generate a hash of the query for cache invalidation
        query_hash = self._generate_query_hash(queryset)
        full_cache_key = f"{cache_key}:{query_hash}"
        
        # Try to get from cache first
        cached_result = cache.get(full_cache_key)
        if cached_result is not None:
            db_logger.info(f"Cache hit for query: {cache_key}")
            return cached_result
        
        # Execute query and cache result
        with self.query_profiler(f"cached_query_{cache_key}"):
            result = list(queryset)
        
        cache.set(full_cache_key, result, timeout)
        db_logger.info(f"Cached query result: {cache_key} for {timeout} seconds")
        
        return result
    
    def _generate_query_hash(self, queryset: QuerySet) -> str:
        """Generate a hash for the queryset to use in cache keys."""
        query_str = str(queryset.query)
        return hashlib.md5(query_str.encode()).hexdigest()[:16]
    
    def analyze_query_performance(self, queryset: QuerySet) -> Dict[str, Any]:
        """
        Analyze query performance and provide optimization suggestions.
        
        Args:
            queryset: Django queryset to analyze
        
        Returns:
            Performance analysis results
        """
        analysis = {
            'query_sql': str(queryset.query),
            'estimated_count': None,
            'suggestions': [],
            'performance_score': 100,  # Start with perfect score
        }
        
        # Check for potential N+1 problems
        query_sql = analysis['query_sql'].lower()
        if 'join' not in query_sql and hasattr(queryset.model, '_meta'):
            foreign_keys = [f.name for f in queryset.model._meta.get_fields() 
                          if f.many_to_one or f.one_to_one]
            if foreign_keys:
                analysis['suggestions'].append(
                    f"Consider using select_related({', '.join(foreign_keys)}) to avoid N+1 queries"
                )
                analysis['performance_score'] -= 20
        
        # Check for missing indexes
        if 'where' in query_sql and 'index' not in query_sql:
            analysis['suggestions'].append(
                "Consider adding database indexes for WHERE clause fields"
            )
            analysis['performance_score'] -= 15
        
        # Check for SELECT *
        if 'select *' in query_sql or len(query_sql) > 1000:
            analysis['suggestions'].append(
                "Consider using only() or defer() to select specific fields"
            )
            analysis['performance_score'] -= 10
        
        return analysis


class DatabaseConnectionOptimizer:
    """
    Database connection and pool optimization utilities.
    """
    
    @staticmethod
    def get_connection_stats() -> Dict[str, Any]:
        """Get database connection statistics."""
        stats = {}
        
        for alias in connections:
            conn = connections[alias]
            stats[alias] = {
                'vendor': conn.vendor,
                'queries_count': len(conn.queries),
                'is_usable': conn.is_usable(),
            }
            
            # Add connection pool stats if available
            if hasattr(conn, 'pool'):
                stats[alias]['pool_size'] = getattr(conn.pool, 'size', 'unknown')
                stats[alias]['pool_checked_out'] = getattr(conn.pool, 'checkedout', 'unknown')
        
        return stats
    
    @staticmethod
    def optimize_connection_settings() -> Dict[str, Any]:
        """
        Provide optimized database connection settings.
        
        Returns:
            Recommended database settings
        """
        return {
            'CONN_MAX_AGE': 600,  # 10 minutes
            'CONN_HEALTH_CHECKS': True,
            'OPTIONS': {
                'MAX_CONNS': 20,
                'MIN_CONNS': 5,
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
                'autocommit': True,
            },
            'TEST': {
                'CHARSET': 'utf8mb4',
                'COLLATION': 'utf8mb4_unicode_ci',
            }
        }


class QueryCacheManager:
    """
    Advanced query result caching with intelligent invalidation.
    """
    
    def __init__(self):
        self.cache_prefix = 'vierla_query_cache'
        self.default_timeout = 300  # 5 minutes
    
    def cache_model_queryset(self, model_class: Model, cache_key: str, 
                           queryset: QuerySet, timeout: Optional[int] = None) -> List[Any]:
        """
        Cache model queryset with automatic invalidation on model changes.
        
        Args:
            model_class: Django model class
            cache_key: Cache key identifier
            queryset: Queryset to cache
            timeout: Cache timeout in seconds
        
        Returns:
            Cached or fresh query results
        """
        if timeout is None:
            timeout = self.default_timeout
        
        # Include model version in cache key for invalidation
        model_version = self._get_model_version(model_class)
        full_cache_key = f"{self.cache_prefix}:{model_class.__name__}:{model_version}:{cache_key}"
        
        # Try cache first
        cached_result = cache.get(full_cache_key)
        if cached_result is not None:
            return cached_result
        
        # Execute and cache
        result = list(queryset)
        cache.set(full_cache_key, result, timeout)
        
        return result
    
    def invalidate_model_cache(self, model_class: Model) -> None:
        """Invalidate all cached queries for a specific model."""
        # Increment model version to invalidate all related caches
        version_key = f"{self.cache_prefix}:version:{model_class.__name__}"
        current_version = cache.get(version_key, 0)
        cache.set(version_key, current_version + 1, timeout=86400)  # 24 hours
        
        db_logger.info(f"Invalidated cache for model: {model_class.__name__}")
    
    def _get_model_version(self, model_class: Model) -> int:
        """Get current version number for a model (for cache invalidation)."""
        version_key = f"{self.cache_prefix}:version:{model_class.__name__}"
        return cache.get(version_key, 0)


# Global instances
query_optimizer = QueryOptimizer()
cache_manager = QueryCacheManager()

# Optimization configurations for common Vierla models
OPTIMIZATION_CONFIGS = {
    'ServiceProvider': {
        'select_related': ['user', 'business_profile'],
        'prefetch_related': ['services', 'reviews', 'availability_slots'],
    },
    'Service': {
        'select_related': ['provider', 'category'],
        'prefetch_related': ['reviews', 'bookings'],
    },
    'Booking': {
        'select_related': ['customer', 'service', 'provider'],
        'prefetch_related': ['payments', 'reviews'],
    },
    'Review': {
        'select_related': ['customer', 'service', 'provider'],
    },
    'User': {
        'prefetch_related': ['groups', 'user_permissions'],
    },
}

def optimize_common_queries():
    """Apply optimizations to commonly used queries."""
    db_logger.info("Applying database query optimizations...")
    
    # This would be called during app initialization
    # to set up query optimization patterns
    
    return True
